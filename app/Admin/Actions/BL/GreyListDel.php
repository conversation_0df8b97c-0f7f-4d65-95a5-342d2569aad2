<?php


namespace App\Admin\Actions\BL;


use App\Models\BL\ProjectProduct;
use App\Service\RedisLimitUtils;
use Carbon\Carbon;
use Encore\Admin\Actions\RowAction;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class GreyListDel extends RowAction
{
    public $name = '批量删除';

//    public function dialog() {
//        $this->confirm('确认重提?');
//    }

//    public function href(){
//
//    }

    public function form(ProjectProduct $model)
    {
        $this->file('bl_file', '黑名单文件：')->required();
        $this->confirm('确定导入？');
    }

    public function handle(ProjectProduct $model, Request $request)
    {
//        $opt_logger = Log::channel('optlog');
        $logger    = Log::channel('bl_import_log');
        $max_count = 5000;

        $request->validate(
            [
                'bl_file' => 'required|mimes:txt',
            ], [
                'bl_file.required' => "导入文件不能为空",
                'bl_file.mimes'    => "支持的文件格式：txt",
            ]
        );

        try {
            //处理文件
            $up_file = $request->file('bl_file');
            $ext     = $up_file->getClientOriginalExtension();
            //文件保存到storage/app/upload/bl
            $name      = 'bl' . sprintf('%d', Admin::user()->id) . date('YmdHis') . mt_rand(10, 99); //不包含扩展名
            $storePath = 'upload' . DIRECTORY_SEPARATOR . 'bl' . DIRECTORY_SEPARATOR . date('Ym');
            if (!is_dir(storage_path('app' . DIRECTORY_SEPARATOR . $storePath))) {
                mkdir(storage_path('app' . DIRECTORY_SEPARATOR . $storePath), 0755, true);
            }
            $path = 'app' . DIRECTORY_SEPARATOR . $up_file->storeAs($storePath, $name . '.' . $ext);
            $url  = storage_path($path);//获取文件url

            $logger->info('导入黑名单，' . $url . '。原文件名：' . $up_file->getClientOriginalName());

            $codes = getFileContent($url, 2);
            $codes = array_filter($codes);

            if (empty($codes)) {
                return $this->response()->error('黑名单列表为空！')->refresh();
            }

            if (count($codes) > $max_count) {
                return $this->response()->error("每批最多能导入" . $max_count . "个黑名单！")->refresh();
            }

            //查询存在的黑名单
            $bl_exists = DB::table('bl_greylists')
                ->where('project_id', $model->project_id)
                ->where('product_id', $model->product_id)
                ->select(['id', 'charge_account'])
                ->get()
                ->pluck('id', 'charge_account');

            $now     = date('Y:m:d H:i:s');
            $user_id = Admin::user()->id;

            $insert_data = [];
            $exist_ids   = [];

            foreach ($codes as $c) {
                if ($bl_exists->has($c)) {
                    $exist_ids[] = $bl_exists->get($c);
                } else {
                    $insert_data[] = [
                        'project_id'     => $model->project_id,
                        'project_no'     => $model->project->project_no,
                        'product_id'     => $model->product_id,
                        'product_code'   => $model->product->product_code,
                        'charge_account' => $c,
                        'status'         => 1,
                        'created_by'     => $user_id,
                        'created_at'     => $now,
                    ];
                }
            }

            unset($bl_exists);

            DB::beginTransaction();

            try {
                $affect_rows = 0;
                if (!empty($exist_ids)) {
                    $affect_rows = DB::table('bl_greylists')
                        ->whereIn('id', $exist_ids)
                        ->where('status', 0)
                        ->update(['status' => 1]);
                }

                if (!empty($insert_data)) {
                    DB::table('bl_greylists')->insert($insert_data);
                }

                DB::commit();

            } catch (\Exception $ex) {
                DB::rollBack();
                throw $ex;
            }

            $logger->info($name . sprintf('导入成功%s个，更新%s个。', count($insert_data), $affect_rows));
            return $this->response()->success(sprintf('导入成功，共导入%s个。', $affect_rows + count($insert_data)))->refresh();

        } catch (\Exception $exception) {
            Log::error($exception->getMessage(), $exception->getTrace());
            $logger->error('导入失败。' . $exception->getMessage());
            return $this->response()->error('导入失败！' . $exception->getMessage())->refresh();
        }
    }
}
