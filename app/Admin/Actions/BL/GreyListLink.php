<?php


namespace App\Admin\Actions\BL;


use App\Models\BL\ProjectProduct;
use App\Service\RedisLimitUtils;
use Carbon\Carbon;
use Encore\Admin\Actions\RowAction;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Content;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class GreyListLink extends RowAction
{
    public $name = '查看列表';

    public function href()
    {
        $model = $this->getRow();
        return admin_url(sprintf('bl/grey-lists?project_id=%s&product_id=%s', $model->project_id, $model->product_id));
    }
}
