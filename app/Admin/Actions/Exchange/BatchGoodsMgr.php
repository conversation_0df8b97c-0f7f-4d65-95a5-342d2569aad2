<?php

namespace App\Admin\Actions\Exchange;

use App\Models\ExchangeBatch;
use App\Models\ExchangeDetail;
use App\Models\Order;
use App\Models\ViewExchangeOrder;
use Encore\Admin\Actions\RowAction;
use Encore\Admin\Facades\Admin;
use Illuminate\Support\Facades\DB;

/**
 * 兑换码批次商品管理跳转
 * @date: 2024/10/17
 */
class BatchGoodsMgr extends RowAction
{
    public $name = '商品配置';

    public function href()
    {
        return route('exb-goods-mgr', ['batch_id' => $this->row->id]);
//        return admin_url(sprintf('bl/grey-lists?project_id=%s&product_id=%s', $this->id));

    }

}
