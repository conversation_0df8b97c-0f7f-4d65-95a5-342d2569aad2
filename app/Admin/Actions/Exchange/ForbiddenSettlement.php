<?php

namespace App\Admin\Actions\Exchange;

use App\Models\ExchangeBatch;
use App\Models\ExchangeDetail;
use App\Models\ExchangeForbiddenBatch;
use App\Models\Order;
use App\Models\ViewExchangeOrder;
use Encore\Admin\Actions\RowAction;
use Encore\Admin\Facades\Admin;
use Illuminate\Support\Facades\DB;

/**
 * 核销兑换码结算
 */
class ForbiddenSettlement extends RowAction
{
    public $name = '结算';

    public function dialog()
    {
        $this->confirm('确认结算该核销批次?');
    }

    public function handle(ExchangeForbiddenBatch $model)
    {
        try {
            if (admin_is_third_user()) {
                return $this->response()->error('无权结算权限，请与管理员联系！')->refresh();
            }

            $model->settlement_status = ExchangeForbiddenBatch::SETTLEMENT_STATUS_YES;
            $model->settlement_date   = date('Y-m-d H:i:s');
            if ($model->save()) {
                return $this->response()->success('更新结算状态成功！')->refresh();
            } else {
                return $this->response()->error('更新结算状态失败！')->refresh();
            }
        } catch (\Exception $e) {
            return $this->response()->error('更新结算状态失败。错误：' . $e->getMessage())->refresh();

        }

    }


}
