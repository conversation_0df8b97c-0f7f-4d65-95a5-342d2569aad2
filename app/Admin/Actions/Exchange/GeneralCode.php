<?php

namespace App\Admin\Actions\Exchange;

use App\Models\ExchangeBatch;
use App\Models\ExchangeDetail;
use Encore\Admin\Actions\RowAction;
use Encore\Admin\Facades\Admin;
use Illuminate\Support\Facades\DB;


class GeneralCode extends RowAction
{
    public $name = '生成兑换码';

    public function dialog()
    {
        $this->confirm('确认生成兑换码?');
//        $this->info('无需生成','无需生成兑换码',['showCancelButton'=>false]);
    }

    public function handle(ExchangeBatch $model)
    {

        return $this->doGenerate($model);

    }

    private function doGenerate(ExchangeBatch $model)
    {
        $wait_num      = 0; //待生成数量
        $generated_num = 0; //已生成数量
        $max_num_batch = 2000; //每次批量插入的数量

        try {

            $total = DB::table('exchange_details')->where('exchange_batch_id', $this->row->id)->count();

            if ($total >= $this->row->total_num) {
                return $this->response()->error('无需生成兑换码')->refresh();
            }

            $activity = DB::table('activities')->find($this->row->activity_id);//获取活动信息
            if (!$activity) {
                return $this->response()->error('没找到该批次所属活动信息！')->refresh();
            }
            if ($activity->exchange_code_len < 6 || $activity->exchange_code_len > 32) {
                return $this->response()->error("活动中配置的兑换码长度为{$activity->exchange_code_len}，应在6~32之间。")->refresh();
            }

            $wait_num = $this->row->total_num - $total;

            $loop = ceil($wait_num / $max_num_batch);//大循环次数

            $num_begin = 10000 + $total + 1;
            for ($i = 0; $i < $loop; $i++) {

                $curr_generate_num = $i < ($loop - 1) ? $max_num_batch : ($wait_num - $i * $max_num_batch); //当次循环生成的条数

                $insert_details = [];

                for ($j = 0; $j < $curr_generate_num; $j++) {

                    $insert_details[] = [
                        'activity_id'       => $this->row->activity_id,
                        'exchange_batch_id' => $this->row->id,
                        'num'               => 'N' . $this->row->batch_no . sprintf('%05s', $num_begin),
                        'code'              => $this->getRandomCode($activity->exchange_code_len),
                        'endtime'           => $this->row->endtime,
                        'exchanged_times'   => 0,
                        'status'            => ExchangeDetail::STATUS_NO_EXCHANGE,
                        'created_by'        => Admin::user()->id,
                        'created_at'        => date('Y-m-d H:i:s'),
                    ];

                    $num_begin = $num_begin + 1;
                }

                if (DB::table('exchange_details')->insert($insert_details)) {
                    $generated_num += count($insert_details);
                }

            } //end for loop

            try {
                DB::table('exchange_batches')->where('id', $this->row->id)->update([
                    'generated_num' => DB::table('exchange_details')->where('exchange_batch_id', $this->row->id)->count()
                ]);
            } catch (\Exception $e) {

            }

            return $this->response()->success('生成成功。生成兑换码' . $generated_num . '个。')->refresh();

        } catch (\Exception $e) {

            return $this->response()->error('生成失败。错误：' . $e->getMessage())->refresh();

        }
    }


    private $randomCode = [];

    public function getRandomCode($code_length = 8)
    {
        $code = getCardStr($code_length);
        while (in_array($code, $this->randomCode) || ExchangeDetail::where('code', $code)->count() > 0) {
            $code = getCardStr($code_length);
        }

        $this->randomCode[] = $code;

        return $code;
    }

}
