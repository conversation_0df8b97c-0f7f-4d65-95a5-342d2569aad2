<?php

namespace App\Admin\Actions\Exchange;

use App\Models\ExchangeBatch;
use App\Models\ExchangeDetail;
use App\Models\Order;
use App\Models\ViewExchangeOrder;
use Encore\Admin\Actions\RowAction;
use Encore\Admin\Facades\Admin;
use Illuminate\Support\Facades\DB;

/**
 * 取消兑换码订单。兑换码兑换次数复原
 * Date: 12/6/21
 */
class RollBack extends RowAction
{
    public $name = '取消兑换码订单';

    public function dialog() {
        $this->confirm('确认取消兑换码订单?');
    }

    public function handle(ViewExchangeOrder $model) {

        return $this->doRollBack($model);

    }

    private function doRollBack(ViewExchangeOrder $model) {

        try {

            $ret = Order::exchangeRollback($model->id);

            return $this->response()->success($ret['error_msg'])->refresh();

        } catch (\Exception $e) {

            return $this->response()->error('取消兑换码订单失败。错误：' . $e->getMessage())->refresh();

        }
    }

}
