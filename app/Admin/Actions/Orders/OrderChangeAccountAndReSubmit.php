<?php


namespace App\Admin\Actions\Orders;


use App\Http\Controllers\Api\SysCode\SysCode;
use App\Models\Order;
use App\Models\OrderSub;
use Encore\Admin\Actions\RowAction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class OrderChangeAccountAndReSubmit extends RowAction
{
    public $name = '更改充值账号后重提';

    public function form() {
        $options = [
            1 => '是',
            0 => '否',
        ];
        $this->text('charge_account', '新充值账号：')->required();
        $this->radio('is_change_all_fail', '是否同时更新所属主订单，及主订单下所有失败子订单的充值账号？')->options($options)->default(0);
        $this->radio('is_resubmit_all_fail', '是否重提该主订单下所有失败子订单？')->options($options)->default(0);

        $this->confirm('请确认所填是否正确，确定提交？');
    }

    public function handle(OrderSub $model, Request $request) {
        $logger = Log::channel('admin_special_optlog');
        $result = [];

        $new_charge_account   = $request->get('charge_account');//新充值账号
        $is_change_all_fail   = $request->get('is_change_all_fail');//
        $is_resubmit_all_fail = $request->get('is_resubmit_all_fail');
        $change_msg           = '';//更新充值账号的结果描述。

        if (empty($new_charge_account)) {
            return $this->response()->error('新的充值账号不能为空')->refresh();
        }

        $sub_org_charge_account = [];
        $subs                   = OrderSub::where(['order_id' => $model->order_id, 'status' => SysCode::ORDER_SUB_STATUS_4])->get();
        foreach ($subs as $sub) {
            $sub_org_charge_account[$sub->sub_order_no] = $sub->charge_account;
        }

        DB::beginTransaction();
        try {
            if ($is_change_all_fail == '1') {
                //更新所有相关订单充值账号
                if (DB::table('order_subs')
                    ->where(['order_id' => $model->order_id, 'status' => SysCode::ORDER_SUB_STATUS_4, 'goods_type' => SysCode::GOODS_TYPE_3])
                    ->update(['charge_account' => $new_charge_account])
                ) {

                    DB::table('orders')
                        ->where(['id' => $model->order_id])
                        ->update(['charge_account' => $new_charge_account]);
                }
            } else {
                //只更新本子订单充值账号
                DB::table('order_subs')
                    ->where(['id' => $model->id, 'status' => SysCode::ORDER_SUB_STATUS_4, 'goods_type' => SysCode::GOODS_TYPE_3])
                    ->update(['charge_account' => $new_charge_account]);
            }

            $change_msg = '充值账号更新成功。';

            if ($is_resubmit_all_fail == '1') {
                //重提所有订单
                $result = Order::reSubmitAllSub($model->order_id);
            } else {
                //只重提该子订单
                $result = OrderSub::reSubmit($model->id);
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $result['error_msg'] = $e->getMessage();
            $logger->error($e->getMessage(), $e->getTrace());
        }

        $result['error_msg'] = $change_msg . $result['error_msg'];

        if ($is_change_all_fail == '1') {
            $result['org_charge_account'] = $sub_org_charge_account;
        } else {
            $result['org_charge_account'] = $model->charge_account;
        }
        $result['new_charge_account']   = $new_charge_account;
        $result['is_change_all_fail']   = $is_change_all_fail;
        $result['is_resubmit_all_fail'] = $is_resubmit_all_fail;

        $logger->info(json_encode($result, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE));

        return $this->response()->success($change_msg . ($result['error_msg'] ?? '处理成功'))->refresh();
    }
}

