<?php


namespace App\Admin\Actions\Orders;


use App\Models\OrderSub;
use Encore\Admin\Actions\RowAction;
use Illuminate\Support\Facades\Log;

class OrderReSubmit extends RowAction
{
    public $name = '失败重提';

    public function dialog() {
        $this->confirm('确认重提?');
    }

    public function handle(OrderSub $model) {
        $logger = Log::channel('optlog');
        $result = OrderSub::reSubmit($model->id);

        $logger->info(json_encode($result, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE));
        return $this->response()->success($result['error_msg'] ?? '处理成功')->refresh();
    }
}
