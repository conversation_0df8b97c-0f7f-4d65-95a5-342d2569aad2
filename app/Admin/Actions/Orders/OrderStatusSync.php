<?php

namespace App\Admin\Actions\Orders;

use App\Models\Order;
use Encore\Admin\Actions\RowAction;
use Illuminate\Support\Facades\Log;

class OrderStatusSync extends RowAction
{
    public $name = '订单状态重置';

    public function dialog() {
        $this->confirm('重置当前订单状态', '确定要根据子订单状态，重置当前订单状态?', ['confirmButtonText' => "确定", 'cancelButtonText' => '不干']);
    }

    public function handle(Order $model) {
        $result = Order::dealStatus($model->id);
        Log::channel('optlog')->info(json_encode($result, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE));
        return $this->response()->success($result['error_msg'] ?? '处理成功')->refresh();
    }

}
