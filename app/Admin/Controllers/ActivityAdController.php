<?php

namespace App\Admin\Controllers;


use App\Models\Activity;
use App\Models\ActivityAd;
use App\Models\ProjectArea;
use App\Models\ProjectAreaAd;
use Carbon\Carbon;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use App\Admin\SysCode\SysCode;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Auth\Database\Administrator;
use Encore\Admin\Controllers\AdminController;

class ActivityAdController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = '活动广告配置';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new ActivityAd());

        $grid->column('id', __('Id'));
        $grid->column('activity.activity_name', __('活动名称'));
        $grid->column('show_title', __('广告展示标题'));
        $grid->column('show_img_url', __('广告图片'))->image(config('app.oss_url'), 100, 100);
        $grid->column('jump_url', __('图片跳转地址'));
        $grid->column('sort', __('排序'))->help('数字越小越靠前');
        $grid->column('status', __('状态'))->using(SysCode::$common_status)->dot(SysCode::$common_dot);
        $grid->column('valid_start_at', __('生效时间'))->help('广告图生效时间');
        $grid->column('valid_end_at', __('失效时间'))->help('广告图失效时间');
//        $grid->column('created_by', __('Created by'))->display(function ($created_by) {
//            return Administrator::where('id', $created_by)->value('name');
//        })->hide();

        $grid->column('updated_by', __('更新人'))->display(function ($updated_by) {
            return Administrator::where('id', $updated_by)->value('name');

        });

//        $grid->column('created_at', __('Created at'));
        $grid->column('updated_at', __('更新时间'));

        $grid->actions(function ($actions) {
            $actions->disableView();
        });

        $grid->filter(function ($filter) {
            $filter->disableIdFilter();
            $filter->equal('activity_id', __('活动名称'))->select(function () {
                return Activity::where(['status'=> SysCode::COMMON_STATUS_1])->pluck('activity_name', 'id');
            });
        });

        return $grid;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $form = new Form(new ActivityAd());

        $form->select('activity_id', __('项目板块名称'))->options(function () {
            return Activity::where(['status'=> SysCode::COMMON_STATUS_1])->pluck('activity_name', 'id');
        });
        $form->text('show_title', __('广告展示标题'))->help('Tips:为空则不显示。');
        $form->image('show_img_url', __('广告图片'))->retainable();
        $form->text('jump_url', __('跳转地址'))->help('Tips:点击图片的跳转地址');
        $form->hidden('created_by')->value(Admin::user()->id);
        $form->hidden('updated_by')->value(Admin::user()->id);
        $form->number('sort', __('排序'))->default(1000)->help('数字越小越靠前');
        $states = [
            'on' => ['value' => 1, 'text' => '启用', 'color' => 'success'],
            'off' => ['value' => 0, 'text' => '禁用', 'color' => 'danger'],
        ];
        $form->switch('status', __('是否启用'))->states($states)->default(1);
        $form->datetime('valid_start_at',
            __('生效时间'))->rules('required|date_format:Y-m-d H:i:s')->help('Tips:广告图生效时间');
        $form->datetime('valid_end_at',
            __('失效时间'))->help('Tips:广告图失效时间')->default(Carbon::now()->endOfMonth());
        return $form;
    }
}
