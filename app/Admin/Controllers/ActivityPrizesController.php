<?php

namespace App\Admin\Controllers;

use App\Models\Activity;
use App\Models\ActivityPrize;
use App\Models\Goods;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Controllers\AdminController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Encore\Admin\Facades\Admin;
use App\Admin\SysCode\SysCode;

class ActivityPrizesController extends AdminController
{

    protected $title = '奖品配置';

    protected function grid()
    {
        $grid = new Grid(new ActivityPrize());
        $grid->model()->orderByDesc('id');

        $grid->actions(function ($actions) {
            $actions->disableDelete(false);
            $actions->disableView();
        });

        $grid->column('id', __('Id'))->sortable();

        $grid->column('activity.activity_name', __('活动名称'));
        $grid->column('prize_level', __('礼品等级'))->using(ActivityPrize::$level);
        $grid->column('prize_level2', __('礼品等级2'))->using(ActivityPrize::$level);
        $grid->column('goods.id', __('礼品ID'))->hide();
        $grid->column('goods.goods_name', __('礼品名称'));
        $grid->column('order_by', __('排序'))->hide();
        $grid->column('created_at', __('创建时间'));
        $grid->column('updated_at', __('修改时间'))->hide();
        $grid->column('created_by', __('创建人'))->display(function ($created_by) {
            return DB::table('admin_users')->where('id', $created_by)->value('name');
        })->hide();
        $grid->column('updated_by', __('更新人'))->display(function ($updated_by) {
            return DB::table('admin_users')->where('id', $updated_by)->value('name');
        })->hide();
        $grid->column('status', __('状态'))->using(SysCode::$common_status)->dot(SysCode::$common_dot);

        //查询过滤
        $grid->filter(function ($filter) {

            $filter->equal('activity.id', '活动名称')->select(Activity::all(['id', 'activity_name'])->sortByDesc('id')->pluck('activity_name', 'id'));
            $filter->equal('prize_level', '礼品等级')->select(ActivityPrize::$level);
            $filter->like('goods.goods_name', '礼品名称');

        });

        return $grid;
    }

    protected function form()
    {
        $form = new Form(new ActivityPrize());

        $form->select('activity_id', __('活动名称'))->options(function () {
            return Activity::where(['status' => 1])->orderBy('id', 'desc')->pluck('activity_name', 'id');
        })->required();
        $form->radio('prize_level', __('礼品等级'))->options(ActivityPrize::$level)->default(1);
        $form->number('prize_level2', __('礼品等级二级'))->min(1)->default(1);
        $form->select('goods_id', __('商品名称'))->options(function () {
            return Goods::where(['status' => 1])->orderBy('id', 'desc')->select('goods_name', 'id')->get()->map(function ($goods) {
                $goods->id_goods_name = "[ {$goods->id} ] {$goods->goods_name}";
                return $goods;
            })->pluck('id_goods_name', 'id');

        })->required();
        $form->number('order_by', __('排序'))->min(1)->default(1);
        $states = [
            'on'  => ['value' => 1, 'text' => '启用', 'color' => 'primary'],
            'off' => ['value' => 0, 'text' => '禁用', 'color' => 'default'],
        ];
        $form->switch('status', '状态')->states($states)->default(1);

        $form->hidden('created_by')->value(Admin::user()->id);
        $form->hidden('updated_by')->value(Admin::user()->id);

        return $form;
    }

    public function getGoodsList(Request $request)
    {

        $activity_id = $request->get('q');

        $goods = DB::table('activity_prizes')
            ->leftJoin('goods', 'activity_prizes.goods_id', '=', 'goods.id')
            ->where('activity_prizes.activity_id', $activity_id)
            ->orderBy('id', 'desc')
            ->get(['goods.id', 'goods.goods_name']);

        $ret = [];
        foreach ($goods as $g) {
            $ret[] = ['id' => $g->id, 'text' => $g->goods_name];
        }

//        $arr = ['id' => 0, 'text' => '无'];
//        array_unshift($ret, $arr);

        return $ret;
    }


}
