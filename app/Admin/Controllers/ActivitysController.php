<?php

namespace App\Admin\Controllers;

use App\Models\Activity;
use Encore\Admin\Auth\Database\Administrator;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Controllers\AdminController;
use Illuminate\Support\Facades\DB;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Show;
use App\Service\Aes;
use App\Models\Customer;

class ActivitysController extends AdminController
{

    protected $title = '活动';

    protected function grid()
    {
        $grid = new Grid(new Activity());
        $grid->model()->orderByDesc('id');

        $grid->actions(function ($actions) {
            $actions->disableDelete();
        });

        $grid->column('id', __('Id'))->hide();
        $grid->column('customer.cust_name', __('客户'));
        $grid->column('activity_name', __('活动名称'));
        $grid->column('activity_type', __('活动标识'));
        $grid->column('begin_time', __('活动开始时间'));
        $grid->column('end_time', __('活动结束时间'));
        $grid->column('exchange_code_len', __('兑换码长度'));
        $grid->column('exchange_url', __('短链url'))->hide();
        $grid->column('is_send_sms', __('是否发短信'))->using(Activity::$is_send_sms)->help('是否推送到订单网关平台发送')->hide();
        $grid->column('is_local_send_sms', __('是否平台自己发短信'))->using(Activity::$is_send_sms)->help('个性化配置发送短信')->hide();
        $grid->column('channel_appid', __('Qcp1 appid'));
        $grid->column('channel2_appid', __('Qcp2 appid'));
        $grid->column('status', __('状态'))->using(Activity::$status)->dot([0 => 'danger', 1 => 'success']);
        $grid->column('created_at', __('创建时间'));
        $grid->column('updated_at', __('修改时间'))->hide();
        $grid->column('created_by', __('创建人'))->display(function ($created_by) {
            return DB::table('admin_users')->where('id', $created_by)->value('name');
        })->hide();
        $grid->column('updated_by', __('更新人'))->display(function ($updated_by) {
            return DB::table('admin_users')->where('id', $updated_by)->value('name');
        })->hide();

        //查询过滤
        $grid->filter(function ($filter) {
            $filter->like('activity_name', '活动名称');
            $filter->equal('status', '状态')->select(Activity::$status);
            $filter->between('created_at', '创建时间')->datetime();
        });

        return $grid;
    }

    protected function form()
    {
        $form = new Form(new Activity());

        $form->select('customer_id', __('所属客户'))->options(function () {
            return Customer::where(['status' => Customer::CUSTOMER_STATUS_ON])->pluck('cust_name', 'id');
        })->required();
        $form->text('activity_name', __('活动名称'));
        $form->text('activity_type', __('活动标识'));
        $form->datetime('begin_time', __('活动开始时间'))->default(date('Y-m-d 00:00:00'));
        $form->datetime('end_time', __('活动结束时间'))->default(date('Y-m-d 23:59:59'));
        //        $form->radio('is_send_sms', __('是否发短信'))->options(Activity::$is_send_sms);
//        $form->radio('is_local_send_sms', __('是否平台自己发短信'))->options(Activity::$is_send_sms);
        $form->number('exchange_code_len', __('兑换码长度'))->min(6)->max(32)->default(8);
        $form->text('exchange_url', __('短链url'))->help('短链形式兑换码导出时附加的url。导出时url中的<span style="color:red;">${0}</span>会被替换成兑换码。例如：http://g.limeb.cn/xxx/?s=<span style="color:red;">${0}</span>');
        $form->text('channel_appid', __('Qcp1 Appid'))->rules('max:32');
        $form->text('channel_secketkey', __('Qcp1 Secret Key'))->rules('max:32');
        $form->text('channel2_appid', __('Qcp2 Appid'))->rules('max:32');
        $form->text('channel2_secret_key', __('Qcp2 签名密钥'))->rules('max:32');
        $form->text('channel2_encrypt_key', __('Qcp2 加密密钥'))->rules('max:64');
        $form->text('channel2_encrypt_iv', __('Qcp2 加密向量'))->rules('max:64');
        $states = [
            'on' => ['value' => 1, 'text' => '启用', 'color' => 'primary'],
            'off' => ['value' => 0, 'text' => '禁用', 'color' => 'default'],
        ];
        $form->switch('status', '状态')->states($states)->default(1);

        $form->hidden('created_by')->value(Admin::user()->id);
        $form->hidden('updated_by')->value(Admin::user()->id);
        return $form;
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        $show = new Show(Activity::findOrFail($id));
        $show->field('activity_name', __('活动名称'));
        $show->field('activity_type', __('活动标识'));
        $show->field('activity_type_aes', __('活动标识加密串'))->as(function () {
            return (new Aes(config('api.app_key')))->encrypt($this->activity_type);
        });
        $show->field('begin_time', __('活动开始时间'));
        $show->field('end_time', __('活动结束时间'));
        $show->field('exchange_code_len', __('兑换码长度'))->min(6)->max(32)->default(8);
        $show->field('exchange_url', __('短链url'));
        $show->field('status', __('状态'))->using(Activity::$status);
        $show->field('channel_appid', __('Qcp1 Appid'));
        $show->field('channel_secketkey', __('Qcp1 Secret Key'));
        $show->field('channel2_appid', __('Qcp2 Appid'));
        $show->field('channel2_secret_key', __('Qcp2 Secret Key'));
        $show->field('channel2_encrypt_key', __('Qcp2 Encrypt Key'));
        $show->field('channel2_encrypt_iv', __('Qcp2 Encrypt IV'));
        $show->field('created_by', __('创建者'))->as(function ($created_by) {
            return Administrator::where('id', $created_by)->value('name');
        });
        $show->field('created_at', __('创建时间'));
        $show->field('updated_by', __('更新者'))->as(function ($created_by) {
            return Administrator::where('id', $created_by)->value('name');
        });
        $show->field('updated_at', __('更新时间'));

        return $show;
    }
}
