<?php

namespace App\Admin\Controllers;

use Encore\Admin\Controllers\AuthController as BaseAuthController;
use Encore\Admin\Facades\Admin;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Validator;

class AuthController extends BaseAuthController
{
    public function getLogin() {
        if (\request('s') != 'lime' && \request('s') != 'hx'&& \request('s') != 'gd') {
//            abort(404,'PAGE NOT FOUND');
//            abort(403,'Unauthorized Action');
            return view('admin.login_s_rdr');
        }
        if (!Auth::guard('admin')->guest()) {
            return redirect(config('admin.route.prefix'));
        }
        session(['admin_login_extra' => '?' . \request()->getQueryString()]);//?s=lime
        return view('admin.login');
    }

    public function postLogin(Request $request) {
        $credentials = $request->only(['username', 'password', 'captcha']);
        $validator   = Validator::make($credentials, [
            'username' => 'required',
            'password' => 'required',
            'captcha'  => 'required|captcha'
        ]);
        if ($validator->fails()) {
            return Redirect::back()->withInput()->withErrors($validator);
        }
        unset($credentials['captcha']);
        if (Auth::guard('admin')->attempt($credentials)) {
            admin_toastr(trans('admin.login_successful'));
            return redirect()->intended(config('admin.route.prefix'));
        }
        return Redirect::back()->withInput()->withErrors(['username' => $this->getFailedLoginMessage()]);
    }

    protected function getFailedLoginMessage() {
        return Lang::has('auth.failed')
            ? trans('auth.failed')
            : 'These credentials do not match our records.';
    }

    /**
     * User logout.
     *
     * @return Redirect
     */
    public function getLogout(Request $request)
    {
        $login_extra = session('admin_login_extra');//?s=lime

        $this->guard()->logout();

        $request->session()->invalidate();

        if($login_extra){
            session(['admin_login_extra' => $login_extra]);//?s=lime
        }

        return redirect(config('admin.route.prefix'));
    }
}
