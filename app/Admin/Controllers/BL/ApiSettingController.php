<?php

namespace App\Admin\Controllers\BL;

use App\Admin\SysCode\SysCode;
use App\Models\BL\ApiSetting;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Show;

class ApiSettingController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = 'Api接口配置';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new ApiSetting());

//        $grid->column('id', __('Id'));
        $grid->column('appid', __('Appid'));
        $grid->column('app_name', __('App Name'));
        $grid->column('secret_key', __('签名密钥'));
        $grid->column('ips', __('IPs'));
        $grid->column('remark', __('备注'));
        $grid->column('status', __('状态'))->using(SysCode::$common_status);
        $grid->column('created_at', __('添加时间'));

        $grid->disableFilter(true);

        $grid->actions(function ($actions) {
            $actions->disableDelete(true);
            $actions->disableView(true);
        });

        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        $show = new Show(ApiSetting::findOrFail($id));

        $show->field('id', __('Id'));
        $show->field('appid', __('Appid'));
        $show->field('app_name', __('App name'));
        $show->field('secret_key', __('签名密钥'));
        $show->field('ips', __('IPs'));
        $show->field('remark', __('备注'));
        $show->field('status', __('状态'))->using(SysCode::$common_status);
        $show->field('created_at', __('添加时间'));

        return $show;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $form = new Form(new ApiSetting());

        $form->text('appid', __('Appid'));
        $form->text('app_name', __('App name'));
        $form->text('secret_key', __('签名密钥'));
        $form->text('ips', __('IPs'));
        $form->text('remark', __('备注'));
        $form->switch('status', __('状态'))->states(SysCode::$common_states)->default(1);

        $form->submitted(function (Form $form) {
            $model = $form->model();
            if ($form->isEditing()) {
                $model->updated_by = Admin::user()->id;
            } else {
                $model->created_by = Admin::user()->id;
            }
        });

        return $form;
    }
}
