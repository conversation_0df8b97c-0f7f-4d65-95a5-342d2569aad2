<?php

namespace App\Admin\Controllers\BL;

use App\Admin\SysCode\SysCode;
use App\Models\BL\GreyList;
use App\Models\BL\Product;
use App\Models\BL\Project;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Grid\Filter;
use Encore\Admin\Layout\Content;
use Encore\Admin\Show;
use App\Admin\Extensions\WidgetForm;
use Encore\Admin\Widgets;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class GreyListController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = '黑名单列表';

    protected $import_max_count = 5000;

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new GreyList());

        if (request('project_id')) {
            $grid->model()->where('project_id', request('project_id'));
        }
        if (request('product_id')) {
            $grid->model()->where('product_id', request('product_id'));
        }

        $grid->column('project.project_name', __('项目'));
        $grid->column('product.product_name', __('产品'));
        $grid->column('charge_account', __('充值账号'));
        $grid->column('status', __('状态'))->using(SysCode::$common_status)->dot([0 => 'danger', 1 => 'success']);
        $grid->column('created_at', __('添加时间'));

        $grid->disableCreateButton(true);
        $grid->actions(function ($actions) {
            $actions->disableDelete(false);
            $actions->disableView(true);
            $actions->disableEdit(true);
        });

        //查询过滤
        $grid->filter(function (Filter $filter) {
            $filter->disableIdFilter();
            $filter->column(1 / 2, function (Filter $filter) {
                $filter->equal('charge_account', '充值账号');
                $filter->equal('status', '状态')->select(SysCode::$common_status);
            });
            $filter->column(1 / 2, function (Filter $filter) {

                $filter->between('created_at', '添加时间')->datetime();
            });
        });

        $grid->tools(function (Grid\Tools $tools) {
            $url_params = sprintf('project_id=%s&product_id=%s', request('project_id', ''), request('product_id', ''));
            $tools->append('<a class="btn btn-sm btn-default" style="float: right;margin-right: 20px;" href="' . url('admin/bl/greylist/import?tag=del&' . $url_params) . '"><i class="fa fa-minus"></i>&nbsp;批量删除&nbsp;</a>');
            $tools->append('<a class="btn btn-sm btn-default" style="float: right;margin-right: 20px;margin-left: 20px;" href="' . url('admin/bl/greylist/import?tag=add&' . $url_params) . '"><i class="fa fa-plus"></i>&nbsp;批量导入&nbsp;</a>');
        });

        return $grid;
    }

    public function bl_import(Content $content, Request $request)
    {
        if (!$request->get('project_id') || !$request->get('product_id')) {
            admin_error("参数错误！");
            return back()->withInput();
        }

//        $content->title('批量处理');
        $form = new WidgetForm();
        $form->method('post');
        if ($request->get('tag') === 'del') {
            $content->title('批量删除');
            $form->action(url('admin/bl/greylist/do-delete?' . request()->getQueryString()));
        } else {
            $content->title('批量导入');
            $form->action(url('admin/bl/greylist/do-import?' . request()->getQueryString()));
        }

        $form->file('bl_file', '黑名单文件')->removable()->options([
            'showPreview'           => false,
            'allowedFileExtensions' => ['txt'],
        ])->required();
        $form->html('<div>上传文件格式为 txt 文本文件；<br/>每行一个充值账号，每次最多 ' . $this->import_max_count . ' 个充值账号；<br/>文件第一行为标题，写"code"即可，从<span style="color:red;">第二行</span>开始读取充值账号。</div>');

        $form->disableReset();

        $form_id = $form->getFormId();
        $script  = <<<EOT
$('form#{$form_id}').off('submit').on('submit', function (e) {
    event.preventDefault();
    var form = this;
    $.admin.swal({
        title: "确认要提交吗？",
        type: "question",
        showCancelButton: true,
        confirmButtonText: "确定",
        showLoaderOnConfirm: true,
        cancelButtonText: "取消"
    }).then(function(result) {
      if (result.value === true) {
        form.submit();
      } else if (result.dismiss === 'cancel') {
        event.preventDefault();
        return false;
      }
    });
    return false;
});
EOT;
        Admin::script($script);
        $content->body(new Widgets\Box('', $form));
        return $content;
    }

    public function bl_do_import(Content $content, Request $request)
    {
        if (!$request->get('project_id') || !$request->get('product_id')) {
//            $content->withError('提示', "参数错误aaaa");
//            return redirect($request->header('referer'))->withInput();
            admin_error("参数错误！");
            return redirect(sprintf('admin/bl/grey-lists?project_id=%s&product_id=%s', $request->get('project_id'), $request->get('product_id')));
        }

        $logger    = Log::channel('bl_import_log');
        $max_count = 5000;

        $request->validate(
            [
                'bl_file' => 'required|mimes:txt',
            ], [
                'bl_file.required' => "导入文件不能为空",
                'bl_file.mimes'    => "支持的文件格式：txt",
            ]
        );

        $project = Project::find($request->get('project_id'));
        $product = Product::find($request->get('product_id'));

        if (empty($project) || $project->status != SysCode::COMMON_STATUS_1) {
            admin_error("项目不存在，请确认所选项目是否正确！");
            return redirect(sprintf('admin/bl/grey-lists?project_id=%s&product_id=%s', $request->get('project_id'), $request->get('product_id')));
        }

        if (empty($product)) {
            admin_error("产品不存在！");
            return redirect(sprintf('admin/bl/grey-lists?project_id=%s&product_id=%s', $request->get('project_id'), $request->get('product_id')));
        }

        try {
            //处理文件
            $up_file = $request->file('bl_file');
            $ext     = $up_file->getClientOriginalExtension();
            //文件保存到storage/app/upload/bl
            $name      = 'bl' . sprintf('%d', Admin::user()->id) . date('YmdHis') . mt_rand(10, 99); //不包含扩展名
            $storePath = 'upload' . DIRECTORY_SEPARATOR . 'bl' . DIRECTORY_SEPARATOR . 'add' . DIRECTORY_SEPARATOR . date('Ym');
            if (!is_dir(storage_path('app' . DIRECTORY_SEPARATOR . $storePath))) {
                mkdir(storage_path('app' . DIRECTORY_SEPARATOR . $storePath), 0755, true);
            }
            $path = 'app' . DIRECTORY_SEPARATOR . $up_file->storeAs($storePath, $name . '.' . $ext);
            $url  = storage_path($path);//获取文件url

            $logger->info('导入黑名单，' . $url . '。原文件名：' . $up_file->getClientOriginalName());

            $codes = getFileContent($url, 2);
            $codes = array_filter($codes);
            list($codes, $repeat_num) = $this->preDealCode($codes, $url, $name);

            if (empty($codes)) {
                admin_error("文件中黑名单列表为空！");
                return back()->withInput();
            }

            if (count($codes) > $max_count) {
                admin_error("每批最多能处理" . $max_count . "条黑名单！");
                return back()->withInput();
            }

            //查询存在的黑名单
            $bl_exists = DB::table('bl_greylists')
                ->where('project_id', $project->id)
                ->where('product_id', $product->id)
                ->select(['id', 'charge_account'])
                ->get()
                ->pluck('id', 'charge_account');

            $now     = date('Y:m:d H:i:s');
            $user_id = Admin::user()->id;

            $insert_data = [];
            $exist_ids   = [];

            foreach ($codes as $c) {
                if ($bl_exists->has($c)) {
                    $exist_ids[] = $bl_exists->get($c);
                } else {
                    $insert_data[] = [
                        'project_id'     => $project->id,
                        'product_id'     => $product->id,
                        'charge_account' => $c,
                        'status'         => 1,
                        'created_by'     => $user_id,
                        'created_at'     => $now,
                    ];
                }
            }

            unset($bl_exists);

            DB::beginTransaction();

            try {
                $affect_rows = 0;
                if (!empty($exist_ids)) {
                    $affect_rows = DB::table('bl_greylists')
                        ->whereIn('id', $exist_ids)
                        ->where('status', 0)
                        ->update(['status' => 1]);
                }

                if (!empty($insert_data)) {
                    DB::table('bl_greylists')->insert($insert_data);
                }

                DB::commit();

            } catch (\Exception $ex) {
                DB::rollBack();
                throw $ex;
            }

            $logger->info($name . sprintf('导入成功%s条，更新%s条。', count($insert_data), $affect_rows) . ($repeat_num > 0 ? '重复账号共' . $repeat_num . '条。' : ''));
            admin_success(sprintf('导入成功，共导入%s条。', $affect_rows + count($insert_data)) . ($repeat_num > 0 ? '重复账号共' . $repeat_num . '条。' : ''));
            return redirect(sprintf('admin/bl/grey-lists?project_id=%s&product_id=%s', $request->get('project_id'), $request->get('product_id')));

        } catch (\Exception $exception) {
            Log::error($exception->getMessage(), $exception->getTrace());
            $logger->error('导入失败。错误：' . $exception->getMessage());
            admin_error('导入失败。错误：' . $exception->getMessage());
            return back()->withInput();
        }
    }

    public function bl_do_delete(Content $content, Request $request)
    {
        if (!$request->get('project_id') || !$request->get('product_id')) {
            admin_error("参数错误！");
            return redirect(sprintf('admin/bl/grey-lists?project_id=%s&product_id=%s', $request->get('project_id'), $request->get('product_id')));
        }

        $logger    = Log::channel('bl_import_log');
        $max_count = 5000;

        $request->validate(
            [
                'bl_file' => 'required|mimes:txt',
            ], [
                'bl_file.required' => "导入文件不能为空",
                'bl_file.mimes'    => "支持的文件格式：txt",
            ]
        );

        $project = Project::find($request->get('project_id'));
        $product = Product::find($request->get('product_id'));

        try {
            //处理文件
            $up_file = $request->file('bl_file');
            $ext     = $up_file->getClientOriginalExtension();
            //文件保存到storage/app/upload/bl
            $name      = 'bl' . sprintf('%d', Admin::user()->id) . date('YmdHis') . mt_rand(10, 99); //不包含扩展名
            $storePath = 'upload' . DIRECTORY_SEPARATOR . 'bl' . DIRECTORY_SEPARATOR . 'del' . DIRECTORY_SEPARATOR . date('Ym');
            if (!is_dir(storage_path('app' . DIRECTORY_SEPARATOR . $storePath))) {
                mkdir(storage_path('app' . DIRECTORY_SEPARATOR . $storePath), 0755, true);
            }
            $path = 'app' . DIRECTORY_SEPARATOR . $up_file->storeAs($storePath, $name . '.' . $ext);
            $url  = storage_path($path);//获取文件url

            $logger->info('删除黑名单，' . $url . '。原文件名：' . $up_file->getClientOriginalName());

            $codes = getFileContent($url, 2);
            $codes = array_filter($codes);
            list($codes, $repeat_num) = $this->preDealCode($codes, $url, $name);

            if (empty($codes)) {
                admin_error("文件中黑名单列表为空！");
                return back()->withInput();
            }

            if (count($codes) > $max_count) {
                admin_error("每批最多能处理" . $max_count . "条黑名单！");
                return back()->withInput();
            }

            $affect_rows = DB::table('bl_greylists')
                ->where('project_id', $project->id)
                ->where('product_id', $product->id)
                ->whereIn('charge_account', $codes)
                ->delete();

            $logger->info($name . sprintf('删除%s条黑名单。', $affect_rows) . ($repeat_num > 0 ? '重复账号共' . $repeat_num . '条。' : ''));
            admin_success(sprintf('删除成功，共删除%s条黑名单。', $affect_rows) . ($repeat_num > 0 ? '重复账号共' . $repeat_num . '条。' : ''));
            return redirect(sprintf('admin/bl/grey-lists?project_id=%s&product_id=%s', $request->get('project_id'), $request->get('product_id')));

        } catch (\Exception $exception) {
            Log::error($exception->getMessage(), $exception->getTrace());
            $logger->error('删除失败。错误：' . $exception->getMessage());
            admin_error('删除失败。错误：' . $exception->getMessage());
            return back()->withInput();
        }
    }

    private function preDealCode($codes, $filepath, $name)
    {
        $repeat_num = 0;

        // 获取去掉重复数据的数组
        $unique_arr = array_unique($codes);
        // 获取重复数据的数组
        $repeat_arr = array_diff_assoc($codes, $unique_arr);
        if (!empty($repeat_arr)) {
            $f_r = fopen(dirname($filepath) . DIRECTORY_SEPARATOR . $name . '_repeat.txt', 'a');
            fwrite($f_r, implode("\r\n", $repeat_arr) . "\r\n");
            fclose($f_r);
            $repeat_num = count($repeat_arr);
        }

        unset($repeat_arr, $codes);

        return [$unique_arr, $repeat_num];
    }
}
