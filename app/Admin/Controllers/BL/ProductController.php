<?php

namespace App\Admin\Controllers\BL;

use App\Models\BL\Product;
use App\Models\BL\ProjectProduct;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Grid\Filter;
use Encore\Admin\Show;
use Illuminate\Http\Request;

class ProductController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = '产品配置';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new Product());

        $grid->column('product_code', __('产品编号'));
        $grid->column('product_name', __('产品名称'));
        $grid->column('created_at', __('添加时间'));

        $grid->actions(function ($actions) {
            $actions->disableDelete(true);
            $actions->disableView(true);
        });

        //查询过滤
        $grid->filter(function (Filter $filter) {
            $filter->disableIdFilter();
            $filter->column(1 / 2, function (Filter $filter) {
                $filter->equal('product_code', '产品编号');
            });
            $filter->column(1 / 2, function (Filter $filter) {
                $filter->between('created_at', '添加时间')->datetime();
            });
        });

        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        $show = new Show(Product::findOrFail($id));

        $show->field('id', __('Id'));
        $show->field('product_code', __('产品编号'));
        $show->field('product_name', __('产品名称'));
        $show->field('created_by', __('添加时间'));

        return $show;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $form = new Form(new Product());

        $form->text('product_code', __('产品编号'));
        $form->text('product_name', __('产品名称'));
//        $form->multipleSelect('projects', '项目')->options(Project::where('status', 1)->get(['project_name', 'id'])->pluck('project_name', 'id'));

        $form->submitted(function (Form $form) {
            $model = $form->model();
            if ($form->isEditing()) {
                $model->updated_by = Admin::user()->id;
            } else {
                $model->created_by = Admin::user()->id;
            }
        });

        return $form;
    }
}
