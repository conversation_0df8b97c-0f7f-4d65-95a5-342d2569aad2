<?php

namespace App\Admin\Controllers\BL;

use App\Admin\SysCode\SysCode;
use App\Models\BL\Product;
use App\Models\BL\Project;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Show;

class ProjectController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = '项目管理';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new Project());

        $grid->column('project_no', __('项目编号'));
        $grid->column('project_name', __('项目名称'));
        $grid->column('status', __('状态'))->using(SysCode::$common_status)->dot([0 => 'danger', 1 => 'success']);;
        $grid->column('remark', __('备注'));
        $grid->column('created_at', __('添加时间'));

        $grid->disableFilter(true);
        $grid->actions(function ($actions) {
            $actions->disableDelete(true);
            $actions->disableView(true);
        });

        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        $show = new Show(Project::findOrFail($id));

        $show->field('id', __('Id'));
        $show->field('project_no', __('项目编号'));
        $show->field('project_name', __('项目名称'));
        $show->field('status', __('状态'))->using(SysCode::$common_status);
        $show->field('remark', __('备注'));
        $show->field('created_by', __('添加时间'));

        return $show;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $form = new Form(new Project());

        $form->text('project_no', __('项目编号'));
        $form->text('project_name', __('项目名称'));
        $form->switch('status', __('状态'))->states(SysCode::$common_states)->default(1);
        $form->text('remark', __('备注'));
//        $form->multipleSelect('products', '包括产品')->options(Product::where('status', 1)->get(['product_name', 'id'])->pluck('product_name', 'id'));

        $form->submitted(function (Form $form) {
            $model = $form->model();
            if ($form->isEditing()) {
                $model->updated_by = Admin::user()->id;
            } else {
                $model->created_by = Admin::user()->id;
            }
        });

        return $form;
    }
}
