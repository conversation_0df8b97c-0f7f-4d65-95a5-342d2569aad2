<?php

namespace App\Admin\Controllers\BL;

use App\Admin\Actions\BL\GreyListImport;
use App\Admin\Actions\BL\GreyListLink;
use App\Admin\SysCode\SysCode;
use App\Models\BL\Product;
use App\Models\BL\Project;
use App\Models\BL\ProjectProduct;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Grid\Filter;
use Encore\Admin\Show;

class ProjectProductController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = '项目产品管理';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new ProjectProduct());
        $grid->model()->resetOrderBy();
        $grid->model()->orderBy('project_id', 'desc')->orderBy('id', 'desc');
        $grid->column('project.project_name', __('项目'));
        $grid->column('project.project_no', __('项目编号'));
        $grid->column('product.product_name', __('产品'));
        $grid->column('product.product_code', __('产品编号'));
        $grid->column('settlement_price', __('结算价'));
        $grid->column('status', __('状态'))->using(SysCode::$common_status)->dot([0 => 'danger', 1 => 'success']);

        $grid->actions(function ($actions) {
            $actions->disableView(true);
            $actions->add(new GreyListLink());
        });

        //查询过滤
        $grid->filter(function (Filter $filter) {
            $filter->disableIdFilter();
            $filter->column(1 / 2, function (Filter $filter) {
                $filter->equal('project_id', '项目')->select(Project::getOptions());
                $filter->equal('product_id', '产品')->select(Product::getOptions());
            });
            $filter->column(1 / 2, function (Filter $filter) {
                $filter->equal('status', '状态')->select(SysCode::$common_status);
            });
        });
        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        $show = new Show(ProjectProduct::findOrFail($id));

        return $show;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $form = new Form(new ProjectProduct());

        $form->select('project_id', __('项目'))->options(function () use ($form) {
            return Project::getOptions($form->isCreating() ? SysCode::COMMON_STATUS_1 : null);
        })->required();
        $form->select('product_id', __('产品'))->options(function () {
            return Product::getOptions();
        })->required();
        $form->decimal('settlement_price', __('结算价'))->required();
        $form->switch('status', __('状态'))->states(SysCode::$common_states)->default(1);

        $form->submitted(function (Form $form) {
            $model               = $form->model();
            $model->project_no   = Project::find(\request('project_id'))->project_no;
            $model->product_code = Product::find(\request('product_id'))->product_code;
            if ($form->isEditing()) {
                $model->updated_by = Admin::user()->id;
            } else {
                $model->created_by = Admin::user()->id;
            }
        });

        return $form;
    }
}
