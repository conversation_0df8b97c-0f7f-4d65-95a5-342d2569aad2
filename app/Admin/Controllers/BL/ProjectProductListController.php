<?php

namespace App\Admin\Controllers\BL;

use App\Admin\Actions\BL\GreyListImport;
use App\Admin\Actions\BL\GreyListLink;
use App\Admin\SysCode\SysCode;
use App\Models\BL\Product;
use App\Models\BL\Project;
use App\Models\BL\ProjectProduct;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Grid\Filter;
use Encore\Admin\Show;
use Illuminate\Http\Request;

//第三方使用该页面
class ProjectProductListController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = '黑名单管理';//第三方使用

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new ProjectProduct());
        $grid->model()->resetOrderBy();
        $grid->model()->leftJoin('bl_projects', 'project_id', 'bl_projects.id')
            ->where('bl_project_products.status', SysCode::COMMON_STATUS_1)
            ->where('bl_projects.status', SysCode::COMMON_STATUS_1);
        $grid->model()->orderBy('project_id', 'desc')->orderBy('bl_project_products.id', 'desc');

        $grid->column('project.project_name', __('项目'));
        $grid->column('product.product_name', __('产品'));
        $grid->column('settlement_price', __('结算价'));
        $grid->column('status', __('状态'))->using(SysCode::$common_status)->dot([0 => 'danger', 1 => 'success']);
        $grid->column('', __('黑名单'))->action(GreyListLink::class);
        $grid->disableCreateButton(true);
        $grid->disableActions(true);

        //查询过滤
        $grid->filter(function (Filter $filter) {
            $filter->disableIdFilter();
            $filter->column(1 / 2, function (Filter $filter) {
                $filter->equal('project_id', '项目')->select(Project::getOptions(1));
                $filter->equal('product_id', '产品')->select(Product::getOptions());
            });
            $filter->column(1 / 2, function (Filter $filter) {

            });
        });

        return $grid;
    }

    //根据项目id在项目商品关系表中筛选出产品。联动下拉款使用
    public function api_for_select(Request $request, $status = null)
    {
        $project_id = $request->get('q');

        $list = ProjectProduct::leftJoin('bl_products', 'product_id', 'bl_products.id')
            ->where('project_id', $project_id)
            ->orderBy('bl_products.product_name');
        if ($status) {
            $list->where('bl_project_products.status', SysCode::COMMON_STATUS_1);
        }
        $list = $list->get(['bl_products.id', 'product_name']);

        $ret = [];
        foreach ($list as $k => $v) {
            $ret[] = [
                'id'   => $v->id,
                'text' => $v->product_name,
            ];
        }
        return $ret;
    }
}
