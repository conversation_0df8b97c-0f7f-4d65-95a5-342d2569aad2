<?php

namespace App\Admin\Controllers\BL;

use App\Admin\Extensions\WidgetForm;
use App\Models\BL\Product;
use App\Models\BL\Project;
use App\Models\BL\Record;
use App\Models\ExchangeForbiddenBatch;
use Carbon\Carbon;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Grid;
use Encore\Admin\Grid\Filter;
use Encore\Admin\Layout\Content;
use Encore\Admin\Widgets;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

//管理员使用。有导出及结算功能
class RecordController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = '订单结算';//管理员使用

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new Record());

        $grid->column('project.project_name', __('项目'));
        $grid->column('out_trade_no', __('客户订单号'));
        $grid->column('order_no', __('订单号'));
        $grid->column('product.product_name', __('产品'));
        $grid->column('charge_account', __('充值账号'));
        $grid->column('created_at', __('订单日期'));
        $grid->column('settlement_price', __('结算价'))->totalRow(function ($amount) {
            return sprintf("<span class='text-danger text-bold'>合计：<i class='fa fa-yen'></i> %s 元</span>", number_format($amount, 2, '.', ','));
        });
        $grid->column('settlement_status', __('结算状态'))->using(Record::$settlement_status);
        $grid->column('settlement_date', __('结算日期'));
        $grid->column('attach', __('备注'))->hide();

        $grid->disableCreateButton(true);
        $grid->disableExport(false);
        $grid->disableActions(true);
        $grid->actions(function ($actions) {
            $actions->disableDelete(true);
            $actions->disableView(true);
            $actions->disableEdit(true);
        });

        //查询过滤
        $grid->filter(function (Filter $filter) {
            $filter->disableIdFilter();
            $filter->column(1 / 2, function (Filter $filter) {
                $filter->equal('project_id', '项目')->select(Project::all([
                    'id', 'project_name'
                ])->sortByDesc('id')->pluck('project_name', 'id')
                );
                $filter->equal('product_id', '产品')->select(Product::all([
                    'id', 'product_name'
                ])->sortByDesc('id')->pluck('product_name', 'id')
                );
                $filter->equal('charge_account', '充值账号');
            });
            $filter->column(1 / 2, function (Filter $filter) {
                $filter->equal('order_no', '订单号');
                $filter->between('created_at', '订单日期')->datetime();
                $filter->equal('settlement_status', '结算状态')->select(Record::$settlement_status);
            });
        });

        $grid->tools(function (Grid\Tools $tools) {
            $tools->append('<a class="btn btn-sm btn-default" style="float: right;margin-right: 20px;margin-left: 20px;" href="' . url('admin/bl/records/settlement') . '"><i class="fa fa-plus"></i>&nbsp;批量结算&nbsp;</a>');
        });

        return $grid;
    }

    //结算处理页面
    public function settlement(Content $content)
    {
        $content->title('结算处理');
        $form = new WidgetForm();
        $form->method('post');
        $form->action(url('admin/bl/records/do-settlement'));
        $form->html('<div>上传文件格式为 txt 文本文件，utf-8 编码；<br/>每行一个订单号。<br/>文件第一行为标题，写"code"即可，从<span style="color:red;">第二行</span>开始读取批次号。</div>');
        $form->file('bl_file', '结算订单')->removable()->options([
            'showPreview'           => false,
            'allowedFileExtensions' => ['txt'],
        ])->required();

        $form->disableReset();

        $form_id = $form->getFormId();
        $script  = <<<EOT
$('form#{$form_id}').off('submit').on('submit', function (e) {
    event.preventDefault();
    var form = this;
    $.admin.swal({
        title: "确定要提交吗？",
        type: "question",
        showCancelButton: true,
        confirmButtonText: "确定",
        showLoaderOnConfirm: true,
        cancelButtonText: "取消"
    }).then(function(result) {
      if (result.value === true) {
        form.submit();
      } else if (result.dismiss === 'cancel') {
        event.preventDefault();
        return false;
      }
    });
    return false;
});
EOT;
        Admin::script($script);

        $content->body(new Widgets\Box('', $form));
        return $content;
    }

    //结算处理
    public function do_settlement(Content $content, Request $request)
    {
        $logger = Log::channel('bl_settlement_log');

        $request->validate(
            [
                'bl_file' => 'required|mimes:txt',
            ], [
                'bl_file.required' => "导入文件不能为空",
                'bl_file.mimes'    => "支持的文件格式：txt",
            ]
        );

        try {
            //处理文件
            $up_file = $request->file('bl_file');
            $ext     = $up_file->getClientOriginalExtension();
            //文件保存到storage/app/upload/bl
            $name      = 'settlement' . sprintf('%d', Admin::user()->id) . date('YmdHis') . mt_rand(10, 99); //不包含扩展名
            $storePath = 'upload' . DIRECTORY_SEPARATOR . 'bl' . DIRECTORY_SEPARATOR . 'settlement' . DIRECTORY_SEPARATOR . date('Ym');
            if (!is_dir(storage_path('app' . DIRECTORY_SEPARATOR . $storePath))) {
                mkdir(storage_path('app' . DIRECTORY_SEPARATOR . $storePath), 0755, true);
            }
            $path = 'app' . DIRECTORY_SEPARATOR . $up_file->storeAs($storePath, $name . '.' . $ext);
            $url  = storage_path($path);//获取文件url

            $logger->info('结算处理，' . $url . '。原文件名：' . $up_file->getClientOriginalName());

            $codes = getFileContent($url, 2);
            $codes = array_filter($codes);
            list($codes, $repeat_num) = $this->preDealCode($codes, $url, $name);

            if (empty($codes)) {
                $content->withError('提示', "订单列表为空");
                return redirect('admin/bl/records/settlement')->withInput();
            }

            //查询有效的批次
            $orders = DB::table('bl_records')
                ->whereIn('order_no', $codes)
                ->where('settlement_status', Record::SETTLEMENT_STATUS_NO)
                ->select(['id', 'order_no'])
                ->get();

            if ($orders->count() == 0) {
                $message = "文件中没有可结算的订单号！";
                $logger->info($name . $message);
                $with_data = [
                    'warning' => new \Illuminate\Support\MessageBag([
                        'title'   => '提示',
                        'message' => '文件中没有可结算的订单号！',
                    ]),
                ];
                return redirect('admin/bl/records/settlement')->withInput()->with($with_data);
            }

            $orders_in_table = $orders->pluck('order_no')->toArray();  //库里存在的批次号。

            $affected_rows = DB::table('bl_records')
                ->whereIn('id', $orders->pluck('id')->toArray())
                ->where('settlement_status', ExchangeForbiddenBatch::SETTLEMENT_STATUS_NO)
                ->update([
                    'settlement_status' => ExchangeForbiddenBatch::SETTLEMENT_STATUS_YES,
                    'settlement_date'   => Carbon::now()->toDateTimeString()
                ]);

            $logger->info($name . ' 结算' . $affected_rows . '个订单。订单号：' . implode(',', $orders_in_table));

            $with_data = [
                'success' => new \Illuminate\Support\MessageBag([
                    'title'   => '成功',
                    'message' => '结算 ' . $affected_rows . ' 个订单。订单列表：' . implode(',', $orders_in_table),
                ]),
            ];
            return redirect('admin/bl/records')->withInput()->with($with_data);

        } catch (\Exception $exception) {
            Log::error($exception->getMessage(), $exception->getTrace());
            $content->withError('错误提示', $exception->getMessage());
            $logger->error('结算失败。' . $exception->getMessage());
        }
        return redirect('admin/bl/records/settlement')->withInput();
    }

    private function preDealCode($codes, $filepath, $name)
    {
        $repeat_num = 0;

        // 获取去掉重复数据的数组
        $unique_arr = array_unique($codes);
        // 获取重复数据的数组
        $repeat_arr = array_diff_assoc($codes, $unique_arr);
        if (!empty($repeat_arr)) {
            $f_r = fopen(dirname($filepath) . DIRECTORY_SEPARATOR . $name . '_repeat.txt', 'a');
            fwrite($f_r, implode("\r\n", $repeat_arr) . "\r\n");
            fclose($f_r);
            $repeat_num = count($repeat_arr);
        }

        unset($repeat_arr, $codes);

        return [$unique_arr, $repeat_num];
    }

}
