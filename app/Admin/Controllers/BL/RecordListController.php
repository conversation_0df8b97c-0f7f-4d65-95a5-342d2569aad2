<?php

namespace App\Admin\Controllers\BL;

use App\Models\BL\Product;
use App\Models\BL\Project;
use App\Models\BL\Record;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Grid;
use Encore\Admin\Grid\Filter;

//第三方使用该页面
class RecordListController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = '订单记录';//第三方使用

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new Record());

        $grid->column('project.project_name', __('项目'));
        $grid->column('out_trade_no', __('客户订单号'));
        $grid->column('order_no', __('订单号'));
        $grid->column('product.product_name', __('产品'));
        $grid->column('charge_account', __('充值账号'));
        $grid->column('created_at', __('订单日期'));
        $grid->column('settlement_price', __('结算价'))->totalRow(function ($amount) {
            return sprintf("<span class='text-danger text-bold'>合计：<i class='fa fa-yen'></i> %s 元</span>", number_format($amount, 2, '.', ','));
        });
        $grid->column('settlement_status', __('结算状态'))->using(Record::$settlement_status);
        $grid->column('settlement_date', __('结算日期'));
        $grid->column('attach', __('备注'))->hide();

        $grid->disableCreateButton(true);
        $grid->disableExport(false);
        $grid->disableActions(true);
        $grid->actions(function ($actions) {
            $actions->disableDelete(true);
            $actions->disableView(true);
            $actions->disableEdit(true);
        });

        //查询过滤
        $grid->filter(function (Filter $filter) {
            $filter->disableIdFilter();
            $filter->column(1 / 2, function (Filter $filter) {
                $filter->equal('project_id', '项目')->select(Project::all([
                    'id', 'project_name'
                ])->sortByDesc('id')->pluck('project_name', 'id')
                );
                $filter->equal('product_id', '产品')->select(Product::all([
                    'id', 'product_name'
                ])->sortByDesc('id')->pluck('product_name', 'id')
                );
                $filter->equal('charge_account', '充值账号');
            });
            $filter->column(1 / 2, function (Filter $filter) {
                $filter->equal('order_no', '订单号');
                $filter->between('created_at', '订单日期')->datetime();
                $filter->equal('settlement_status', '结算状态')->select(Record::$settlement_status);
            });
        });

        return $grid;
    }

}
