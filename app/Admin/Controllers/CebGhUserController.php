<?php

namespace App\Admin\Controllers;

use App\Admin\Actions\Exchange\GeneralCode;
use App\Admin\Extensions\CsvExporter;
use App\Admin\Extensions\WidgetForm;
use App\Exceptions\MyException;
use App\Logic\CebGh;
use App\Models\Activity;
use App\Models\CebGhDepartment;
use App\Models\CebGhUser;
use App\Models\ExchangeBatch;
use App\Models\Goods;
use App\Models\Order;
use EasyWeChat\Kernel\Messages\Raw;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Grid\Filter;
use Encore\Admin\Layout\Content;
use Encore\Admin\Show;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Encore\Admin\Widgets;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CebGhUserController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = '光大工会线上视听会员活动';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new CebGhUser());
        $grid->model()->where('status', CebGhUser::STATUS_ENABLE);
        $grid->model()->resetOrderBy();
        $grid->model()->orderBy('no');
        $grid->paginate(50);

        if (Admin::user()->inRoles(['administrator', 'order_manager', 'definition_p'])) {
            $grid->column('id', __('Id'));
        }
        $grid->column('no', '序号');
        $grid->column('name', '姓名');
        $grid->column('department', '所属机构');
        $grid->column('employee_no', '员工号');
        $grid->column('mobile', '手机号');
        $grid->column('get_status', __('兑换状态'))->using(CebGhUser::$get_status);
        $grid->column('get_time', '兑换时间');
        $grid->column('goods_name', '兑换商品');
        if (Admin::user()->inRoles(['administrator', 'order_manager', 'definition_p', 'cebgh'])) {
            $grid->column('goods_price', '商品价格');
            $grid->disableExport(false);//导出功能控制
        } else {
            $grid->disableExport(true);
        }
        $grid->column('charge_account', '充值账号');
        if (Admin::user()->inRoles(['administrator', 'definition_p'])) {
            $grid->column('order.status', '订单状态')->using(Order::$status);
            $grid->column('status', __('可用状态'))->hide();
        }

        $grid->disableCreateButton();

        if (Admin::user()->inRoles(['administrator', 'order_manager', 'definition_p', 'cebgh_xm'])) {
            $grid->actions(function ($actions) {
                $actions->disableDelete(true);
                $actions->disableView(true);
                if ($this->row->get_status == 1) {
                    $actions->disableEdit(true);
                } else {
                    $actions->disableEdit(false);
                }
            });
        } else {
            $grid->disableActions();
            $grid->actions(function ($actions) {
                $actions->disableDelete(true);
                $actions->disableView(false);
                $actions->disableEdit(false);
            });
        }

        //查询过滤
        $grid->filter(function (Filter $filter) {
            $filter->disableIdFilter();
            $filter->column(1 / 2, function (Filter $filter) {
                $filter->equal('mobile', '手机号');
                $filter->equal('employee_no', '员工号');
                $filter->equal('get_status', '兑换状态')->select(CebGhUser::$get_status);
            });
            $filter->column(1 / 2, function (Filter $filter) {
                $filter->equal('department', '所属机构')->select(CebGhDepartment::get_list());
                $filter->between('get_time', '兑换时间')->datetime();
                if (Admin::user()->inRoles(['administrator', 'order_manager', 'definition_p'])) {
                    $filter->equal('id', 'ID');
                }
            });
        });

        //导出配置
        $excel = new CsvExporter();
        $excel->setAttr('兑换记录' . date('Y-m-d'),
            ['序号', '姓名', '所属机构', '员工号', '手机号', '兑换状态', '兑换时间', '兑换商品', '商品价格', '充值账号'],
            ['no', 'name', 'department', 'employee_no', 'mobile', 'get_status', 'get_time', 'goods_name', 'goods_price', 'charge_account'],
            ['get_status' => CebGhUser::$get_status]
        );
        $grid->exporter($excel);

        $grid->tools(function (Grid\Tools $tools) {
            if (Admin::user()->inRoles(['administrator', 'order_manager', 'definition_p'])) {
                $tools->append('<a class="btn btn-sm btn-warning" style="float: right;margin-right: 20px;" href="' . url('admin/cebgh/import') . '"><i class="fa fa-upload"></i>&nbsp;名单导入&nbsp;</a>');
            }
        });

        return $grid;
    }

//    protected function export_convert($val, $col, $item)
//    {
//        static $goods = [];
//        if ($col == 'goods_id') {
//            if (!array_key_exists($val, $goods)) {
//                $goods[$val] = Goods::find($val)->goods_name ?? '';
//            }
//            return $goods[$val];
//        } elseif ($col == 'order_id') {
//            return Order::find($val)->charge_account ?? '';
//        } else {
//            return $val;
//        }
//    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        $show = new Show(CebGhUser::findOrFail($id));

        return $show;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $form = new Form(new CebGhUser());
        $form->text('mobile', '手机号')->required();
        $form->submitted(function (Form $form) {

            if ($form->isCreating()) {
                admin_error("警告", "请使用导入功能添加白名单！");
                return back()->withInput();
            }

            $model = $form->model();
            if ($model->get_status == CebGhUser::GET_STATUS_YES) {
                admin_error("警告", "该用户已兑换完毕！");
                return back()->withInput();
            }

            $mobile = \request('mobile');
            if (empty($mobile) || !check_mobile($mobile)) {
                admin_error("警告", "手机号不正确！");
                return back()->withInput();
            }

            $exists = CebGhUser::where('mobile', $mobile)->first();
            if ($exists && $exists->id != $model->id) {
                admin_error("警告", "手机号已存在！");
                return back()->withInput();
            }
        });
        return $form;
    }

    //禁用兑换码导入。
    public function import(Content $content)
    {
        $content->title('名单导入');
        $form = new WidgetForm();
        $form->method('post');
        $form->action(url('admin/cebgh/do-import'));

        $form->file('wl_file', '白名单文件')->removable()->options([
            'showPreview'           => false,
            'allowedFileExtensions' => ['txt'],
        ])->required();
        $form->html('<div>上传文件格式为 txt 文本文件，utf-8 编码；<span style="color:red;">每行一个白名单，数据项用半角逗号隔开。</span>。<br/>文件标题头包含以下内容(不分顺序)：序号,姓名,所属机构,员工号,手机号</div>');
        $form->disableReset();

        $form_id = $form->getFormId();
        $script  = <<<EOT
$('form#{$form_id}').off('submit').on('submit', function (e) {
    event.preventDefault();
    var form = this;
    $.admin.swal({
        title: "确定要导入吗？",
        type: "question",
        showCancelButton: true,
        confirmButtonText: "确认",
        showLoaderOnConfirm: true,
        cancelButtonText: "取消"
    }).then(function(result) {
      if (result.value === true) {
        form.submit();
      } else if (result.dismiss === 'cancel') {
        event.preventDefault();
        return false;
      }
    });
    return false;
});
EOT;
        Admin::script($script);

        $content->body(new Widgets\Box('', $form));
        return $content;
    }

    public function do_import(Content $content, Request $request)
    {
        ini_set('memory_limit', '256M');
        ini_set('max_execution_time', 600);

        $logger = Log::channel('cebgh_log');
        $request->validate(
            [
                'wl_file' => 'required|mimes:txt',
            ], [
                'wl_file.required' => "导入文件不能为空",
                'wl_file.mimes'    => "支持的文件格式：txt",
            ]
        );

        try {
            //处理文件
            $up_file = $request->file('wl_file');
            $ext     = $up_file->getClientOriginalExtension();
            //文件保存到storage/app/upload/exchange
            $name      = 'ceb_gh_' . sprintf('%d', Admin::user()->id) . date('YmdHis') . mt_rand(10, 99); //不包含扩展名
            $storePath = 'upload' . DIRECTORY_SEPARATOR . 'ceb_gh' . DIRECTORY_SEPARATOR . date('Ym');
            if (!is_dir(storage_path('app' . DIRECTORY_SEPARATOR . $storePath))) {
                mkdir(storage_path('app' . DIRECTORY_SEPARATOR . $storePath), 0755, true);
            }
            $path = 'app' . DIRECTORY_SEPARATOR . $up_file->storeAs($storePath, $name . '.' . $ext);
            $url  = storage_path($path);//获取文件url

            $logger->info('导入光大工会活动白名单，' . $url . '。原文件名：' . $up_file->getClientOriginalName() . '。 memory: ' . getMemoryUsage());

            $head         = [];
            $datas        = file_get_content($url, function ($val, $index) use (&$head) {
                $val = str_replace([' ', "\r",], '', trim($val));
                if ($index === 0) {
                    $head = $val;
                    return false;
                }
                if ($val === '') {
                    return false;
                }
                return $val;
            });
            $col_relation = ['序号' => 'no', '姓名' => 'name', '所属机构' => 'department', '员工号' => 'employee_no', '手机号' => 'mobile'];
            $columns      = [];
            $head         = explode(',', $head);
            foreach ($head as $val) {
                if (array_key_exists($val, $col_relation)) {
                    $columns[] = $col_relation[$val];
                }
            }

            if (empty($datas)) {
                $content->withError('提示', "列表为空");
                return redirect('admin/cebgh/import')->withInput();
            }

            $now = date('Y:m:d H:i:s');

            DB::beginTransaction();

            $import_count = 0;
            try {
                $per_num    = 1000;
                $for_length = ceil(count($datas) / $per_num);
                for ($i = 0; $i < $for_length; $i++) {
                    $slice_data  = array_slice($datas, $i * $per_num, $per_num);
                    $insert_data = [];
                    //处理并插入数据库
                    foreach ($slice_data as $d) {
                        $col_vals = explode(',', $d);
                        if (count($columns) != count($col_vals)) {
                            throw new MyException('以下数据项不正确:' . $d);
                        }

                        $rec = [];
                        for ($j = 0; $j < count($columns); $j++) {
                            $rec[$columns[$j]] = $col_vals[$j];
                        }
                        $rec['created_at'] = $now;

                        $insert_data[] = $rec;

                        unset($rec);
                    }
                    //过滤已存在记录
                    $slice_mobiles = array_column($insert_data, 'mobile');
                    $exist_mobiles = DB::table('ceb_gh_users')
                        ->whereIn('mobile', $slice_mobiles)
                        ->select('mobile')
                        ->pluck('mobile')
                        ->toArray();
                    unset($slice_mobiles);
                    $insert_data = array_filter($insert_data, function ($item) use ($exist_mobiles) {
                        if (in_array($item['mobile'], $exist_mobiles)) {
                            return false;
                        }
                        return $item;
                    });
                    if (count($insert_data) > 0) {
                        $import_count += count($insert_data);
                        $logger->info("file:$name, " . json_encode(['insert_data' => ['begin' => reset($insert_data), 'end' => end($insert_data)], 'memory' => getMemoryUsage()], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
                        unset($exist_mobiles);
                        $result = DB::table('ceb_gh_users')->insert($insert_data);
                        if (!$result) {
                            throw new MyException('白名单写入数据库失败！' . json_encode($slice_data, JSON_UNESCAPED_UNICODE));
                        }
                    }

                    unset($slice_data, $insert_data);
                }
                $departments = DB::table('ceb_gh_users')
                    ->select(DB::raw('distinct department as department_name'))
                    ->orderBy('department')
                    ->get()->map(function ($item) use ($now) {
                        $item->created_at = $now;
                        return (array)$item;
                    });
                if (count($departments) > 0) {
                    $builder = DB::table('ceb_gh_departments');
                    $builder->truncate();
                    $builder->insert($departments->toArray());
                }

                DB::commit();

            } catch (\Exception $ex) {
                DB::rollBack();
                throw $ex;
            }

            $logger->info($name . '导入成功' . $import_count . '个。memory: ' . getMemoryUsage());
            $content->withInfo('导入成功', '导入' . $import_count . '个。');
            return redirect('admin/cebgh')->withInput();

        } catch (\Exception $exception) {
            if ($exception instanceof MyException || $exception instanceof \Illuminate\Database\QueryException) {
                $logger->error('导入失败。' . $exception->getMessage());
            } else {
                $logger->error(get_class($exception) . ':' . $exception->getMessage(), $exception->getTrace());
                Log::error($exception->getMessage(), $exception->getTrace());
            }
            $logger->info('memory: ' . getMemoryUsage());
            $content->withError('错误提示', $exception->getMessage());
            return redirect('admin/cebgh/import')->withInput();
        }

    }
}
