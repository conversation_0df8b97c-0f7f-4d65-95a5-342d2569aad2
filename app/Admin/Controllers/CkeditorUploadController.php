<?php

namespace App\Admin\Controllers;

use Illuminate\Routing\Controller;
use Illuminate\Http\Request;
use App\Handlers\ImageUploadHandler;


class CkeditorUploadController extends Controller
{

    public function uploadImage(Request $request,ImageUploadHandler $uploader)
    {

//        $image = request()->file('upload');
//        //保存到當前默認disk
//        $path = $image->store('images');
//
//        //保存到本地或者選定特定的disk
////      $path = Storage::disk('public')->put('images',$image);
//        $url = Storage::disk('public')->url($path);
//
//        $callback = $request->input("CKEditorFuncNum");
//        $CKEditor = $request->input('CKEditor');
//
//        return "<script>window.parent.CKEDITOR.tools.callFunction(1,'{$url}','')</script>";
//        return '<script>window.parent.CKEDITOR.tools.callFunction('.$funcNum.', "'.$url.'", "'.$message.'")</script>'

        $data = [
            'errno' => 1,
        ];
        // 判断是否有上传文件，并赋值给 $file
        if ($file = $request->upload_file) {
            // 保存图片到本地
            $result = $uploader->save($request->upload_file, 'admin', 1, 1024);
            // 图片保存成功的话
            if ($result) {
                $data['data'][] = $result['path'];
                $data['errno']  = 0;
            }
        }

        return $data;


    }
}
