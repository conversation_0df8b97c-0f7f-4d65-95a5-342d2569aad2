<?php

namespace App\Admin\Controllers;

use App\Models\Customer;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Show;
use Illuminate\Support\Facades\DB;

class CustomerController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = '客户';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new Customer());

        $grid->column('id', __('Id'));
        $grid->column('cust_name', __('客户名称'));
        $grid->column('status', __('状态'))->using(Customer::$customer_status)->dot([0 => 'danger', 1 => 'success']);
        $grid->column('created_at', __('创建时间'));
        $grid->column('updated_at', __('修改时间'))->hide();
        $grid->column('created_by', __('创建人'))->display(function ($created_by) {
            return DB::table('admin_users')->where('id', $created_by)->value('name');
        });
        $grid->column('updated_by', __('更新人'))->display(function ($updated_by) {
            return DB::table('admin_users')->where('id', $updated_by)->value('name');
        })->hide();

        $grid->filter(function ($filter) {
            // 去掉默认的id过滤器
            $filter->disableIdFilter();
            // 在这里添加字段过滤器
            $filter->like('cust_name', '客户名称');
        });

        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        $show = new Show(Customer::findOrFail($id));

        $show->field('id', __('Id'));
        $show->field('cust_name', __('Cust name'));
        $show->field('status', __('Status'));
        $show->field('created_by', __('Created by'));
        $show->field('updated_by', __('Updated by'));
        $show->field('created_at', __('Created at'));
        $show->field('updated_at', __('Updated at'));

        return $show;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $form = new Form(new Customer());

        $form->text('cust_name', __('客户名称'));
        $form->hidden('created_by')->value(Admin::user()->id);
        $form->hidden('updated_by')->value(Admin::user()->id);
        $states = [
            'on'  => ['value' => 1, 'text' => '启用', 'color' => 'primary'],
            'off' => ['value' => 0, 'text' => '禁用', 'color' => 'default'],
        ];
        $form->switch('status', '状态')->states($states)->default(1); //启用状态

        return $form;
    }
}
