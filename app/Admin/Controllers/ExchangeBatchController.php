<?php

namespace App\Admin\Controllers;

use App\Admin\Actions\Exchange\BatchGoodsMgr;
use App\Admin\Actions\Exchange\GeneralCode;
use App\Admin\SysCode\SysCode;
use App\Models\Activity;
use App\Models\ExchangeBatch;
use App\Models\ExchangeDetail;
use App\Models\ExchangeGoods;
use App\Models\ExchangeGroup;
use App\Models\Goods;
use Encore\Admin\Auth\Database\Administrator;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Grid\Filter;
use Encore\Admin\Show;
use Encore\Admin\Widgets\Table;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ExchangeBatchController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = '兑换码批次管理';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new ExchangeBatch());

        $grid->column('id', __('Id'));
//        $grid->column('activity_id', __('活动id'))->hide();
        $grid->column('activity.activity_name', __('活动名称'));
        $grid->column('type', __('类型'))->using(ExchangeBatch::$type);
        $grid->column('total_num', __('兑换码数量'));
        $grid->column('generated_num', __('已生成数量'));
        $grid->column('endtime', __('有效期'));
        $grid->column('max_times', __('兑换次数'));
        $grid->column('goods_price', __('销售单价'));
        $grid->column('tax_rate', __('税率'));
        $grid->column('exchange_cycle', __('兑换周期'))->using(ExchangeBatch::$exchange_cycle);
        $grid->column('package_name', __('批次名称'));
        $grid->column('status', __('状态'))->using(ExchangeBatch::$status);
        $grid->column('goods', __('商品'))->expand(function ($model) {
            if (in_array($model->type, [ExchangeBatch::TYPE_LIPINKA, ExchangeBatch::TYPE_JICIKA, ExchangeBatch::TYPE_JICIKA_REP])) {
                $headers = ['商品ID', '商品名称', '类型', '大类', 'QCP编号', '排序', '商品状态'];
                $data    = $model->get_goods()->map(function (ExchangeGoods $goods) {
                    $tmp               = $goods->only(['goods_id', 'goods_name', 'goods_type', 'ecp_target', 'ecp_pcode', 'sort', 'status']);
                    $tmp['goods_type'] = Goods::$goods_type[$goods->goods_type];
                    $tmp['status']     = SysCode::$common_status[$goods->status];
                    return $tmp;
                })->toArray();
            } else {
                $headers = ['分组名称', '兑换次数', '允许重复', '分组排序', '分组状态', '商品ID', '商品名称', '类型', '大类', '编号', '排序', '状态', '商品状态'];
                $data    = DB::table('exchange_goods')->leftJoin('exchange_groups', 'exchange_goods.exchange_group_id', '=', 'exchange_groups.id')
                    ->leftJoin('goods', 'exchange_goods.goods_id', '=', 'goods.id')
                    ->where('exchange_goods.exchange_batch_id', $model->id)
                    ->where('exchange_groups.exchange_batch_id', $model->id)
                    ->select([
                        'exchange_groups.group_name', 'exchange_groups.exchange_times', 'exchange_groups.allow_repeat', DB::raw('exchange_groups.sort group_sort'), DB::raw('exchange_groups.status group_status'),
                        'exchange_goods.goods_id', 'goods.goods_name', 'goods.goods_type', 'goods.ecp_target', 'goods.ecp_pcode', 'exchange_goods.sort', 'exchange_goods.status', DB::raw('goods.status goods_status'),
                    ])
                    ->orderBy('exchange_groups.sort')
                    ->orderBy('exchange_groups.id')
                    ->orderBy('exchange_goods.sort')
                    ->get()->map(function ($item) {
                        $item                 = (array)$item;
                        $item['allow_repeat'] = $item['allow_repeat'] == 1 ? '是' : '否';
                        $item['group_status'] = ExchangeGroup::$status[$item['group_status']];
                        $item['goods_type']   = Goods::$goods_type[$item['goods_type']];
                        $item['status']       = $item['status'] == 1 ? '启用' : '禁用';
                        $item['goods_status'] = $item['goods_status'] == 1 ? '启用' : '禁用';
                        return $item;
                    })->toArray();

            }

            return new Table($headers, $data);
        });
        $grid->column('batch_desc', __('批次描述'));
        $grid->column('created_by', __('创建者'))->display(function ($created_by) {
            return Administrator::where('id', $created_by)->value('name');
        });
        $grid->column('updated_by', __('更新者'))->display(function ($updated_by) {
            return Administrator::where('id', $updated_by)->value('name');

        })->hide();
        $grid->column('created_at', __('创建时间'))->hide();
        $grid->column('updated_at', __('更新时间'));

        $grid->actions(function (\Encore\Admin\Grid\Displayers\Actions $actions) {
            $actions->disableDelete(true);
            $actions->disableView(false);
            $actions->add(new BatchGoodsMgr());
            $actions->add(new GeneralCode());
        });

        //查询过滤
        $grid->filter(function (Filter $filter) {
            $filter->disableIdFilter();
            $filter->column(1 / 2, function (Filter $filter) {
                $filter->equal('activity_id', '活动')->select(Activity::all([
                    'id', 'activity_name'
                ])->sortByDesc('id')->pluck('activity_name', 'id')
                );
                $filter->equal('status', '状态')->select(ExchangeBatch::$status);
            });
            $filter->column(1 / 2, function (Filter $filter) {
                $filter->between('created_at', '创建时间')->datetime();
                $filter->like('package_name', '批次名称');
            });
        });

        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        $show = new Show(ExchangeBatch::findOrFail($id));

        $show->field('id', __('Id'));
        $show->field('activity_id', __('活动id'));
        $show->field('activity.activity_name', __('活动名称'));
        $show->field('type', __('类型'))->using(ExchangeBatch::$type);
        $show->field('total_num', __('兑换码数量'));
        $show->field('generated_num', __('已生成数量'));
        $show->field('endtime', __('有效期'));
        $show->field('max_times', __('兑换次数'));
        $show->field('goods_price', __('销售单价'));
        $show->field('tax_rate', __('税率'));
        $show->field('exchange_cycle', __('兑换周期'))->using(ExchangeBatch::$exchange_cycle);
        $show->field('package_name', __('礼包名称'));
        $show->field('status', __('状态'))->using(ExchangeBatch::$status);
        $show->field('batch_desc', __('描述'));
        $show->field('instr.show_img', __('展示图'))->image(config('app.oss_url'), 100, 100);
        $show->field('created_by', __('创建者'))->as(function ($created_by) {
            return Administrator::where('id', $created_by)->value('name');
        });
        $show->field('created_at', __('创建时间'));
        $show->field('updated_by', __('更新者'))->as(function ($created_by) {
            return Administrator::where('id', $created_by)->value('name');
        });
        $show->field('updated_at', __('更新时间'));

        return $show;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $form = new Form(new ExchangeBatch());

        if ($form->isCreating()) {
            $form->select('activity_id', __('活动'))->options(function () {
                return Activity::where(['status' => 1])->orderBy('id', 'desc')->pluck('activity_name', 'id');
            })->required()->load('exch_goods', route('exb-get-goods-by-act'));
        } else {
            $form->display('activity_id', __('活动'))->with(function ($val) {
                return Activity::find($val)->activity_name ?? $val;
            });;
        }
        $form->select('type', __('类型'))->options(ExchangeBatch::$type)->required();
        $form->number('total_num', __('兑换码数量'))->default(1)->rules('max:5')->required();
        $form->date('endtime', __('有效期'))->default(date('Y-m-d'))->required();
        $form->number('max_times', __('兑换次数'))->default(1)->required()->help('<span class="text-blue">套餐卡</span>的兑换次数，在配置分组时，会更新为所有分组中兑换次数的总和；<span class="text-blue">礼包卡</span>兑换时会依据分组兑换次数来判断是否已兑换完毕；<span class="text-blue">其它</span>类型卡需要谨慎配置。');
        $form->decimal('goods_price', __('销售单价'))->default(0.0);
        $form->decimal('tax_rate', __('税率'))->default(0.06);
        $form->select('exchange_cycle', __('兑换周期'))->options(ExchangeBatch::$exchange_cycle)->default(ExchangeBatch::EXCHANGE_CYCLE_NO_CYCLE);
        $form->text('package_name', __('兑换卡名称'));
        $form->switch('status', __('状态'))->options(ExchangeBatch::$status)->default(1);
        $form->text('batch_desc', __('批次描述'));
        $form->image('instr.show_img', __('展示图'))->removable();
        $form->html('<span class="text-blue">富文本图片上传限制单张图片大小在2M以内，只支持一张一张上传。</span>');
        $form->editor('instr.instr', __('兑换说明'));
        if ($form->isCreating()) {
            $form->hidden('batch_no', __('批次编号'))->default(time() . mt_rand(0, 9));
        }
        $form->html('<span class="text-blue">注：批次商品请在"批次列表->操作->商品配置"里进行配置。</span>');

        $form->submitted(function (Form $form) {
            $request = request();
            //有该键，表示是删除图片或文件
            if ($request->has('_file_del_')) {
                return true;
            }
            $model = $form->model();
            $count = ExchangeDetail::where('exchange_batch_id', $model->id)->count();

            if (\request('total_num') < $count) {
                admin_error("兑换码数量不正确", "兑换码数量不能小于 " . $count . '。');
                return back();
            }

            $type      = $request->get('type');
            $max_times = $request->get('max_times');
            //礼品卡只能兑换一次
            if ($type == ExchangeBatch::TYPE_LIPINKA && $max_times != 1) {
                admin_error("兑换次数错误", "礼品卡兑换次数只能为1。");
                return back();
            }

            $exchange_goods = $request->get('exchange_goods');
            if ($exchange_goods) {
                $goods_ids = [];
                foreach ($exchange_goods as $key => $val) {
                    if (!empty($val['goods_id']) && in_array($val['goods_id'], $goods_ids)) {
                        admin_error("批次商品重复！");
                        return back()->withInput();
                    } else {
                        $goods_ids[] = $val['goods_id'];
                    }
                }
            }

            if ($form->isEditing()) {
                $model->updated_by = Admin::user()->id;
            } else {
                $model->created_by = Admin::user()->id;
            }
            return true;
        });

        return $form;
    }

    public function getGoodsList(Request $request)
    {

        $exchange_batch_id = $request->get('q');

        $exchange_batch = ExchangeBatch::find($exchange_batch_id);

        if (empty($exchange_batch)) {
            return [];
        }

        $goods = DB::table('activity_prizes')
            ->leftJoin('goods', 'activity_prizes.goods_id', '=', 'goods.id')
            ->where('activity_prizes.activity_id', $exchange_batch->activity_id)
            ->orderBy('id', 'desc')
            ->get(['goods.id', 'goods.goods_name']);

        $ret = [];
        foreach ($goods as $g) {
            $ret[] = ['id' => $g->id, 'text' => $g->goods_name];
        }

//        $arr = ['id' => 0, 'text' => '无'];
//        array_unshift($ret, $arr);

        return $ret;
    }

    public function getGoodsByActivityId(Request $request)
    {

        $activity_id = $request->get('q');

        $goods = DB::table('activity_prizes')
            ->leftJoin('goods', 'activity_prizes.goods_id', '=', 'goods.id')
            ->where('activity_prizes.activity_id', $activity_id)
            ->orderBy('id', 'desc')
            ->get(['goods.id', 'goods.goods_name']);

        $ret = [];
        foreach ($goods as $g) {
            $ret[] = ['id' => $g->id, 'text' => $g->goods_name];
        }
        return $ret;
    }

    public function getBatches(Request $request)
    {
        $activity_id    = $request->get('q');
        $exchange_batch = ExchangeBatch::where('activity_id', $activity_id)->orderBy('id', 'desc')->get(['id', 'package_name']);

        if (count($exchange_batch) == 0) {
            return [];
        }
        $ret = [];
        foreach ($exchange_batch as $g) {
            $ret[] = ['id' => $g->id, 'text' => $g->package_name];
        }
        return $ret;
    }

    private function prepareExchangeGoods($exchange_goods, $is_edit = false)
    {
        if (empty($exchange_goods)) {
            return [];
        }
//        foreach ($exchange_goods as $key => $val) {
//
//        }
        return $exchange_goods;
    }

}
