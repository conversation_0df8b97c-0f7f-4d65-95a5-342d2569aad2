<?php

namespace App\Admin\Controllers;

use App\Admin\Extensions\WidgetForm;
use App\Exceptions\MyException;
use App\Http\Controllers\Api\SysCode\SysCode;
use App\Models\ExchangeGroup;
use Encore\Admin\Layout\Content;
use Illuminate\Routing\Controller;
use App\Models\Activity;
use App\Models\ExchangeBatch;
use App\Models\ExchangeDetail;
use App\Models\ExchangeGoods;
use App\Models\Goods;
use Encore\Admin\Auth\Database\Administrator;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Grid\Filter;
use Encore\Admin\Show;
use Encore\Admin\Widgets\Table;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Encore\Admin\Widgets;

/**
 * 项目商品配置（在项目批次列表中使用）
 * @date: 2024/10/18
 */
class ExchangeBatchGoodsController extends Controller
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = '兑换码批次商品管理';


//    public function goodsMgr2(Content $content, $batch_id)
//    {
//        $content->title($this->title);
//
//        $exchange_batch = ExchangeBatch::find($batch_id);
//        if (empty($exchange_batch)) {
//            admin_error('错误', '批次不存在!');
//            return back()->withInput();
//        }
//
//
//        $form = new WidgetForm();
//        $form->method('post');
//        $form->action(url(sprintf('admin/exchange-batches/goods-mgr/%d/submit', $exchange_batch->id)));
//
//        $form->multipleSelect('exchange_batch_good_ids', '批次商品')->options(function () use ($exchange_batch) {
//            $goods = DB::table('activity_prizes')
//                ->leftJoin('goods', 'activity_prizes.goods_id', '=', 'goods.id')
//                ->where('activity_prizes.activity_id', $exchange_batch->activity_id)
//                ->orderBy('goods.goods_name')
//                ->pluck('goods.goods_name', 'goods.id');
//            return $goods;
//        })->value($exchange_batch->exchange_goods->pluck('goods_id')->toArray());
//
//        $form->disableReset();
//        $form->disablePjax();
//
//        $content->body(new Widgets\Box('', $form));
//        return $content;
//    }

    public function goodsMgr($batch_id, Content $content)
    {
        $exchange_batch = ExchangeBatch::find($batch_id);
        if (empty($exchange_batch)) {
            admin_error('错误', '批次不存在!');
            return back()->withInput();
        }

        //活动中配置的所有商品
        $all_goods = DB::table('activity_prizes')
            ->leftJoin('goods', 'activity_prizes.goods_id', '=', 'goods.id')
            ->where('activity_prizes.activity_id', $exchange_batch->activity_id)
            ->orderBy('activity_prizes.id', 'desc')
            ->select(['activity_prizes.*', 'goods.goods_no', 'goods.goods_name', 'goods.goods_type', DB::raw('goods.status goods_status')])
            ->get();

        $goods = [];
        foreach ($all_goods as $item) {
            $goods[$item->goods_id] = sprintf('%s[%s]', $item->goods_name, $item->goods_no);
        }

        $exchange_goods = DB::table('exchange_goods')
            ->where('exchange_goods.exchange_batch_id', $exchange_batch->id)
            ->orderBy('sort')
            ->get()
            ->toArray();
        //给exchange_goods按组id分组
        $exchange_group_arr = [];
        foreach ($exchange_goods as $item) {
            $exchange_group_arr[$item->exchange_group_id][] = (array)$item;
        }
        //TEST: EXAMPLE
//        if (empty($exchange_group_arr)) {
//            $exchange_group_arr[1] = [
//                ['id' => 66, 'exchange_batch_id' => $exchange_batch->id, 'exchange_group_id' => 1, 'goods_id' => 35, 'sort' => 99, 'status' => 1],
//                ['id' => 67, 'exchange_batch_id' => $exchange_batch->id, 'exchange_group_id' => 1, 'goods_id' => 37, 'sort' => 99, 'status' => 1],
//            ];
//            $exchange_group_arr[2] = [
//                ['id' => 68, 'exchange_batch_id' => $exchange_batch->id, 'exchange_group_id' => 2, 'goods_id' => 38, 'sort' => 99, 'status' => 1],
//            ];
//        }

        $view      = '';
        $view_vars = [
            'exchange_batch' => $exchange_batch,
            'goods'          => $goods,
            'exchange_goods' => $exchange_group_arr,
        ];

        if (ExchangeBatch::hasGroups($exchange_batch->type)) {
            //需要分组：礼包卡、套餐卡（分组计次）
            //分组信息
            $group_list = DB::table('exchange_groups')
                ->where('exchange_batch_id', $exchange_batch->id)
                ->orderBy('sort')
                ->get();

            $view_vars['exchange_groups'] = $group_list->toArray();
            //TEST: EXAMPLE
//            if (empty($view_vars['exchange_groups'])) {
//                $view_vars['exchange_groups'] = [
//                    ['id' => 1, 'exchange_batch_id' => $exchange_batch->id, 'group_name' => '分组1', 'exchange_times' => 2, 'allow_repeat' => 1, 'sort' => 99, 'status' => 1],
//                    ['id' => 2, 'exchange_batch_id' => $exchange_batch->id, 'group_name' => '分组2', 'exchange_times' => 1, 'allow_repeat' => 0, 'sort' => 98, 'status' => 1],
//                ];
//            }

            $view = 'admin.exchange_batch_goods_groups';
        } else {
            //不需要分组：礼品卡、计次卡
            $view = 'admin.exchange_batch_goods';
        }

//        dd($view, $view_vars);

        return $content
            ->header($this->title)
            //->description('')
            ->body(view($view)->with($view_vars));
    }

    public function doSubmit(Request $request, $batch_id)
    {
        try {
            //分组：{"data":[{"id":"1","group_name":"分组1","exchange_times":2, "allow_repeat":0,"sort":"99","goods":[{"id":"66","goods_id":"35","sort":"99","status":"1"},{"id":"67","goods_id":"37","sort":"99","status":"1"}]},{"id":"2","group_name":"分组2","exchange_times":2, "allow_repeat":1,"sort":"98","goods":[{"id":"68","goods_id":"38","sort":"99","status":"1"}]}],"_token":"QeUByeFgigsrmcZK36lwRFh4zEdLvf3UnH5e2eTo"}
            //非分组：{"data":[{"id":"66","goods_id":"35","sort":"99","status":"1"},{"id":"67","goods_id":"37","sort":"99","status":"1"}],"_token":"QeUByeFgigsrmcZK36lwRFh4zEdLvf3UnH5e2eTo"}
            $req_params = $request->all();//取其中的data
            $req_data   = $req_params['data'];

            $exchange_batch = ExchangeBatch::find($batch_id);
            if (empty($exchange_batch)) {
                throw new MyException('批次不存在', SysCode::PARAMS_ERROR);
            }

            if (empty($req_data)) {
                //清空批次商品配置
                DB::beginTransaction();
                try {
                    ExchangeGroup::where('exchange_batch_id', $batch_id)->delete();
                    ExchangeGoods::where('exchange_batch_id', $batch_id)->delete();
                    DB::commit();
                } catch (\Exception $exc) {
                    DB::rollBack();
                    throw $exc;
                }

                return [
                    'code' => SysCode::SUCCESS,
                    'msg'  => SysCode::$resp_msg[SysCode::SUCCESS],
                ];
            }

            if (ExchangeBatch::hasGroups($exchange_batch->type)) {
                //需分组：礼包卡， 套餐卡（分组计次）
                return $this->doGroupSubmit($exchange_batch, $req_data);
            } else {
                //不需分组
                return $this->doNoGroupSubmit($exchange_batch, $req_data);
            }

        } catch (\Exception $exc) {
            return standardized_api_exception($exc);
        }
    }

    //分组商品配置
    protected function doGroupSubmit($exchange_batch, $req_data)
    {
        $group_ids = DB::table('exchange_groups')
            ->where('exchange_batch_id', $exchange_batch->id)
            ->orderBy('sort')
            ->pluck('id')
            ->toArray();

        $admin_user_id = Admin::user()->id;
        $now           = date('Y-m-d H:i:s');

        DB::beginTransaction();
        try {
            //先删除该批次所有商品
            DB::table('exchange_goods')
                ->where('exchange_batch_id', $exchange_batch->id)
                ->delete();

            $curr_group_ids = [];//有效的分组id列表
            $goods_rows     = [];//商品列表
            $exchange_times = 0;
            foreach ($req_data as $idx => $group) {
                $group['exchange_batch_id'] = $exchange_batch->id;
                $exchange_times             += $group['exchange_times'];
                if (!empty($group['id'])) {
                    //修改
                    if (!in_array($group['id'], $group_ids)) {
                        throw new MyException('分组id参数错误，请刷新后重试', SysCode::PARAMS_ERROR);
                    }

                    DB::table('exchange_groups')->where([
                        'id'                => $group['id'],
                        'exchange_batch_id' => $exchange_batch->id,
                    ])->update([
                        'group_name'     => $group['group_name'],
                        'sort'           => $group['sort'],
                        'exchange_times' => $group['exchange_times'],
                        'allow_repeat'   => $group['allow_repeat'],
                        'status'         => $group['status'] ?? 1,
                        'updated_by'     => $admin_user_id,
                        'updated_at'     => $now,
                    ]);

                } else {
                    $req_data[$idx]['id'] = $group['id'] = DB::table('exchange_groups')
                        ->insertGetId([
                            'exchange_batch_id' => $group['exchange_batch_id'],
                            'group_name'        => $group['group_name'],
                            'sort'              => $group['sort'],
                            'exchange_times'    => $group['exchange_times'],
                            'allow_repeat'      => $group['allow_repeat'],
                            'status'            => $group['status'] ?? 1,
                            'created_by'        => $admin_user_id,
                            'created_at'        => $now,
                        ]);
                }

                $curr_group_ids[] = $group['id'];

                $goods_rows = array_merge($goods_rows, $this->buildGoodsAttrs($exchange_batch->id, $group['id'], $group['goods'], $admin_user_id, $now));
            }

            if (!empty($goods_rows)) DB::table('exchange_goods')->insert($goods_rows);

            //删除库中多余分组
            DB::table('exchange_groups')
                ->where('exchange_batch_id', $exchange_batch->id)
                ->whereNotIn('id', $curr_group_ids)
                ->delete();

            //套餐卡（分组计次），同步更新批次的兑换次数
            if ($exchange_batch->type == ExchangeBatch::TYPE_JICIKA_WITH_GROUP && $exchange_batch->max_times != $exchange_times) {
                DB::table('exchange_batches')
                    ->where('id', $exchange_batch->id)
                    ->update([
                        'max_times'  => $exchange_times,
                        'updated_by' => $admin_user_id,
                        'updated_at' => $now,
                    ]);
            }

            DB::commit();
        } catch (\Exception $exc) {
            DB::rollBack();
            throw $exc;
        }

        return [
            'code' => SysCode::SUCCESS,
            'msg'  => SysCode::$resp_msg[SysCode::SUCCESS],
            'data' => $req_data,
        ];
    }

    //非分组商品配置
    protected function doNoGroupSubmit($exchange_batch, $req_data)
    {
        $admin_user_id = Admin::user()->id;
        $now           = date('Y-m-d H:i:s');

        DB::beginTransaction();
        try {
            //删除库中多余分组及对应商品
            DB::table('exchange_groups')
                ->where('exchange_batch_id', $exchange_batch->id)
                ->delete();

            DB::table('exchange_goods')
                ->where('exchange_batch_id', $exchange_batch->id)
                ->delete();

            $goods_rows = $this->buildGoodsAttrs($exchange_batch->id, 0, $req_data, $admin_user_id, $now);

            if (!empty($goods_rows)) DB::table('exchange_goods')->insert($goods_rows);

            DB::commit();
        } catch (\Exception $exc) {
            DB::rollBack();
            throw $exc;
        }

        return [
            'code' => SysCode::SUCCESS,
            'msg'  => SysCode::$resp_msg[SysCode::SUCCESS],
            'data' => $req_data,
        ];
    }

    protected function buildGoodsAttrs($batch_id, $group_id, $goods, $admin_user_id, $now)
    {
        $rows = [];
        foreach ($goods as $item) {
            $rows[] = [
                'exchange_batch_id' => $batch_id,
                'exchange_group_id' => $group_id,
                'goods_id'          => $item['goods_id'],
                'sort'              => $item['sort'],
                'status'            => $item['status'],
                'created_by'        => $admin_user_id,
                'created_at'        => $now,
            ];
        }
        return $rows;
    }
}
