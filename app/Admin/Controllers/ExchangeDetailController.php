<?php

namespace App\Admin\Controllers;

use App\Admin\Extensions\WidgetForm;
use App\Admin\Support\LogOpt;
use App\Models\Activity;
use App\Models\ExchangeBatch;
use App\Models\ExchangeDetail;
use App\Models\ExchangeGroup;
use App\Models\ExchangeRecord;
use App\Models\Goods;
use App\Models\Order;
use Carbon\Carbon;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Grid\Filter;
use Encore\Admin\Layout\Content;
use Encore\Admin\Show;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Encore\Admin\Widgets;

class ExchangeDetailController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = '兑换码';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new ExchangeDetail());

        $grid->column('id', __('Id'));
        $grid->column('activity.activity_name', __('活动名称'));
        $grid->column('exchange_batch.package_name', __('批次名称'));
        $grid->column('num', __('序列号'))->expand(function ($model) {

            $orders = [];
            if ($model->status > ExchangeDetail::STATUS_NO_EXCHANGE) {
                $records = $model->exchange_records;
                foreach ($records as $r) {
                    $order = $r->order;
                    $row   = [];
//                $row[] = ExchangeRecord::$exchange_status[$r->exchange_status];
                    $row[] = $order->order_no;
                    $row[] = $order->goods_no;
                    $row[] = $order->goods_name;
                    $row[] = $order->charge_account;
                    $row[] = $order->consignee_name;
                    $row[] = $order->consignee_phone;
                    $row[] = Order::$status[$order->status];
                    $row[] = $order->created_at;

                    $orders[] = $row;
                }
            }

            return new Widgets\Table(['订单号', '商品编号', '商品名称', '充值账号', '收货人', '收货人联系方式', '订单状态', '下单时间'], $orders);
        });
        $grid->column('code', __('兑换码'))->display(function ($vaule) {
            return str_hide_middle($vaule);
        });
        $grid->column('endtime', __('过期日期'));
        $grid->column('exchanged_times', __('已兑次数'));
//        $grid->column('bind_by', __('绑定手机号'));
//        $grid->column('bind_at', __('绑定时间'));
        $grid->column('status', __('状态'))->using(ExchangeDetail::$status);
        $grid->column('enable', __('可用状态'))->using(ExchangeDetail::$enable);
        $grid->column('expired', __('过期'))->using(ExchangeDetail::$expired);
        $grid->column('exchanged_goods', __('已兑换礼品'))->display(function ($value) {
            if (!empty($value)) {
                return ExchangeDetailController::getExchangeGoodsDisplay($value);
            }
            return '';
        });
        $grid->column('last_exchange_time', __('最后兑换时间'));
        $grid->column('created_at', __('生成时间'));

        $grid->disableCreateButton();
        $grid->actions(function ($actions) {
            $actions->disableView();
            $actions->disableDelete();
//            $actions->disableEdit();
        });


        //查询过滤
        $grid->filter(function (Filter $filter) {
            $filter->disableIdFilter();
            $filter->column(1 / 2, function (Filter $filter) {
                $filter->equal('activity_id', '活动')->select(Activity::all([
                    'id', 'activity_name'
                ])->sortByDesc('id')->pluck('activity_name', 'id')
                );
                $filter->equal('exchange_batch_id', '批次')->select(ExchangeBatch::all([
                    'id', 'package_name'
                ])->sortByDesc('id')->pluck('package_name', 'id')
                );
                $filter->equal('status', '状态')->select(ExchangeDetail::$status);
                $filter->equal('enable', '可用状态')->select(ExchangeDetail::$enable);

            });
            $filter->column(1 / 2, function (Filter $filter) {
                $filter->equal('num', '序号');
                $filter->equal('code', '兑换码');
                $filter->between('last_exchange_time', '最后兑换时间')->datetime();
                $filter->between('created_at', '创建时间')->datetime();

            });
        });

        $grid->tools(function (Grid\Tools $tools) {
            $tools->append('<a class="btn btn-sm btn-success" style="float: right;margin-right: 20px;" href="' . url('admin/exchange-details/export-list') . '"><i class="fa fa-download"></i>&nbsp;导出&nbsp;</a>');
        });

        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        $show = new Show(ExchangeDetail::findOrFail($id));

//        $show->field('id', __('Id'));
//        $show->field('activity_id', __('Activity id'));
//        $show->field('exchange_batch_id', __('Exchange batch id'));
//        $show->field('num', __('Num'));
//        $show->field('code', __('Code'));
//        $show->field('endtime', __('Endtime'));
//        $show->field('exchanged_times', __('Exchanged times'));
//        $show->field('bind_by', __('Bind by'));
//        $show->field('bind_at', __('Bind at'));
//        $show->field('status', __('Status'));
//        $show->field('enable', __('Enable'));
//        $show->field('expired', __('Expired'));
//        $show->field('exchanged_goods', __('Exchanged goods'));
//        $show->field('exchanged_group_id', __('Exchanged group id'));
//        $show->field('last_exchange_time', __('Last exchange time'));
//        $show->field('created_by', __('Created by'));
//        $show->field('updated_by', __('Updated by'));
//        $show->field('created_at', __('Created at'));
//        $show->field('updated_at', __('Updated at'));

        return $show;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $form = new Form(new ExchangeDetail());

        $form->date('endtime', __('过期时间'))->default(date('Y-m-d'));
        $enable = ExchangeDetail::$enable;
        unset($enable[ExchangeDetail::ENABLE_HEXIAO]);
        $form->radio('enable', __('可用状态'))->options($enable);
        $form->switch('expired', __('是否过期'));
        //因渠道已退款，所以已成功的也可以作废、禁用。这里不再限制兑换状态 2025-05-26
        //$form->submitted(function (Form $form) {
        //    $model   = $form->model();
        //    $request = request();
        //    if ($model->status != ExchangeDetail::STATUS_NO_EXCHANGE
        //        && in_array(request('enable'), [ExchangeDetail::ENABLE_HEXIAO, ExchangeDetail::ENABLE_ABANDON, ExchangeDetail::ENABLE_NO])) {
        //
        //        admin_error("该兑换码已使用！");
        //        return back();
        //    }
        //});

        return $form;
    }

    public function export_list(Content $content)
    {
        $content->title('兑换码导出');
        $form = new WidgetForm();
        $form->method('get');
        $form->action(url('admin/exchange-details/export'));
        $options = DB::table('activities')->orderBy('id', 'desc')->pluck('activity_name', 'id')->toArray();
        $form->select('activity_id', '所属活动')->options($options)->required()
            ->load('exchange_batch_ids', route('exb-get-batches'));
        if (\request('activity_id')) {
            $form->multipleSelect('exchange_batch_ids', '所属批次')->options(function () {
                return DB::table('exchange_batches')
                    ->where('activity_id', \request('activity_id'))
                    ->orderBy('id', 'desc')
                    ->pluck('activity_name', 'id')
                    ->toArray();
            });
        } else {
            $form->multipleSelect('exchange_batch_ids', '所属批次');
        }
        $form->datetime('start_date', '起始时间');
        $form->datetime('end_date', '截止时间')->help('起始日期和截止日期均按卡生成时间筛选');
        $form->listbox('column_list', '导出数据项')->options(ExchangeDetail::$export_column_list)
            ->help('导出数据项不能为空，该项的值对应导出Excel表格的列标题');

        $form->disableReset();
        $form->disablePjax();

        $content->body(new Widgets\Box('', $form));
        return $content;
    }

    public function export(Content $content, Request $request)
    {
        try {
            $activity_id        = $request->get('activity_id');
            $exchange_batch_ids = array_filter($request->get('exchange_batch_ids'));
            $column_list        = $request->get('column_list');
            $start_date         = $request->get('start_date');
            $end_date           = $request->get('end_date');

            if (empty($activity_id)) {
                throw new \Exception('请选择活动！');
            }

            foreach ($column_list as $key => $value) {
                if (empty($value)) {
                    unset($column_list[$key]);
                }
            }

            if (empty($column_list)) {
                throw new \Exception('请选择要导出的数据项！');
            }

            $activitiy = DB::table('activities')->find($activity_id);
            if ($exchange_batch_ids) {
                $batches = DB::table('exchange_batches')->where('activity_id', $activity_id)
                    ->whereIn('id', $exchange_batch_ids)->pluck('package_name', 'id')
                    ->toArray();
            } else {
                $batches = DB::table('exchange_batches')->where('activity_id', $activity_id)
                    ->pluck('package_name', 'id')
                    ->toArray();
            }

            $val_converts = [
                'activity_id'       => [$activity_id => $activitiy->activity_name],
                'status'            => ExchangeDetail::$status,
                'exchange_batch_id' => $batches,
                'expired'           => ExchangeDetail::$expired,
                'enable'            => ExchangeDetail::$enable,
                'code'              => $activitiy->exchange_url,
            ];

            $zip_file  = storage_path() . '/app/export/exchange_detail/兑换码_' . Admin::user()->id . '_' . date('ymdHis') . '.zip';
            $csv_files = [];
            foreach ($batches as $batch_id => $package_name) {
                //其他项目的数据
                $list = DB::table('exchange_details')
                    ->where('activity_id', $activity_id)
                    ->where('exchange_batch_id', $batch_id)
                    ->select($column_list);

                if (!empty($start_date)) {
                    $list->where('created_at', '>=', $start_date);
                }

                if (!empty($end_date)) {
                    $list->where('created_at', '<=', $end_date);
                }

                if ($list->count() <= 0) {
                    continue;
                }

                $file_name   = str_replace('+', '_', $package_name) . '_' . Admin::user()->id . '_' . date('ymdHis') . '.csv';
                $savePath    = storage_path() . '/app/export/exchange_detail/' . $file_name;
                $csv_files[] = $savePath = $this->export_csv($list, $column_list, $val_converts, $savePath);
                LogOpt::log(true, '兑换码导出', [], $savePath);
            }

            if (empty($csv_files)) {
                $success = new \Illuminate\Support\MessageBag([
                    'title'   => '提示',
                    'message' => '没有满足条件的记录，请重新选择！',
                ]);
                return redirect('admin/exchange-details/export-list')->withInput()->with(compact('success'));
            }

            $zip = new \ZipArchive ();
            $res = $zip->open($zip_file, \ZipArchive::CREATE);
            if ($res === TRUE) {
                foreach ($csv_files as $file) {
                    $zip->addFile($file, basename($file));
                }
            }
            $zip->close();

            return response()->download($zip_file);

        } catch (\Exception $exception) {
            $content->withError('错误提示', $exception->getMessage() . '。 file:' . $exception->getFile() . ':' . $exception->getLine());
        }

        return redirect('admin/exchange-details/export-list')->withInput();
    }

    /**
     * @param \Illuminate\Database\Query\Builder $builder
     * @param array $column_list 列名与标题关系。例如['goods_name' => '商品名称','charge_account' => '充值账号']
     * @param array $val_converts 列值转换表  ['status' => ['1' => '待发货','2' =>'发货中' ],'sms_status'  => Order::$sms_status,];
     * @param string $savePath 文件生成物理路径
     * @param mixed $format_str_col_index 需要转换成文本的列索引，例如[1,2,4]。
     *
     * @return mixed
     * @throws \Exception
     */
    private function export_csv($builder, $column_list, $val_converts, $savePath)
    {
        // 设置过期时间
        set_time_limit(300);
        $delimiter = ',';
        if (empty($savePath)) {
            throw new \Exception('导出文件名不能为空！');
        }

        $fp = fopen($savePath, 'a');

        $title_data = [];

        foreach ($column_list as $k => $column) {
            $title_data[] = filterCsvVal(ExchangeDetail::$export_column_list[$column]);
            if (strstr($column, '.')) {
                $column_list[$k] = explode('.', $column)[1];
            }
        }
        $csv_title = chr(0xEF) . chr(0xBB) . chr(0xBF);
        $csv_title .= implode($delimiter, $title_data) . "\r\n";

        fputs($fp, $csv_title, strlen($csv_title));

        $count = $builder->count();

        $perSize = 2000;//每次查询的条数
        $pages   = ceil($count / $perSize);

        for ($i = 1; $i <= $pages; $i++) {
            //需要导出的数据
            $lists    = $builder->forPage($i, $perSize)->get();
            $csv_data = '';

            foreach ($lists as $v) {
                $arr       = (array)$v;
                $data      = [];
                $col_index = 1;

                foreach ($column_list as $column) {

                    if (array_key_exists($column, $val_converts)) {
                        if (is_array($val_converts[$column]) && array_key_exists($arr[$column], $val_converts[$column])) {
                            $curr_col_data = $val_converts[$column][$arr[$column]];
                        } elseif (!empty($val_converts[$column]) && is_string($val_converts[$column]) && strpos($val_converts[$column], '${0}') !== false) {
                            $curr_col_data = str_replace('${0}', $arr[$column], $val_converts[$column]);
                        } else {
                            $curr_col_data = $arr[$column];
                        }
                    } else {
                        $curr_col_data = $arr[$column];
                    }

                    $curr_col_data = filterCsvVal($curr_col_data);

                    $data[] = $curr_col_data;

                    unset($curr_col_data);

                    $col_index += 1;
                }

                $csv_data .= implode($delimiter, $data) . "\r\n";

                unset($data);
            }

            fputs($fp, $csv_data, strlen($csv_data));
            unset($csv_data);//释放变量的内存
        }
        fclose($fp);
        return $savePath;
    }

    //缓存数据
    static $cached = ['goods' => [], 'groups' => []];

    static function getExchangeGoodsDisplay($exchange_goods)
    {
        if (str_contains($exchange_goods, '-')) {
            //分组
            $exchanged_goods_arr = explode(',', $exchange_goods);
            $goods_ids           = [];
            $group_ids           = [];
            foreach ($exchanged_goods_arr as $item) {
                list($group_id, $goods_id) = exchange_split($item);
                $goods_ids[] = $goods_id;
                $group_ids[] = $group_id;
            }
            self::getGoodsFillCache($goods_ids);
            self::getGroupFillCache($group_ids);
            $arr = [];
            foreach ($exchanged_goods_arr as $item) {
                list($group_id, $goods_id) = exchange_split($item);
                $arr[] = sprintf('%s-%s', self::$cached['groups'][$group_id] ?? $group_id, self::$cached['goods'][$goods_id] ?? $goods_id);
            }
            return implode('，', $arr);
        } else {
            //非分组
            $goods_ids = explode(',', $exchange_goods);
            //求差集
            self::getGoodsFillCache($goods_ids);
            $arr = [];
            foreach ($goods_ids as $item) {
                $arr[] = self::$cached['goods'][$item] ?? $item;
            }
            return implode('，', $arr);
        }
    }

    static function getGoodsFillCache($goods_ids)
    {
        $diff_goods_ids = array_diff($goods_ids, array_keys(self::$cached['goods']));
        if ($diff_goods_ids) {
            $goods = Goods::whereIn('id', $diff_goods_ids)->select(['goods_name', 'id'])->pluck('goods_name', 'id')->toArray();
            foreach ($goods as $key => $val) {
                self::$cached['goods'][$key] = $val;
            }
        }
    }

    static function getGroupFillCache($group_ids)
    {
        $diff_goods_ids = array_diff($group_ids, array_keys(self::$cached['groups']));
        if ($diff_goods_ids) {
            $groups = ExchangeGroup::whereIn('id', $group_ids)->select(['group_name', 'id'])->pluck('group_name', 'id')->toArray();
            foreach ($groups as $key => $val) {
                self::$cached['groups'][$key] = $val;
            }
        }
    }
}
