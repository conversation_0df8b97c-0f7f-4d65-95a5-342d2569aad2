<?php

namespace App\Admin\Controllers;

use App\Admin\Extensions\WidgetForm;
use App\Admin\Support\LogOpt;
use App\Http\Controllers\Api\SysCode\SysCode;
use App\Models\Activity;
use App\Models\ExchangeBatch;
use App\Models\ExchangeDetail;
use App\Models\ExchangeDetailForbidden;
use App\Models\ExchangeGroup;
use App\Models\ExchangeRecord;
use App\Models\Goods;
use App\Models\Order;
use Carbon\Carbon;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Grid\Filter;
use Encore\Admin\Layout\Content;
use Encore\Admin\Show;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Encore\Admin\Widgets;
use Illuminate\Support\Facades\Log;

class ExchangeDetailForbiddenController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = '兑换码禁用记录';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new ExchangeDetailForbidden());

        $grid->column('id', __('Id'));
        $grid->column('activity.activity_name', __('活动名称'));
//        $grid->column('exchange_batch.package_name', __('批次名称'));
        $grid->column('exchange_detail.num', __('序列号'));
        $grid->column('code', __('兑换码'));
//        $grid->column('forbidden_status', __('状态'))->using(ExchangeDetailForbidden::$forbidden_status);
        $grid->column('exchange_detail.status', __('兑换码兑换状态'))->using(ExchangeDetail::$status);
        $grid->column('exchange_detail.enable', __('兑换码可用状态'))->using(ExchangeDetail::$enable);
        $grid->column('batch_num', __('批次标识'));
        $grid->column('forbidden_at', __('禁用时间'));
        $grid->column('created_at', __('入库时间'));
        $grid->column('remark', __('备注'));

        $grid->disableCreateButton();
        $grid->disableExport(false);
        $grid->actions(function ($actions) {
            $actions->disableView();
            $actions->disableDelete();
            $actions->disableEdit();
        });

        //查询过滤
        $grid->filter(function (Filter $filter) {
            $filter->disableIdFilter();
            $filter->column(1 / 2, function (Filter $filter) {
                $filter->equal('code', '兑换码');
                $filter->equal('activity_id', '活动')->select(Activity::all([
                    'id', 'activity_name'
                ])->sortByDesc('id')->pluck('activity_name', 'id')
                );
                $filter->equal('exchange_batch_id', '批次')->select(ExchangeBatch::all([
                    'id', 'package_name'
                ])->sortByDesc('id')->pluck('package_name', 'id')
                );
            });
            $filter->column(1 / 2, function (Filter $filter) {
                $filter->equal('batch_num', '批次标志');
//                $filter->equal('forbidden_status', '状态')->select(ExchangeDetailForbidden::$forbidden_status);
                $filter->between('created_at', '入库时间')->datetime();

            });
        });

//        $grid->tools(function (Grid\Tools $tools) {
//            $tools->append('<a class="btn btn-sm btn-success" style="float: right;margin-right: 20px;" href="' . url('admin/exchange-detail-forbidden/import') . '"><i class="fa fa-download"></i>&nbsp;禁用兑换码处理&nbsp;</a>');
//        });

        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        $show = new Show(ExchangeDetail::findOrFail($id));

//        $show->field('id', __('Id'));
//        $show->field('activity_id', __('Activity id'));
//        $show->field('exchange_batch_id', __('Exchange batch id'));
//        $show->field('num', __('Num'));
//        $show->field('code', __('Code'));
//        $show->field('endtime', __('Endtime'));
//        $show->field('exchanged_times', __('Exchanged times'));
//        $show->field('bind_by', __('Bind by'));
//        $show->field('bind_at', __('Bind at'));
//        $show->field('status', __('Status'));
//        $show->field('enable', __('Enable'));
//        $show->field('expired', __('Expired'));
//        $show->field('exchanged_goods', __('Exchanged goods'));
//        $show->field('exchanged_group_id', __('Exchanged group id'));
//        $show->field('last_exchange_time', __('Last exchange time'));
//        $show->field('created_by', __('Created by'));
//        $show->field('updated_by', __('Updated by'));
//        $show->field('created_at', __('Created at'));
//        $show->field('updated_at', __('Updated at'));

        return $show;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $form = new Form(new ExchangeDetail());
        return $form;
    }

    //禁用兑换码导入。
    public function forbidden_import(Content $content)
    {
        $content->title('兑换码禁用处理');
        $form = new Widgets\Form();
        $form->method('post');
        $form->action(url('admin/exchange-detail-forbidden/do_import'));
//        $form->select('activity_id', '请选择活动')->options(function () {
//            return Activity::where(['status' => 1])->orderBy('id', 'desc')->pluck('activity_name', 'id');
//        })->required();
        $form->html('<div>上传文件为txt格式，第一行为标题，写"title"即可，每行一个兑换码（不包含网址）。</div>');
        $form->file('forbidden_file', '兑换码文件')->removable()->options([
            'showPreview'           => false,
            'allowedFileExtensions' => ['txt'],
        ])->required();
        $form->textarea('remark', '备注')->rules('max:200');

        $form->disableReset();
        $content->body(new Widgets\Box('待禁用兑换码导入：', $form));
        return $content;
    }

    /**
     * 禁用兑换码导入处理
     * @param Content $content
     * @param Request $request
     * @return $this
     */
    public function do_forbidden_import(Content $content, Request $request)
    {
        $logger = Log::channel('exchange_forbidden_log');

        $request->validate(
            [
                'forbidden_file' => 'required|mimes:txt',
            ], [
                'forbidden_file.required' => "导入文件不能为空",
                'forbidden_file.mimes'    => "支持的文件格式：txt",
            ]
        );

        try {
            //处理文件
            //按时间命名文件
            $up_file   = $request->file('forbidden_file');
            $ext       = $up_file->getClientOriginalExtension();
            $org_name  = $up_file->getClientOriginalName();
            $file_name = substr($org_name, 0, strripos($org_name, '.'));
            $name      = $file_name . '_' . date('YmdHis') . '_' . sprintf('%04d', Admin::user()->id) . '.' . $ext;
            //文件保存到storage/app/upload/exchange
            $path = $up_file->storeAs('upload' . DIRECTORY_SEPARATOR . 'exchange', $name);
            //获取文件url
            $url = storage_path() . DIRECTORY_SEPARATOR . 'app' . DIRECTORY_SEPARATOR . $path;

            $logger->info('导入待禁用兑换码，' . $url);

            $codes = getFileContent($url, 2);
            $codes = array_filter($codes);

            if (count($codes) != count(array_unique($codes))) {
                $content->withError('提示', "上传文件中兑换码有重复值。");
                return redirect('admin/exchange-detail-forbidden/import')->withInput();
            }

            if (empty($codes)) {
                $content->withError('提示', "兑换码列表为空");
                return redirect('admin/exchange-detail-forbidden/import')->withInput();
            }

            $wrong_details = DB::table('exchange_details')
                ->whereIn('code', $codes)
                ->where(function ($query) {
                    $query->where('status', '<>', ExchangeDetail::STATUS_NO_EXCHANGE)
                        ->orWhere('enable', '<>', ExchangeDetail::ENABLE_YES);
                })
                ->select(['code', 'status', 'enable'])
                ->pluck('code');

            if ($wrong_details && $wrong_details->count() > 0) {
                $message = "以下兑换码已兑换或已禁用：" . implode(',', $wrong_details->toArray());
                $logger->info($message);
                $content->withError('提示', $message);
                return redirect('admin/exchange-detail-forbidden/import')->withInput();
            }

            $details = DB::table('exchange_details')
//                ->leftjoin("exchange_batches", 'exchange_details.exchange_batch_id', '=', "exchange_batches.id")
                ->whereIn('exchange_details.code', $codes)
                ->where('exchange_details.status', ExchangeDetail::STATUS_NO_EXCHANGE)
                ->where('exchange_details.enable', ExchangeDetail::ENABLE_YES)
                ->select(['exchange_details.id', 'exchange_details.activity_id', 'exchange_details.exchange_batch_id', 'exchange_details.num', 'exchange_details.code', 'exchange_details.status', 'exchange_details.enable'])
                ->get();
            if ($details->count() != count($codes)) {
                $codes_in_table = $details->pluck('code')->toArray();  //库里存在的码列表。

                //找到兑换码表不存在的那些兑换码
                $not_exist = array_filter($codes, function ($item) use ($codes_in_table) {
                    if (!in_array($item, $codes_in_table)) {
                        return true;
                    }
                    return false;
                });

                if (!empty($not_exist)) {
                    $message = "以下兑换码不存在：" . implode(',', $not_exist);
                    $logger->info($message);
                    $content->withError('提示', $message);
                    return redirect('admin/exchange-detail-forbidden/import')->withInput();
                }

                $message = "查询到的兑换码数量，和上传文件中的兑换码数量不一致。请查询文件中是否有重复的兑换码。";
                $logger->info($message
                    . '查询到的兑换码清单：' . implode(',', $codes_in_table)
                    . '。文件中的兑换码：' . implode(',', $codes));
                $content->withError('提示', $message);
                return redirect('admin/exchange-detail-forbidden/import')->withInput();
            }

            $now       = date('Y:m:d H:i:s');
            $user_id   = Admin::user()->id;
            $batch_num = time() * 1000 + 100 * mt_rand(0, 9) + $user_id;
            $remark    = $request->remark;

            $insert_data = [];

            foreach ($details as $d) {
                $insert_data[] = [
                    'batch_num'          => $batch_num,
                    'activity_id'        => $d->activity_id,
                    'code'               => $d->code,
                    'exchange_batch_id'  => $d->exchange_batch_id,
                    'exchange_detail_id' => $d->id,
                    'forbidden_status'   => ExchangeDetailForbidden::FORBIDDEN_YES,
                    'forbidden_at'       => $now,
                    'remark'             => $remark,
                    'created_by'         => $user_id,
                    'updated_by'         => $user_id,
                    'created_at'         => $now,
                    'updated_at'         => $now,
                ];
            }

            DB::beginTransaction();

            try {
                DB::table('exchange_detail_forbiddens')->insert($insert_data);
                $affected_rows = DB::table('exchange_details')
                    ->whereIn('code', $codes)
                    ->where('status', ExchangeDetail::STATUS_NO_EXCHANGE)
                    ->where('enable', ExchangeDetail::ENABLE_YES)
                    ->update(['enable' => ExchangeDetail::ENABLE_HEXIAO]);

                if ($affected_rows == count($insert_data)) {
                    DB::commit();
                } else {
                    DB::rollBack();
                    $logger->error('兑换码禁用数量与插入的记录数不一致。' . json_encode(['insert_data' => $insert_data, 'update_affected_rows' => $affected_rows]));
                    $content->withError('错误提示', '兑换码禁用数量，与插入禁用日志表的记录数不一致。');
                    $logger->error('禁用兑换码失败。');
                }
            } catch (\Exception $ex) {
                DB::rollBack();
                throw $ex;
            }

            $logger->info('兑换码禁用成功。兑换码清单：' . implode(',', $codes));

            $success = new \Illuminate\Support\MessageBag([
                'title'   => '成功',
                'message' => '禁用兑换码' . count($insert_data) . '条。批次标识：' . $batch_num,
            ]);

            return redirect('admin/exchange-detail-forbidden/import')->withInput()->with(compact('success'));

        } catch (\Exception $exception) {
            $content->withError('错误提示', $exception->getMessage());
            $logger->error('禁用兑换码失败。' . $exception->getMessage());
        }
        return redirect('admin/exchange-detail-forbidden/import')->withInput();
    }
}
