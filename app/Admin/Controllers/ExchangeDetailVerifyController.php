<?php

namespace App\Admin\Controllers;

use App\Admin\Actions\Exchange\ForbiddenSettlement;
use App\Admin\Extensions\WidgetForm;
use App\Admin\Support\LogOpt;
use App\Exceptions\MyException;
use App\Http\Controllers\Api\SysCode\SysCode;
use App\Models\Activity;
use App\Models\ExchangeBatch;
use App\Models\ExchangeDetail;
use App\Models\ExchangeDetailForbidden;
use App\Models\ExchangeForbiddenBatch;
use App\Models\ExchangeGroup;
use App\Models\ExchangeRecord;
use App\Models\Goods;
use App\Models\Order;
use App\Service\RedisLimitUtils;
use Carbon\Carbon;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Grid\Displayers\Actions;
use Encore\Admin\Grid\Filter;
use Encore\Admin\Layout\Content;
use Encore\Admin\Show;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Encore\Admin\Widgets;
use Illuminate\Support\Facades\Log;

/**
 * 兑换码核销，对外服务。记录核销批次及明细，并在日志目录写入相对应文件。
 * 2022-03-17 无效码分类别写明原因及时间。
 */
class ExchangeDetailVerifyController extends AdminController
{
    protected $limit_key;         //每天核销限制的redis key
    protected $invalid_limit_key; //每天全无效码计数的redis key
    protected $redis_utils;
    protected $invalid_max_limit = 20;   //全无效码的次数上线
    protected $max_limit         = 20;   //每天核销限制次数。线上配置见.env配置文件
    protected $max_count         = 1000;  //每次核销数量上限。线上配置见.env配置文件

    protected $logger;

    public function __construct()
    {
        $this->redis_utils = RedisLimitUtils::getInstance();
        $this->max_limit   = config('app.hexiao_max_limit');
        $this->max_count   = config('app.hexiao_max_count');
        $this->logger      = Log::channel('exchange_forbidden_log');
    }

    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = '兑换码核销批次';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new ExchangeForbiddenBatch());
        if (admin_is_third_user()) {
            $grid->model()->where('created_by', Admin::user()->id);
            $grid->disableActions();
        }

//        $grid->column('id', __('Id'));
        $grid->column('batch_tag', __('批次号'));
        $grid->column('forbidden_num', __('核销数量'));
        $grid->column('created_by', __('核销人'))->display(function ($created_by) {
            return DB::table('admin_users')->where('id', $created_by)->value('name');
        });
        $grid->column('created_at', __('核销时间'));
        $grid->column('remark', __('备注'))->hide();
        $grid->column('settlement_status', __('结算状态'))->using(ExchangeForbiddenBatch::$settlement_status);
        if (!admin_is_third_user()) {
            $grid->column('settlement_date', __('结算时间'));
        }

        $grid->disableCreateButton();
        $grid->disableExport(false);

        $grid->actions(function (Actions $actions) {
            $actions->disableView();
            $actions->disableDelete();
            $actions->disableEdit();
            if (!admin_is_third_user() && $this->row->settlement_status == ExchangeForbiddenBatch::SETTLEMENT_STATUS_NO) {
                $actions->add(new ForbiddenSettlement());
            }
        });

        //查询过滤
        $grid->filter(function (Filter $filter) {
            $filter->disableIdFilter();
            $filter->column(1 / 2, function (Filter $filter) {
                $filter->between('created_at', '核销时间')->datetime();
            });
            $filter->column(1 / 2, function (Filter $filter) {
                $filter->equal('settlement_status', '结算状态')->select(ExchangeForbiddenBatch::$settlement_status);
            });
        });

        $grid->tools(function (Grid\Tools $tools) {
            if (!admin_is_third_user()) {
                $tools->append('<a class="btn btn-sm btn-warning" style="float: right;margin-right: 20px;" href="' . url('admin/exch-hx/cost-price') . '"><i class="fa fa-upload"></i>&nbsp;结算价&nbsp;</a>');
                $tools->append('<a class="btn btn-sm btn-warning" style="float: right;margin-right: 20px;" href="' . url('admin/exch-hx/settlement') . '"><i class="fa fa-upload"></i>&nbsp;批量结算&nbsp;</a>');
            }
            $tools->append('<a class="btn btn-sm btn-success" style="float: right;margin-right: 20px;" href="' . url('admin/exch-hx/import') . '"><i class="fa fa-upload"></i>&nbsp;兑换码核销&nbsp;</a>');

        });

        return $grid;
    }

    public function cost_price(Content $content)
    {
        $cost_price = DB::table('exchange_forbidden_cost_prices')->first();
        $content->title('兑换码成本价');
        $form = new WidgetForm();
        $form->method('post');
        $form->action(url('admin/exch-hx/do-cost-price'));
        $form->text('cost_price', '成本价')->default($cost_price->cost_price);
        $form->text('cost_tax_rate', '税率')->default($cost_price->cost_tax_rate)->help('一般为0.06');
        $form->disableReset();
        $form_id = $form->getFormId();
        $script  = <<<EOT
$('form#{$form_id}').off('submit').on('submit', function (e) {
    event.preventDefault();
    var form = this;
    $.admin.swal({
        title: "确定成本价正确吗？",
        type: "question",
        showCancelButton: true,
        confirmButtonText: "确认",
        showLoaderOnConfirm: true,
        cancelButtonText: "取消"
    }).then(function(result) {
      if (result.value === true) {
        form.submit();
      } else if (result.dismiss === 'cancel') {
        event.preventDefault();
        return false;
      }
    });
    return false;
});
EOT;
        Admin::script($script);

        $content->body(new Widgets\Box('', $form));
        return $content;
    }

    public function do_cost_price(Content $content, Request $request)
    {
        if (admin_is_third_user()) {
            throw new MyException('无权限!');
        }
        $request->validate(
            [
                'cost_price'    => 'required',
                'cost_tax_rate' => 'required',
            ], [
                'cost_price.required'    => "成本价不能为空",
                'cost_tax_rate.required' => "税率不能为空",
            ]
        );

        try {
            //查询有效的批次
            $cost_price = DB::table('exchange_forbidden_cost_prices')->first();
            $data       = [
                'cost_price'    => $request->cost_price,
                'cost_tax_rate' => $request->cost_tax_rate,
            ];
            if (empty($cost_price)) {
                DB::table('exchange_forbidden_cost_prices')->insert($data);
            } else {
                DB::table('exchange_forbidden_cost_prices')->where('id', $cost_price->id)->update($data);
            }

            $with_data = [
                'success' => new \Illuminate\Support\MessageBag([
                    'title'   => '成功',
                    'message' => '添加成功',
                ]),
            ];
            return redirect('admin/exch-hx/cost-price')->withInput()->with($with_data);

        } catch (\Exception $exception) {
            Log::error($exception->getMessage(), $exception->getTrace());
            $content->withError('错误提示', $exception->getMessage());
            $this->logger->error('失败。' . $exception->getMessage());

            $with_data = [
                'error' => new \Illuminate\Support\MessageBag([
                    'title'   => '失败',
                    'message' => '添加失败！',
                ]),
            ];
            return redirect('admin/exch-hx/cost-price')->withInput()->with($with_data);
        }
    }

    //禁用兑换码导入。
    public function import(Content $content)
    {
        $this->preLimitSet();

        $content->title('兑换码核销处理');
        $form = new WidgetForm();
        $form->method('post');
        $form->action(url('admin/exch-hx/do-import'));
        $form->html('<div>上传文件格式为 txt 文本文件，utf-8 编码；<br/><span style="color:red;">每行一个兑换码</span>，每个文件最多 ' . $this->max_count . ' 个兑换码；<br/>文件第一行为标题，写"code"即可，从<span style="color:red;">第二行</span>开始读取兑换码。<br/>每天最多核销' . $this->max_limit . '次。</div>');
        $form->file('hx_file', '兑换码文件')->removable()->options([
            'showPreview'           => false,
            'allowedFileExtensions' => ['txt'],
        ])->required();
        $form->textarea('remark', '备注')->rules('max:200');

        $form->disableReset();

        $form_id = $form->getFormId();
        $script  = <<<EOT
$('form#{$form_id}').off('submit').on('submit', function (e) {
    event.preventDefault();
    var form = this;
    $.admin.swal({
        title: "确定要进行核销吗？",
        type: "question",
        showCancelButton: true,
        confirmButtonText: "确认",
        showLoaderOnConfirm: true,
        cancelButtonText: "取消"
    }).then(function(result) {
      if (result.value === true) {
        form.submit();
      } else if (result.dismiss === 'cancel') {
        event.preventDefault();
        return false;
      }
    });
    return false;
});
EOT;
        Admin::script($script);

        $content->body(new Widgets\Box('', $form));
        return $content;
    }

    /**
     * 禁用兑换码导入处理
     * @param Content $content
     * @param Request $request
     * @return $this
     */
    public function do_import(Content $content, Request $request)
    {
        $this->preLimitSet();

        $user_activity_rights = [];//当前第三方用户拥有的核销项目权限配置。
        //判断有没有给该外部用户配置项目权限。
        if (admin_is_third_user()) {
            $activity_rights = config('app.hexiao_rights');     //外部用户的项目权限配置
            if (!array_key_exists(Admin::user()->username, $activity_rights)) {
                $content->withError('提示', "尚未配置核销权限，请联系相关商务人员！");
                return redirect('admin/exch-hx/import')->withInput();
            } else {
                $user_activity_rights = $activity_rights[Admin::user()->username];//有权限的活动id数组
            }
            unset($activity_rights);
        }

        //判断当天核销次数是否超限
        if (!$this->redis_utils->check($this->limit_key, $this->max_limit)) {
            $content->withError('超限提示', "今日核销次数已达上限，请明日再试！");
            return redirect('admin/exch-hx/import')->withInput();
        }

        //判断当天全无效码次数是否超限
        if (!$this->redis_utils->check($this->invalid_limit_key, $this->invalid_max_limit)) {
            $content->withError('超限提示', "今日核销次数已达上限，请明日再试！");
            return redirect('admin/exch-hx/import')->withInput();
        }

        $request->validate(
            [
                'hx_file' => 'required|mimes:txt',
            ], [
                'hx_file.required' => "导入文件不能为空",
                'hx_file.mimes'    => "支持的文件格式：txt",
            ]
        );

        try {
            //处理文件
            $up_file = $request->file('hx_file');
            $ext     = $up_file->getClientOriginalExtension();
            //文件保存到storage/app/upload/exchange
            $name      = 'hx' . sprintf('%d', Admin::user()->id) . date('YmdHis') . mt_rand(10, 99); //不包含扩展名
            $storePath = 'upload' . DIRECTORY_SEPARATOR . 'exchange' . DIRECTORY_SEPARATOR . date('Ym');
            if (!is_dir(storage_path('app' . DIRECTORY_SEPARATOR . $storePath))) {
                mkdir(storage_path('app' . DIRECTORY_SEPARATOR . $storePath), 0755, true);
            }
            $path = 'app' . DIRECTORY_SEPARATOR . $up_file->storeAs($storePath, $name . '.' . $ext);
            $url  = storage_path($path);//获取文件url

            $this->logger->info('导入待核销兑换码，' . $url . '。原文件名：' . $up_file->getClientOriginalName());

            $codes = getFileContent($url, 2);
            $codes = array_filter($codes);

            if (empty($codes)) {
                $content->withError('提示', "兑换码列表为空");
                return redirect('admin/exch-hx/import')->withInput();
            }

            if (count($codes) > $this->max_count) {
                $content->withError('提示', "每批最多只能导入" . $this->max_count . "个兑换码！");
                return redirect('admin/exch-hx/import')->withInput();
            }

            //核销次数增加
            $this->redis_utils->increment($this->limit_key, $this->redis_utils->getSeconds(RedisLimitUtils::TYPE_END_OF_DAY));

            $batch_info                = new ExchangeForbiddenBatch();
            $batch_info->batch_tag     = $name;
            $batch_info->total_num     = count($codes);
            $batch_info->invalid_num   = 0;
            $batch_info->repeat_num    = 0;
            $batch_info->forbidden_num = 0;
            $batch_info->import_file   = $path;
            $batch_info->original_file = $up_file->getClientOriginalName();
            $batch_info->remark        = $request->get('remark', '');
            $batch_info->created_by    = Admin::user()->id;

            list($dealed, $repeat_num) = $this->preDealCode($codes, $url, $name); //文件中的兑换码预处理，重复码写入*_repeat.txt

            $batch_info->repeat_num = $repeat_num;
            unset($repeat_num);

            //查询有效的兑换码
            $details = DB::table('exchange_details')
                ->leftjoin("exchange_batches", 'exchange_details.exchange_batch_id', '=', "exchange_batches.id")
                ->whereIn('exchange_details.code', $dealed)
                ->where('exchange_details.status', ExchangeDetail::STATUS_NO_EXCHANGE)
                ->where('exchange_batches.status', 1)//add 2022-08-16
                ->where('exchange_batches.endtime', '>=', Carbon::now()->format('Y-m-d'))////add 2022-08-16
                ->where('exchange_details.enable', ExchangeDetail::ENABLE_YES)
                ->where('exchange_details.expired', ExchangeDetail::EXPIRED_NO)
                ->select(['exchange_details.id', 'exchange_details.activity_id', 'exchange_details.exchange_batch_id', 'exchange_details.num', 'exchange_details.code', 'exchange_details.status', 'exchange_details.enable']);

//            $details = DB::table('exchange_details')
//                ->whereIn('exchange_details.code', $dealed)
//                ->where('exchange_details.status', ExchangeDetail::STATUS_NO_EXCHANGE)
//                ->where('exchange_details.enable', ExchangeDetail::ENABLE_YES)
//                ->where('exchange_details.expired', ExchangeDetail::EXPIRED_NO)
//                ->select(['exchange_details.id', 'exchange_details.activity_id', 'exchange_details.exchange_batch_id', 'exchange_details.num', 'exchange_details.code', 'exchange_details.status', 'exchange_details.enable']);

            if (admin_is_third_user()) {
                $details->whereIn('exchange_details.activity_id', $user_activity_rights);
            }
            $details = $details->get();

            if ($details->count() == 0) {

                $batch_info->invalid_num = count($dealed);

                $message = "文件中没有可核销的兑换码！";
                $this->logger->info($name . $message);
                //本次核销次数回退
                $this->redis_utils->decrement($this->limit_key);//全无效码时退回核销计数
                //全无效码的计数
                $this->redis_utils->increment($this->invalid_limit_key, $this->redis_utils->getSeconds(RedisLimitUtils::TYPE_END_OF_DAY));

                $this->dealInvalidCodes($dealed, $url, $name, $user_activity_rights);//无效码写入文件

                $with_data = [
                    'error'         => new \Illuminate\Support\MessageBag([
                        'title'   => '失败',
                        'message' => '文件中没有可核销的兑换码！',
                    ]),
                    'import_result' => [
                        'tag'           => $batch_info->batch_tag,
                        'import_file'   => $batch_info->import_file,
                        'total_num'     => $batch_info->total_num,
                        'invalid_num'   => $batch_info->invalid_num,
                        'repeat_num'    => $batch_info->repeat_num,
                        'forbidden_num' => $batch_info->forbidden_num,
                    ]
                ];
                return redirect('admin/exch-hx/import-result')->withInput()->with($with_data);
            }

            $codes_in_table = $details->pluck('code')->toArray();  //库里存在的码列表。

            if ($details->count() != count($dealed)) {
                //找到兑换码表不存在的那些兑换码并记录
                $invilid = array_filter($dealed, function ($item) use ($codes_in_table) {
                    if (!in_array($item, $codes_in_table)) {
                        return true;
                    }
                    return false;
                });

                $this->dealInvalidCodes($invilid, $url, $name, $user_activity_rights);

                $batch_info->invalid_num = count($invilid);

                unset($invilid);
            }

            //与文件顺序保持一致
            $valid_codes = array_filter($dealed, function ($item) use ($codes_in_table) {
                if (in_array($item, $codes_in_table)) {
                    return true;
                }
                return false;
            });
            unset($dealed);
            if (count($valid_codes) == count($codes_in_table)) {
                $codes_in_table = $valid_codes;
            }
            unset($valid_codes);

            $batch_info->forbidden_num = $details->count();

            $now     = date('Y:m:d H:i:s');
            $user_id = Admin::user()->id;

            DB::beginTransaction();

            try {
                if (!$batch_info->save()) {
                    $this->redis_utils->decrement($this->limit_key);//系统错误，再还原当前用户核销次数
                    $content->withError('提示', '导入记录保存失败！');
                    return redirect('admin/exch-hx/import')->withInput();
                }

                $insert_data = [];
                $cost_price  = DB::table('exchange_forbidden_cost_prices')->first();
                foreach ($details as $d) {
                    $insert_data[] = [
                        'batch_num'                   => 0,
                        'exchange_forbidden_batch_id' => $batch_info->id,
                        'activity_id'                 => $d->activity_id,
                        'code'                        => $d->code,
                        'exchange_batch_id'           => $d->exchange_batch_id,
                        'exchange_detail_id'          => $d->id,
                        'forbidden_status'            => ExchangeDetailForbidden::FORBIDDEN_YES,
                        'remark'                      => '',
                        'created_by'                  => $user_id,
                        'created_at'                  => $now,
                        // 增加成本价和税率
                        'cost_price'                  => $cost_price->cost_price ?? 0,
                        'cost_tax_rate'               => $cost_price->cost_tax_rate ?? 0,
                    ];
                }

                unset($details);

                $affected_rows = DB::table('exchange_details')
                    ->whereIn('code', $codes_in_table)
                    ->where('status', ExchangeDetail::STATUS_NO_EXCHANGE)
                    ->where('enable', ExchangeDetail::ENABLE_YES)
                    ->update(['enable' => ExchangeDetail::ENABLE_HEXIAO, 'updated_at' => $now]);

                if ($affected_rows == count($insert_data)) {
                    DB::table('exchange_detail_forbiddens')->insert($insert_data);
                    DB::commit();

                    try {
                        $f_i = fopen(dirname($url) . DIRECTORY_SEPARATOR . $name . '_imported.txt', 'a');
                        fwrite($f_i, implode("\r\n", $codes_in_table) . "\r\n");
                        fclose($f_i);
                    } catch (\Exception $e) {
                    }
                } else {
                    DB::rollBack();
                    $this->redis_utils->decrement($this->limit_key);//系统错误，再还原当前用户核销次数
                    $this->logger->error($name . '核销数量与插入的记录数不一致，请重试。' . json_encode(['insert_data' => $insert_data, 'update_affected_rows' => $affected_rows]));
                    $content->withError('错误提示', '兑换码核销数量，与写入记录数不一致，请重试！');
                    return redirect('admin/exch-hx/import')->withInput();
                }
            } catch (\Exception $ex) {
                DB::rollBack();
                $this->redis_utils->decrement($this->limit_key);//系统错误，再还原当前用户核销次数
                throw $ex;
            }

            $this->logger->info($name . '兑换码核销成功' . count($insert_data) . '个。');

            $with_data = [
                'success'       => new \Illuminate\Support\MessageBag([
                    'title'   => '成功',
                    'message' => '核销兑换码 ' . count($insert_data) . ' 个。批次标识：' . $name,
                ]),
                'import_result' => [
                    'tag'           => $batch_info->batch_tag,
                    'import_file'   => $batch_info->import_file,
                    'total_num'     => $batch_info->total_num,
                    'invalid_num'   => $batch_info->invalid_num,
                    'repeat_num'    => $batch_info->repeat_num,
                    'forbidden_num' => $batch_info->forbidden_num,
                ]
            ];
            return redirect('admin/exch-hx/import-result')->withInput()->with($with_data);

        } catch (\Exception $exception) {
            Log::error($exception->getMessage(), $exception->getTrace());
            $content->withError('错误提示', $exception->getMessage());
            $this->logger->error('禁用兑换码失败。' . $exception->getMessage());
        }
        return redirect('admin/exch-hx/import')->withInput();
    }

    public function import_result(Content $content)
    {
        $content
            ->header('导入结果')
            ->description('&nbsp;')
            ->body(view('admin.hx_succ')->with([
                'data' => session('import_result'),
            ]));
        session()->reflash();
        return $content;
    }

    public function download(Request $request)
    {
        $tag  = $request->get('tag');
        $type = $request->get('type');
        $path = session('import_result.import_file');
        session()->reflash();
        if (empty($path)) {
            $batch = ExchangeForbiddenBatch::where('batch_tag', $tag)->first();
            $path  = $batch->import_file ?? '';
        }
        $file_path = str_replace($tag, $tag . '_' . $type, $path);

        return response()->download(storage_path($file_path));
    }

    //预处理限制相关的key及次数
    private function preLimitSet()
    {
        $this->limit_key         = 'exch_hx_' . Admin::user()->id;         //每天导入次数限制key
        $this->invalid_limit_key = 'exch_hx_invalid_' . Admin::user()->id; //全无效码次数限制key

//        if (!admin_is_third_user()) {
//            //内部用户限制可以放宽泛点
//            $this->max_limit = 20;
//            $this->max_count = 1000;
//        }
    }

    //预处理兑换码，去掉前缀网址，去重。返回处理过的不重复的兑换码及文件中重复码数量。重复码写入文件*_repeat.txt
    private function preDealCode($codes, $url, $name)
    {
        $repeat_num = 0;

        // 获取去掉重复数据的数组
        $unique_arr = array_unique($codes);
        // 获取重复数据的数组
        $repeat_arr = array_diff_assoc($codes, $unique_arr);
        if (!empty($repeat_arr)) {
            $f_r = fopen(dirname($url) . DIRECTORY_SEPARATOR . $name . '_repeat.txt', 'a');
            fwrite($f_r, implode("\r\n", $repeat_arr) . "\r\n");
            fclose($f_r);
            $repeat_num = count($repeat_arr);
            $message    = "文件中有重复兑换码：" . implode(", ", $repeat_arr);
            $this->logger->info($name . $message);
        }

        unset($repeat_arr, $codes);

        $dealed = [];
        foreach ($unique_arr as $code_maybe) {
            $code = trim($code_maybe);
            if (strpos($code, 'http') === 0) {
                $tmp = convertUrlQuery(parse_url($code)['query']);
                if (!empty($tmp['s'])) {
                    $code = trim($tmp['s']);
                }
            }
            $dealed[] = $code;
        }

        unset($unique_arr);

        return [$dealed, $repeat_num];
    }

    //处理无效兑换码，写入*_invalid.txt文件，并写入已使用/已核销原因及时间。
    private function dealInvalidCodes($invalid_codes, $url, $name, $user_activity_rights)
    {
        if (!empty($invalid_codes)) {
            $message = "以下兑换码无效（不存在/已兑换/已核销）：" . implode(',', $invalid_codes);
            $this->logger->info($name . $message);

            //已使用
            $details_used = DB::table('exchange_details')
                ->whereIn('code', $invalid_codes)
                ->where('status', '>', ExchangeDetail::STATUS_NO_EXCHANGE)
                ->select(['id', 'code', 'status', 'enable', 'last_exchange_time']);
            if (admin_is_third_user()) {
                $details_used->whereIn('activity_id', $user_activity_rights);
            }
            $details_used = $details_used->pluck('last_exchange_time', 'code');

            //已核销
            $details_disabled = DB::table('exchange_detail_forbiddens')
                ->whereIn('code', $invalid_codes)
                ->select(['exchange_detail_id', 'code', 'created_at']);
            if (admin_is_third_user()) {
                $details_disabled->whereIn('activity_id', $user_activity_rights);
            }
            $details_disabled = $details_disabled->pluck('created_at', 'code');

            foreach ($invalid_codes as $key => $val) {
                if ($details_used->offsetExists($val)) {
                    $invalid_codes[$key] = sprintf("%s\t%s\t%s", $val, '已使用', $details_used->get($val, ''));
                } elseif ($details_disabled->offsetExists($val)) {
                    $invalid_codes[$key] = sprintf("%s\t%s\t%s", $val, '已核销', $details_disabled->get($val, ''));
                } else {
                    $invalid_codes[$key] = sprintf("%s\t%s", $val, '无效');
                }
            }

            $f_i = fopen(dirname($url) . DIRECTORY_SEPARATOR . $name . '_invalid.txt', 'a');
            fwrite($f_i, implode("\r\n", $invalid_codes) . "\r\n");
            fclose($f_i);
        }
    }


    //结算处理页面
    public function settlement(Content $content)
    {
        $content->title('结算处理');
        $form = new WidgetForm();
        $form->method('post');
        $form->action(url('admin/exch-hx/do-settlement'));
        $form->html('<div>上传文件格式为 txt 文本文件，utf-8 编码；<br/>每行一个结算批次号。<br/>文件第一行为标题，写"batch"即可，从<span style="color:red;">第二行</span>开始读取批次号。</div>');
        $form->file('hx_file', '结算批次')->removable()->options([
            'showPreview'           => false,
            'allowedFileExtensions' => ['txt'],
        ])->required();

        $form->disableReset();

        $form_id = $form->getFormId();
        $script  = <<<EOT
$('form#{$form_id}').off('submit').on('submit', function (e) {
    event.preventDefault();
    var form = this;
    $.admin.swal({
        title: "确定要进行结算吗？",
        type: "question",
        showCancelButton: true,
        confirmButtonText: "确认",
        showLoaderOnConfirm: true,
        cancelButtonText: "取消"
    }).then(function(result) {
      if (result.value === true) {
        form.submit();
      } else if (result.dismiss === 'cancel') {
        event.preventDefault();
        return false;
      }
    });
    return false;
});
EOT;
        Admin::script($script);

        $content->body(new Widgets\Box('', $form));
        return $content;
    }

    //结算处理
    public function do_settlement(Content $content, Request $request)
    {
        if (admin_is_third_user()) {
            throw new MyException('无结算权限!');
        }
        $request->validate(
            [
                'hx_file' => 'required|mimes:txt',
            ], [
                'hx_file.required' => "导入文件不能为空",
                'hx_file.mimes'    => "支持的文件格式：txt",
            ]
        );

        try {
            //处理文件
            $up_file = $request->file('hx_file');
            $ext     = $up_file->getClientOriginalExtension();
            //文件保存到storage/app/upload/exchange
            $name      = 'settlement' . sprintf('%d', Admin::user()->id) . date('YmdHis') . mt_rand(10, 99); //不包含扩展名
            $storePath = 'upload' . DIRECTORY_SEPARATOR . 'exchange_settlement' . DIRECTORY_SEPARATOR . date('Ym');
            if (!is_dir(storage_path('app' . DIRECTORY_SEPARATOR . $storePath))) {
                mkdir(storage_path('app' . DIRECTORY_SEPARATOR . $storePath), 0755, true);
            }
            $path = 'app' . DIRECTORY_SEPARATOR . $up_file->storeAs($storePath, $name . '.' . $ext);
            $url  = storage_path($path);//获取文件url

            $this->logger->info('结算处理，' . $url . '。原文件名：' . $up_file->getClientOriginalName());

            $codes = getFileContent($url, 2);
            $codes = array_filter($codes);

            if (empty($codes)) {
                $content->withError('提示', "批次号列表为空");
                return redirect('admin/exch-hx/settlement')->withInput();
            }

            foreach ($codes as $key => $code) {
                $codes[$key] = trim($code);
            }

            //查询有效的批次
            $batches = DB::table('exchange_forbidden_batches')
                ->whereIn('batch_tag', $codes)
                ->where('settlement_status', ExchangeForbiddenBatch::SETTLEMENT_STATUS_NO)
                ->select(['id', 'batch_tag'])
                ->get();

            if ($batches->count() == 0) {
                $message = "文件中没有可结算的批次号！";
                $this->logger->info($name . $message);
                $with_data = [
                    'error' => new \Illuminate\Support\MessageBag([
                        'title'   => '失败',
                        'message' => '文件中没有可结算的批次号！',
                    ]),
                ];
                return redirect('admin/exch-hx/settlement')->withInput()->with($with_data);
            }

            $batches_in_table = $batches->pluck('batch_tag')->toArray();  //库里存在的批次号。

            $affected_rows = DB::table('exchange_forbidden_batches')
                ->whereIn('id', $batches->pluck('id')->toArray())
                ->where('settlement_status', ExchangeForbiddenBatch::SETTLEMENT_STATUS_NO)
                ->update([
                    'settlement_status' => ExchangeForbiddenBatch::SETTLEMENT_STATUS_YES,
                    'settlement_date'   => Carbon::now()->toDateTimeString()
                ]);

            $this->logger->info($name . ' 结算' . $affected_rows . '个批次。批次号：' . implode(',', $batches_in_table));

            $with_data = [
                'success' => new \Illuminate\Support\MessageBag([
                    'title'   => '成功',
                    'message' => '结算 ' . $affected_rows . ' 个。批次列表：' . implode(',', $batches_in_table),
                ]),
            ];
            return redirect('admin/exch-hx/')->withInput()->with($with_data);

        } catch (\Exception $exception) {
            Log::error($exception->getMessage(), $exception->getTrace());
            $content->withError('错误提示', $exception->getMessage());
            $this->logger->error('结算失败。' . $exception->getMessage());
        }
        return redirect('admin/exch-hx/settlement')->withInput();
    }
}
