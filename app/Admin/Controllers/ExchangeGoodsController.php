<?php

namespace App\Admin\Controllers;

use App\Admin\SysCode\SysCode;
use App\Models\Activity;
use App\Models\ExchangeBatch;
use App\Models\ExchangeGoods;
use App\Models\ExchangeGroup;
use Encore\Admin\Auth\Database\Administrator;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Grid\Filter;
use Encore\Admin\Show;
use Illuminate\Support\Facades\DB;

class ExchangeGoodsController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = '批次对应商品管理';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new ExchangeGoods());

        $grid->column('id', __('Id'));
        $grid->column('exchange_batch.activity_id', __('活动'))->display(function ($val) {
            return Activity::find($val)->activity_name ?? '';
        });
        $grid->column('exchange_batch.package_name', __('批次名称'));
        $grid->column('exchange_group_id', __('分组id'))->hide();
        $grid->column('exchange_group.group_name', __('分组名称'));
        $grid->column('goods_id', __('商品id'))->hide();
        $grid->column('goods.goods_name', __('商品名称'));
        $grid->column('sort', __('排序'));
        $grid->column('status', __('状态'))->using(SysCode::$common_status);
        $grid->column('created_by', __('创建者'))->display(function ($created_by) {
            return Administrator::where('id', $created_by)->value('name');
        });
        $grid->column('updated_by', __('更新者'))->display(function ($updated_by) {
            return Administrator::where('id', $updated_by)->value('name');

        })->hide();
        $grid->column('created_at', __('创建时间'))->hide();
        $grid->column('updated_at', __('更新时间'));

        $grid->actions(function ($actions) {
            $actions->disableDelete(false);
        });

        $grid->filter(function (Filter $filter) {
            $filter->disableIdFilter();
            $filter->column(1 / 2, function (Filter $filter) {
                $filter->equal('exchange_batch_id', '批次')->select(
                    ExchangeBatch::all([
                        'id', 'package_name'
                    ])->sortByDesc('id')->pluck('package_name', 'id')
                );
            });
            $filter->column(1 / 2, function (Filter $filter) {
                $filter->equal('exchange_batch.activity_id', '活动')->select(
                    Activity::all([
                        'id', 'activity_name'
                    ])->sortByDesc('id')->pluck('activity_name', 'id')
                );
            });
        });

        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        $show = new Show(ExchangeGoods::findOrFail($id));

        $show->field('id', __('Id'));
        $show->field('exchange_batch.activity_id', __('活动'))->as(function ($val) {
            return Activity::find($val)->activity_name ?? '';
        });
        $show->field('exchange_batch.package_name', __('批次名称'));
        $show->field('exchange_group.group_name', __('分组名称'));
        $show->field('goods.goods_name', __('商品名称'));
        $show->field('sort', __('排序'));
        $show->field('status', __('状态'))->using(SysCode::$common_status);
        $show->field('created_by', __('创建者'))->as(function ($created_by) {
            return Administrator::where('id', $created_by)->value('name');
        });
        $show->field('created_at', __('创建时间'));
        $show->field('updated_by', __('更新者'))->as(function ($created_by) {
            return Administrator::where('id', $created_by)->value('name');
        });
        $show->field('updated_at', __('更新时间'));

        return $show;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $form = new Form(new ExchangeGoods());

        $form->select('activity_id', __('活动'))->options(function () {
            return Activity::where(['status' => 1])
                ->orderBy('id', 'desc')
                ->pluck('activity_name', 'id');
        })->load('exchange_batch_id', route('exb-get-batches'));

        $form->select('exchange_batch_id', __('批次'))->required()
            ->load('goods_id', route('exb-get-goods'));

        $form->select('goods_id', __('商品'))->required();

//        $form->select('exchange_group_id', __('分组'))->options(function () {
//            return ExchangeGroup::where(['status' => 1])
//                ->orderBy('id', 'desc')
//                ->pluck('group_name', 'id');
//        })->help('礼包类兑换券需选择分组');

        $form->editing(function (Form $form) {
            $model              = $form->model();
            $model->activity_id = $model->exchange_batch->activity_id;

            $form->builder()->field('exchange_batch_id')->options(function () use ($model) {
                return ExchangeBatch::where(['activity_id' => $model->exchange_batch->activity_id])
                    ->orderBy('id', 'desc')
                    ->pluck('package_name', 'id');
            });

            $form->builder()->field('goods_id')->options(function () use ($model) {
                $goods = DB::table('activity_prizes')
                    ->leftJoin('goods', 'activity_prizes.goods_id', '=', 'goods.id')
                    ->where('activity_prizes.activity_id', $model->exchange_batch->activity_id)
                    ->orderBy('goods.id', 'desc')
                    ->pluck('goods.goods_name', 'goods.id');
                return $goods;
            });
        });

        $form->number('sort', __('排序'))->default(100)->help('数字越小，排列越靠前。');
        $form->switch('status', __('状态'))->default(1);

        $form->submitted(function (Form $form) {
            $model = $form->model();

            if (empty(\request('exchange_group_id'))) {
                $form->exchange_group_id = 0;
            }

            $form->ignore('activity_id');

            if ($form->isEditing()) {
                $form->model()->updated_by = Admin::user()->id;
            } else {
                $form->model()->created_by = Admin::user()->id;
            }
        });

        return $form;
    }
}
