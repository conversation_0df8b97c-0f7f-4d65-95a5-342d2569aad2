<?php

namespace App\Admin\Controllers;

use App\Models\Activity;
use App\Models\ExchangeBatch;
use App\Models\ExchangeGroup;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Show;

class ExchangeGroupController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = '礼包卡分组列表';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid() {
        $grid = new Grid(new ExchangeGroup());

        $grid->column('id', __('Id'));
        $grid->column('exchange_batch_id', __('批次id'))->hide();
        $grid->column('exchange_batch.package_name', __('批次名称'));
        $grid->column('group_name', __('分组名称'));
        $grid->column('sort', __('排序'));
        $grid->column('status', __('状态'))->using(ExchangeGroup::$status);
        $grid->column('created_at', __('创建时间'));
        $grid->column('updated_at', __('更新时间'));

        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id) {
        $show = new Show(ExchangeGroup::findOrFail($id));

        $show->field('id', __('Id'));
        $show->field('exchange_batch_id', __('Exchange batch id'));
        $show->field('group_name', __('Group name'));
        $show->field('sort', __('Sort'));
        $show->field('status', __('Status'));
        $show->field('created_by', __('Created by'));
        $show->field('updated_by', __('Updated by'));
        $show->field('created_at', __('Created at'));
        $show->field('updated_at', __('Updated at'));

        return $show;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form() {
        $form = new Form(new ExchangeGroup());

        $form->select('exchange_batch_id', __('批次'))->options(function () {
            return ExchangeBatch::where(['status' => 1, 'type' => ExchangeBatch::TYPE_LIBAOKA])
                ->orderBy('id', 'desc')
                ->pluck('package_name', 'id');
        })->required();
        $form->text('group_name', __('分组名称'));
        $form->number('sort', __('排序'))->default(100);
        $form->switch('status', __('状态'))->default(1);
//        $form->number('created_by', __('Created by'));
//        $form->number('updated_by', __('Updated by'));

        return $form;
    }
}
