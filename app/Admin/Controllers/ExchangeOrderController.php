<?php

namespace App\Admin\Controllers;

use App\Admin\Actions\Exchange\RollBack;
use App\Http\Controllers\Api\SysCode\SysCode;
use App\Models\Activity;
use App\Models\ExchangeBatch;
use App\Models\ExchangeDetail;
use App\Models\Goods;
use App\Models\Order;
use App\Models\ViewExchangeOrder;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Grid;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Grid\Filter;
use Encore\Admin\Show;

class ExchangeOrderController extends AdminController
{
    protected $title = '兑换码订单';

    protected function grid()
    {
        $grid = new Grid(new ViewExchangeOrder());
        $grid->disableDefineEmptyPage();
        $grid->disableCreateButton();
        $grid->disableExport(true);
        $grid->expandFilter();
        $grid->actions(function ($actions) {
            $actions->disableDelete();
            $actions->disableEdit();
            //直充类的才需要改充值账号
            if ($actions->row->status == SysCode::ORDER_STATUS_4 && in_array($actions->row->goods_type, [SysCode::GOODS_TYPE_2, SysCode::GOODS_TYPE_3])) {
                $actions->add(new RollBack());
            }
        });

        $request    = request();
        $req_params = $request->all();
        unset($req_params['_pjax']);
        //if (check_value_all_empty($req_params)) {
        if (empty($req_params['code']) && empty($req_params['charge_account']) && empty($req_params['mobile']) && empty($req_params['user_mobile'])
            && empty($req_params['consignee_phone']) && empty($req_params['order_no'])
            && empty($req_params['created_at']['start']) && empty($req_params['created_at']['end'])
        ) {
            $grid->model()->oneWeek();
        }

//        $grid->column('id', __('Id'))->sortable();
        $grid->column('activity_name', __('活动名称'));
        $grid->column('order_no', __('订单编号'));
        $grid->column('status', __('订单状态'))
            ->display(function ($status) {
                if ($this->goods->goods_type == 3 && $status == 2) {
                    return '充值成功';
                } elseif ($this->goods->goods_type == 1 && $status == 2) {
                    return '已发货';
                } else {
                    return Order::$status[$status];
                }
            })->dot([4 => 'danger', 2 => 'success', 1 => 'warning', 3 => 'warning', 0 => 'default', -1 => 'default']);
//        $grid->column('goods_no', __('商品编号'));
        $grid->column('goods_name', '商品名称');
//        $grid->column('num', '兑换码序号');
        $grid->column('code', '兑换码')->display(function ($code) {
            return str_hide_middle($code);
        });
//        $grid->column('exchange_detail_status', '兑换码状态')->using(ExchangeDetail::$status);
//        $grid->column('exchange_created_at', '制码时间');
        $grid->column('activity_user_id', __('活动用户ID'))->hide();
        $grid->column('mobile', __('登录手机号'));
        $grid->column('user_mobile', __('用户手机号'));
        $grid->column('charge_account', __('充值账号'));
        $grid->column('order_remark', __('订单备注'));
        $grid->column('consignee_name', __('收货人姓名'))->hide();
        $grid->column('consignee_phone', __('收货人电话'))->hide();
        $grid->column('consignee_address', __('收货人地址'))->hide();
        $grid->column('service_date', __('服务日期'))->hide();
        $grid->column('service_time', __('服务时间'))->hide();
        $grid->column('order_time', __('下单时间'));
        $grid->column('deliver_complete_time', __('订单完成时间'));

//查询过滤
        $grid->filter(function ($filter) {
            $filter->disableIdFilter();
            $filter->column(1 / 2, function (Filter $filter) {
//                $filter->equal('activity_id', '活动名称')->select(Activity::all([
//                    'id', 'activity_name'
//                ])->sortByDesc('id')->pluck('activity_name', 'id'));
//                $filter->equal('exchange_batch_id', '批次')->select(ExchangeBatch::all([
//                    'id', 'package_name'
//                ])->sortByDesc('id')->pluck('package_name', 'id'));
//                $filter->equal('num', '序列号');

                $filter->equal('code', '兑换码');
                $filter->equal('charge_account', '充值账号');
                $filter->equal('user_mobile', '用户手机号');
                $filter->equal('mobile', '登录手机号');
                $filter->equal('consignee_phone', '收货人电话');
            });
            $filter->column(1 / 2, function ($filter) {
                $filter->equal('order_no', '订单号');
//                $filter->equal('goods_id', '商品')->select(Goods::orderBy('goods_name', 'asc')->get(['id', 'goods_name'])->pluck('goods_name', 'id'));
                $filter->equal('status', '订单状态')->select(Order::$status);
//                $filter->between('deliver_complete_time', '订单完成时间')->datetime();
                $filter->between('created_at', '下单时间')->datetime();
//                $filter->between('exchange_created_at', '制码时间')->datetime();
                $filter->equal('activity_id', '活动名称')->select(Activity::all([
                    'id', 'activity_name'
                ])->sortByDesc('id')->pluck('activity_name', 'id'));

            });
        });

        $grid->tools(function (Grid\Tools $tools) {
            $tools->append('<div style="color:blue;float: right;margin-right: 20px;">&nbsp;（若无除<span style="color:red;">订单状态、活动名称</span>之外的筛选条件，则只显示最近一周的记录）&nbsp;</div>');
        });

//        $grid->header(function ($query) {
////            return new Box('', '<div style="color:red;float: left;margin-right: 20px;">&nbsp;（若无筛选条件，则只显示最近一周的记录）&nbsp;</div>');
//            return '<div style="color:blue;float: left;margin-right: 20px;">&nbsp;（若无筛选条件，则只显示最近一周的记录）&nbsp;</div>';
//        });

        //解决Grid列表中行菜单Dropdown Menu在表格Responsive table内会被外层遮挡住的问题
        Admin::script(<<<EOF
$('.table-responsive').on('show.bs.dropdown', function () {
     $('.table-responsive').css( "overflow-y", "auto" );
});

$('.table-responsive').on('hide.bs.dropdown', function () {
     $('.table-responsive').css( "overflow-y", "hidden" );
});
EOF
        );

        return $grid;
    }

    protected function detail($id)
    {
        $show = new Show(ViewExchangeOrder::findOrFail($id));
        $show->field('id', __('Id'));
        $show->field('activity_name', __('活动名称'));
        $show->field('order_no', __('订单编号'));
        $show->field('status', __('订单状态'))->using(Order::$status);
        $show->field('goods_id', __('商品ID'));
        $show->field('goods_no', __('商品编号'));
        $show->field('goods_name', __('商品名称'));
        $show->field('goods_attr', __('商品属性'));
        $show->field('ecp_target', __('渠道商品大类编码'));
        $show->field('ecp_pcode', __('渠道商品编码'));
        $show->field('num', __('兑换码序号'));
        $show->field('code', __('兑换码'));
        $show->field('exchange_detail_status', __('兑换码状态'))->using(ExchangeDetail::$status);
        $show->field('exchange_created_at', __('制码时间'));
        $show->field('user_mobile', __('用户手机号'));
        $show->field('charge_account', __('充值账号'));
        $show->field('consignee_name', __('收货人姓名'));
        $show->field('consignee_phone', __('收货人电话'));
        $show->field('consignee_address', __('收货人地址'));
        $show->field('service_date', __('预约日期'));
        $show->field('service_time', __('预约时间'));
        $show->field('created_at', __('创建时间'));
        $show->field('deliver_complete_time', __('订单完成时间'));

        return $show;
    }

}
