<?php

namespace App\Admin\Controllers;

use App\Models\Activity;
use App\Models\ExchangeWhitelist;
use Carbon\Carbon;
use Encore\Admin\Auth\Database\Administrator;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Grid\Displayers\Actions;
use Encore\Admin\Show;
use Illuminate\Support\Facades\DB;

class ExchangeWhitelistController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = '兑换白名单管理';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new ExchangeWhitelist());

        $grid->column('id', __('Id'));
        $grid->column('activity.activity_name', __('活动名称'));
        $grid->column('user_mobile', __('手机号'));
        $grid->column('charge_account', __('充值账号'));
        $grid->column('expired_at', __('过期日期'))->display(function ($val) {
            if (Carbon::parse($val . ' 23:59:59')->isBefore(Carbon::now())) {
                return "<span style='color:red'>$val</span>";
            } else {
                return $val;
            }
        });
        $grid->column('remark', __('备注'));
        $grid->column('created_at', __('创建时间'));
        $grid->column('updated_at', __('修改时间'))->hide();
        $grid->column('created_by', __('创建人'))->display(function ($created_by) {
            return DB::table('admin_users')->where('id', $created_by)->value('name');
        })->hide();
        $grid->column('updated_by', __('更新人'))->display(function ($updated_by) {
            return DB::table('admin_users')->where('id', $updated_by)->value('name');
        })->hide();

        $grid->actions(function (Actions $actions) {
            $actions->disableView();
            $actions->disableDelete(false);
        });

        $grid->filter(function ($filter) {
            // 去掉默认的id过滤器
            $filter->disableIdFilter();
            // 在这里添加字段过滤器
            $filter->equal('user_mobile', '手机号');
            $filter->equal('charge_account', '充值账号');
        });

        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        $show = new Show(ExchangeWhitelist::findOrFail($id));
        $show->field('id', __('Id'));
        $show->field('activity.activity_name', __('活动名称'));
        $show->field('user_mobile', __('手机号'));
        $show->field('charge_account', __('充值账号'));
        $show->field('expired_at', __('过期日期'));
        $show->field('remark', __('备注'));
        $show->field('created_by', __('创建者'))->as(function ($created_by) {
            return Administrator::where('id', $created_by)->value('name');
        });
        $show->field('created_at', __('创建时间'));
        $show->field('updated_by', __('更新者'))->as(function ($created_by) {
            return Administrator::where('id', $created_by)->value('name');
        });
        $show->field('updated_at', __('更新时间'));

        return $show;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $form = new Form(new ExchangeWhitelist());

        if ($form->isCreating()) {
            $form->select('activity_id', __('活动'))->options(function () {
                return Activity::where(['status' => 1])->where('activity_type', 'TianMao')->orderBy('id', 'desc')->pluck('activity_name', 'id');
            })->required();
        } else {
            $form->display('activity_id', __('活动'))->with(function ($val) {
                return Activity::find($val)->activity_name ?? $val;
            });
        }

        $form->text('user_mobile', __('手机号'))->rules('max:15');
        $form->text('charge_account', __('充值账号'))->rules('max:64');
        $form->date('expired_at', __('过期日期'))->help('在该日期之后白名单失效。非必须。');
        $form->textarea('remark', __('备注'))->rules('max:100');

        $form->submitted(function (Form $form) {
            $model          = $form->model();
            $request        = request();
            $user_mobile    = $request->get('user_mobile');
            $charge_account = $request->get('charge_account');
            if (empty($user_mobile) && empty($charge_account)) {
                admin_error("参数错误", "手机号和充值账号不能同时为空！");
                return back()->withInput();
            }
            if ($form->isEditing()) {
                $model->updated_by = Admin::user()->id;
            } else {
                $model->created_by = Admin::user()->id;

                if($user_mobile){
                    $wl = ExchangeWhitelist::where('activity_id', $request->get('activity_id'))
                        ->where('user_mobile', $user_mobile)
                        ->first();
                    if($wl){
                        admin_error("参数错误", "手机号已在白名单！");
                        return back()->withInput();
                    }
                }

                if($charge_account){
                    $wl = ExchangeWhitelist::where('activity_id', $request->get('activity_id'))
                        ->where('charge_account', $charge_account)
                        ->first();
                    if($wl){
                        admin_error("参数错误", "充值账号已在白名单！");
                        return back()->withInput();
                    }
                }
            }
        });

        return $form;
    }
}
