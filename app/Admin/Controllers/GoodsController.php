<?php

namespace App\Admin\Controllers;

use App\Libraries\Enums;
use App\Models\Goods;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Controllers\AdminController;
use Illuminate\Support\Facades\DB;
use Encore\Admin\Facades\Admin;

class GoodsController extends AdminController
{

    protected $title = '商品';

    protected function grid()
    {
        $grid = new Grid(new Goods());
        $grid->model()->orderByDesc('id');

        $grid->actions(function ($actions) {
            $actions->disableDelete(false);
            $actions->disableView();
        });

        $input_patterns = DB::table('input_patterns')
            ->pluck(DB::raw("concat(name,'[',form_name,']')"), 'id')
            ->toArray();
        $grid->column('id', __('Id'))->hide();
        $grid->column('combin_type', __('组合类型'))->using(Goods::$combin_type);
        $grid->column('goods_type', __('商品类型'))->using(Goods::$goods_type);
        $grid->column('goods_no', __('商品编号'));
        $grid->column('goods_name', __('商品名称'));
        $grid->column('goods_attr', __('商品属性'));
        //$grid->column('goods_show_img', __('商品展示图'));
        $grid->column('ecp_target', __('Qcp大类'))->help('Qcp大类为空时，订单会提交到Qcp2平台。')->hide();
        $grid->column('ecp_pcode', __('Qcp商品编号'));
        $grid->column('is_show_card_no', __('是否显示卡号'))->using(['0' => '隐藏', '1' => '显示'])->hide();
        $grid->column('pre_verify_type', __('充值验证类型'))->using(Enums::$pre_verify_type)->hide();
        $grid->column('input_pattern_ids', __('表单格式校验'))->display(function ($value) use ($input_patterns) {
            $show = [];
            foreach ($value as $item) {
                if (array_key_exists($item, $input_patterns)) {
                    $show[] = $input_patterns[$item];
                }
            }
            return implode('，', $show);
        })->hide();
        $grid->column('stocks', __('库存'))->hide();
        $grid->column('goods_price', __('商品价格'));
        $grid->column('goods_tax_rate', __('商品价格税率'))->hide();
        $grid->column('cost_price', __('成本价'));
        $grid->column('cost_tax_rate', __('成本价税率'))->hide();
        $grid->column('advance_days', __('预约提前天数'))->hide();
        $grid->column('service_time', __('可预约时间'))->hide();
        $grid->column('status', __('状态'))->using(Goods::$status)->dot([2 => 'danger', 1 => 'success']);
        $grid->column('created_at', __('创建时间'));
//        $grid->column('updated_at', __('修改时间'))->hide();
        $grid->column('created_by', __('创建人'))->display(function ($created_by) {
            return DB::table('admin_users')->where('id', $created_by)->value('name');
        })->hide();
//        $grid->column('updated_by', __('更新人'))->display(function ($updated_by) {
//            return DB::table('admin_users')->where('id', $updated_by)->value('name');
//        })->hide();

        //查询过滤
        $grid->filter(function ($filter) {

            $filter->equal('goods_no', '商品编码');
            $filter->like('goods_name', '商品名称');
            $filter->equal('goods_type', '商品类型')->select(Goods::$goods_type);
            $filter->equal('status', '状态')->select(Goods::$status);
            $filter->between('created_at', '创建时间')->datetime();

        });

        return $grid;
    }

    protected function form()
    {
        $is_show_card_no = [
            'on'  => ['value' => 1, 'text' => '显示', 'color' => 'success'],
            'off' => ['value' => 0, 'text' => '隐藏', 'color' => 'danger'],
        ];

        $input_patterns = DB::table('input_patterns')->where('form_name', 'charge_account')
            ->pluck(DB::raw("concat(name,'[',form_name,']')"), 'id')
            ->toArray();

        $form = new Form(new Goods());
        $form->radio('combin_type', __('组合类型'))->options(Goods::$combin_type)->default(1)
            ->when('2', function (Form $form) {
                $form->hasMany('children', '子商品', function (Form\NestedForm $nestedForm) {
                    $nestedForm->select('child_goods_id', __('商品'))->options(function () {
                        $goods = DB::table('goods')
                            ->where('combin_type', '1')
                            ->where('status', '1')
                            ->orderBy('goods_name', 'desc')
                            ->pluck('goods.goods_name', 'goods.id');
                        return $goods;
                    });
                });
            });
        $form->radio('goods_type', __('商品类型'))->options(Goods::$goods_type)->default(1);
        $form->number('stocks', __('库存'))->min(1)->default(1);
        $form->decimal('goods_price', __('商品价格'))->default(0.0);
        $form->decimal('goods_tax_rate', __('商品价格税率'))->default(0.06);
        $form->decimal('cost_price', __('成本价'))->default(0.0);
        $form->decimal('cost_tax_rate', __('成本价税率'))->default(0.06);
        $form->text('goods_no', __('商品编号'));
        $form->text('goods_name', __('商品名称'));
        $form->text('goods_attr', __('商品属性'));
        $form->image('goods_show_img', __('商品展示图'))->removable();
        $form->text('ecp_target', __('Qcp大类'))->help('Qcp大类为空时，订单会提交到Qcp2平台。');
        $form->text('ecp_pcode', __('Qcp商品编号'));
        $form->switch('is_show_card_no', '是否显示卡号')->states($is_show_card_no)->default(1);
        $form->radio('pre_verify_type', __('充值验证类型'))->options(Enums::$pre_verify_type)->default(Enums::PRE_VERIFY_TYPE_NONE);
        $form->multipleSelect('input_pattern_ids', __('表单格式校验'))->options($input_patterns);
        $form->number('advance_days', __('预约提前天数'))->default(5);
        $form->text('service_time', __('可预约时间'))->default('08:00,13:00')->help('多个时间用英文逗号隔开。');
        $form->multipleImage('goods_detail.goods_imgs', __('商品图片'))->removable();
        $form->html('<span class="text-red">富文本图片上传限制单张图片大小在2M以内，只支持一张一张上传。</span>');
//        $form->ckeditor('goods_detail.goods_params', __('商品参数'));
//        $form->ckeditor('goods_detail.goods_instr', __('购物须知'));
//        $form->ckeditor('goods_detail.goods_desc', __('商品详情'));
        $form->editor('goods_detail.goods_params', __('商品参数'));
        $form->editor('goods_detail.goods_instr', __('购物须知'));
        $form->editor('goods_detail.goods_desc', __('商品详情'));
        $states = [
            'on'  => ['value' => 1, 'text' => '启用', 'color' => 'primary'],
            'off' => ['value' => 0, 'text' => '禁用', 'color' => 'default'],
        ];
        $form->switch('status', '状态')->states($states)->default(1);

        $form->hidden('created_by')->value(Admin::user()->id);
        $form->hidden('updated_by')->value(Admin::user()->id);

        return $form;
    }


}
