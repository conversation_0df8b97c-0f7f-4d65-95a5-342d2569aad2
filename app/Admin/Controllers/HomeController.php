<?php

namespace App\Admin\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Activity;
use App\Models\CebGhUser;
use App\Models\Goods;
use App\Models\Order;
use App\Models\OrderSub;
use App\Models\WhqCoupon;
use App\Models\WhqOrder;
use App\Models\WhqProduct;
use Encore\Admin\Controllers\Dashboard;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Layout\Column;
use Encore\Admin\Layout\Content;
use Encore\Admin\Layout\Row;
use Encore\Admin\Widgets;

class HomeController extends Controller
{
    public function index(Content $content)
    {
        return $content
            ->title('首页')
            ->description('')
            ->row(function (Row $row) {
//                $row->column(3, new Widgets\InfoBox('活动数', 'users', 'blue', '/admin/activity', Activity::count()));
//                $row->column(3, new Widgets\InfoBox('产品数', 'product-hunt', 'green', '/admin/goods',Goods::count()));
//                $row->column(3, new Widgets\InfoBox('订单数', 'list', 'aqua', '/admin/orders', Order::count()));
//                $row->column(3, new Widgets\InfoBox('子订单数', 'first-order', 'yellow', '/admin/sub-order', OrderSub::count()));
                if (Admin::user()->inRoles(['administrator', 'order_manager', 'definition_p', 'cebgh', 'cebgh_xm'])) {
                    $row->column(3, new Widgets\InfoBox('已兑换', 'users', 'blue', '/admin/cebgh?get_status=1', CebGhUser::where('get_status', 1)->count()));
                    $row->column(3, new Widgets\InfoBox('未兑换', 'users', 'green', '/admin/cebgh?get_status=0', CebGhUser::where('get_status', 0)->count()));
                }
            });
    }
}
