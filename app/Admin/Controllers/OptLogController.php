<?php

namespace App\Admin\Controllers;

use App\Models\OptLog;
use Encore\Admin\Grid;
use Encore\Admin\Controllers\AdminController;
use Illuminate\Support\Facades\DB;

class OptLogController extends AdminController
{

    protected $title = '操作日志';

    protected function grid() {
        $grid = new Grid(new OptLog());
        $grid->model()->orderByDesc('id');

        $grid->disableExport();
        $grid->disableCreateButton();
        $grid->disableRowSelector();
        $grid->disableBatchActions();
        $grid->disableActions();

        $grid->column('id', __('Id'))->sortable();

        $grid->column('user_id', __('User'))->display(function ($user_id) {
            return DB::table('admin_users')->where('id', $user_id)->value('name');
        });
        $grid->column('method', __('Method'));
        $grid->column('path', __('Path'));
        $grid->column('ip', __('IP'));
        $grid->column('opt_desc', __('操作描述'));
        $grid->column('file_name', __('操作文件'))->link(function(){
            $url = url('admin/download-optfile').'/'.$this->id;
            return $url;
        });
        $grid->column('created_at', __('操作时间'));

        //查询过滤
        $grid->filter(function ($filter) {
//            $filter->expand();
            // 去掉默认的id过滤器
            $filter->disableIdFilter();
            $userModel = config('admin.database.users_model');
            $filter->equal('user_id', '操作人')->select($userModel::all()->pluck('name', 'id'));
            $filter->like('opt_desc', '操作描述');
            $filter->between('created_at', '操作时间')->datetime();

        });

        return $grid;
    }

    public function download($id) {

        $file_path = DB::table('opt_logs')->where('id',$id)->value('file_path');
        $fileName = substr($file_path, strripos($file_path, '/')+1);
        header("Content-Type: application/force-download");
        header("Content-Disposition: attachment; filename=".$fileName);
        readfile(storage_path().$file_path);
    }
}