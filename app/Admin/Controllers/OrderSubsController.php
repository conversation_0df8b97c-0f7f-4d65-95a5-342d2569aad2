<?php

namespace App\Admin\Controllers;

use App\Admin\Actions\Orders\OrderChangeAccountAndReSubmit;
use App\Admin\Actions\Orders\OrderReSubmit;
use App\Admin\Support\LogOpt;
use App\Exceptions\MyException;
use App\Http\Controllers\Api\SysCode\SysCode;
use App\Models\Activity;
use App\Models\Goods;
use App\Models\OrderSub;
use Carbon\Carbon;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Grid;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Grid\Filter;
use Encore\Admin\Layout\Content;
use Encore\Admin\Show;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Encore\Admin\Widgets;
use Illuminate\Support\Facades\Log;

class OrderSubsController extends AdminController
{

    protected $title = '子订单';

    private $column_list = [
        'order_subs.order_no'          => '订单号',
        'order_subs.sub_order_no'      => '子订单号',
        'activities.activity_name'     => '活动名称',
        'order_subs.goods_no'          => '商品编号',
        'order_subs.goods_name'        => '商品名称',
        'order_subs.created_at'        => '订单时间',
        'order_subs.goods_attr'        => '商品属性',
        'order_subs.goods_price'       => '商品单价',
        'order_subs.goods_num'         => '商品数量',
        'order_subs.user_mobile'       => '用户手机号',
        'order_subs.charge_account'    => '充值账号',
        'order_subs.service_date'      => '服务日期',
        'order_subs.service_time'      => '服务时间',
        'order_subs.consignee_name'    => '收货人姓名',
        'order_subs.consignee_phone'   => '收货人电话',
        'order_subs.consignee_address' => '收货地址',
        'order_subs.logistics_company' => '物流公司名称',
        'order_subs.logistics_sn'      => '物流单号',
    ];


    protected function grid()
    {
        $grid = new Grid(new OrderSub());
        $grid->model()->orderByDesc('id');

        $grid->disableExport(false);
        $grid->disableCreateButton();
        $grid->actions(function ($actions) {
            $actions->disableDelete();
            $actions->disableEdit();
            //重提订单，直充和卡密的均需要重提订单。
            if ($actions->row->status == SysCode::ORDER_SUB_STATUS_4 && in_array($actions->row->goods_type, [SysCode::GOODS_TYPE_2, SysCode::GOODS_TYPE_3])) {
                $actions->add(new OrderReSubmit());
            }

            //直充类的才需要改充值账号
            if ($actions->row->status == SysCode::ORDER_SUB_STATUS_4 && in_array($actions->row->goods_type, [SysCode::GOODS_TYPE_3])) {
                $actions->add(new OrderChangeAccountAndReSubmit());
            }
        });

        $grid->column('id', __('Id'))->sortable();

        $grid->column('order_no', __('订单编号'));
        $grid->column('sub_order_no', __('子订单编号'));

//        $grid->column('goods_name', '商品名称')->expand(function ($model) {
//            $content     = [
//                'goods_id'   => $model->goods_id,
//                'goods_no'   => $model->goods_no,
//                'goods_type' => SysCode::$goods_type[$model->goods_type],
//                'goods_attr' => $model->goods_attr,
//                'goods_num'  => $model->goods_num,
//            ];
//            $contents [] = $content;
//            return new Widgets\Table(['商品ID', '商品编号', '商品类型', '商品属性', '商品数量'], $contents);
//        });
        $grid->column('goods_no', '商品编号');
        $grid->column('goods_name', '商品名称');

        $grid->column('user_mobile', __('用户手机号'));
        $grid->column('charge_account', __('充值账号'));

        $grid->column('order_info', '订单详情')->expand(function ($model) {

            if (in_array($model->goods_type, [SysCode::GOODS_TYPE_2, SysCode::GOODS_TYPE_3, SysCode::GOODS_TYPE_5])) {
                $content     = [
                    'ecp_target'      => $model->ecp_target,
                    'ecp_pcode'       => $model->ecp_pcode,
                    'activation_code' => $model->activation_code,
                    'sequence_no'     => $model->sequence_no,
                    'endtime'         => $model->endtime,
                    'req_order_no'    => $model->req_order_no,
                    'third_order_no'  => $model->third_order_no,
                    'order_req_count' => $model->order_req_count,
                    'order_req_time'  => $model->order_req_time,
                ];
                $contents [] = $content;
                return new Widgets\Table(['qcp大类', 'qcp商品编号', '激活码', '序号', '有效期', '请求订单号', '上游订单号', '请求次数', '请求时间'], $contents);

            } elseif ($model->goods_type == SysCode::GOODS_TYPE_1) {
                $content     = [
                    'consignee_name'    => $model->consignee_name,
                    'consignee_phone'   => $model->consignee_phone,
                    'consignee_address' => $model->consignee_address,
                    'logistics_sn'      => $model->logistics_sn,
                    'logistics_company' => $model->logistics_company,
                    'third_order_no'    => $model->third_order_no,
                ];
                $contents [] = $content;
                return new Widgets\Table(['收货人姓名', '收货人电话', '收货人地址', '物流单号', '物流公司', '上游订单号'], $contents);
            } elseif ($model->goods_type == SysCode::GOODS_TYPE_6) {
                $content     = [
                    'consignee_name'    => $model->consignee_name,
                    'consignee_phone'   => $model->consignee_phone,
                    'consignee_address' => $model->consignee_address,
                    'logistics_sn'      => $model->logistics_sn,
                    'logistics_company' => $model->logistics_company,
                    'service_date'      => $model->service_date,
                    'service_time'      => $model->service_time,
                    'third_order_no'    => $model->third_order_no,
                ];
                $contents [] = $content;
                return new Widgets\Table(['收货人姓名', '收货人电话', '收货人地址', '服务单号', '服务公司', '预约日期', '预约时间', '上游订单号'], $contents);
            } else {
                return new Widgets\Table([], []);
            }

        });

//        $grid->column('status', __('状态'))->using(OrderSub::$status)->dot([
//            4 => 'danger', 3 => 'success', 2 => 'default', 1 => 'warning'
//        ]);
        // 商品类型。0-组合商品，1-实物商品，2-虚拟商品卡密类，3-虚拟商品-异步直充类。4-虚拟_实物 5-短连接
        // 状态。 1-未处理 2-处理中，3-已发货，4-发货失败，5-失败重提
        $grid->column('status', __('订单状态'))->display(function ($status) {
            if ($this->goods_type == 3 && $status == 3) {
                return '充值成功';
            } elseif ($this->goods_type == 1 && $status == 3) {
                return '已发货';
            } else {
                return OrderSub::$status[$status];
            }
        })->dot([4 => 'danger', 3 => 'success', 2 => 'warning', 1 => 'warning', 5 => 'warning', '-1' => 'default']);
        $grid->column('third_order_no', __('上游订单编号'));
        $grid->column('order_req_time', __('订单请求时间'));
        $grid->column('sms_gateway_seq', __('短信网关seq'))->hide();
        $grid->column('sms_callback_time', __('短信回调时间'))->hide();
        $grid->column('created_at', __('创建时间'));
        $grid->column('deliver_complete_time', __('订单完成时间'));


        $grid->column('created_by', __('创建人'))->display(function ($created_by) {
            return DB::table('admin_users')->where('id', $created_by)->value('name');
        })->hide();
        $grid->column('updated_by', __('更新人'))->display(function ($updated_by) {
            return DB::table('admin_users')->where('id', $updated_by)->value('name');
        })->hide();

        $grid->tools(function (Grid\Tools $tools) {
            $tools->append('<a class="btn btn-sm btn-default" style="float: right;margin-right: 20px;" href="' . url('admin/order-sub/service-import') . '"><i class="fa fa-plus"></i>&nbsp;家政服务订单发货信息导入&nbsp;</a>');
            $tools->append('<a class="btn btn-sm btn-default" style="float: right;margin-right: 20px;" href="' . url('admin/order-sub/order-deliver-service-view') . '"><i class="fa fa-plus"></i>&nbsp;家政服务订单发货&nbsp;</a>');

            $tools->append('<a class="btn btn-sm btn-default" style="float: right;margin-right: 20px;" href="' . url('admin/order-sub/logistics-import') . '"><i class="fa fa-plus"></i>&nbsp;实物订单发货信息导入&nbsp;</a>');
            $tools->append('<a class="btn btn-sm btn-default" style="float: right;margin-right: 20px;" href="' . url('admin/order-sub/order-deliver-view') . '"><i class="fa fa-plus"></i>&nbsp;实物订单发货&nbsp;</a>');
        });

        //查询过滤
        $grid->filter(function ($filter) {

            $filter->column(1 / 2, function (Filter $filter) {
                $filter->equal('order.activity_id', '所属活动')->select(Activity::all([
                    'id', 'activity_name'
                ])->sortByDesc('id')->pluck('activity_name', 'id')
                );
                $filter->equal('order_no', '订单编号');
                $filter->equal('sub_order_no', '子订单编号');
                $filter->equal('user_mobile', '用户手机号');
                $filter->equal('charge_account', '充值账号');
            });
            $filter->column(1 / 2, function ($filter) {
                $filter->equal('status', '状态')->select(OrderSub::$status);
                $filter->equal('goods_id', '商品')->select(Goods::getSelectList());
//                $filter->equal('sms_status', '短信状态')->select(OrderSub::$sms_status);
                $filter->between('created_at', '创建时间')->datetime();
                $filter->between('deliver_complete_time', '订单完成时间')->datetime();
            });

        });

        return $grid;
    }

    protected function detail($id)
    {
        $show = new Show(OrderSub::findOrFail($id));

        $show->field('id', __('Id'));

        $show->field('order_no', __('订单编号'));
        $show->field('sub_order_no', __('子订单编号'));
        $show->field('goods_id', __('商品ID'));
        $show->field('goods_no', __('商品编号'));
        $show->field('goods_type', __('商品编号'))->using(SysCode::$goods_type);
        $show->field('goods_name', __('商品名称'));
        $show->field('goods_attr', __('商品属性'));
        $show->field('goods_num', __('商品属性'));
        $show->field('ecp_target', __('qcp大类'));
        $show->field('ecp_pcode', __('qcp商品编号'));
        $show->field('activation_code', __('激活码'));
        $show->field('sequence_no', __('序号'));
        $show->field('endtime', __('有效期'));
        $show->field('user_mobile', __('用户手机号'));
        $show->field('charge_account', __('充值账号'));
        $show->field('service_date', __('服务日期'));
        $show->field('service_time', __('服务时间'));
        $show->field('consignee_name', __('收货人姓名'));
        $show->field('consignee_phone', __('收货人电话'));
        $show->field('consignee_address', __('收货人地址'));
        $show->field('logistics_sn', __('物流单号'));
        $show->field('logistics_company', __('物流公司'));
        $show->field('status', __('状态'))->using(OrderSub::$status);
        $show->field('third_order_no', __('上游订单编号'));
        $show->field('order_req_time', __('订单请求时间'));
        $show->field('sms_status', __('短信状态'))->using(OrderSub::$sms_status);
        $show->field('sms_gateway_seq', __('短信网关seq'));
        $show->field('sms_callback_time', __('短信回调时间'));
        $show->field('created_at', __('创建时间'));
        $show->field('deliver_complete_time', __('订单完成时间'));
        $show->field('created_by', __('创建人'))->as(function ($created_by) {
            return DB::table('admin_users')->where('id', $created_by)->value('name');
        });
        $show->field('updated_by', __('更新人'))->as(function ($updated_by) {
            return DB::table('admin_users')->where('id', $updated_by)->value('name');
        });
        return $show;
    }

    public function order_deliver_view(Content $content)
    {
        $content->title('实物订单发货');
        $form = new Widgets\Form();
        $form->method('post');
        $form->action(url('admin/order-sub/do-order-deliver'));
        $form->datetime('start_date', '起始时间')->default(Carbon::now()->addDays(-1)->format('Y-m-d 00:00:00'));
        $form->datetime('end_date', '截止时间')->default(Carbon::now()->format("Y-m-d 23:59:59"));
        $form->html('<span class="text-red">起始日期和截止日期均按 “订单创建时间” 筛选</span>');
        $form->multipleSelect('activity_ids', '请选择活动')->options(Activity::orderBy('id', "desc")->get()->pluck("activity_name", "id"));
        if (session()->exists('file_path')) {
            $form->html(view('admin.export_succ', [
                'data' => [
                    'file_path'     => session('file_path'), 'file_name' => session('file_name'),
                    'deliver_count' => session('deliver_count')
                ]
            ]));
            session()->reflash();
        }

//        $form->disableReset();
//        $form->disablePjax();

        $content->body(new Widgets\Box('', $form));
        return $content;
    }

    //导出实物订单
    public function do_order_deliver(Content $content, Request $request)
    {

        $pre_route_str = 'admin/order-sub/order-deliver-view';
        $request->validate(
            [
                'start_date' => 'required|date_format:Y-m-d H:i:s',
                'end_date'   => 'required|date_format:Y-m-d H:i:s|after:start_date',
            ], [
                'start_date.required'    => "起始时间",
                'end_date.required'      => "截止日期不能为空",
                'start_date.date_format' => "起始日期必须是日期时间格式",
                'end_date.date_format'   => "截止日期必须是日期时间格式",
                'end_date.after'         => "截止日期必须是起始日期之后的一个日期",
            ]
        );

        $start_date   = $request->get('start_date');
        $end_date     = $request->get('end_date');
        $activity_ids = $request->get('activity_ids');
        $activity_ids = array_filter($activity_ids);
        try {

            $col_list = $this->column_list;
            unset($col_list['order_subs.charge_account']);
            unset($col_list['order_subs.service_date']);
            unset($col_list['order_subs.service_time']);

            // 获取待发货订单
            // 状态。 1-未处理 2-处理中，3-已发货，4-发货失败
            $builder = DB::table('order_subs')
                ->leftJoin("orders", "order_subs.order_id", "=", "orders.id")
                ->leftJoin("activities", "orders.activity_id", "=", "activities.id")
                ->select(array_merge(['order_subs.id'], array_keys($col_list)))
                ->where('order_subs.status', SysCode::ORDER_SUB_STATUS_1)
                ->where('order_subs.goods_type', SysCode::GOODS_TYPE_1)
                ->whereBetween('order_subs.created_at', [$start_date, $end_date]);

            if (!empty($activity_ids)) {
                $builder->whereIn("orders.activity_id", $activity_ids);
            }

            $export_order_list = $builder->orderBy('order_subs.id')->get()->toArray();

            $export_count = count($export_order_list);
            if (empty($export_order_list)) {
                $success = new \Illuminate\Support\MessageBag([
                    'title'   => '提示',
                    'message' => '没有待发货的订单！',
                ]);
                return redirect($pre_route_str)->withInput()->with(compact('success'));
            }

            // 数据库查询出来的数据转换成导出excel需要的数据
            $export_data = array();
            foreach ($export_order_list as $order) {
                $tmp = [];
                foreach (array_keys($col_list) as $col) {
                    $col_arr = explode('.', $col);
                    $col     = $col_arr[count($col_arr) - 1];
                    $tmp[]   = $order->$col;
                }
                $export_data[] = $tmp;
                unset($tmp);
            }

            // 导出excel的列名称，与导出的数据相对应
            $title_data   = array_values($col_list);
            $file_name    = '实物订单发货_' . date('Ymd_His');
            $file_options = ['fileType' => 'xlsx', 'format_str' => [1, 2, 3, 4, 5, 10, 11, 12, 13, 14, 15]];
            // 导出excel
            $file_path = export_excel($title_data, $export_data, $file_name, $file_options);

            unset($export_data);

            try {
                DB::transaction(function () use ($export_order_list) {
                    $per_num    = 1000;
                    $for_length = ceil(count($export_order_list) / $per_num);
                    for ($i = 0; $i < $for_length; $i++) {
                        $tmp = array_slice($export_order_list, $i * $per_num, $per_num);
                        $ids = [];
                        foreach ($tmp as $o) {
                            $ids[] = $o->id;
                        }
                        $result = DB::table('order_subs')->whereIn('id', $ids)->update([
                            'status'         => SysCode::ORDER_SUB_STATUS_2,
                            'order_req_time' => Carbon::now()->format('Y-m-d H:i:s')
                        ]);
                        if (!$result) {
                            //更新失败
                            throw new MyException('订单状态更新失败！');
                        }
                        unset($ids, $tmp);
                    }
                });
            } catch (\Exception $ex) {
                throw $ex;
            }
            unset($export_order_list);

            LogOpt::log(true, '实物订单发货', [], $file_path);
            $success = new \Illuminate\Support\MessageBag([
                'title'   => '提示',
                'message' => '发货成功！',
            ]);

            return redirect($pre_route_str)->withInput()->with(array_merge([
                'file_path'     => str_replace(storage_path("app"), "", $file_path),
                'file_name'     => $file_name,
                'deliver_count' => $export_count,
                'success'       => $success
            ]));

        } catch (\Exception $exception) {
            $content->withError('错误提示', $exception->getMessage());
        }
        return redirect($pre_route_str)->withInput();
    }

    /**
     * 导出发货订单
     *
     * @param Request $request
     *
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function export_download(Request $request)
    {

        $filePath = $request->get('filePath');
        return response()->download(storage_path("app") . $filePath);
    }

//    /**
//     * 下载模板（实物订单）
//     */
//    public function download_template() {
//        $file_path = base_path() . '/public/upload/logistics_template.xlsx';
//        $fileName  = 'logistics_template.xlsx';
//        header("Content-Type: application/force-download");
//        header("Content-Disposition: attachment; filename=" . $fileName);
//        readfile($file_path);
//    }

    public function logistics_import(Content $content)
    {

        $content->title('导入实物订单物流信息');
        $form = new Widgets\Form();
        $form->method('post');
        $form->action(url('admin/order-sub/do-logistics-import'));
        $form->file('order_file', '物流文件')->removable()->options([
            'showPreview'           => false,
            'allowedFileExtensions' => ['xls', 'xlsx'],
        ]);
//        $form->html('<a href="' . url('admin/order-sub/download-template') . '" target="_blank"> 下载模板</a>');
        $form->html('<a href="' . url('upload/logistics_template.xlsx') . '" target="_blank"> 下载模板</a>');
        $form->disableReset();
        $content->body(new Widgets\Box('', $form));
        return $content;
    }

    /**
     * 物流导入处理
     *
     * @param Content $content
     * @param Request $request
     *
     * @return $this
     */
    public function do_logistics_import(Content $content, Request $request)
    {

        $logger = Log::channel('optlog');

        $request->validate(
            [
                'order_file' => 'required|mimes:xls,xlsx',
            ], [
                'order_file.required' => "导入文件不能为空",
                'order_file.mimes'    => "上传文件格式为：xls，xlsx",
            ]
        );

        try {
            //处理文件
            //按时间命名文件
            $up_file   = $request->file('order_file');
            $ext       = $up_file->getClientOriginalExtension();
            $org_name  = $up_file->getClientOriginalName();
            $file_name = substr($org_name, 0, strripos($org_name, '.'));
            $name      = $file_name . '_' . date('YmdHis') . '_' . sprintf('%04d', Admin::user()->id) . '.' . $ext;
            //文件保存到storage/app/upload/order
            $path = $up_file->storeAs('upload' . DIRECTORY_SEPARATOR . 'order', $name);
            //获取文件url
            $url = storage_path() . DIRECTORY_SEPARATOR . 'app' . DIRECTORY_SEPARATOR . $path;

            LogOpt::log(true, '导入物流', [], $url);

            $logger->info('导入物流，' . $url);

            $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader(ucfirst($ext));
            $reader->setReadDataOnly(true);
            $spreadsheet = $reader->load($url); //载入excel表格

            $worksheet = $spreadsheet->getActiveSheet();

            $order_data   = array();
            $head_array   = array();
            $columns_file = [
                'order_no' => '订单号', 'sub_order_no' => '子订单号', 'logistics_company' => '物流公司名称', 'logistics_sn' => '物流单号'
            ];

            foreach ($worksheet->getRowIterator(1) as $row) {

                $tmp       = array();
                $row_index = $row->getRowIndex();

                foreach ($row->getCellIterator() as $cell) {

                    if ($row_index == 1) {
                        $col_v     = $cell->getFormattedValue();
                        $col_index = $cell->getColumn();
                        foreach ($columns_file as $k => $v) {
                            if (!empty($v) && $v == $col_v) {
                                $head_array[$col_index] = $k;
                            }
                        }

                    } else {
                        $col = $cell->getColumn();
                        if (array_key_exists($col, $head_array)) {
                            $tmp[$head_array[$col]] = trim($cell->getFormattedValue());
                        }
                    }
                }
                if ($row_index > 1) {
                    //判断必须数据
                    if (!array_key_exists('order_no', $tmp) || empty($tmp['order_no'])) {
                        throw new \Exception('订单编号不能为空！行号：' . $row_index);
                    }
                    if (!array_key_exists('sub_order_no', $tmp) || empty($tmp['sub_order_no'])) {
                        throw new \Exception('子订单编号不能为空！行号：' . $row_index);
                    }
                    if (!array_key_exists('logistics_company', $tmp)) {
                        throw new \Exception('物流公司名称不能为空！行号：' . $row_index);
                    }
                    if (!array_key_exists('logistics_sn', $tmp) || empty($tmp['logistics_sn'])) {
                        throw new \Exception('物流单号不能为空！行号：' . $row_index);
                    }

                    $order_data[] = $tmp;
                }

            }

            $update_count = 0;
            if (!empty($order_data)) {

                DB::beginTransaction();
                try {
                    foreach ($order_data as $order) {

                        $ret = DB::table('order_subs')
                            ->where([
                                'sub_order_no' => $order['order_no'], 'sub_order_no' => $order['sub_order_no'],
                                'status'       => SysCode::ORDER_SUB_STATUS_2
                            ])
                            ->update([
                                'status'                => SysCode::ORDER_SUB_STATUS_3,
                                'logistics_company'     => $order['logistics_company'],
                                'logistics_sn'          => $order['logistics_sn'],
                                'deliver_complete_time' => date('Y-m-d H:i:s'),
                            ]);
                        if (!$ret) {
                            throw new \Exception('物流信息更新失败！订单编号：' . $order['order_no'] . '，子订单编号：' . $order['sub_order_no']);
                        } else {
                            $update_count++;
                        }
                    }

                    DB::commit();

                } catch (\Illuminate\Database\QueryException $ex) {
                    DB::rollBack();
                    throw $ex;
                } catch (\Exception $ex) {
                    DB::rollBack();
                    throw $ex;
                }

            } else {
                throw new \Exception('从上传的文件中没有获取到订单信息！');
            }

            $success = new \Illuminate\Support\MessageBag([
                'title'   => '成功',
                'message' => '更新发货订单' . $update_count . '条。',
            ]);

            $logger->info('导入物流信息成功。');

            return redirect('admin/order-sub/logistics-import')->withInput()->with(compact('success'));

        } catch (\Exception $exception) {
            $content->withError('错误提示', $exception->getMessage());
            $logger->error('导入物流信息失败。' . $exception->getMessage());
        }
        return redirect('admin/order-sub/logistics-import')->withInput();
    }

    //服务类订单发货页面
    public function order_deliver_service_view(Content $content)
    {
        $content->title('家政服务订单发货');
        $form = new Widgets\Form();
        $form->method('post');
        $form->action(url('admin/order-sub/do-order-deliver-service'));
        $form->datetime('start_date', '起始时间')->default(Carbon::now()->addDays(-1)->format('Y-m-d 00:00:00'));
        $form->datetime('end_date', '截止时间')->default(Carbon::now()->format("Y-m-d 23:59:59"));
        $form->html('<span class="text-red">起始日期和截止日期均按 “订单创建时间” 筛选</span>');
        $form->multipleSelect('activity_ids', '请选择活动')->options(Activity::orderBy('id', "desc")->get()->pluck("activity_name", "id"));
        if (session()->exists('file_path_6')) {
            $form->html(view('admin.export_succ', [
                'data' => [
                    'file_path'     => session('file_path_6'), 'file_name' => session('file_name_6'),
                    'deliver_count' => session('deliver_count_6')
                ]
            ]));
            session()->reflash();
        }

//        $form->disableReset();
//        $form->disablePjax();

        $content->body(new Widgets\Box('', $form));
        return $content;
    }

    public function do_order_deliver_service(Content $content, Request $request)
    {

        $pre_route_str = 'admin/order-sub/order-deliver-service-view';
        $request->validate(
            [
                'start_date' => 'required|date_format:Y-m-d H:i:s',
                'end_date'   => 'required|date_format:Y-m-d H:i:s|after:start_date',
            ], [
                'start_date.required'    => "起始时间",
                'end_date.required'      => "截止日期不能为空",
                'start_date.date_format' => "起始日期必须是日期时间格式",
                'end_date.date_format'   => "截止日期必须是日期时间格式",
                'end_date.after'         => "截止日期必须是起始日期之后的一个日期",
            ]
        );

        $start_date   = $request->get('start_date');
        $end_date     = $request->get('end_date');
        $activity_ids = $request->get('activity_ids');
        $activity_ids = array_filter($activity_ids);
        try {
            $col_list = $this->column_list;
            unset($col_list['order_subs.charge_account']);
            $col_list['order_subs.logistics_company'] = '服务公司名称';
            $col_list['order_subs.logistics_sn']      = '服务单号';

            // 获取待发货订单
            // 状态。 1-未处理 2-处理中，3-已发货，4-发货失败
            $builder = DB::table('order_subs')
                ->leftJoin("orders", "order_subs.order_id", "=", "orders.id")
                ->leftJoin("activities", "orders.activity_id", "=", "activities.id")
                ->select(array_merge(['order_subs.id'], array_keys($col_list)))
                ->where('order_subs.status', SysCode::ORDER_SUB_STATUS_1)
                ->where('order_subs.goods_type', SysCode::GOODS_TYPE_6)
                ->whereBetween('order_subs.created_at', [$start_date, $end_date]);

            if (!empty($activity_ids)) {
                $builder->whereIn("orders.activity_id", $activity_ids);
            }

            $export_order_list = $builder->orderBy('order_subs.id')->get()->toArray();

            $export_count = count($export_order_list);
            if (empty($export_order_list)) {
                $success = new \Illuminate\Support\MessageBag([
                    'title'   => '提示',
                    'message' => '没有待发货的订单！',
                ]);
                return redirect($pre_route_str)->withInput()->with(compact('success'));
            }

            // 数据库查询出来的数据转换成导出excel需要的数据
            $export_data = array();
            foreach ($export_order_list as $order) {
                $tmp = [];
                foreach (array_keys($col_list) as $col) {
                    $col_arr = explode('.', $col);
                    $col     = $col_arr[count($col_arr) - 1];
                    $tmp[]   = $order->$col;
                }
                $export_data[] = $tmp;
                unset($tmp);
            }

            // 导出excel的列名称，与导出的数据相对应
            $title_data   = array_values($col_list);
            $file_name    = '家政服务订单发货_' . date('Ymd_His');
            $file_options = ['fileType' => 'xlsx', 'format_str' => [1, 2, 3, 10, 11, 13, 16]];
            // 导出excel
            $file_path = export_excel($title_data, $export_data, $file_name, $file_options);

            unset($export_data);

            try {
                DB::transaction(function () use ($export_order_list) {
                    $per_num    = 1000;
                    $for_length = ceil(count($export_order_list) / $per_num);
                    for ($i = 0; $i < $for_length; $i++) {
                        $tmp = array_slice($export_order_list, $i * $per_num, $per_num);
                        $ids = [];
                        foreach ($tmp as $o) {
                            $ids[] = $o->id;
                        }
                        $result = DB::table('order_subs')->whereIn('id', $ids)->update([
                            'status'         => SysCode::ORDER_SUB_STATUS_2,
                            'order_req_time' => Carbon::now()->format('Y-m-d H:i:s')
                        ]);
                        if (!$result) {
                            //更新失败
                            throw new MyException('订单状态更新失败！');
                        }
                        unset($ids, $tmp);
                    }
                });
            } catch (\Exception $ex) {
                throw $ex;
            }
            unset($export_order_list);

            LogOpt::log(true, '家政服务订单发货', [], $file_path);
            $success = new \Illuminate\Support\MessageBag([
                'title'   => '提示',
                'message' => '发货成功！',
            ]);

            return redirect($pre_route_str)->withInput()->with(array_merge([
                'file_path_6'     => str_replace(storage_path("app"), "", $file_path),
                'file_name_6'     => $file_name,
                'deliver_count_6' => $export_count,
                'success'         => $success
            ]));

        } catch (\Exception $exception) {
            $content->withError('错误提示', $exception->getMessage());
        }
        return redirect($pre_route_str)->withInput();
    }

//    /**
//     * 下载模板（家政服务订单）
//     */
//    public function download_service_template() {
//        $file_path = base_path() . '/public/upload/service_template.xlsx';
//        $fileName  = 'service_template.xlsx';
//        header("Content-Type: application/force-download");
//        header("Content-Disposition: attachment; filename=" . $fileName);
//        readfile($file_path);
//    }

    //家政服务订单信息导入
    public function service_import(Content $content)
    {

        $content->title('导入家政服务订单服务信息');
        $form = new Widgets\Form();
        $form->method('post');
        $form->action(url('admin/order-sub/do-service-import'));
        $form->file('order_file', '物流文件')->removable()->options([
            'showPreview'           => false,
            'allowedFileExtensions' => ['xls', 'xlsx'],
        ]);
//        $form->html('<a href="' . url('admin/order-sub/download-template') . '" target="_blank"> 下载模板</a>');
        $form->html('<a href="' . url('upload/service_template.xlsx') . '" target="_blank"> 下载模板</a>');
        $form->disableReset();
        $content->body(new Widgets\Box('', $form));
        return $content;
    }

    /**
     * 家政服务导入处理
     *
     * @param Content $content
     * @param Request $request
     *
     * @return $this
     */
    public function do_service_import(Content $content, Request $request)
    {

        $logger = Log::channel('optlog');

        $request->validate(
            [
                'order_file' => 'required|mimes:xls,xlsx',
            ], [
                'order_file.required' => "导入文件不能为空",
                'order_file.mimes'    => "上传文件格式为：xls，xlsx",
            ]
        );

        try {
            //处理文件
            //按时间命名文件
            $up_file   = $request->file('order_file');
            $ext       = $up_file->getClientOriginalExtension();
            $org_name  = $up_file->getClientOriginalName();
            $file_name = substr($org_name, 0, strripos($org_name, '.'));
            $name      = $file_name . '_' . date('YmdHis') . '_' . sprintf('%04d', Admin::user()->id) . '.' . $ext;
            //文件保存到storage/app/upload/order
            $path = $up_file->storeAs('upload' . DIRECTORY_SEPARATOR . 'order', $name);
            //获取文件url
            $url = storage_path() . DIRECTORY_SEPARATOR . 'app' . DIRECTORY_SEPARATOR . $path;

            LogOpt::log(true, '导入服务信息', [], $url);

            $logger->info('导入服务信息，' . $url);

            $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader(ucfirst($ext));
            $reader->setReadDataOnly(true);
            $spreadsheet = $reader->load($url); //载入excel表格

            $worksheet = $spreadsheet->getActiveSheet();

            $order_data   = array();
            $head_array   = array();
            $columns_file = [
                'order_no' => '订单号', 'sub_order_no' => '子订单号', 'logistics_company' => '服务公司名称', 'logistics_sn' => '服务单号'
            ];

            foreach ($worksheet->getRowIterator(1) as $row) {

                $tmp       = array();
                $row_index = $row->getRowIndex();

                foreach ($row->getCellIterator() as $cell) {

                    if ($row_index == 1) {
                        $col_v     = $cell->getFormattedValue();
                        $col_index = $cell->getColumn();
                        foreach ($columns_file as $k => $v) {
                            if (!empty($v) && $v == $col_v) {
                                $head_array[$col_index] = $k;
                            }
                        }

                    } else {
                        $col = $cell->getColumn();
                        if (array_key_exists($col, $head_array)) {
                            $tmp[$head_array[$col]] = trim($cell->getFormattedValue());
                        }
                    }
                }
                if ($row_index > 1) {
                    //判断必须数据
                    if (!array_key_exists('order_no', $tmp) || empty($tmp['order_no'])) {
                        throw new \Exception('订单编号不能为空！行号：' . $row_index);
                    }
                    if (!array_key_exists('sub_order_no', $tmp) || empty($tmp['sub_order_no'])) {
                        throw new \Exception('子订单编号不能为空！行号：' . $row_index);
                    }
                    if (!array_key_exists('logistics_company', $tmp)) {
                        throw new \Exception('服务公司名称不能为空！行号：' . $row_index);
                    }
                    if (!array_key_exists('logistics_sn', $tmp) || empty($tmp['logistics_sn'])) {
                        throw new \Exception('服务单号不能为空！行号：' . $row_index);
                    }

                    $order_data[] = $tmp;
                }

            }

            $update_count = 0;
            if (!empty($order_data)) {

                DB::beginTransaction();
                try {
                    foreach ($order_data as $order) {

                        $ret = DB::table('order_subs')
                            ->where([
                                'sub_order_no' => $order['order_no'], 'sub_order_no' => $order['sub_order_no'],
                                'status'       => SysCode::ORDER_SUB_STATUS_2
                            ])
                            ->update([
                                'status'                => SysCode::ORDER_SUB_STATUS_3,
                                'logistics_company'     => $order['logistics_company'],
                                'logistics_sn'          => $order['logistics_sn'],
                                'deliver_complete_time' => date('Y-m-d H:i:s'),
                            ]);
                        if (!$ret) {
                            throw new \Exception('服务信息更新失败！订单编号：' . $order['order_no'] . '，子订单编号：' . $order['sub_order_no']);
                        } else {
                            $update_count++;
                        }
                    }

                    DB::commit();

                } catch (\Illuminate\Database\QueryException $ex) {
                    DB::rollBack();
                    throw $ex;
                } catch (\Exception $ex) {
                    DB::rollBack();
                    throw $ex;
                }

            } else {
                throw new \Exception('从上传的文件中没有获取到订单信息！');
            }

            $success = new \Illuminate\Support\MessageBag([
                'title'   => '成功',
                'message' => '更新发货订单' . $update_count . '条。',
            ]);

            $logger->info('导入服务信息成功。');

            return redirect('admin/order-sub/service-import')->withInput()->with(compact('success'));

        } catch (\Exception $exception) {
            $content->withError('错误提示', $exception->getMessage());
            $logger->error('导入服务信息失败。' . $exception->getMessage());
        }
        return redirect('admin/order-sub/service-import')->withInput();
    }

}
