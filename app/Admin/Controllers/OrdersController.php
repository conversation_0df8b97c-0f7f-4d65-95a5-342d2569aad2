<?php

namespace App\Admin\Controllers;

use App\Admin\Actions\Orders\OrderStatusSync;
use App\Models\Activity;
use App\Models\Goods;
use App\Models\Order;
use Encore\Admin\Grid;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Grid\Filter;
use Encore\Admin\Show;
use Illuminate\Support\Facades\DB;
use Encore\Admin\Widgets;
use Encore\Admin\Layout\Content;
use App\Admin\Support\LogOpt;
use Encore\Admin\Facades\Admin;
use Illuminate\Http\Request;
use App\Admin\Extensions\WidgetForm;

class OrdersController extends AdminController
{

    protected $title = '订单';

    protected function grid()
    {
        $grid = new Grid(new Order());
        $grid->model()->orderByDesc('id');

        $grid->disableCreateButton();
        $grid->actions(function ($actions) {
            $actions->disableDelete();
            $actions->disableEdit();
            $actions->add(new OrderStatusSync());
        });

        $grid->column('id', __('Id'))->sortable();

        $grid->column('activity.activity_name', __('所属活动'));
        $grid->column('order_no', __('订单编号'));
        $grid->column('goods_no', __('商品编号'));
        $grid->column('goods_name', '商品名称');
//        $grid->column('status', __('订单状态'))->using(Order::$status)->dot([
//            4 => 'danger', 2 => 'success', 1 => 'warning', 3 => 'warning', 0 => 'default', -1 => 'default'
//        ]);
        //状态。 1-处理中，2-已发货，3-部分发货，4-发货失败
        $grid->column('status', __('订单状态'))->display(function ($status) {
            if ($this->goods->goods_type == 3 && $status == 2) {
                return '充值成功';
            } elseif ($this->goods->goods_type == 1 && $status == 2) {
                return '已发货';
            } else {
                return Order::$status[$status];
            }
        })->dot([4 => 'danger', 2 => 'success', 1 => 'warning', 3 => 'warning', 0 => 'default', -1 => 'default']);
        $grid->column('mobile', __('登录手机号'));
        $grid->column('user_mobile', __('用户手机号'))->help('取值顺序：登录手机号->充值账号->收货人联系方式。');
        $grid->column('charge_account', __('充值账号'));
        $grid->column('consignee_name', __('收货人姓名'));
        $grid->column('consignee_phone', __('收货人电话'));
        $grid->column('consignee_address', __('收货人地址'))->hide();
        $grid->column('service_date', __('服务日期'));
        $grid->column('service_time', __('服务时间'));
        $grid->column('order_time', __('下单时间'));
        $grid->column('deliver_complete_time', __('订单完成时间'));
        $grid->column('activity_user_id', __('活动用户ID'));
        $grid->column('order_remark', __('订单备注'))->hide();
//        $grid->column('created_by', __('创建人'))->display(function ($created_by) {
//            return DB::table('admin_users')->where('id', $created_by)->value('name');
//        })->hide();
//        $grid->column('updated_by', __('更新人'))->display(function ($updated_by) {
//            return DB::table('admin_users')->where('id', $updated_by)->value('name');
//        })->hide();

        //查询过滤
        $grid->filter(function ($filter) {
            $filter->disableIdFilter();
            $filter->column(1 / 2, function (Filter $filter) {
                $filter->equal('activity_id', '所属活动')->select(Activity::all([
                    'id', 'activity_name'
                ])->sortByDesc('id')->pluck('activity_name', 'id'));
                $filter->equal('activity_user_id', '活动用户ID');
                $filter->equal('order_no', '订单编号');
                $filter->equal('status', '状态')->select(Order::$status);
                $filter->equal('goods_id', '商品')->select(Goods::getSelectList());
            });
            $filter->column(1 / 2, function ($filter) {
                $filter->equal('mobile', '登录手机号');
                $filter->equal('user_mobile', '用户手机号');
                $filter->equal('charge_account', '充值账号');
                $filter->equal('consignee_phone', '收货人电话');
                $filter->between('deliver_complete_time', '订单完成时间')->datetime();
                $filter->between('created_at', '创建时间')->datetime();

            });
        });

        $grid->tools(function (Grid\Tools $tools) {
            $tools->append('<a class="btn btn-sm btn-success" style="float: right;margin-right: 20px;" href="' . url('admin/orders/export-list') . '"><i class="fa fa-download"></i>&nbsp;订单导出&nbsp;</a>');
        });

        return $grid;
    }

    protected function detail($id)
    {
        $show = new Show(Order::findOrFail($id));
        $show->field('id', __('Id'));
        $show->field('activity', __('所属活动'))->as(function ($activity) {
            return $activity->activity_name;
        });
        $show->field('activity_user_id', __('活动用户ID'));
        $show->field('order_no', __('订单编号'));
        $show->field('goods_id', __('商品ID'));
        $show->field('goods_no', __('商品编号'));
        $show->field('goods_name', __('商品名称'));
        $show->field('goods_attr', __('商品属性'));
        $show->field('goods.ecp_target', __('渠道商品大类编码'));
        $show->field('goods.ecp_pcode', __('渠道商品编码'));
        $show->field('user_mobile', __('用户手机号'));
        $show->field('charge_account', __('充值账号'));
        $show->field('consignee_name', __('收货人姓名'));
        $show->field('consignee_phone', __('收货人电话'));
        $show->field('consignee_address', __('收货人地址'));
        $show->field('service_date', __('预约日期'));
        $show->field('service_time', __('预约时间'));
        $show->field('status', __('状态'))->using(Order::$status);
        $show->field('sms_status', __('短信状态'))->using(Order::$sms_status);
        $show->field('created_at', __('创建时间'));
        $show->field('deliver_complete_time', __('订单完成时间'));
        $show->field('created_by', __('创建人'))->as(function ($created_by) {
            return DB::table('admin_users')->where('id', $created_by)->value('name');
        });
        $show->field('updated_by', __('更新人'))->as(function ($updated_by) {
            return DB::table('admin_users')->where('id', $updated_by)->value('name');
        });

        return $show;
    }

    /**
     * 导出订单页面
     *
     * @param Content $content
     *
     * @return Content
     */
    public function export_list(Content $content)
    {

        $content->title('订单导出');
        $form = new WidgetForm();
        $form->method('get');
        $form->action(url('admin/orders/export'));
        $form->datetime('start_date', '起始日期')->default(date('Y-m-d 00:00:00'));
        $form->datetime('end_date', '截止日期')->default(date('Y-m-d 23:23:59'))->help('起始日期和截止日期均按 “订单日期” 筛选');
        $options = \App\Models\Activity::all()->pluck('activity_name', 'id')->toArray();
        $form->multipleSelect('activity_id', '所属活动')->options($options)->help('活动可多选，如不选，则默认导出全部活动的订单');
        $form->listbox('column_list', '订单数据项')->options(Order::$column_list)->help('订单数据项不能为空，该项的值对应导出Excel表格的列标题');

        $form->disableReset();
        $form->disablePjax();

        $content->body(new Widgets\Box('', $form));
        return $content;
    }

    /**
     * 导出订单
     *
     * @param Content $content
     * @param Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector|\Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function export(Content $content, Request $request)
    {
        $request->validate(
            [
                'start_date' => 'required|date_format:Y-m-d H:i:s',
                'end_date'   => 'required|date_format:Y-m-d H:i:s|after:start_date',
            ], [
                'start_date.required'    => "起使日期不能为空",
                'end_date.required'      => "截止日期不能为空",
                'start_date.date_format' => "起始日期必须是日期时间格式",
                'end_date.date_format'   => "截止日期必须是日期时间格式",
                'end_date.after'         => "截止日期必须是起始日期之后的一个日期",
            ]
        );

        try {
            $start_date  = $request->get('start_date');
            $end_date    = $request->get('end_date');
            $activity_id = $request->get('activity_id');
            $column_list = $request->get('column_list');

            foreach ($activity_id as $key => $value) {
                if (empty($value)) {
                    unset($activity_id[$key]);
                }
            }

            foreach ($column_list as $key => $value) {
                if (empty($value)) {
                    unset($column_list[$key]);
                }
            }

            if (empty($column_list)) {
                throw new \Exception('请选择要导出的订单数据项！');
            }

            if (empty($activity_id)) {
                $activity_id = DB::table('activities')->where('status', 1)->pluck('id')->toArray();
            }

            // 9:是星点值的活动id
            if (in_array(9, $activity_id) && count($activity_id) > 1) {
                throw new \Exception('星点值项目需要单独导出！');
            }

            if (in_array(9, $activity_id) && count($activity_id) == 1) {
                //这是只导出星点值的数据
                $list = DB::table('orders')
                    ->leftJoin('icbc_star_points', 'orders.id', '=', 'icbc_star_points.order_id')
                    ->whereBetween('order_time', [$start_date, $end_date])
                    ->whereIn('activity_id', $activity_id)
                    ->select($column_list);
            } else {
                //其他项目的数据
                $list = DB::table('orders')
                    ->whereBetween('order_time', [$start_date, $end_date])
                    ->whereIn('activity_id', $activity_id)
                    ->select($column_list);
            }
            if ($list->count() <= 0) {
                $success = new \Illuminate\Support\MessageBag([
                    'title'   => '提示',
                    'message' => '没有满足条件的订单，请重新选择！',
                ]);
                return redirect('admin/orders/export-list')->withInput()->with(compact('success'));
            }
            $activities   = DB::table('activities')->pluck('activity_name', 'id')->toArray();
            $file_name    = '订单_' . Admin::user()->id . '_' . date('Ymd-His') . '.csv';
            $savePath     = storage_path() . '/app/export/order/' . $file_name;
            $val_converts = [
                'activity_id' => $activities,
                'status'      => Order::$status,
                'sms_status'  => Order::$sms_status,
            ];

            $savePath = $this->export_csv($list, $column_list, $val_converts, $savePath);
            LogOpt::log(true, '订单导出', [], $savePath);
            return response()->download($savePath);

        } catch (\Exception $exception) {
            $content->withError('错误提示', $exception->getMessage());
        }

        return redirect('admin/orders/export-list')->withInput();
    }


    /**
     * @param $builder                    \Illuminate\Database\Query\Builder对象。
     * @param $column_list                列名与标题关系。例如['goods_name' => '商品名称','charge_account' => '充值账号']
     * @param $val_converts               列值转换表  ['status' => ['1' => '待发货','2' =>'发货中' ],'sms_status'  => Order::$sms_status,];
     * @param $savePath                   文件生成物理路径
     * @param mixed $format_str_col_index 需要转换成文本的列索引，例如[1,2,4]。
     *
     * @return mixed
     * @throws \Exception
     */
    public function export_csv($builder, $column_list, $val_converts, $savePath)
    {
        // 设置过期时间
        set_time_limit(300);
        $delimiter = ',';
        if (empty($savePath)) {
            throw new \Exception('导出文件名不能为空！');
        }

        $fp = fopen($savePath, 'a');

        $title_data = [];

        foreach ($column_list as $k => $column) {
            $title_data[] = filterCsvVal(Order::$column_list[$column]);
            if (strstr($column, '.')) {
                $column_list[$k] = explode('.', $column)[1];
            }
        }
        $csv_title = chr(0xEF) . chr(0xBB) . chr(0xBF);
        $csv_title .= implode($delimiter, $title_data) . "\r\n";

        fputs($fp, $csv_title, strlen($csv_title));

        $count = $builder->count();

        $perSize = 2000;//每次查询的条数
        $pages   = ceil($count / $perSize);

        for ($i = 1; $i <= $pages; $i++) {
            //需要导出的数据
            $lists    = $builder->forPage($i, $perSize)->get();
            $csv_data = '';

            foreach ($lists as $v) {
                $arr       = (array)$v;
                $data      = [];
                $col_index = 1;

                foreach ($column_list as $column) {

                    if (array_key_exists($column, $val_converts) && array_key_exists($arr[$column],
                            $val_converts[$column])) {
                        $curr_col_data = $val_converts[$column][$arr[$column]];
                    } else {
                        $curr_col_data = $arr[$column];
                    }

                    $curr_col_data = filterCsvVal($curr_col_data);

                    $data[] = $curr_col_data;

                    unset($curr_col_data);

                    $col_index += 1;
                }

                $csv_data .= implode($delimiter, $data) . "\r\n";

                unset($data);
            }

            fputs($fp, $csv_data, strlen($csv_data));
            unset($csv_data);//释放变量的内存
        }
        fclose($fp);
        return $savePath;
    }

}
