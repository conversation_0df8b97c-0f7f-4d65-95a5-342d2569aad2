<?php

namespace App\Admin\Controllers;

use App\Admin\Support\LogOpt;
use App\Http\Controllers\Controller;
use App\Models\Activity;
use App\Models\ExchangeWhitelist;
use Carbon\Carbon;
use Encore\Admin\Admin;
use Encore\Admin\Layout\Content;
use Encore\Admin\Widgets\Form;
use Encore\Admin\Widgets\Tab;
use Encore\Admin\Widgets\Table;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * 短信验证码发送限制处理
 * @Date  : 2024/9/23
 */
class SmsCaptchaBlackDealController extends Controller
{
    private $pre_key = ''; //config('cache.black_key_pre') . ':' . 'api/captcha_xxxxxxxxxxx'

    public function __construct()
    {
        $this->pre_key = config('cache.black_key_pre') . ':api/captcha_';
    }

    public function doQuery(Request $request)
    {
        try {
            $redis = app('redis.connection');

            $mobile = $request->get('m', '');

            if (!empty($mobile) && !check_mobile($mobile)) {
                return response()->json(['code' => 300, 'msg' => '手机号错误']);
            }

            $curr_sms_day = '--';

            $curr_sms_per_day = $redis->get($this->pre_key . $mobile);

            $rsp_data = [
                'sms_day' => $curr_sms_per_day ?? '--',
            ];

            return response()->json(['code' => 200, 'msg' => '查询成功！', 'data' => $rsp_data]);

        } catch (\Exception $e) {
            Log::error($e->getMessage(), $e->getTrace());
            return response()->json(['code' => 500, 'msg' => '操作失败！' . $e->getMessage()]);
        }
    }

    /**
     * 缓存操作处理
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function doOpt(Request $request)
    {
        try {
            $mobile = $request->get('m', '');
            $opt    = $request->get('opt', '');//sub、add
            $type   = $request->get('type', '');//sms_day

            if (!in_array($opt, ['sub', 'add'])) {
                return response()->json(['code' => 300, 'msg' => '参数错误！']);
            }

            if (empty($mobile)) {
                return response()->json(['code' => 300, 'msg' => '手机号不能为空！']);
            }

            if (!check_mobile($mobile)) {
                return response()->json(['code' => 300, 'msg' => '手机号错误！']);
            }

            $new_val = null;

            if ($type == 'sms_day') {
                $key     = $this->pre_key . $mobile;
                $new_val = $this->do_cache_opt($key, $opt);
            } else {
                return response()->json(['code' => 300, 'msg' => '操作类型错误！']);
            }

            if ($new_val === null) {
                return response()->json(['code' => 300, 'msg' => '缓存不存在！']);
            }

            return response()->json(['code' => 200, 'msg' => '操作成功！', 'new_value' => $new_val]);

        } catch (\Exception $e) {
            Log::error($e->getMessage(), $e->getTrace());
            return response()->json(['code' => 500, 'msg' => '操作失败！' . $e->getMessage()]);
        }
    }

    //操作缓存
    private function do_cache_opt($key, $opt)
    {
        $redis = app('redis.connection');
        $val   = $redis->get($key);
        if ($val == null || $val > -10) {//不存在的缓存也可以操作
            if ($opt == 'sub') {
                $val = $redis->decr($key);
            } elseif ($opt == 'add') {
                $val = $redis->incr($key);
            }
            LogOpt::log(false, '短信验证码缓存操作', ['key' => $key, 'opt' => $opt, 'new_value' => $val]);
        }
        return $val;
    }
}
