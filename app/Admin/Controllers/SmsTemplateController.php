<?php

namespace App\Admin\Controllers;


use App\Models\Goods;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use App\Models\Activity;
use App\Models\SmsTemplate;
use App\Admin\SysCode\SysCode;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Auth\Database\Administrator;
use Encore\Admin\Controllers\AdminController;


class SmsTemplateController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = '短信模板';

    protected function grid()
    {
        $grid = new Grid(new SmsTemplate());
        $grid->column('id', __('Id'));
        $grid->column('category.activity_name', __('活动名称'));
        $grid->column('identity', __('标识'))->display(function () {
            return $this->getIdentityShow();
        });
        $grid->column('content', __('短信模板'));
        $grid->column('remark', __('备注'))->hide();
//        $grid->column('status', __('状态'))->using(SysCode::$common_status)->dot(SysCode::$common_dot);
        $grid->column('created_by', __('Created by'))->display(function ($created_by) {
            return Administrator::where('id', $created_by)->value('name');
        })->hide();
        $grid->column('updated_by', __('Updated by'))->display(function ($updated_by) {
            return Administrator::where('id', $updated_by)->value('name');
        })->hide();
        $grid->column('created_at', __('Created at'))->hide();
        $grid->column('updated_at', __('Updated at'))->hide();

        $grid->perPage = 10;

        $grid->filter(function ($filter) {
            $filter->disableIdFilter();
            $filter->equal('category_id', __('活动名称'))->select(function () {
                return Activity::where('status', 1)->pluck('activity_name', 'id');
            });
        });

        return $grid;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $form = new Form(new SmsTemplate());

        $form->select('i_table', __('模板分类'))->options($form->model()->getIdentityTable())->rules('required');
//        $form->select('i_table', __('模板分类'))->options(['order_subs'=>'订单'])->rules('required');
        $form->select('category_id', __('活动名称'))->options(function () {
            return Activity::where('status', SysCode::COMMON_STATUS_1)->pluck('activity_name', 'id');
        })->rules('required');
        $form->select('goods_type', __('商品类型'))->options(Goods::$goods_type)->rules('required');
        $form->select('deal_status', __('对应状态标识'))->options($form->model()->getIdentityDealStatus())->rules('required');
        $form->select('i_ext', __('订单类型'))->options($form->model()->identityExtensions());
        $form->radio('is_show_card_no', __('是否发送卡号'))->options([
            '0' => '否', '1' => '是'
        ])->default('0')->rules('required');
        $form->textarea('content', __('模板内容'))->rows(2)->rules('required');
        $form->html('<span class="text-red">模板内可使用变量如下： <br/>
        <table width="100%">
        <tr>
        <td width="25%">{order_no}&nbsp;&nbsp;=>&nbsp;&nbsp;订单号</td>
        <td width="25%">{goods_name}&nbsp;&nbsp;=>&nbsp;&nbsp;商品名称</td>
        <td width="25%">{charge_account}&nbsp;&nbsp;=>&nbsp;&nbsp;直充账号</td>
        <td width="25%">{sub_order_no}&nbsp;&nbsp;=>&nbsp;&nbsp;服务单号</td>
        </tr>
        <tr>
        <td>{activation_code}&nbsp;&nbsp;=>&nbsp;&nbsp;激活码</td>
        <td>{sequence_no}&nbsp;&nbsp;=>&nbsp;&nbsp;序列号</td>
        <td>{endtime}&nbsp;&nbsp;=>&nbsp;&nbsp;激活码有效期</td>
        </tr>
        <tr>
        <td>{logistics_company}&nbsp;&nbsp;=>&nbsp;&nbsp;物流公司</td>
        <td>{logistics_sn}&nbsp;&nbsp;=>&nbsp;&nbsp;物流单号</td>
        <td>{created_at}&nbsp;&nbsp;=>&nbsp;&nbsp;订单生成时间</td>
        </tr></table></span>');
        $form->text('remark', __('备注'));
        $states = [
            'on'  => ['value' => '1', 'text' => '启用', 'color' => 'success'],
            'off' => ['value' => '0', 'text' => '禁用', 'color' => 'danger'],
        ];
        $form->switch('status', __('状态'))->states($states)->default(SysCode::COMMON_STATUS_1);


        $form->submitted(function (Form $form) {

            $model = $form->model();

            $identity_arr    = [
                'i_table'         => request('i_table'),
                'project_id'      => request('category_id'),
                'goods_type'      => request('goods_type'),
                'deal_status'     => request('deal_status'),
                'is_show_card_no' => request('is_show_card_no'),
                'i_ext'           => request('i_ext'),
            ];
            $model->identity = $identity_arr;

            if (!empty($model->id)) {
                //更新
                $model->updated_by = Admin::user()->id;
                $model->updated_at = date('Y:m:d H:i:s');
            } else {
                //创建
                $model->created_by = Admin::user()->id;
                $model->created_at = date('Y:m:d H:i:s');
                $model->updated_by = Admin::user()->id;
                $model->updated_at = date('Y:m:d H:i:s');
            }

            $form->ignore(['i_table', 'project_id', 'goods_type', 'deal_status', 'is_show_card_no', 'i_ext']);

        });

        $form->editing(function (Form $form) {
            $arr = $form->model()->identityDecode();
            foreach ($arr as $key => $val) {
                $form->model()->$key = $val;
            }

        });

        return $form;
    }

}
