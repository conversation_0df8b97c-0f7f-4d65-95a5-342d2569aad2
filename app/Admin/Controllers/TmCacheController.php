<?php

namespace App\Admin\Controllers;

use App\Admin\Support\LogOpt;
use App\Http\Controllers\Controller;
use App\Models\Activity;
use App\Models\ExchangeWhitelist;
use Carbon\Carbon;
use Encore\Admin\Admin;
use Encore\Admin\Layout\Content;
use Encore\Admin\Widgets\Form;
use Encore\Admin\Widgets\Tab;
use Encore\Admin\Widgets\Table;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * 明苑风华天猫充值次数限制缓存管理
 * @author: liujq
 * @Time  : 2022/4/29 12:36
 */
class TmCacheController extends Controller
{
    private $pre_key = 'tianmao_'; //每天每淘宝账号订单计数redis key前缀  最终key前缀： gift_db_tianmao_

    public function index(Content $content)
    {
//        Admin::style(' .redStarAfter::afert{content: "*";color: red;}  .redStarBefore::before{content: "*";color: red;}');
        $tab = new Tab();

//        //Table
//        $headers = ['Id', 'Email', 'Name', 'Company'];
//        $rows    = [
//            [1, '<EMAIL>', 'Ms. Clotilde Gibson', 'Goodwin-Watsica'],
//            [2, '<EMAIL>', 'Allie Kuhic', 'Murphy, Koepp and Morar'],
//            [3, '<EMAIL>', 'Prof. Drew Heller', 'Kihn LLC'],
//            [4, '<EMAIL>', 'William Koss', 'Becker-Raynor'],
//            [5, '<EMAIL>', 'Ms. Antonietta Kozey Jr.'],
//        ];
//        $table   = new Table($headers, $rows);
//
//        //form
//        $form = new Form();
//
//        $form->action('example');
//
//        $form->email('email')->default('<EMAIL>');
//        $form->password('password');
//        $form->text('name', '输入框');
//        $form->url('url');
//        $form->color('color');
//        $form->map('lat', 'lng');
//        $form->date('date');
//        $form->json('val');
//        $form->dateRange('created_at', 'updated_at');


        //Tab
        $tab->add('明苑风华-天猫', view('admin.tm_cache'));
        $tab->add('日志查询-明苑风华-天猫', view('admin.tm_api_log'));
        $tab->add('短信验证码', view('admin.sms_captcha_black'));
//        $tab->add('Form', $form);
//        $tab->add('Text', 'blablablabla....');


        return $content
            ->title('下单限制查询')
            ->description('')
//            ->row(view('admin.cache')->render());
            ->row($tab->render());
    }

    public function doQueryCache_TM(Request $request)
    {
        try {
            $redis = app('redis.connection');

            $mobile         = $request->get('m', '');
            $charge_account = $request->get('ca', '');

            if (!empty($mobile) && !check_mobile($mobile)) {
                return response()->json(['code' => 300, 'msg' => '手机号错误']);
            }

            $curr_account_per_day   = '--';
            $curr_account_per_month = '--';
            $curr_mobile_per_day    = '--';
            $curr_mobile_per_month  = '--';
            $am_list                = [];
            $ma_list                = [];
            $in_whitelist           = [];

            $activity = Activity::where(['status' => 1])->where('activity_type', 'TianMao')
                ->select(['id', 'activity_name'])->first();

            if ($mobile) {
                $curr_mobile_per_day   = $redis->get($this->pre_key . $mobile);
                $curr_mobile_per_month = $redis->get($this->pre_key . 'm_' . $mobile);
                $ma_list               = $redis->sMembers($this->pre_key . 'ma_' . $mobile);

                if ($activity) {
                    $wl_mobile = ExchangeWhitelist::where('activity_id', $activity->id)
                        ->where('user_mobile', $mobile)
                        ->first();
                    if ($wl_mobile) {
                        if (empty($wl_mobile->expired_at) ||
                            Carbon::parse($wl_mobile->expired_at . ' 23:59:59')->isAfter(Carbon::now())) {
                            $in_whitelist[] = "手机号 $mobile 在白名单中";
                        }
                    }
                }
            }

            if ($charge_account) {
                $curr_account_per_day   = $redis->get($this->pre_key . md5($charge_account));
                $curr_account_per_month = $redis->get($this->pre_key . 'm_' . md5($charge_account));
                $am_list                = $redis->sMembers($this->pre_key . 'am_' . md5($charge_account));

                if ($activity) {
                    $wl_account = ExchangeWhitelist::where('activity_id', $activity->id)
                        ->where('charge_account', $charge_account)
                        ->first();
                    if ($wl_account) {
                        if (empty($wl_account->expired_at) ||
                            Carbon::parse($wl_account->expired_at . ' 23:59:59')->isAfter(Carbon::now())) {
                            $in_whitelist[] = "充值账号 $charge_account 在白名单中";
                        }
                    }
                }
            }

            $rsp_data = [
                'curr'      => [
                    'm_day'    => $curr_mobile_per_day ?? '--',
                    'm_month'  => $curr_mobile_per_month ?? '--',
                    'ca_day'   => $curr_account_per_day ?? '--',
                    'ca_month' => $curr_account_per_month ?? '--',
                ],
                'whitelist' => $in_whitelist ? '【 ' . implode('，', $in_whitelist) . ' 】' : '&nbsp;',
                'am_list'   => $am_list,
                'ma_list'   => $ma_list,
            ];

            return response()->json(['code' => 200, 'msg' => '查询成功！', 'data' => $rsp_data]);

        } catch (\Exception $e) {
            Log::error($e->getMessage(), $e->getTrace());
            return response()->json(['code' => 500, 'msg' => '操作失败！' . $e->getMessage()]);
        }
    }

    /**
     * 缓存操作处理
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @author: liujq
     * @Time  : 2022/5/12 01:10
     */
    public function doOptCache_TM(Request $request)
    {
        try {
            $mobile         = $request->get('m', '');
            $charge_account = $request->get('ca', '');
            $opt            = $request->get('opt', '');//sub、add
            $type           = $request->get('type', '');//m_day、m_month、ca_day、ca_month

            if (!in_array($opt, ['sub', 'add'])) {
                return response()->json(['code' => 300, 'msg' => '参数错误！']);
            }

//            if (!in_array($type, ['m_day', 'm_month', 'ca_day', 'ca_month'])) {
//                return response()->json(['code' => 300, 'msg' => '参数错误！']);
//            }

            if (!empty($mobile) && !check_mobile($mobile)) {
                return response()->json(['code' => 300, 'msg' => '手机号错误！']);
            }

            $new_val = null;

            if (in_array($type, ['m_day', 'm_month'])) {
                if (empty($mobile)) {
                    return response()->json(['code' => 300, 'msg' => '手机号不能为空！']);
                }
                $key = '';
                if ($type == 'm_day') {
                    $key = $this->pre_key . $mobile;
                } elseif ($type == 'm_month') {
                    $key = $this->pre_key . 'm_' . $mobile;
                }
                $new_val = $this->do_cache_opt_tm($key, $opt);
            } elseif (in_array($type, ['ca_day', 'ca_month'])) {
                if (empty($charge_account)) {
                    return response()->json(['code' => 300, 'msg' => '充值账号不能为空！']);
                }
                $key = '';
                if ($type == 'ca_day') {
                    $key = $this->pre_key . md5($charge_account);
                } elseif ($type == 'ca_month') {
                    $key = $this->pre_key . 'm_' . md5($charge_account);
                }
                $new_val = $this->do_cache_opt_tm($key, $opt);
            } else {
                return response()->json(['code' => 300, 'msg' => '操作类型错误！']);
            }

            if ($new_val === null) {
                return response()->json(['code' => 300, 'msg' => '缓存不存在！']);
            }

            return response()->json(['code' => 200, 'msg' => '操作成功！', 'new_value' => $new_val]);

        } catch (\Exception $e) {
            Log::error($e->getMessage(), $e->getTrace());
            return response()->json(['code' => 500, 'msg' => '操作失败！' . $e->getMessage()]);
        }
    }

    /**
     * 缓存操作处理(手机号与淘宝账号对应关系限制处理)
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @author: liujq
     * @Time  : 2023/3/22
     */
    public function doMAAM_TM(Request $request)
    {
        try {
            $type = $request->get('type', '');
            $key  = $request->get('key', '');
            $data = $request->get('maam', '');

            if (!in_array($type, ['am', 'ma'])) {
                return response()->json(['code' => 300, 'msg' => 'type参数错误！']);
            }

            if (empty($key) || empty($data)) {
                return response()->json(['code' => 300, 'msg' => '参数错误！']);
            }

            if ($type == 'am') {
                $redis_key = $this->pre_key . 'am_' . md5($key);
            } else {
                $redis_key = $this->pre_key . 'ma_' . $key;
            }
            $redis = app('redis.connection');
            $redis->sRem($redis_key, $data);

            return response()->json(['code' => 200, 'msg' => '操作成功！', 'new_value' => $redis->sMembers($redis_key)]);

        } catch (\Exception $e) {
            Log::error($e->getMessage(), $e->getTrace());
            return response()->json(['code' => 500, 'msg' => '操作失败！' . $e->getMessage()]);
        }
    }

    //查询api日志
    public function doQueryApiLog_TM(Request $request)
    {
        try {
            $log_day = $request->get('log_day', Carbon::now()->format('Y-m-d'));
            $type    = $request->get('type', 0);//0-所有，1-登录，2-下单，3-订单列表，4-短信验证码，5-超限
            $key1    = $request->get('key1', '');//兑换码或手机号

            if ($type != 5 && empty($key1)) {
                return response()->json(['code' => 300, 'msg' => '查询的兑换码或手机号不能为空！']);
            }

            $command = 'cat ' . storage_path('logs/api/api_log-' . $log_day . '.log');
            switch ($type) {
                case 1:
                    $command = $command . ' | grep "\"path\":\"api/login\""';
                    break;
                case 2:
                    $command = $command . ' | grep "\"path\":\"api/order\""';
                    break;
                case 3:
                    $command = $command . ' | grep "\"path\":\"api/order-record\""';
                    break;
                case 4:
                    $command = $command . ' | grep "\"path\":\"api/captcha\""';
                    break;
                case 5:
                    $command = $command . ' | grep "\"code\":3030"';
                    break;
                default:
                    break;
            }

            if ($key1) {
//                $command = $command . ' | grep "\"' . $key1 . '\""';
                $command = $command . ' | grep "' . $key1 . '"';
            }

            $command = $command . ' |tail -200';
            $command = $command . ' 2>&1';

            $output     = [];
            $return_var = -1;
            $result     = exec($command, $output, $return_var);

            if ($output) {
                $output = array_reverse($output);
            }
            for ($i = 0; $i < count($output); $i++) {
                $output[$i] = json_decode($output[$i], true);
                unset($output[$i]['message']['user-agent'], $output[$i]['message']['content-type'],
                    $output[$i]['message']['session']['_token'], $output[$i]['message']['session']['captcha'],
                    $output[$i]['message']['session']['activity'], $output[$i]['message']['session']['user_info'],
                );
//                $output[$i] = json_encode($output[$i],JSON_UNESCAPED_UNICODE);
            }

            return response()->json(['code' => 200, 'msg' => '查询成功！', 'data' => ['command' => $command, 'return_var' => $return_var, 'result' => $result, 'logs' => $output]]);
//            return response()->json(['code' => 200, 'msg' => '查询成功！', 'data' => ['logs' => $output]]);

        } catch (\Exception $e) {
            Log::error($e->getMessage(), $e->getTrace());
            return response()->json(['code' => 500, 'msg' => '查询失败！' . $e->getMessage()]);
        }
    }

    //操作缓存
    private function do_cache_opt_tm($key, $opt)
    {
        $redis = app('redis.connection');
        $val   = $redis->get($key);
//        if ($val !== null && $val > -10) {
        if ($val == null || $val > -10) {//不存在的缓存也可以操作
            if ($opt == 'sub') {
                $val = $redis->decr($key);
            } elseif ($opt == 'add') {
                $val = $redis->incr($key);
            }
            LogOpt::log(false, '明苑风华-天猫-缓存操作', ['key' => $key, 'opt' => $opt, 'new_value' => $val]);
        }
        return $val;
    }
}
