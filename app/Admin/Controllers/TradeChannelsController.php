<?php

namespace App\Admin\Controllers;

use App\Models\TradeChannel;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Controllers\AdminController;
use Illuminate\Support\Facades\DB;
use Encore\Admin\Facades\Admin;

class TradeChannelsController extends AdminController
{

    protected $title = '支付渠道';

    protected function grid() {
        $grid = new Grid(new TradeChannel());
        $grid->model()->orderByDesc('id');

        $grid->actions(function ($actions) {
            $actions->disableDelete();
            $actions->disableView();
        });

        $grid->column('id', __('Id'))->sortable();

        $grid->column('name', __('渠道名称'));
        $grid->column('sign', __('渠道标识'));
        $grid->column('req_url', __('提交地址'));
        $grid->column('query_url', __('查询地址'));
        $grid->column('sign_url', __('加签地址'))->help('一般支付渠道不会用，目前华夏渠道有用到');
        $grid->column('req_parameters', __('支付所需参数'))->help('支付渠道提交支付网关所需的参数集合');
        $grid->column('query_parameters', __('查询所需参数'))->help('查询支付网关支付结果所需的参数集合');
        $grid->column('channel_parameters', __('支付渠道参数'))->help('支付渠道自身所需参数，提交支付网关之外用到的参数');

        $grid->column('created_at', __('创建时间'));
        $grid->column('updated_at', __('修改时间'));
        $grid->column('created_by', __('创建人'))->display(function ($created_by) {
            return DB::table('admin_users')->where('id', $created_by)->value('name');
        });
        $grid->column('updated_by', __('更新人'))->display(function ($updated_by) {
            return DB::table('admin_users')->where('id', $updated_by)->value('name');
        });

        //查询过滤
        $grid->filter(function ($filter) {

            $filter->like('name', '渠道名称');
            $filter->equal('sign', '渠道标识');

        });

        return $grid;
    }

    protected function form() {
        $form = new Form(new TradeChannel());

        $form->text('name', __('渠道名称'));
        $form->text('sign', __('渠道标识'));
        $form->text('req_url', __('提交地址'));
        $form->text('query_url', __('查询地址'));
        $form->text('sign_url', __('加签地址'));
        $form->listbox('req_parameters', __('支付所需参数'))->options(TradeChannel::$reqParams)->height(300);
        $form->listbox('query_parameters', __('查询所需参数'))->options(TradeChannel::$queryParams)->height(200);

        $form->text('channel_parameters', __('支付渠道所需参数'))->help('支付渠道自身所需参数,输入json格式数据');

        $form->hidden('created_by')->value(Admin::user()->id);
        $form->hidden('updated_by')->value(Admin::user()->id);
        return $form;
    }


}