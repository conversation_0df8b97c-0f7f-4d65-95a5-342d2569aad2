<?php

namespace App\Admin\Controllers;

use App\Models\Activity;
use App\Models\Trade;
use Encore\Admin\Grid;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Show;

class TradesController extends AdminController
{

    protected $title = '交易信息';

    protected function grid() {
        $grid = new Grid(new Trade());
        $grid->model()->orderByDesc('id');
        $grid->disableExport(false);

        $grid->disableCreateButton();
        $grid->actions(function ($actions) {
            $actions->disableDelete();
            $actions->disableEdit();
        });

        $grid->column('id', __('Id'))->sortable();

        $grid->column('activity.activity_name', __('所属活动'));
//        $grid->column('activity_id', __('活动id'));
        $grid->column('order_no', __('支付订单号'));
        $grid->column('pay_from_table_name', __('来源支付订单表名称'))->hide();
        $grid->column('appid', __('公众账号ID'))->hide();
//        $grid->column('pay_channel_id', __('支付渠道id'));
        $grid->column('trade_channel.name', __('支付渠道'));
        $grid->column('pay_amount', __('支付金额(单位分）'));
        $grid->column('pay_expire_time', __('支付过期时间'))->hide();
        $grid->column('pay_result', __('支付结果'))->using(Trade::$pay_result);
        $grid->column('pay_card_no', __('支付卡号'))->help('支付卡号或微信支付宝openid')->hide();
        $grid->column('gateway_orderid', __('网关交易订单号'));
        $grid->column('platform_orderid', __('第三方支付订单号'));
        $grid->column('gateway_seq', __('网关交易流水号'))->hide();
        $grid->column('bank_type', __('付款银行'))->help('付款银行或微信，支付宝')->hide();
        $grid->column('prepay_id', __('预支付交易会话标识'))->hide();
        $grid->column('code_url', __('微信扫码支付链接地址'))->hide();
        $grid->column('mweb_url', __('微信h5支付的mweb_url'))->hide();
        $grid->column('qrcode', __('二维码生成地址'))->help('e生活二维码支付 二维码生成地址')->hide();
        $grid->column('callback_code', __('回调结果'))->hide();
        $grid->column('points', __('积分抵扣金额（单位分）'))->hide();
        $grid->column('discount', __('电子券抵扣金额（单位分）'))->hide();
        $grid->column('shop_reduction_discount', __('优惠立减金额（单位分）'))->hide();
        $grid->column('card_discount', __('银行补贴金额（单位分）'))->hide();
        $grid->column('real_pay_fee', __('实际支付金额（单位分）'))->hide();
        $grid->column('platform_trade_type', __('第三方交易类型'))->hide();
        $grid->column('intra_rdr', __('内部回跳地址'))->help('支付完成或取消的内部回跳地址')->hide();
        $grid->column('created_at', __('创建时间'));
        $grid->column('updated_at', __('修改时间'))->hide();

        //查询过滤
        $grid->filter(function ($filter) {
            $filter->disableIdFilter();

            $filter->equal('activity_id', '所属活动')->select(Activity::all(['id', 'activity_name'])->sortByDesc('id')->pluck('activity_name', 'id'));
            $filter->equal('order_no', '支付订单号');
            $filter->equal('gateway_orderid', '网关交易订单号');
            $filter->equal('platform_orderid', '第三方支付订单号');
            $filter->equal('pay_result', '支付状态')->select(Trade::$pay_result);
            $filter->between('created_at', '创建时间')->datetime();
        });

        return $grid;
    }

    protected function detail($id) {
        $show = new Show(Trade::findOrFail($id));
        $show->field('id', __('Id'));
        $show->field('activity', __('所属活动'))->as(function($activity){
            return $activity->activity_name;
        });
//        $show->field('activity_id', __('活动id'));
        $show->field('order_no', __('支付订单号'));
        $show->field('pay_from_table_name', __('来源支付订单表名称'));
        $show->field('appid', __('公众账号ID'));
        $show->field('trade_channel', __('支付渠道'))->as(function($trade_channel){
            return $trade_channel->name;
        });
        $show->field('pay_amount', __('支付金额(单位分）'));
        $show->field('pay_expire_time', __('支付过期时间'));
        $show->field('pay_result', __('支付结果'))->using(Trade::$pay_result);
        $show->field('pay_card_no', __('支付卡号或微信支付宝openid'));
        $show->field('gateway_orderid', __('网关交易订单号'));
        $show->field('gateway_seq', __('网关交易流水号'));
        $show->field('bank_type', __('付款银行或微信，支付宝'));
        $show->field('prepay_id', __('预支付交易会话标识'));
        $show->field('code_url', __('微信扫码支付链接地址'));
        $show->field('mweb_url', __('微信h5支付的mweb_url'));
        $show->field('qrcode', __('e生活二维码支付 二维码生成地址'));
        $show->field('callback_code', __('回调结果'));
        $show->field('platform_orderid', __('第三方支付订单号(微信等)'));
        $show->field('points', __('积分抵扣金额（单位分）'));
        $show->field('discount', __('电子券抵扣金额（单位分）'));
        $show->field('shop_reduction_discount', __('优惠立减金额（单位分）'));
        $show->field('card_discount', __('银行补贴金额（单位分）'));
        $show->field('real_pay_fee', __('实际支付金额（单位分）'));
        $show->field('platform_trade_type', __('第三方交易类型'));
        $show->field('intra_rdr', __('支付完成或取消的内部回跳地址'));
        $show->field('created_at', __(''));
        $show->field('updated_at', __(''));

        return $show;
    }


}