<?php

use Illuminate\Routing\Controller;
use Illuminate\Http\Request;
use App\Handlers\ImageUploadHandler;

class UploadImageController extends Controller
{

    public function upload(Request $request, ImageUploadHandler $uploader)
    {

//        $res = ['errno' => 1, 'errmsg' => '上传图片错误'];
//        if ($file = $request->upload_file ){
//            $data = [];
//                $ext = strtolower($file->extension());
//                $exts = ['jpg', 'png', 'gif', 'jpeg'];
//                if(!in_array($ext, $exts)) {
//                    $res = ['errno' => 1, 'errmsg' => '请上传正确的图片类型，支持jpg, png, gif, jpeg类型'];
//                    return json_encode($res);
//                } else {
//                    $fileName = time() . str_random(6) . "." . $ext;
//                    $filePath = public_path('upload'). DIRECTORY_SEPARATOR . date('Ym') . '/';
//                    if (!file_exists($filePath)) {
//                        @mkdir($filePath);
//                    }
//                    $savePath = env('APP_URL'). '/' . $filePath . $fileName;
//                    if ($file->move($filePath, $fileName)) {
//                        $data[] = $savePath;
//                    }
//                }
//            $res = ['errno' => 0, 'data' => $data];
//        }
//        return json_encode($res);

        // 初始化返回数据，默认是失败的
        $data = [
            'errno' => 1,
        ];
        // 判断是否有上传文件，并赋值给 $file
        if ($file = $request->upload_file) {
            // 保存图片到本地
            $result = $uploader->save($request->upload_file, 'admin', 1, 1024);
            // 图片保存成功的话
            if ($result) {
                $data['data'][] = $result['path'];
                $data['errno']  = 0;
            }
        }

        return $data;


    }
}
