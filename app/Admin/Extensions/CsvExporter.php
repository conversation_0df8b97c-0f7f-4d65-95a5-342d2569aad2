<?php

namespace App\Admin\Extensions;

use App\Libraries\CsvExporterHelper;
use Encore\Admin\Grid\Exporters\AbstractExporter;
use Illuminate\Support\Facades\Log;

class CsvExporter extends AbstractExporter
{
    protected $file_name     = 'file';
    protected $head          = [];
    protected $cols          = [];
    protected $trans_arr     = [];
    protected $col_callbacks = [];

    public function setAttr($file_name, $head, $cols, $trans_arr = [], $col_callbacks = [])
    {
        $this->file_name     = $file_name;
        $this->head          = $head;
        $this->cols          = $cols;
        $this->trans_arr     = $trans_arr;
        $this->col_callbacks = $col_callbacks;
    }

    //分页导出
    public function export()
    {
        $exporter = new CsvExporterHelper();
        $exporter->init($this->file_name, $this->head);
        $this->chunk(function ($records, $page) use ($exporter) {
            $bodyRows = $records->map(function ($item) {
                $arr = [];
                foreach ($this->cols as $col) {
                    $val = $item[$col];
                    if (array_key_exists($col, $this->trans_arr)) {
                        $val = $this->trans_arr[$col][$val] ?? $val;
                    } elseif (array_key_exists($col, $this->col_callbacks)) {
                        $val = $this->col_callbacks[$col]($val, $col, $item);
                    }
                    $arr[] = $val;
                    unset($val);
                }
                return $arr;
            });
            $exporter->write($bodyRows);
        }, 1000);
        Log::info(sprintf('export file: %s [%s]', $this->file_name, getMemoryUsage()));
        $exporter->finish();
    }
}
