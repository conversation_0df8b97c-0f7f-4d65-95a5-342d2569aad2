<?php

namespace App\Admin\Extensions;

use Encore\Admin\Form\Field\MultipleSelect as BaseMultipleSelect;
use Illuminate\Support\Arr;

class MultipleSelect extends BaseMultipleSelect
{
    /**
     * {@inheritdoc}
     */
    public function setOriginal($data)
    {
        //fix：Arr::ger增加默认值[] 2022-03-10
        $relations = Arr::get($data, $this->column, []);

        if (is_string($relations)) {
            $this->original = explode(',', $relations);
        }

        if (!is_array($relations)) {
            return;
        }

        $first = current($relations);

        if (is_null($first)) {
            $this->original = null;

            // MultipleSelect value store as an ont-to-many relationship.
        } elseif (is_array($first)) {
            foreach ($relations as $relation) {
                $this->original[] = Arr::get($relation, "pivot.{$this->getOtherKey()}");
            }

            // MultipleSelect value store as a column.
        } else {
            $this->original = $relations;
        }
    }
}
