<?php

namespace App\Admin\Extensions;

use Encore\Admin\Form\Field;
use Encore\Admin\Form\NestedForm as BaseNestedForm;
use Illuminate\Support\Arr;

class NestedForm extends BaseNestedForm
{
    /**
     * Do prepare work before store and update.
     *
     * @param array $record
     *
     * @return array
     */
    protected function prepareRecord($record)
    {
        if ($record[static::REMOVE_FLAG_NAME] == 1) {
            return $record;
        }

        $prepared = [];

        /* @var Field $field */
        foreach ($this->fields as $field) {
            $columns = $field->column();

            $value = $this->fetchColumnValue($record, $columns);

            if (is_null($value)) {
                continue;
            }

            if (method_exists($field, 'prepare')) {
                $value = $field->prepare($value);
            }

            //fix: 如果$value为数组，则更新不到数据。 2022-03-10
            $field_original = $field->original() ?? [];
            if (
                $field instanceof \Encore\Admin\Form\Field\Hidden
                || (is_array($value) ?
                    (count($field_original) != count($value) || !empty(array_diff($value, $field_original))) :
                    ($value != $field->original())
                )
            ) {
                if (is_array($columns)) {
                    foreach ($columns as $name => $column) {
                        Arr::set($prepared, $column, $value[$name]);
                    }
                } elseif (is_string($columns)) {
                    Arr::set($prepared, $columns, $value);
                }
            }
        }

        $prepared[static::REMOVE_FLAG_NAME] = $record[static::REMOVE_FLAG_NAME];

        return $prepared;
    }
}
