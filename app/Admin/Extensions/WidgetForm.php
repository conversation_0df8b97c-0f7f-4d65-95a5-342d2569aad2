<?php

namespace App\Admin\Extensions;

use Encore\Admin\Facades\Admin;

class WidgetForm extends \Encore\Admin\Widgets\Form
{
    public function getFormId() {
        return $this->attributes['id'];
    }

    protected function addCascadeScript()
    {
        $id = $this->attributes['id'];

        $script = <<<SCRIPT
;(function () {
    $('form#{$id}').submit(function (e) {
        $(this).find('div.cascade-group.hide :input').attr('disabled', true);
    });
})();
SCRIPT;

        Admin::script($script);
    }
}
