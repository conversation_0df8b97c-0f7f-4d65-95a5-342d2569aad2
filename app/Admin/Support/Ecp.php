<?php

namespace App\Admin\Support;
use Illuminate\Support\Facades\Log;

class Ecp
{

    private $username;
    private $secretkey;
    public function __construct(){
        $this->username = env('ECP_USERNAME');
        $this->secretkey = env('ECP_SECRETKEY');
    }

    /**
     * 增值商品大类
     */
    public function getCategories(){
        $url = env('ECP_API_CATEGORIES_URL');
        $data = array();
        $re = requestUrlByPostBuild($url, $data, $result);
        if ($re){
            $ecp_result = json_decode($result, true);
            unset($result);  //解除引用绑定
            if ($ecp_result['retcode']=='0000'&&!empty($ecp_result['retinfo'])) {
                return $ecp_result['retinfo'];
            }
        }
        unset($result);  //解除引用绑定
        return array();
    }

    /**
     * 根据大类查询增值商品小类
     *
     * @param string $pcode 大类
     * @return array
     */
    public function getSubCategories($pcode){
        $url = env('ECP_API_SUBCATEGORIES_URL');
        $data = array(
            'username' => $this->username,
            'pcode' => $pcode,
            'timestamp' => time(),
            'msgencrypt' => 1,
            'secretkey' => $this->secretkey
        );
        //把请求数组按键名排序
        ksort($data);
        $data['appsign'] = md5(implode("", $data));
        unset($data['secretkey']);
        $re = requestUrlByPostBuild($url, $data, $result);
        if ($re){
            $ecp_result = json_decode($result, true);
            unset($result);  //解除引用绑定
            if ($ecp_result['retcode']=='0000'&&!empty($ecp_result['retinfo'])) {
                return $ecp_result['retinfo'];
            }
            $this -> _ecpLogError($url, $data, $ecp_result);
        }
        unset($result);  //解除引用绑定
        return array();
    }


    /**
     * 增值商品订购
     *
     * @param string $pcode 格式:大类商品代号_小类商品代号(tel_yd1)
     * @param string $orderid 订单号
     * @param string $user_mobile 用户手机号。tel、flow类的充值手机号。卡券类中的同步直充也用该参数做充值账号。如果大类非tel、flow，且user_mobile为空，则赋默认值***********。
     * @param string $account 直充账号。如果大类为tel、flow，且$account不为空，则覆盖$user_mobile参数；如果大类为四大卡券类，且$account验证为手机号，则会覆盖$user_mobile参数。
     * @return array 请求成功返回 array(6) {["sequence_no"]=>string(7) "aaa1574"["activation_code"]=>string(7) "aaa1574"["endtime"]=>string(19) "2017-01-31 00:00:00"["requestid"]=>string(14) "**************"["orderid"]=>string(20) "18h16032115381623911"}

     */
    public function agentApiDefault($pcode, $orderid, $user_mobile, $account = ''){
        // tel, flow, qcoins, recharge, mtickets-电子票务, mcoupons-O2O电子消费券, onlinevip-线上会员特权, mvoucher-电商代金券
        // tel, flow, qcoins, recharge, mtickets, mcoupons, onlinevip, mvoucher
        $url = env('ECP_API_URL');
        $ecp_code_arr = explode('_', $pcode);
        $data = array(
            'username'   => $this->username,
            'pcode'      => $pcode,
            'requestid'  => $orderid,
            'timestamp'  => time(),
            'msgencrypt' => 1,
            'telphone'   => $user_mobile,
// 				'number' => $number,	TODO数量，ecp暂未开放该参数
            'secretkey'  => $this->secretkey
        );

        if($ecp_code_arr[0] == 'qcoins'){
            $data['qq'] = $account;
        }elseif($ecp_code_arr[0] == 'recharge'){
            $data['recharge_no'] = $account;
        }elseif(in_array($ecp_code_arr[0], array('tel', 'flow'))){
            if(!empty($account)){
                $data['telphone'] = $account;
            }
        }else{
            if(!empty($account) && checkphone($account)){
                //卡券类里的同步直充账号处理，比如爱奇艺直充
                $data['telphone'] = $account;
            }
        }

//        if(!in_array($ecp_code_arr[0], array('tel', 'flow')) && empty($data['telphone'])){
//            $data['telphone'] = '***********';
//        }
        if(empty($data['telphone'])){
            return array();
        }

        //把请求数组按键名排序
        ksort($data);
        $data['sign'] = md5(implode("", $data));
        unset($data['secretkey']);
        $re = requestUrlByPostBuild($url, $data, $result);
        if ($re){
            $ecp_result = json_decode($result, true);
            unset($result);  //解除引用绑定
            $back['code'] = $ecp_result['retcode'];
            if ($ecp_result['retcode']=='0000'&&!empty($ecp_result['retinfo'])) {
                $this -> _ecpLogSucc($url, $data, $ecp_result);
                $back['data'] = $ecp_result['retinfo'];
            }else{
                $this -> _ecpLogError($url, $data, $ecp_result);
                $back['data'] = $ecp_result['retmsg'];
            }
            return $back;
        }
        unset($result);  //解除引用绑定
        return array();
    }

    /**
     * 查询手机号归属地
     * @param string $mobile
     * @return array
     */
    public function mobileTelSegment($mobile){
        $url = env('ECP_API_MOBILETELSEGMENT_URL');
        $data = array(
            'mobile' => $mobile,
        );
        $re = requestUrlByPostBuild($url, $data, $result);
        if ($re){
            $ecp_result = json_decode($result, true);
            unset($result);  //解除引用绑定
            if ($ecp_result['retcode']=='0000'&&!empty($ecp_result['retinfo'])) {
                return $ecp_result['retinfo'];
            }
        }
        unset($result);  //解除引用绑定
        return array();
    }

    /**
     * 查询订单
     * @param string $pcode 格式:大类商品代号_小类商品代号(tel_yd1)
     * @param string $orderid 订单号
     * @return
     */
    public function getAgentOrderState($pcode, $orderid) {
        $url = env('ECP_API_AGENTORDERSTATE_URL');
        $data = array(
            'username' => $this->username,
            'pcode' => $pcode,
            'request_id' => $orderid,
            'timestamp' => time(),
            'msgencrypt' => 1,
            'secretkey' => $this->secretkey
        );
        //把请求数组按键名排序
        ksort($data);
        $data['appsign'] = md5(implode("", $data));
        unset($data['secretkey']);
        $re = requestUrlByPostBuild($url, $data, $result);
        if ($re){
            $ecp_result = json_decode($result, true);
            unset($result);  //解除引用绑定
            $back['code'] = $ecp_result['retcode'];
            if ($ecp_result['retcode']=='0000'&&!empty($ecp_result['retinfo'])) {
                $this -> _ecpLogSucc($url, $data, $ecp_result);
                $back['data'] = $ecp_result['retinfo'];
            }else{
                $this -> _ecpLogError($url, $data, $ecp_result);
                $back['data'] = $ecp_result['retmsg'];
            }
            return $back;
        }
        unset($result);  //解除引用绑定
        return array();
    }

    /**
     * 调接口成功记入日志
     * @param String $url 地址
     * @param array $data 传入值
     * @param array $result 返回值
     *
     */
    private function _ecpLogSucc($url, $data, $result)
    {
        $logger = Log::channel('ecp_log');
        $log_ecp_str = "";
        $log_ecp_str .= "url:".$url."|";//地址
        $log_ecp_str .= "request:".json_encode($data, JSON_UNESCAPED_UNICODE)."|";
        $log_ecp_str .= "response:".json_encode($result, JSON_UNESCAPED_UNICODE);
        $logger->info('submit ecp, '.$log_ecp_str);
    }

    /**
     * 调接口失败记入日志
     * @param String $url 地址
     * @param array $data 传入值
     * @param array $result 返回值
     *
     */
    private function _ecpLogError($url, $data, $result)
    {
        $logger = Log::channel('ecp_log');
        $log_ecp_str = "";
        $log_ecp_str .= "url:".$url."|";//地址
        $log_ecp_str .= "request:".json_encode($data, JSON_UNESCAPED_UNICODE)."|";
        $log_ecp_str .= "response:".json_encode($result, JSON_UNESCAPED_UNICODE);
        write_log(LOG_LEVEL_ERROR,"ecp_request_err",$log_ecp_str);
        $logger->error('submit ecp, '.$log_ecp_str);
    }
}
