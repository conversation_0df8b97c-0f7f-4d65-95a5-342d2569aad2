<?php

namespace App\Admin\Support;

use Encore\Admin\Facades\Admin;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class LogOpt
{

    public static function log($is_store = false, $opt_desc ='', $opt_data = [], $file_path = '') {

        $logger = Log::channel('optlog');

        $request = request();
        $request->setTrustedProxies($request->getClientIps(), \Illuminate\Http\Request::HEADER_X_FORWARDED_FOR);  //获取真实ip，需添加该语句。

        $log = [
            'user_id'   => Admin::user()->id,
            'path'      => substr($request->path(), 0, 255),
            'method'    => $request->method(),
            'ip'        => $request->getClientIp(),
            'file_name' => empty($file_path) ? '' : substr($file_path, strripos($file_path, '/')+1),
            'file_path' => $file_path,
            'opt_desc'  => $opt_desc,
            'opt_data'  => $opt_data,
        ];

        $logger->info(json_encode($log, JSON_UNESCAPED_UNICODE));

        if ($is_store) {
            unset($log['opt_data']);
            $log['file_path'] = substr($log['file_path'], strripos($log['file_path'], 'storage')+7);
            $log['created_at'] = date('Y-m-d H:i:s');
            DB::table('opt_logs')->insert($log);
        }
    }

}
