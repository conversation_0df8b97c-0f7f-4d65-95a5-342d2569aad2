<?php

/**
 * <PERSON><PERSON>-admin - admin builder based on <PERSON><PERSON>.
 * <AUTHOR> <https://github.com/z-song>
 *
 * Bootstraper for Admin.
 *
 * Here you can remove builtin form field:
 * Encore\Admin\Form::forget(['map', 'editor']);
 *
 * Or extend custom form field:
 * Encore\Admin\Form::extend('php', PHPEditor::class);
 *
 * Or require js and css assets:
 * Admin::css('/packages/prettydocs/css/styles.css');
 * Admin::js('/packages/prettydocs/js/main.js');
 *
 */

use Encore\Admin\Admin;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Show;
use App\Admin\Extensions\Form\CKEditor;
Form::extend('ckeditor', CKEditor::class);
//Form::forget(['map', 'editor']);
Form::extend('editor', \App\Admin\Extensions\WangEditor::class);
Form::extend('DateMultiple', \App\Admin\Extensions\DateMultiple::class);//因laravel-admin中的 DateMultiple 组件引用了cdn.jsdelivr.net中的内容，该cdn不是太稳定，因此覆盖其中的css和js引用。
//Form::extend('hasManyFix', \App\Admin\Extensions\HasMany::class);//修复Encore\Admin\Form\NestedForm中293行prepareRecord方法中的bug。
//Form::extend('multipleSelect', \App\Admin\Extensions\MultipleSelect::class);//修复$form->hasMany()中使用时，新增的数据默认值会取到上一条记录的对应Field值。

Admin::css('/admin_static/css/common.css');

Form::init(function (Form $form) {

    $form->disableEditingCheck();

    $form->disableCreatingCheck();

    $form->disableViewCheck();

    $form->tools(function (Form\Tools $tools) {
        $tools->disableDelete();
        $tools->disableView();
//        $tools->disableList();
    });
});

//Form::$availableFields['select'] = App\Admin\Extensions\Select::class;

Show::init(function (Show $show){
    $show->panel()->tools(function ($tools) {
        $tools->disableEdit();
        $tools->disableDelete();
    });
});

Grid::init(function (Grid $grid){

    $grid->disableBatchActions();

    $grid->model()->orderBy('id','desc');

    $grid->disableExport();

//    $grid->disableCreateButton();

    $grid->actions(function ($actions) {
        $actions->disableDelete();
//        $actions->disableEdit();
//        $actions->disableShow();
    });

    $grid->filter(function ($filter) {
//        $filter->expand();
        $filter->disableIdFilter();
    });
});
