<?php

use Encore\Admin\Facades\Admin;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Admin::routes();

Route::group([
    'prefix'     => config('admin.route.prefix'),
    'namespace'  => config('admin.route.namespace'),
    'middleware' => config('admin.route.middleware'),
], function (Router $router) {

    $router->get('/', 'HomeController@index')->name('admin.home');
    $router->get('orders/export-list', 'OrdersController@export_list');
    $router->get('orders/export', 'OrdersController@export');
    $router->get('download-optfile/{id}', 'OptLogController@download');
    $router->get('order-sub/order-deliver-view', 'OrderSubsController@order_deliver_view');
    $router->post('order-sub/do-order-deliver', 'OrderSubsController@do_order_deliver');
    $router->get('order-sub/order-deliver-service-view', 'OrderSubsController@order_deliver_service_view');
    $router->post('order-sub/do-order-deliver-service', 'OrderSubsController@do_order_deliver_service');
    $router->get('order-sub/export-download', 'OrderSubsController@export_download');
    $router->get('order-sub/logistics-import', 'OrderSubsController@logistics_import');
    $router->post('order-sub/do-logistics-import', 'OrderSubsController@do_logistics_import');
    $router->get('order-sub/service-import', 'OrderSubsController@service_import');
    $router->post('order-sub/do-service-import', 'OrderSubsController@do_service_import');
//    $router->get('order-sub/download-template', 'OrderSubsController@download_template');
//    $router->get('order-sub/download-service-template', 'OrderSubsController@download_service_template');

    $router->get('activity-prize/get-goods', 'ExchangeBatchController@getGoodsList')->name('exb-get-goods');//根据批次id获取该活动对应的商品列表
    $router->get('exchange-batches/get-goods-by-act', 'ExchangeBatchController@getGoodsByActivityId')->name('exb-get-goods-by-act');
    $router->get('exchange-batches/get-batches', 'ExchangeBatchController@getBatches')->name('exb-get-batches');//根据活动id获取该活动的批次列表
    $router->get('exchange-batches/goods-mgr/{batch_id}', 'ExchangeBatchGoodsController@goodsMgr')->name('exb-goods-mgr');
    $router->post('exchange-batches/goods-mgr/{batch_id}/submit', 'ExchangeBatchGoodsController@doSubmit')->name('exb-goods-mgr-submit');

    $router->get('exchange-details/export-list', 'ExchangeDetailController@export_list');
    $router->get('exchange-details/export', 'ExchangeDetailController@export');

    //禁用激活码
    $router->get('exchange-detail-forbidden/import', 'ExchangeDetailForbiddenController@forbidden_import');
    $router->post('exchange-detail-forbidden/do_import', 'ExchangeDetailForbiddenController@do_forbidden_import');

    //对外核销激活码
    $router->get('exch-hx/import', 'ExchangeDetailVerifyController@import');
    $router->post('exch-hx/do-import', 'ExchangeDetailVerifyController@do_import');
    $router->get('exch-hx/import-result', 'ExchangeDetailVerifyController@import_result');
    $router->get('exch-hx/download', 'ExchangeDetailVerifyController@download');
    $router->get('exch-hx/test_result', 'ExchangeDetailVerifyController@test_result');
    $router->get('exch-hx/settlement', 'ExchangeDetailVerifyController@settlement');
    $router->post('exch-hx/do-settlement', 'ExchangeDetailVerifyController@do_settlement');
    $router->get('exch-hx/cost-price', 'ExchangeDetailVerifyController@cost_price');
    $router->post('exch-hx/do-cost-price', 'ExchangeDetailVerifyController@do_cost_price');

    //明苑风华-天猫充值缓存管理
    $router->get('cache-mgr/tm', 'TmCacheController@index');
    $router->post('cache-mgr/tm/query', 'TmCacheController@doQueryCache_TM');//查询
    $router->post('cache-mgr/tm/do-opt-cache', 'TmCacheController@doOptCache_TM');//执行缓存操作
    $router->post('cache-mgr/tm/do-maam-cache', 'TmCacheController@doMAAM_TM');//手机号与淘宝账号对应关系限制处理
    $router->post('cache-mgr/tm/query-api-log', 'TmCacheController@doQueryApiLog_TM');//查询api日志操作
    $router->post('cache-mgr/sms-captcha/query', 'SmsCaptchaBlackDealController@doQuery');//查询短信验证码发送次数限制
    $router->post('cache-mgr/sms-captcha/do-opt', 'SmsCaptchaBlackDealController@doOpt');//

    //黑名单
    $router->get('bl/greylist/import', 'BL\GreyListController@bl_import');
    $router->post('bl/greylist/do-import', 'BL\GreyListController@bl_do_import');
    $router->post('bl/greylist/do-delete', 'BL\GreyListController@bl_do_delete');
    $router->get('bl/selects/project-products/{status?}', 'BL\ProjectProductListController@api_for_select');//根据项目id在项目商品关系表中筛选出产品
    $router->get('bl/records/settlement', 'BL\RecordController@settlement');//结算
    $router->post('bl/records/do-settlement', 'BL\RecordController@do_settlement');//结算

    //光大工会活动白名单导入
    $router->get('cebgh/import', 'CebGhUserController@import');
    $router->post('cebgh/do-import', 'CebGhUserController@do_import');

    $router->post('ckeditor/upload','CkeditorUploadController@uploadImage');


    $router->resources([
        'optlog'                     => OptLogController::class,
        'activity'                   => ActivitysController::class,
        'activity-prize'             => ActivityPrizesController::class,
        'activity-ads'               => ActivityAdController::class,
        'goods'                      => GoodsController::class,
        'orders'                     => OrdersController::class,
        'sub-order'                  => OrderSubsController::class,
        'trade-channel'              => TradeChannelsController::class,
        'trades'                     => TradesController::class,
        'sms-template'               => SmsTemplateController::class,
        'exchange-batches'           => ExchangeBatchController::class,
        'exchange-groups'            => ExchangeGroupController::class,
        'exchange-goods'             => ExchangeGoodsController::class,
        'exchange-details'           => ExchangeDetailController::class,
        'exchange-orders'            => ExchangeOrderController::class,
        'exchange-details-forbidden' => ExchangeDetailForbiddenController::class,
        'exch-hx'                    => ExchangeDetailVerifyController::class,//对外兑换码核销功能
        'customers'                  => CustomerController::class,
        'exchange-whitelist'         => ExchangeWhitelistController::class,
        'bl/api-settings'            => BL\ApiSettingController::class,
        'bl/projects'                => BL\ProjectController::class,
        'bl/products'                => BL\ProductController::class,
        'bl/grey-lists'              => BL\GreyListController::class,
        'bl/records'                 => BL\RecordController::class,
        'bl/record-list'             => BL\RecordListController::class,
        'bl/project-products'        => BL\ProjectProductController::class,
        'bl/project-product-list'    => BL\ProjectProductListController::class,
        'cebgh'                      => CebGhUserController::class,//光大工会线上视听会员活动
    ]);
});
