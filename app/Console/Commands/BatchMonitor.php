<?php

namespace App\Console\Commands;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Console\Command;
use EasyWeChat\Kernel\Messages\File;
use phpDocumentor\Reflection\Types\Collection;

/**
 * 码批次统计
 * 以企业微信的方式发送
 * @package App\Console\Commands
 */
class BatchMonitor extends Command
{
    protected $signature = 'ap:batch-monitor';

    protected $description = '统计每批码的使用情况';

    protected $logger;
    protected $weWork;
    protected $messenger;

    public function __construct()
    {
        parent::__construct();
        $this->logger    = Log::channel('batch_monitor');
        $config          = config('wechat.work.default');
        $this->weWork    = \EasyWeChat\Factory::work($config);
        $this->messenger = $this->weWork->messenger; // 获取 Messenger 实例

    }

    /**
     * @return void
     * 之前的统计方式
     * "select d.*,b.batch_desc,a.activity_name,b.created_at,(total-used-forbiddens-abandon) as notUse from (
     * select
     * exchange_batch_id,
     * count(*) as total,
     * sum(CASE status WHEN 3 THEN 1 ELSE 0 END) AS  used,
     * sum(CASE enable WHEN 0 THEN 1 ELSE 0 END) AS  forbiddens,
     * sum(CASE enable WHEN 2 THEN 1 ELSE 0 END) AS  abandon
     * from exchange_details
     * GROUP BY exchange_batch_id
     * ) d
     * left join exchange_batches as b on d.exchange_batch_id=b.id
     * left join activities as a on b.activity_id=a.id
     * where b.status=1
     * order by a.activity_name,b.id desc"
     */


    //青柠礼品兑换平台 10，
    //蒙商银行图书家政11，
    //爱奇艺兑换 14，
    //九一畅享 15，
    //泰隆银行权益兑换 19 ，
    //瑞幸咖啡 22，
    //京科联通爱奇艺 23，
    //京科网易云 28，
    //京科联通瑞幸咖啡 30
    // 农行天猫购物券 13,  保留2024-01-01 到现在的数据 2024-01-01 00:00:00
    public function handle()
    {
        try {
            $batch_info = DB::select("SELECT
    d.*,
    b.batch_desc,
    a.activity_name,
    b.created_at,
    (total-used-forbiddens-abandon) as notUse
FROM (
    SELECT
        exchange_batch_id,
        COUNT(*) as total,
        SUM(CASE status WHEN 3 THEN 1 ELSE 0 END) AS used,
        SUM(CASE enable WHEN 0 THEN 1 ELSE 0 END) AS forbiddens,
        SUM(CASE enable WHEN 2 THEN 1 ELSE 0 END) AS abandon
    FROM exchange_details
    GROUP BY exchange_batch_id
) d
LEFT JOIN exchange_batches as b ON d.exchange_batch_id = b.id
LEFT JOIN activities as a ON b.activity_id = a.id
WHERE b.status = 1
AND a.id NOT IN (10,11,14,15,19,22,23,28,30)
AND (
    (a.id = 13 AND b.created_at >= '2024-01-01 00:00:00')
    OR a.id != 13
)
ORDER BY a.activity_name, b.id DESC");

            if (count($batch_info) == 0) {
                throw new MyException('没有查到批次码的使用情况。');
            }

            $batch_info = collect($batch_info)->filter(function ($value, $key) {
                return !empty($value->activity_name);
            });
            $batch_info->all();

            foreach ($batch_info as $k => $v) {

                //核销率 = 核销/核销+已兑换
                //rate = forbiddens/forbiddens+used
                if ($v->forbiddens + $v->used == 0) {
                    $batch_info[$k]->rate = 0.00;
                } else {
                    $yhx                  = $v->forbiddens + $v->used;
                    $batch_info[$k]->rate = number_format($v->forbiddens / $yhx, 2);
                }

            }

            $file_name = date('YmdHis') . '兑换码使用情况.csv';
            $file_path = storage_path() . '/app/exchange/batch/' . $file_name;

            $title = ['活动', '批次描述', '批次创建时间', '总数', '已兑换', '未使用', '核销', '核销率'];

            $csv_title = chr(0xEF) . chr(0xBB) . chr(0xBF);
            $csv_title .= implode(',', $title) . "\r\n";
            $fp        = fopen($file_path, 'w');
            fputs($fp, $csv_title, strlen($csv_title));
            $str = chr(0xEF) . chr(0xBB) . chr(0xBF);
            foreach ($batch_info as $val) {
                $total = $val->total - $val->abandon; //总数 = 总数 - 作废
                $str   .= $val->activity_name . ',' . $val->batch_desc . ',' .
                    $val->created_at . ',' . $total . ',' .
                    $val->used . ',' . $val->notUse . ',' .
                    $val->forbiddens . ',' . $val->rate . "\r\n";
            }
            fputs($fp, $str, strlen($str));
            fclose($fp);

            //上传临时素材
            $resp_array = $this->weWork->media->uploadFile($file_path);

            //发送文件消息
            if ($resp_array['errcode'] == 0) {
                $media_id = $resp_array['media_id'];
                $message  = new File($media_id);
//                $alert_to = "yangyong";
                $alert_to = config("wechat.work.alert.to_tag");
                $this->sendAlert($alert_to, $message);
            }


        } catch (\Exception $exc) {
            $msg = $exc->getMessage();
            $this->logger->error($msg);
            $this->logger->error($exc->getTraceAsString());
        }
    }


    private function sendAlert($tags, $message)
    {
        $this->messenger->message($message)->toTag($tags)->send();
//        $this->messenger->message($message)->toUser($tags)->send();
    }
}
