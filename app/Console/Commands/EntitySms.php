<?php

/**
 * 在兑换实物商品的时候 需要发送一下短信
 */

namespace App\Console\Commands;

use App\Exceptions\MyException;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Api\SysCode\SysCode;
use GuzzleHttp\Client;
use Exception;
use Carbon\Carbon;
use App\Models\Order;

use function GuzzleHttp\json_decode;

// use Exception;

/**
 * 订单提交
 *
 */
class EntitySms extends Command
{

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ap:send-entity-message';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '发送实物订单短信';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $logger = Log::channel('send_entity_sms');
        try {
            $min_id     = 0;
            $once_limit = 200;
            //首先查出符合条件的最小id
            $min_id_obj = self::getMinId();
            if (count($min_id_obj) > 0) {
                //取得最小id值
                $min_id = $min_id_obj[0]->id - 1;
            } else {
                throw new MyException('没有可以提交的订单');
            }
            //释放内存
            unset($min_id_obj);
            $orders = self::getTasks($min_id, $once_limit);
            //判断是否有符合条件的订单
            while (count($orders) > 0) {
                //TODO
                //开始给业务系统推送这批订单
                $min_id = self::startSend($orders, $logger);
                //处理完上一批,再获取下一批符合条件的订单
                $orders = self::getTasks($min_id, $once_limit);
            }
        } catch (\Exception $exc) {
            $msg = $exc->getMessage();
            $logger->info($msg);
        }
    }

    /**
     * 获取要推送的任务
     */
    private static function getTasks($min_id, $once_limit)
    {
        //IS_SEND_MSG_1 代表需要发送短信的实物订单
        return Order::where('order_notify_sms_status', SysCode::IS_SEND_MSG_1)
            ->where('id', '>', $min_id)
            ->orderBy('id')
            ->limit($once_limit)
            ->get();
    }

    /**
     * 获取最小id
     */
    private static function getMinId()
    {
        return Order::where('order_notify_sms_status', SysCode::IS_SEND_MSG_1)
            ->orderBy('id')
            ->limit(1)
            ->get();
    }

    /**
     * 开始推送
     */
    private static function startSend($orders, $logger)
    {
        foreach ($orders as $order) {
            $min_id      = $order->id;
            $smsTemplate = env('ENTITY_SMS_TEMPLATE');

            $content = str_replace_brackets($smsTemplate, ['goods_name' => $order->goods_name]);
            $logger->info('send sms content: '.$order->user_mobile.', content: '.$content);
            $send_result = self::sendSms($order->user_mobile, $content);
            $logger->info('send sms result : '.$send_result);

            $smsResult = json_decode($send_result, true);
            if (!empty($smsResult) && ($smsResult['result'] == 0)) {
                $order->order_notify_sms_status   = SysCode::IS_SEND_MSG_2;
                $order->order_notify_sms_req_time = Carbon::now()->format("Y-m-d H:i:s");
                $order->sms_gateway_seq           = $smsResult['gateway_seq'];
            } else {
                $order->order_notify_sms_status   = SysCode::IS_SEND_MSG_4;
                $order->order_notify_sms_req_time = Carbon::now()->format("Y-m-d H:i:s");
            }

            $order->save();

        }

        return $min_id;
    }


    /**
     * 发送短信
     *
     * @param $mobile
     * @param $content
     *
     * @return mixed
     */
    private static function sendSms($mobile, $content)
    {
        $timestamp = time();

        if (mb_substr($content, 0, 1) != '【') {
            $content = env('SMS_SIGN').$content;
        }

        $requestArgs = array(
            'uid'       => env('SMS_UID'),
            "dest"      => $mobile,
            "content"   => $content,
            "timestamp" => $timestamp,
            "secret"    => md5(env('SMS_UID').$mobile.$content.$timestamp.env('SMS_SECRET_KEY'))
        );

        return self::sendCurl(env('SMS_URL'), $requestArgs);
    }

    /**
     * 发送curl 请求
     *
     * @param $url
     * @param  array  $params
     * @param  string  $mode
     * @param  int  $time
     *
     * @return mixed
     */
    private static function sendCurl($url, array $params = array(), $mode = 'post', $time = 30)
    {
        $curlHandle = curl_init();
        curl_setopt($curlHandle, CURLOPT_TIMEOUT, $time); //最大响应时间
        curl_setopt($curlHandle, CURLOPT_RETURNTRANSFER, true);
        if ($mode == 'post') {
            curl_setopt($curlHandle, CURLOPT_HTTPHEADER, array('Content-Type: application/x-www-form-urlencoded'));
            curl_setopt($curlHandle, CURLOPT_POST, true);
            curl_setopt($curlHandle, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_0); //强制使用哪个版本
            curl_setopt($curlHandle, CURLOPT_POSTFIELDS, http_build_query($params));
        } else {
            $url .= (strpos($url, '?') === false ? '?' : '&').http_build_query($params);
        }
        curl_setopt($curlHandle, CURLOPT_URL, $url);
        if (substr($url, 0, 5) == 'https') {
            curl_setopt($curlHandle, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($curlHandle, CURLOPT_SSL_VERIFYHOST, false);
        }

        $result = curl_exec($curlHandle);
        curl_close($curlHandle);
        return $result;
    }
}
