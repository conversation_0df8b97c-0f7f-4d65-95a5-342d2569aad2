<?php

namespace App\Console\Commands;

use App\Exceptions\MyException;
use App\Models\ExchangeDetail;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Api\SysCode\SysCode;
use GuzzleHttp\Client;
use Exception;
use Carbon\Carbon;
use App\Models\Order;

use function GuzzleHttp\json_decode;

// use Exception;

/**
 * 兑换码过期时间处理
 *
 */
class ExchangeExpired extends Command
{

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ap:exchange-expired';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '兑换码过期时间处理';

    protected $min_id = 0;//任务的最小id

    protected $logger;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
        $this->logger = Log::channel('exchange_log');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        try {

            $this->getMinId();
            $this->dealTask();

        } catch (\Exception $exc) {
            $msg = $exc->getMessage();
            $this->logger->info($msg);
        }
    }

    /**
     * 获取最小id
     */
    private function getMinId() {
        $detail = ExchangeDetail::where('expired', ExchangeDetail::EXPIRED_NO)
            ->whereIn('status', [ExchangeDetail::STATUS_NO_EXCHANGE, ExchangeDetail::STATUS_EXCHANGEING])
            ->where('endtime', '<', Carbon::now()->startOfDay())
            ->orderBy('id')
            ->first();

        if ($detail) {
            $this->min_id = $detail->id;
        } else {
            throw new MyException('没有过期的兑换码。');
        }

    }

    /**
     * 开始推送
     */
    private function dealTask() {

        if ($this->min_id) {

            $this->logger->info('deal expired exchange-detail.min-id:' . $this->min_id);

            $result = DB::table('exchange_details')
                ->where('expired', ExchangeDetail::EXPIRED_NO)
                ->whereIn('status', [ExchangeDetail::STATUS_NO_EXCHANGE, ExchangeDetail::STATUS_EXCHANGEING])
                ->where('endtime', '<', Carbon::now()->startOfDay())
                ->update(['expired' => ExchangeDetail::EXPIRED_YES]);

            $this->logger->info('deal expired exchange-detail.result:' . $result);
        }

    }

}
