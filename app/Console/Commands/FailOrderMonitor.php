<?php

namespace App\Console\Commands;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Console\Command;
use EasyWeChat\Kernel\Messages\File;
use phpDocumentor\Reflection\Types\Collection;
use Carbon\Carbon;

/**
 * 订单失败比例监控
 * 以企业微信的方式发送
 * @package App\Console\Commands
 */
class FailOrderMonitor extends Command
{
    protected $signature = 'ap:fail-order-monitor';

    protected $description = '统计一定时间段内失败比例';

    protected $logger;
    protected $weWork;
    protected $messenger;

    public function __construct()
    {
        parent::__construct();
        $this->logger    = Log::channel('batch_monitor');
        $config          = config('wechat.work.default');
        $this->weWork    = \EasyWeChat\Factory::work($config);
        $this->messenger = $this->weWork->messenger; // 获取 Messenger 实例

    }

    public function handle()
    {
        try {
            $minutes_ago = Carbon::now()->subMinutes(config('console.minutes_ago'));
//            $orders_info = DB::select("select ecp_pcode,
//sum(CASE status when 4 THEN 1 ELSE 0 END) AS fail,
//count(*) as total_num
//from order_subs where created_at > $minutes_ago and ecp_pcode<> '' GROUP BY ecp_pcode;
//");

            $orders_info = DB::select("select ecp_pcode,goods_name,
sum(CASE status when 4 THEN 1 ELSE 0 END) AS fail,
count(*) as total_num
from order_subs where ecp_pcode<> '' GROUP BY ecp_pcode,goods_name;
");

            if (count($orders_info) <= 0) {
                throw new MyException('该时间段没有订单。');
            }

//            $orders_info = collect($orders_info)->filter(function ($value, $key) {
//                return !empty($value->activity_name);
//            });
//            $orders_info->all();

            foreach ($orders_info as $k => $v) {
                if ($v->fail + $v->total_num == 0) {
                    $orders_info[$k]->rate = 0.00;
                } else {
                    $orders_info[$k]->rate = number_format($v->fail / $v->total_num, 2);
                }

            }

            $orders_info = collect($orders_info)->filter(function ($value, $key) {
                //失败率大于配置将会报警
                return $value->rate > config('consoles.fail_rate');
            });
            $orders_info->all();

            if (count($orders_info) <= 0) {
                throw new MyException('没有符合条件的订单。');
            }


            $file_name = date('YmdHis') . '兑换码使用情况.csv';
            $file_path = storage_path() . '/app/exchange/batch/' . $file_name;

//            $title = ['活动', '商品名称', 'pcode', '总数', '失败', '失败率'];
            $title = ['商品名称', 'pcode', '总数', '失败', '失败率'];

            $csv_title = chr(0xEF) . chr(0xBB) . chr(0xBF);
            $csv_title .= implode(',', $title) . "\r\n";
            $fp        = fopen($file_path, 'w');
            fputs($fp, $csv_title, strlen($csv_title));
            $str = chr(0xEF) . chr(0xBB) . chr(0xBF);
            foreach ($orders_info as $val) {
                $str .= $val->goods_name . ',' . $val->ecp_pcode . ',' .
                    $val->total_num . ',' . $val->fail . ',' .
                    $val->rate . "\r\n";
            }
            fputs($fp, $str, strlen($str));
            fclose($fp);

            //上传临时素材
            $resp_array = $this->weWork->media->uploadFile($file_path);

            //发送文件消息
            if ($resp_array['errcode'] == 0) {
                $media_id = $resp_array['media_id'];
                $message  = new File($media_id);
                $alert_to = "yangyong";
//                $alert_to = config("wechat.work.alert.to_tag");
                $this->sendAlert($alert_to, $message);
            }


        } catch (\Exception $exc) {
            $msg = $exc->getMessage();
            $this->logger->error($msg);
            $this->logger->error($exc->getTraceAsString());
        }
    }


    private function sendAlert($tags, $message)
    {
//        $this->messenger->message($message)->toTag($tags)->send();
        $this->messenger->message($message)->toUser($tags)->send();
    }
}
