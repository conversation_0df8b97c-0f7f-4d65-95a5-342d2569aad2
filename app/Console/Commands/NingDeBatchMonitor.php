<?php

namespace App\Console\Commands;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Console\Command;
use EasyWeChat\Kernel\Messages\File;
use phpDocumentor\Reflection\Types\Collection;

/**
 * 宁德专用
 * 码批次统计
 * 以企业微信的方式发送
 * @package App\Console\Commands
 * 备份--
 */
class NingDeBatchMonitor extends Command
{
    protected $signature = 'ap:ningde-batch-monitor';

    protected $description = '统计宁德每批码的使用情况';

    protected $logger;
    protected $weWork;
    protected $messenger;

    public function __construct()
    {
        parent::__construct();
        $this->logger    = Log::channel('ningde_monitor');
        $config          = config('wechat.work.default');
        $this->weWork    = \EasyWeChat\Factory::work($config);
        $this->messenger = $this->weWork->messenger; // 获取 Messenger 实例

    }

    public function handle()
    {
        try {
            $batch_info = DB::select("select d.*,b.batch_desc,a.activity_name,b.created_at,(total-used-forbiddens) as notUse from (
select
exchange_batch_id,
count(*) as total,
sum(CASE status WHEN 3 THEN 1 ELSE 0 END) AS  used,
sum(CASE enable WHEN 0 THEN 1 ELSE 0 END) AS  forbiddens
from exchange_details
GROUP BY exchange_batch_id
) d left join exchange_batches as b on d.exchange_batch_id=b.id left join activities as a on b.activity_id=a.id and b.status=1
 order by a.activity_name,b.id desc");

            if (count($batch_info) == 0) {
                throw new MyException('没有查到批次码的使用情况。');
            }

            $batch_info = collect($batch_info)->filter(function ($value, $key) {
                return !empty($value->activity_name);
            });
            $batch_info->all();


            $zs = DB::select("select count(*) as f,exchange_batch_id from exchange_detail_forbiddens where created_by=6 GROUP BY exchange_batch_id");

            foreach ($zs as $v) {
                $aa[$v->exchange_batch_id] = $v->f;
                $bb[]                      = $v->exchange_batch_id;
            }

            //

            foreach ($batch_info as $k => $v) {

                //核销率 = 核销/核销+已兑换
                //rate = forbiddens/forbiddens+used
                if ($v->forbiddens + $v->used == 0) {
                    $batch_info[$k]->rate = 0.00;
                } else {

//                    $yhx                  = $v->forbiddens + $v->used;
//                    $batch_info[$k]->rate = number_format($v->forbiddens / $yhx, 2);
                    if (in_array($v->exchange_batch_id, $bb)) {
                        $yhx                  = $v->forbiddens + $v->used - $aa[$v->exchange_batch_id];
                        $batch_info[$k]->rate = number_format(($v->forbiddens - $aa[$v->exchange_batch_id]) / $yhx, 2);
                    } else {
                        $yhx                  = $v->forbiddens + $v->used;
                        $batch_info[$k]->rate = number_format($v->forbiddens / $yhx, 2);
                    }

                }

            }

//            var_dump($batch_info);


            $file_name = date('YmdHis') . '兑换码使用情况.csv';
            $file_path = storage_path() . '/app/exchange/batch/' . $file_name;

            $title = ['活动', '批次描述', '批次创建时间', '总数', '已兑换', '未使用', '核销', '核销率'];

            $csv_title = chr(0xEF) . chr(0xBB) . chr(0xBF);
            $csv_title .= implode(',', $title) . "\r\n";
            $fp        = fopen($file_path, 'w');
            fputs($fp, $csv_title, strlen($csv_title));
            $str = chr(0xEF) . chr(0xBB) . chr(0xBF);
            foreach ($batch_info as $val) {
                $str .= $val->activity_name . ',' . $val->batch_desc . ',' .
                    $val->created_at . ',' . $val->total . ',' .
                    $val->used . ',' . $val->notUse . ',' .
                    $val->forbiddens . ',' . $val->rate . "\r\n";
            }
            fputs($fp, $str, strlen($str));
            fclose($fp);

//            //上传临时素材
//            $resp_array = $this->weWork->media->uploadFile($file_path);
//
//            //发送文件消息
//            if ($resp_array['errcode'] == 0) {
//                $media_id = $resp_array['media_id'];
//                $message  = new File($media_id);
////                $alert_to = "yangyong";
//                $alert_to = config("wechat.work.alert.to_tag");
//                $this->sendAlert($alert_to, $message);
//            }


        } catch (\Exception $exc) {
            $msg = $exc->getMessage();
            $this->logger->error($msg);
            $this->logger->error($exc->getTraceAsString());
        }
    }


    private function sendAlert($tags, $message)
    {
        $this->messenger->message($message)->toTag($tags)->send();
//        $this->messenger->message($message)->toUser($tags)->send();
    }
}
