<?php

namespace App\Console\Commands;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Console\Command;
use EasyWeChat\Kernel\Messages\File;
use phpDocumentor\Reflection\Types\Collection;
use Carbon\Carbon;
use App\Exceptions\MyException;

/**
 * 宁德专用
 * 昨天核销数量统计
 * 以企业微信的方式发送
 * @package App\Console\Commands
 */
class NingDeCount extends Command
{
    protected $signature = 'ap:ningde-count';

    protected $description = '统计宁德核销数量';

    protected $logger;
    protected $weWork;
    protected $messenger;
    protected $yesterday_begin;
    protected $yesterday_end;

    public function __construct()
    {
        parent::__construct();
        $this->logger          = Log::channel('ningde_count');
        $config                = config('wechat.work.default');
        $this->weWork          = \EasyWeChat\Factory::work($config);
        $this->messenger       = $this->weWork->messenger; // 获取 Messenger 实例
        $this->yesterday_begin = Carbon::yesterday()->toDateTimeString();
        $this->yesterday_end   = Carbon::yesterday()->format('Y-m-d 23:59:59');
    }

    public function handle()
    {
        try {

            // 宁德部分
            $confirm    = $this->ningDe();
            $users_info = DB::table("admin_users")->pluck('username', 'id')->toArray();

            $file_name = date('YmdHis') . '兑换码核销数量.csv';
            $file_path = storage_path() . '/app/exchange/batch/' . $file_name;

            $title = ['用户', '昨日核销数量', '总核销数量'];

            $csv_title = chr(0xEF) . chr(0xBB) . chr(0xBF);
            $csv_title .= implode(',', $title) . "\r\n";
            $fp        = fopen($file_path, 'w');
            fputs($fp, $csv_title, strlen($csv_title));
            $str = chr(0xEF) . chr(0xBB) . chr(0xBF);
            foreach ($confirm as $key => $val) {
                $confirm_num       = isset($val['confirm_num']) ? $val['confirm_num'] : '0';
                $total_confirm_num = isset($val['total_confirm_num']) ? $val['total_confirm_num'] : '0';
                $str               .= $users_info[$key] . ',' . $confirm_num . ',' . $total_confirm_num . "\r\n";
            }
            fputs($fp, $str, strlen($str));
            fputs($fp, ",,,\r\n", strlen($str)); //空行
            fputs($fp, ",,,\r\n", strlen($str)); //空行
            // 黑名单部分
            $b_title  = ['项目名称', '产品名称', '昨日订单数量', '总订单数量'];
            $lj_title = chr(0xEF) . chr(0xBB) . chr(0xBF);
            $lj_title .= implode(',', $b_title) . "\r\n";
            fputs($fp, $lj_title, strlen($lj_title));
            $b_str = chr(0xEF) . chr(0xBB) . chr(0xBF);

            $bl_result = $this->blackList();

            foreach ($bl_result as $value) {
                $b_str .= $value->project_name . ',' . $value->product_name . ',' . $value->yesterday . ',' . $value->total_num . "\r\n";
            }
            fputs($fp, $b_str, strlen($b_str));

            fclose($fp);

            //上传临时素材
            $resp_array = $this->weWork->media->uploadFile($file_path);

            //发送文件消息
            if ($resp_array['errcode'] == 0) {
                $media_id = $resp_array['media_id'];
                $message  = new File($media_id);
//                $alert_to = "yangyong";
                $alert_to = config("wechat.work.alert.to_tag");
                $this->sendAlert($alert_to, $message);
            }


        } catch (\MyException $exc) {
            $msg = $exc->getMessage();
            $this->logger->error($msg);
            $this->logger->error($exc->getTraceAsString());
        }
    }


    private function sendAlert($tags, $message)
    {
        $this->messenger->message($message)->toTag($tags)->send();
//        $this->messenger->message($message)->toUser($tags)->send();
    }


    // 统计宁德
    private function ningDe()
    {
        $result_yesterday = DB::table('exchange_detail_forbiddens')->whereBetween('created_at', [$this->yesterday_begin, $this->yesterday_end])
            ->select('created_by',
                DB::raw('count(*) as confirm_num')
            )->groupBy("created_by")->get();

        $total_confirm = DB::table('exchange_detail_forbiddens')->where('created_at', '<=', $this->yesterday_end)
            ->select('created_by',
                DB::raw('count(*) as total_confirm_num')
            )->groupBy("created_by")->get();

        $confirm = [];

        foreach ($total_confirm as $v) {
            $confirm[$v->created_by]['total_confirm_num'] = $v->total_confirm_num;
        }
        foreach ($result_yesterday as $v) {
            $confirm[$v->created_by]['confirm_num'] = $v->confirm_num;
        }
        return $confirm;
    }

    private function blackList()
    {
        $result_yesterday = DB::table('bl_records')
            ->leftJoin('bl_projects', 'bl_records.project_id', '=', 'bl_projects.id')
            ->leftJoin('bl_products', 'bl_records.product_id', '=', 'bl_products.id')
            ->whereBetween('bl_records.created_at', [$this->yesterday_begin, $this->yesterday_end])
            ->select('bl_projects.project_name', 'bl_products.product_name',
                DB::raw('count(*) as yesterday_num')
            )->groupBy("bl_projects.project_name")
            ->groupBy('bl_products.product_name')
            ->get();

        $result_total = DB::table('bl_records')
            ->leftJoin('bl_projects', 'bl_records.project_id', '=', 'bl_projects.id')
            ->leftJoin('bl_products', 'bl_records.product_id', '=', 'bl_products.id')
            ->select('bl_projects.project_name', 'bl_products.product_name',
                DB::raw('count(*) as total_num')
            )->groupBy("bl_projects.project_name")
            ->groupBy('bl_products.product_name')
            ->get();

        $bl_result = [];

        foreach ($result_yesterday as $v) {
            $bl_result[$v->project_name][$v->product_name] = $v->yesterday_num;
        }

        return collect($result_total)->map(function ($value, $key) use ($bl_result) {
            $value->yesterday = isset($bl_result[$value->project_name][$value->product_name]) ? $bl_result[$value->project_name][$value->product_name] : 0;
            return $value;
        });
    }
}
