<?php
/**
 * Describe:筛选要发送短信的订单,
 * 生成短信模板插入到sms_results表中,待发送
 */

namespace App\Console\Commands;

use Carbon\Carbon;
use App\Models\OrderSub;
use App\Models\Activity;
use App\Models\SmsResult;
use App\Models\SmsTemplate;
use App\Exceptions\MyException;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Api\SysCode\SysCode;

class OrderToSmsResult extends SignalAndLockedBaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ap:order-to-sms_results';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '将要发送短信的订单组织好插入到sms_results表中';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
        $this->lock_ttl = 3600;
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handleBusiness() {
        $logger = Log::channel('order_to_sms_result');
        try {
            // 1: 启用状态：COMMON_STATUS_1
            $sms_template = SmsTemplate::where('status', SysCode::COMMON_STATUS_1)->get()->toArray();
            if (count($sms_template) <= 0) {
                throw new MyException('未配置短信模板!');
            }
            foreach ($sms_template as $k => $v) {

                $this->dealSignal();//处理信号

                $this->dealCacheTimeout();

                $identity_arr = explode(',', $v['identity']);
                if (empty($identity_arr) || count($identity_arr) < 5) {
                    $logger->info('sms_template表中identity值配置错误');
                    continue;
                }
                $min_id     = 0;
                $once_limit = 200;
                //首先查出符合条件的最小id
                $min_id_obj = $this->getMinId($identity_arr);
                if (count($min_id_obj) > 0) {
                    //取得最小id值
                    $min_id = $min_id_obj[0]->id - 1;
                } else {
                    continue;
                }
                //释放内存
                unset($min_id_obj);
                $orders = $this->getTasks($min_id, $once_limit, $identity_arr);
                //判断是否有符合条件的订单
                while (count($orders) > 0) {
                    $min_id = $this->startHandle($orders, $logger, $v);

                    $this->dealSignal();

                    $this->dealCacheTimeout();

                    //处理完上一批,再获取下一批符合条件的订单
                    $orders = $this->getTasks($min_id, $once_limit, $identity_arr);
                }
            }

        } catch (MyException $exc) {
            $msg = $exc->getMessage();
            $logger->info($msg);
        }
    }

    /**
     * 获取最小id
     */
    private function getMinId($identity_arr) {
        //$template_info  [0]=>表名 [1]=>activity_id [2]=>goods_type [3]=>order_status [4]=>是否发送卡号
        //当配置了扩展信息的时候
        if (!empty($identity_arr[5])) {
            //icbc_star_points.tran_type.1
            //$identity_ext_info[0]:icbc_star_points //表名
            //$identity_ext_info[1]:tran_type //字段名
            //$identity_ext_info[2]:1 //字段值
            $identity_ext_info = explode('.', $identity_arr[5]);
            return DB::table('orders')->leftJoin('order_subs', 'orders.id', '=', 'order_subs.order_id')
                ->leftJoin($identity_ext_info[0], 'orders.id', '=', $identity_ext_info[0] . '.order_id')
                ->select(['order_subs.id'])
                ->where([
                    'orders.activity_id'                                => $identity_arr[1],//活动id
                    'goods_type'                                        => $identity_arr[2],//商品类型
                    'order_subs.status'                                 => $identity_arr[3],//订单状态
                    'is_show_card_no'                                   => $identity_arr[4],//是否发送卡号
                    'order_subs.sms_status'                             => 1,// 1:短信状态未知
                    $identity_ext_info[0] . '.' . $identity_ext_info[1] => $identity_ext_info[2], //
                ])
                ->orderBy('order_subs.id')
                ->limit(1)
                ->get();

        } else {
            return DB::table('orders')->leftJoin('order_subs', 'orders.id', '=', 'order_subs.order_id')
                ->select(['order_subs.id'])
                ->where([
                    'orders.activity_id'    => $identity_arr[1],//活动id
                    'goods_type'            => $identity_arr[2],//商品类型
                    'order_subs.status'     => $identity_arr[3],//订单状态
                    'is_show_card_no'       => $identity_arr[4],//是否发送卡号
                    'order_subs.sms_status' => 1,// 1:短信状态未知
                ])
                ->orderBy('order_subs.id')
                ->limit(1)
                ->get();
        }

    }

    /**
     * 获取要执行的任务
     */
    private function getTasks($min_id, $once_limit, $identity_arr) {

        if (!empty($identity_arr[5])) {
            $identity_ext_info = explode('.', $identity_arr[5]);

            //$template_info  [0]=>表名 [1]=>activity_id [2]=>goods_type [3]=>order_status [4]=>是否发送卡号
            return DB::table('orders')->leftJoin('order_subs', 'orders.id', '=', 'order_subs.order_id')
                ->leftJoin($identity_ext_info[0], 'orders.id', '=', $identity_ext_info[0] . '.order_id')
                ->select(
                    'orders.id',
                    'order_subs.id as sub_id',
                    'order_subs.goods_type',
                    'order_subs.goods_name',
                    'order_subs.order_no',
                    'order_subs.sub_order_no',
                    'order_subs.user_mobile',
                    'order_subs.charge_account',
                    'order_subs.consignee_name',
                    'order_subs.consignee_phone',
                    'order_subs.consignee_address',
                    'order_subs.activation_code',
                    'order_subs.sequence_no',
                    'order_subs.endtime',
                    'order_subs.is_show_card_no',
                    'order_subs.created_at',
                    'order_subs.logistics_company',
                    'order_subs.logistics_sn'
                )->where([
                    'orders.activity_id'                                => $identity_arr[1],
                    'goods_type'                                        => $identity_arr[2],
                    'order_subs.status'                                 => $identity_arr[3],
                    'is_show_card_no'                                   => $identity_arr[4],//是否发送卡号
                    'order_subs.sms_status'                             => 1, //短信状态 1: 未知 (未处理)
                    $identity_ext_info[0] . '.' . $identity_ext_info[1] => $identity_ext_info[2], //
                ])->where('order_subs.id', '>', $min_id)
                ->orderBy('order_subs.id')
                ->limit($once_limit)
                ->get();
        } else {
            //$template_info  [0]=>表名 [1]=>activity_id [2]=>goods_type [3]=>order_status [4]=>是否发送卡号
            return DB::table('orders')->leftJoin('order_subs', 'orders.id', '=', 'order_subs.order_id')
                ->select(
                    'orders.id',
                    'order_subs.id as sub_id',
                    'order_subs.goods_type',
                    'order_subs.goods_name',
                    'order_subs.order_no',
                    'order_subs.sub_order_no',
                    'order_subs.user_mobile',
                    'order_subs.charge_account',
                    'order_subs.consignee_name',
                    'order_subs.consignee_phone',
                    'order_subs.consignee_address',
                    'order_subs.activation_code',
                    'order_subs.sequence_no',
                    'order_subs.endtime',
                    'order_subs.is_show_card_no',
                    'order_subs.created_at',
                    'order_subs.logistics_company',
                    'order_subs.logistics_sn'
                )->where([
                    'orders.activity_id'    => $identity_arr[1],
                    'goods_type'            => $identity_arr[2],
                    'order_subs.status'     => $identity_arr[3],
                    'is_show_card_no'       => $identity_arr[4],//是否发送卡号
                    'order_subs.sms_status' => 1, //短信状态 1: 未知 (未处理)
                ])->where('order_subs.id', '>', $min_id)
                ->orderBy('order_subs.id')
                ->limit($once_limit)
                ->get();
        }

    }

    /**
     * 开始处理
     */

    private function startHandle($orders, $logger, $template_info) {

        $sms_identity = [];

        foreach ($orders as $k => $v) {

            $this->dealSignal();

            $this->dealCacheTimeout();

            $activity_name = Activity::where('id', $template_info['category_id'])->value('activity_name');
            if (empty($activity_name)) {
                $log_data = [
                    'activity_id' => $template_info['category_id']
                ];
                throw new MyException('活动名称错误。', 500, null, $data = $log_data);
            }
            $min_id                    = $v->sub_id;
            $vars['activation_code']   = '';
            $vars['sequence_no']       = '';
            $vars['endtime']           = '';
            $vars['activity_name']     = '';
            $vars['sub_order_no']      = '';
            $vars['created_at']        = '';
            $vars['logistics_company'] = '';
            $vars['logistics_sn']      = '';
            $vars['goods_name']        = '';
            $vars['charge_account']    = '';
            $vars['consignee_name']    = '';
            $vars['consignee_phone']   = '';
            $vars['consignee_address'] = '';

            $vars['activation_code']   = $v->activation_code;
            $vars['sequence_no']       = $v->sequence_no;
            $vars['endtime']           = $v->endtime;
            $vars['activity_name']     = $activity_name;  //todo
            $vars['sub_order_no']      = $v->sub_order_no;
            $vars['created_at']        = date("Y-m-d H:i", strtotime($v->created_at));
            $vars['logistics_company'] = $v->logistics_company;
            $vars['logistics_sn']      = $v->logistics_sn;
            $vars['goods_name']        = $v->goods_name;
            $vars['charge_account']    = $v->charge_account;
            $vars['consignee_name']    = $v->consignee_name;
            $vars['consignee_phone']   = $v->consignee_phone;
            $vars['consignee_address'] = $v->consignee_address;

            $sms_content = $this->smsTempVarReplace($template_info['content'], $vars, $sms_identity, $v);

            $this->sms_create($v, $sms_content, $sms_identity, $template_info['content']);


        }

        return $min_id;

    }


    //模板替换
    private function smsTempVarReplace($smsTemplate, $vars, $sms_identity, $order_sub) {
        $sms_content = str_replace_brackets($smsTemplate, $vars);
        if (empty($sms_content)) {
            $log_data = [
                'order_sub_id' => $order_sub->sub_id,
                'message'      => '',
                'sms_identity' => $sms_identity,
                'sms_template' => $smsTemplate,
                'vars'         => $vars
            ];

            throw new MyException('短信内容变量替换失败。', 500, null, $data = $log_data);
        }
        return $sms_content;
    }


    private function sms_create($order_sub, $content, $sms_identity, $sms_template) {

        //发短信
        $sms                = new SmsResult();
        $sms->mobile        = $order_sub->user_mobile;
        $sms->content       = $content;
        $sms->order_id      = $order_sub->id;
        $sms->order_no      = $order_sub->order_no;
        $sms->order_item_id = $order_sub->sub_id;
        $sms->order_item_no = $order_sub->sub_order_no;
        $sms->status        = 1; //待发送
        $sms->created_at    = Carbon::now()->format('Y-m-d H:i:s');
        $sms->save();

        //更新 order_subs表中的短信状态
        //4 为处理中
        OrderSub::where('id', $order_sub->sub_id)->update(['sms_status' => 4]);

        $log_data = [
            'order_sub_id' => $order_sub->id,
            'mobile'       => $sms->mobile,
            'message'      => '插入短信表',
            'content'      => $content,
            'sms_identity' => $sms_identity,
        ];

        Log::channel('order_to_sms_result')->info(json_encode($log_data,
            JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE));
    }
}
