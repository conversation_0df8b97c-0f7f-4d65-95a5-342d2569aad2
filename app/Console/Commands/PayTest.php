<?php

namespace App\Console\Commands;

use App\Http\Controllers\Api\SysCode\SysCode;
use App\Libraries\OrderUtils;
use App\Models\ExchangeBatch;
use App\Models\ExchangeGoods;
use App\Models\ExchangeGroup;
use App\Models\Goods;
use App\Models\GoodsCombin;
use App\Models\OrderSub;
use App\Service\RedisLimitUtils;
use App\Supports\PayRequestParameter;
use App\Supports\PayService;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;


class PayTest extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:pay-request';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '测试';

    protected $redis;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        try {
            $exchange_batch = ExchangeBatch::where('id', 40)->with(['exchange_groups'])->first();
            dd($exchange_batch);

            $exchange_group = ExchangeGroup::where('id', 1)->with(['exchange_batch', 'exchange_goods'])->first();
            var_dump($exchange_group);
            return;

            var_dump(exchange_split(1));
            var_dump(exchange_split('123'));
            var_dump(exchange_split('1-123'));
            var_dump(exchange_split('1-2-123'));
            return;
            var_dump(preg_match('/^(?=.*[\x{4e00}-\x{9fa5}A-Za-z_-])[\x{4e00}-\x{9fa5}A-Za-z0-9_-]{2,60}$/u', '淘宝-你好_123'));
            var_dump(preg_match('/^(?=.*[\x{4e00}-\x{9fa5}A-Za-z_-])[\x{4e00}-\x{9fa5}A-Za-z0-9_-]{2,60}$/u', 'a-_123'));
            var_dump(preg_match('/^(?=.*[\x{4e00}-\x{9fa5}A-Za-z_-])[\x{4e00}-\x{9fa5}A-Za-z0-9_-]{2,60}$/u', '123456_123'));
            return;

            $channel = OrderUtils::getDefaultChannel(1, '');
            var_dump($channel->getAppid());
            var_dump($channel->getChannelType());

            $channel = OrderUtils::getDefaultChannel(1, 'sss');
            var_dump($channel->getAppid());
            var_dump($channel->getChannelType());

            $channel = OrderUtils::getDefaultChannel(1, 'QcpV2');
            var_dump($channel->getAppid());
            var_dump($channel->getChannelType());

            return;

            $this->redis = app('redis.connection');

            $am_limit = config('api.tianmao.max_limit_account_mobiles');
            $ma_limit = config('api.tianmao.max_limit_mobile_accounts');
            $mobile   = '***********';
            $mobile2  = '***********';
            $mobile3  = '***********';
            $mobile4  = '***********';

            $account  = 'freewinds';
            $account2 = 'freewinds2';
            $account3 = 'freewinds3';
            $account4 = 'freewinds4';

            $key_am = 'tianmao_am_' . md5($account);
            $key_ma = 'tianmao_ma_' . $mobile;

            var_dump(['gift_db_' . $key_am, 'gift_db_' . $key_ma]);

//            var_dump($this->redis->sRem($key_am . "sss", $mobile2));//不存在的key，不报错，返回: int(0)

            echo "----------am----------\n";
            echo "-----sAdd-----\n";
            var_dump($this->redis->sAdd($key_am, $mobile, $mobile2));
            echo "-----sMembers-----\n";
            var_dump($this->redis->sMembers($key_am));
//            var_dump($this->redis->sMembers($key_am . 'sssssss'));
            echo "-----sCard-----\n";
            var_dump($this->redis->sCard($key_am));
//            echo "-----sIsMember-----\n";
//            var_dump($this->redis->sIsMember($key_am, $mobile));
//            var_dump($this->redis->sIsMember($key_am, $mobile4));
//            echo "-----sRem-----\n";
//            var_dump($this->redis->sRem($key_am, $mobile2));
//            var_dump($this->redis->sRem($key_am, $mobile4));

            echo "----------ma----------\n";
            echo "-----sAdd-----\n";
            var_dump($this->redis->sAdd($key_ma, $account, $account2));
            echo "-----sMembers-----\n";
            var_dump($this->redis->sMembers($key_ma));
            echo "-----sCard-----\n";
            var_dump($this->redis->sCard($key_ma));

            return;

//            var_dump(check_virtual_mobile('***********'));
//            var_dump(check_virtual_mobile('***********'));
//            var_dump(check_virtual_mobile('***********'));
//            return;

//            var_dump(RedisLimitUtils::getInstance()->getSeconds(RedisLimitUtils::TYPE_END_OF_DAY));
//            return;
            //array(2) {
            //  [0]=>
            //  int(5)
            //  [1]=>
            //  bool(true)
            //}
            //string(1) "5"
            //int(22311)

//            $key         = 'test_ljq';
//            $result      = $this->redis->multi()
//                ->incr($key)
//                ->expire($key, \Carbon\Carbon::now()->endOfDay()->timestamp - Carbon::now()->timestamp)
//                ->exec();
//            var_dump($result);
//            var_dump($this->redis->get($key));
//            var_dump($this->redis->ttl($key));
//            return;

//            $max_order_limit      = config('api.tianmao.max_limit_per_account');
//            $max_limit_per_mobile = config('api.tianmao.max_limit_per_mobile');
//            var_dump($max_order_limit);
//            var_dump($max_limit_per_mobile);
//            return;

//            $var = OrderSub::where(['order_id' => 143, 'status' => SysCode::ORDER_SUB_STATUS_4])->get()->pluck('sub_order_no')->toArray();
//            var_dump($var);
//            return;

//            $int = DB::table('tianmao_users')->insertGetId(['mobile' => '***********', 'created_at' => \Carbon\Carbon::now()]);
//            var_dump($int);
//            return;


//            $goods = DB::table('goods')->where('id', 13)->first();
//
//            $goods_list = [];
//            if ($goods->combin_type == Goods::COMBIN_TYPE_SINGLE) {
//                $goods_list[] = (array)$goods;
//            } else if ($goods->combin_type == Goods::COMBIN_TYPE_COMBIN) {
//                DB::table('goods_combins')
//                    ->leftJoin('goods', 'goods_combins.child_goods_id', '=', 'goods.id')
//                    ->where('goods_combins.goods_id', $goods->id)
//                    ->select(DB::raw('goods.*'))
//                    ->get()
//                    ->map(function ($goods) use (&$goods_list) {
//                        $goods_list[] = (array)$goods;
//                    });
//            }
//
//            var_dump($goods_list);
//            return;


//
//                var_dump(Goods::find(14)->children);
//            return;
//
//            var_dump(strpos('fdsaafewaf', 'aa'));
//            return;
//
//            $redis = app('redis.connection');
//
//            var_dump($redis->ttl('foo'));
//            return;
//
//            $val = $redis->incr('foo');
//            if ($redis->ttl('foo') === -1) {
//                $redis->expire('foo', 60);
//            }
//            var_dump($val);
//            var_dump($redis->ttl('foo'));
        } catch (\RedisException $e) {

            echo 'RedisException: ' . $e->getMessage() . "\n";
        }


//        $parameter = new PayRequestParameter();
//
//        $parameter->setAppid('appid1');
//        $parameter->setAppOrderid('trade1293291332');
//        $parameter->setDesc('pay-test');
//        $parameter->setAmount('100');
//        $parameter->setT(Carbon::now()->format("YmdHis"));
//
//        $reqParams = ['appid', 'amount', 'app_orderid', 'desc', 't'];
//
//        $service = new PayService('http://www.paytest.com', $reqParams, 'pay-test-secret');
//
//        $insertData = ['activity_id'=> 1,
//            'order_no' => 'trade1293291332',
//            'pay_from_table_name' => 'icbc_autumn_consume_pay_details',
//            'pay_channel_id'=>'1'];
//        $service->doPost($parameter, $insertData);

    }


}
