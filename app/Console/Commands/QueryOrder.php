<?php

namespace App\Console\Commands;

use App\Exceptions\MyException;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Api\SysCode\SysCode;
use GuzzleHttp\Client;
use Exception;
use Carbon\Carbon;
use App\Models\OrderSub;
use App\Models\Activity;

use function GuzzleHttp\json_decode;

// use Exception;

/**
 * 订单查询
 */
class QueryOrder extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ap:query-order';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '查询订单状态';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $logger = Log::channel('query_order');
        try {
            $min_id     = 0;
            $once_limit = 200;
            //首先查出符合条件的最小id
            $min_id_obj = self::getMinId();
            if (count($min_id_obj) > 0) {
                //取得最小id值
                $min_id = $min_id_obj[0]->id - 1;
            } else {
                throw new MyException('没有可以查询的订单');
            }
            //释放内存
            unset($min_id_obj);
            $orders = self::getTasks($min_id, $once_limit);
            //判断是否有符合条件的订单
            while (count($orders) > 0) {
                //TODO
                //开始给业务系统推送这批订单
                $min_id = self::startSend($orders, $logger);
                //处理完上一批,再获取下一批符合条件的订单
                $orders = self::getTasks($min_id, $once_limit);
            }
        } catch (\Exception $exc) {
            $msg = $exc->getMessage();
            $logger->info($msg);
        }
    }

    /**
     * 获取要推送的任务
     */
    private static function getTasks($min_id, $once_limit)
    {
        //2 代表处理中
        return OrderSub::where('status', SysCode::ORDER_SUB_STATUS_2)
            ->where('id', '>', $min_id)
            ->orderBy('id')
            ->limit($once_limit)
            ->get();
    }

    /**
     * 获取最小id
     */
    private static function getMinId()
    {
        return OrderSub::where('status', SysCode::ORDER_SUB_STATUS_2)
            ->orderBy('id')
            ->limit(1)
            ->get();
    }


    /**
     * 开始推送
     */

    private static function startSend($orders, $logger)
    {

        //根据活动id查出 appid secret_key is_sms
        // DB::table()
        $act_info = Activity::where('status', 1)->select('id', 'project_id', 'is_send_sms', 'order_gateway_appid',
            'order_gateway_secketkey', 'is_local_send_sms')->get();
        if (empty($act_info)) {
            $logger->info('活动id配置错误!');
            return;
        }


        foreach ($act_info as $v) {
            $account_info[$v->id] = $v;
        }


        $req_url = env('QUERY_ORDER_URL', config('activity.icbc_e_activity.query_url'));
        foreach ($orders as $order) {

            $activity_id = $order->order()->where('id', $order->order_id)->first()->activity_id;

            $min_id        = $order->id;
            $biz_data      = [
                'order_no'     => $order->order_no,
                'sub_order_no' => $order->sub_order_no,
            ];
            $biz_json_data = json_encode($biz_data);

            $appid             = $account_info[$activity_id]->order_gateway_appid;
            $secret_key        = $account_info[$activity_id]->order_gateway_secketkey;
            $is_local_send_sms = $account_info[$activity_id]->is_local_send_sms;

            $timestamp = Carbon::now()->timestamp;
            $serial_no = session_create_id();
            $sign      = createSign($appid.$biz_json_data.$serial_no.$timestamp.$secret_key);

            $params_data = [
                'serial_no' => $serial_no,
                'appid'     => $appid,
                'timestamp' => $timestamp,
                'biz_data'  => $biz_json_data,
                'sign'      => $sign,
            ];


            $client = new Client();

            $response = $client->request('POST', $req_url, [
                'form_params' => $params_data
            ]);

            $resp_json = $response->getBody()->getContents();

            $resp = json_decode($resp_json, true);
            //提交成功
            if (isset($resp['code']) && $resp['code'] == '200') {

                switch ($resp['biz_data']['status']) {
                    //2代表 成功
                    case 3:
                        $order->status = SysCode::ORDER_SUB_STATUS_3;
                        $result        = true;
                        break;
                    //5代表 失败
                    case 5:
                        $order->status = SysCode::ORDER_SUB_STATUS_4;
                        $result        = true;
                        break;
                    default:
                        $result = false;
                        break;
                }

                if ($result) {
                    $order->deliver_complete_time = $resp['biz_data']['complete_time'] ?? Carbon::now()->format("Y-m-d H:i:s");
                    $order->activation_code       = $resp['biz_data']['activation_code'] ?? '';
                    $order->sequence_no           = $resp['biz_data']['sequence_no'] ?? '';
                    $order->logistics_sn          = $resp['biz_data']['logistics_sn'] ?? '';
                    $order->logistics_company     = $resp['biz_data']['logistics_company'] ?? '';

                    if ($resp['biz_data']['sms_status'] == 0 && $is_local_send_sms == 0) {
                        $order->sms_status = 0; //不发短信
                    }

                    $order->sms_gateway_seq = $resp['biz_data']['sms_gateway_seq'] ?? '';
                    if (isset($resp['biz_data']['endtime'])) {
                        $order->endtime = $resp['biz_data']['endtime'];
                    }
                    $order->sms_callback_time = $resp['biz_data']['sms_complete_time'] ?? Carbon::now()->format("Y-m-d H:i:s");
                    $order->save();
                }
            }
            $log_data = [
                'request'  => [
                    'serial_no' => $serial_no,
                    'appid'     => $appid,
                    'timestamp' => $timestamp,
                    'biz_data'  => $biz_data,
                    'sign'      => $sign,
                ],
                'response' => $resp
            ];
            $logger->info(json_encode($log_data));
        }
        return $min_id;
    }
}
