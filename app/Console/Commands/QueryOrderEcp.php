<?php

namespace App\Console\Commands;

use App\Exceptions\MyNoLogException;
use App\Service\Ecp;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Api\SysCode\SysCode;
use Exception;
use Carbon\Carbon;
use App\Models\OrderSub;
use App\Models\Activity;

// use Exception;

/**
 * 订单查询（ecp）
 */
class QueryOrderEcp extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ap:query-order-ecp';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '查询订单状态';

    protected $ecp;

    protected $act_list = [];

    protected $logger;

    protected $min_id = 0;

    protected $once_limit = 200;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->ecp    = new Ecp();
        $this->logger = Log::channel('query_order');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        try {
            //首先查出符合条件的最小id
            $min_id_obj = $this->getMinId();
            if (count($min_id_obj) > 0) {
                //取得最小id值
                $this->min_id = $min_id_obj[0]->id - 1;
            } else {
                throw new MyNoLogException('没有可以查询的订单');
            }
            //释放内存
            unset($min_id_obj);

            //根据活动id查出 appid secret_key is_sms
            $act_info = Activity::where('status', 1)
                ->select('id', 'channel_appid', 'channel_secketkey', 'is_send_sms', 'is_local_send_sms')
                ->get();
            if (empty($act_info)) {
                throw new MyNoLogException('没有可以提交的订单或者活动id配置错误');
            }

            foreach ($act_info as $v) {
                $this->act_list[$v->id] = $v;
            }


            $orders = $this->getTasks();
            //判断是否有符合条件的订单
            while (count($orders) > 0) {
                //TODO
                //开始给业务系统推送这批订单
                $min_id = $this->startSend($orders);
                //处理完上一批,再获取下一批符合条件的订单
                $orders = $this->getTasks();
            }
        } catch (MyNoLogException $exc) {
            $msg = $exc->getMessage();
            $this->logger->debug($msg);
        } catch (\Exception $exc) {
            $msg = $exc->getMessage();
            $this->logger->info($msg);
        }
    }

    /**
     * 获取要推送的任务
     */
    private function getTasks()
    {
        //2 代表处理中
        return OrderSub::where('status', SysCode::ORDER_SUB_STATUS_2)
            ->where('id', '>', $this->min_id)
            ->whereIn('goods_type', [SysCode::GOODS_TYPE_2, SysCode::GOODS_TYPE_3, SysCode::GOODS_TYPE_5])//卡密、直充、短链接
            ->orderBy('id')
            ->limit($this->once_limit)
            ->get();
    }

    /**
     * 获取最小id
     */
    private function getMinId()
    {
        $days       = intval(config('app.order_submit.days')) + 3; //在提交订单天数基础上再往前加3天。
        $begin_time = date('Y-m-d 00:00:00', strtotime(sprintf('-%d day', $days)));
        return OrderSub::where('status', SysCode::ORDER_SUB_STATUS_2)
            ->where('created_at', '>=', $begin_time)
            ->whereIn('goods_type', [SysCode::GOODS_TYPE_2, SysCode::GOODS_TYPE_3, SysCode::GOODS_TYPE_5])//卡密、直充、短链接
            ->orderBy('created_at')
            ->limit(1)
            ->get();
    }


    /**
     * 开始推送
     */

    private function startSend($orders)
    {

        $max_count = intval(config('app.order_submit.max_count'));

        foreach ($orders as $order) {

            $this->min_id = $order->id;

            if ($order->order_req_time > Carbon::now()->addMinutes(-3)) {
                continue; //时间未到，暂不处理
            }

            if (!in_array($order->ecp_target, ['tel', 'flow', 'qcoins', 'recharge'])) {
                continue;
            }

            $activity_id = $order->order->activity_id;
            $appid       = $this->act_list[$activity_id]->channel_appid;
            $secret_key  = $this->act_list[$activity_id]->channel_secketkey;

            $query_result = $this->ecp->queryOrderState($order->ecp_target, $order->sub_order_no, $appid, $secret_key);

            if (!empty($query_result)) {
                if ($query_result['code'] == '0000' && $query_result['data']['order_id'] == $order->third_order_no) {
                    //提交成功
                    //is_success  0 处理中 1 成功 2 失败
                    if ($query_result['data']['is_success'] == '1') {

                        $order->status = SysCode::ORDER_SUB_STATUS_3;

                    } elseif ($query_result['data']['is_success'] == '2') {

                        $order->status = SysCode::ORDER_SUB_STATUS_5;
                        if ($order->order_req_count >= $max_count) {
                            $order->status = SysCode::ORDER_SUB_STATUS_4;
                        } else {
                            $order->status = SysCode::ORDER_SUB_STATUS_5;
                        }

                    }

                    if ($order->isDirty()) {

//                        if ($order->status != SysCode::ORDER_SUB_STATUS_4 && $order->status != SysCode::ORDER_SUB_STATUS_5){
                        $order->deliver_complete_time = Carbon::now()->format("Y-m-d H:i:s");
//                        }

                        if (!$order->save()) {
                            $this->logger->warning(json_encode([
                                "opt"          => "query_order",
                                "opt_desc"     => "save fail",
                                "id"           => $order->id,
                                "sub_order_no" => $order->sub_order_no,
                                "changes"      => $order->getDirty()
                            ], JSON_UNESCAPED_SLASHES));
                        }
                    }

                } //if ($query_result['code'] == '0000')

            } //if (!empty($query_result))
        }
    }
}
