<?php

namespace App\Console\Commands;

use App\Exceptions\MyNoLogException;
use App\Libraries\Channel\BaseChannel;
use App\Libraries\OrderUtils;
use App\Service\Ecp;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Api\SysCode\SysCode;
use Exception;
use Carbon\Carbon;
use App\Models\OrderSub;
use App\Models\Activity;

// use Exception;

/**
 * 订单查询（支持v1和v2）
 */
class QueryOrderV2 extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ap:query-order-v2';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '查询订单状态';

    protected $act_list = [];

    protected $logger;

    protected $min_id = 0;

    protected $once_limit = 200;

    /**
     * Create a new command instance.
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->logger = Log::channel('query_order');
    }

    /**
     * Execute the console command.
     * @return mixed
     */
    public function handle()
    {
        try {
            //首先查出符合条件的最小id
            $min_id_obj = $this->getMinId();
            if (count($min_id_obj) > 0) {
                //取得最小id值
                $this->min_id = $min_id_obj[0]->id - 1;
            } else {
                throw new MyNoLogException('没有可以查询的订单');
            }
            //释放内存
            unset($min_id_obj);

            $orders = $this->getTasks();
            //判断是否有符合条件的订单
            while (count($orders) > 0) {
                $this->startSend($orders);
                //处理完上一批,再获取下一批符合条件的订单
                $orders = $this->getTasks();
            }
        } catch (MyNoLogException $exc) {
            $msg = $exc->getMessage();
            $this->logger->debug($msg);
        } catch (\Exception $exc) {
            $msg = $exc->getMessage();
            $this->logger->info($msg);
        }
    }

    /**
     * 获取要推送的任务
     */
    private function getTasks()
    {
        //2 代表处理中
        return OrderSub::where('status', SysCode::ORDER_SUB_STATUS_2)
            ->where('id', '>', $this->min_id)
            ->whereIn('goods_type', [SysCode::GOODS_TYPE_2, SysCode::GOODS_TYPE_3, SysCode::GOODS_TYPE_5])//卡密、直充、短链接
            ->orderBy('id')
            ->limit($this->once_limit)
            ->get();
    }

    /**
     * 获取最小id
     */
    private function getMinId()
    {
        $days       = intval(config('app.order_submit.days')) + 3; //在提交订单天数基础上再往前加3天。
        $begin_time = date('Y-m-d 00:00:00', strtotime(sprintf('-%d day', $days)));
        return OrderSub::where('status', SysCode::ORDER_SUB_STATUS_2)
            ->where('created_at', '>=', $begin_time)
            ->whereIn('goods_type', [SysCode::GOODS_TYPE_2, SysCode::GOODS_TYPE_3, SysCode::GOODS_TYPE_5])//卡密、直充、短链接
            ->orderBy('created_at')
            ->limit(1)
            ->get();
    }


    /**
     * 开始推送
     */
    private function startSend($orders)
    {
        $max_count = intval(config('app.order_submit.max_count'));

        foreach ($orders as $order) {

            $this->min_id = $order->id;

            if ($order->order_req_time > Carbon::now()->addMinutes(-3)) {
                continue; //时间未到，暂不处理
            }

            if (!empty($order->ecp_target) && !in_array($order->ecp_target, ['tel', 'flow', 'qcoins', 'recharge'])) {
                continue;
            }

            $channel = OrderUtils::getChannel($order->order->activity_id, $order->ecp_target);
            if (empty($channel)) {
                $this->logger->warning(json_encode([
                    "opt"          => "query_order",
                    "opt_desc"     => "activity error",
                    "id"           => $order->id,
                    "req_order_no" => $order->req_order_no,
                ], JSON_UNESCAPED_SLASHES));
                continue;
            }
            $channel_product_code = empty($order->ecp_target) ? $order->ecp_pcode : ($order->ecp_target . '_' . $order->ecp_pcode);
            //新旧版本交替时req_order_no可能为空
            if (empty($order->req_order_no)) {
                $order->req_order_no = $order->sub_order_no;
            }
            $query_result = $channel->query($order->req_order_no, $channel_product_code);

            switch ($query_result['order_status']) {
                case BaseChannel::ORDER_STATUS_SUCCESS:
                    $order->status         = SysCode::ORDER_SUB_STATUS_3;
                    $order->third_order_no = $query_result['third_order_no'];
                    if (!empty($query_result['cost_price'])) {
                        $order->cost_price = $query_result['cost_price'];
                    }
                    if (!empty($query_result['finish_time'])) {
                        $order->deliver_complete_time = $query_result['finish_time'];
                    } else {
                        $order->deliver_complete_time = Carbon::now()->format("Y-m-d H:i:s");
                    }
                    if (!empty($query_result['cards'])) {
                        $card                   = $query_result['cards'][0];
                        $order->sequence_no     = $card['no'];
                        $order->activation_code = $card['pwd'];
                        $order->endtime         = (empty($card['end']) || $card['end'] == '0000-00-00') ? '2099-12-31' : $card['end'];
                    }
                    break;
                case BaseChannel::ORDER_STATUS_FAIL:
                    if ($order->order_req_count >= $max_count) {
                        $order->status = SysCode::ORDER_SUB_STATUS_4;
                    } else {
                        $order->status = SysCode::ORDER_SUB_STATUS_5;
                    }
                    if (!empty($query_result['third_order_no'])) {
                        $order->third_order_no = $query_result['third_order_no'];
                    }
                    if (!empty($query_result['finish_time'])) {
                        $order->deliver_complete_time = $query_result['finish_time'];
                    } else {
                        $order->deliver_complete_time = Carbon::now()->format("Y-m-d H:i:s");
                    }
                    break;
                case BaseChannel::ORDER_STATUS_DEALING:
                    $order->third_order_no = $query_result['third_order_no'] ?? '';
                    if (!empty($query_result['cost_price'])) {
                        $order->cost_price = $query_result['cost_price'];
                    }
                    break;
                default:
                    break;
            }

            if ($order->isDirty()) {

                if (!$order->save()) {
                    $this->logger->warning(json_encode([
                        "opt"          => "query_order",
                        "opt_desc"     => "save fail",
                        "id"           => $order->id,
                        "req_order_no" => $order->req_order_no,
                        "changes"      => $order->getDirty()
                    ], JSON_UNESCAPED_SLASHES));
                }
            }

        }
    }
}
