<?php

namespace App\Console\Commands;

use App\Supports\PayRequestParameter;
use App\Supports\PayRequest;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Exceptions\MyException;

/**
 * 查询支付结果
 */
class QueryPayResult extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'trade:query-result';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '查询支付结果';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }


    public function handle() {

        $logger = Log::channel('trade_query');
        try {

//            $trades = DB::table('trades')->where(['pay_result'=>'1'])->select()->get()->toArray();
            $starttime = date('Y-m-d H:i:s', strtotime('-2 day'));
            $trades = DB::table('trades')->where(['pay_result' => '1'])->where('created_at' ,'>', $starttime)->select()->get()->toArray();

            foreach ($trades as $trade) {

                try {
                    $tradeChannel = DB::table('trade_channels')->find($trade->pay_channel_id);

                    if (empty($tradeChannel)) {
                        throw new MyException('没有查到支付渠道信息，pay_channel_id:' . $trade->pay_channel_id . ',order_no:' . $trade->order_no);
                    }

                    $parameter = new PayRequestParameter();
                    $parameter->setAppid(config('activity.icbc_autumn_activity.pay_trade_appid'));
                    $parameter->setT(Carbon::now()->format("YmdHis"));
                    $queryParameters = json_decode($tradeChannel->query_parameters, true);
                    if (in_array('orderid', $queryParameters)) {
                        $parameter->setOrderid($trade->gateway_orderid);
                    }

                    if (in_array('userid', $queryParameters)) {
                        $parameter->setUserid(''); //TODO 设置用户openid，目前表里没有这个字段
                    }

                    $secrete = config('activity.icbc_autumn_activity.pay_trade_secret');
                    $request = new PayRequest($tradeChannel->query_url, $queryParameters, $secrete);

                    // 发起查询
                    $result = $request->doPost($parameter->getReqParameterMap());
                    $logger->info('resp:' . $result);

                    $result = json_decode($result, true);
                    if (!empty($result) && ($result['code'] === '0000')) {

                        if (!in_array($result['status'], array('00', '02'))) {
                            throw new MyException('支付结果不是成功或失败状态，暂时不作处理！');
                        }

                        $payResult = $result['status'] == '00' ? '2' : '3';
                        // 交易明细表
                        $tradeData = [];
                        if (isset($result['amount']) && $result['amount'] != '') {
                            $tradeData['real_pay_fee'] = $result['amount'];
                        }
                        if (isset($result['platform_orderid'])) {
                            $tradeData['platform_orderid'] = $result['platform_orderid'];
                        }
                        if (isset($result['seq'])) {
                            $tradeData['gateway_seq'] = $result['seq'];
                        }
                        if (isset($result['card_no'])) {
                            $tradeData['pay_card_no'] = $result['card_no'];
                        }
                        $tradeData['pay_result'] = $payResult;
                        $tradeData['updated_at'] = Carbon::now()->format('Y-m-d H:i:s');

                        // 交易的订单表
                        $orderData               = [];
                        $orderData['pay_result'] = $payResult;
                        $orderData['updated_at'] = Carbon::now()->format('Y-m-d H:i:s');

                        try {
                            DB::transaction(function () use ($tradeData, $orderData, $trade) {

                                $tradeResult = DB::table('trades')->where(['id' => $trade->id, 'pay_result' => '1'])->update($tradeData);
                                if (!$tradeResult) {
                                    throw new MyException('更新支付信息表支失败！,order_no' . $trade->order_no);
                                }
                                if (!empty($trade->pay_from_table_name)) {
                                    $orderResult = DB::table($trade->pay_from_table_name)->where(['order_no' => $trade->order_no, 'pay_result' => '1'])->update($orderData);
                                    if (!$orderResult) {
                                        throw new MyException('更新主订单支付状态失败！order_no: ' . $trade->order_no);
                                    }
                                }
                                DB::table('orders')->where(['order_no' => $trade->order_no, 'pay_result' => '1'])->update($orderData);
                            });
                        } catch (\Exception $ex) {
                            throw $ex;
                        }
                    } else {
                        $message = $result['msg'] ?? '请求失败';
                        $logger->error($message);
                    }
                } catch (MyException $e) {
                    $logger->error($e->getMessage());
                } catch (\Exception $e) {
                    throw $e;
                }
            }
        } catch (\Exception $e) {
            $logger->error($e->getMessage());
        }
    }
}
