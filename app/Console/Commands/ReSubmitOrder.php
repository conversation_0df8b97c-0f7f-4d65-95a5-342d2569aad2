<?php

namespace App\Console\Commands;

use App\Service\Ecp;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Api\SysCode\SysCode;
use GuzzleHttp\Client;
use Exception;
use Carbon\Carbon;
use App\Models\OrderSub;
use App\Models\Activity;
use App\Exceptions\MyNoLogException;

use function GuzzleHttp\json_decode;

// use Exception;

/**
 * 失败订单重新提交
 */
class ReSubmitOrder extends SignalAndLockedBaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ap:resubmit-order';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '失败订单重提';

    protected $logger;

    protected $min_id = 0;

    protected $once_limit = 200;

    protected $act_list = [];

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
        $this->lock_ttl = 3600;
        $this->logger   = Log::channel('submit_order');
        $this->ecp      = new Ecp();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handleBusiness() {

        try {

            //首先查出符合条件的最小id
            $this->getMinId();


            //根据活动id查出 appid secret_key is_sms
            $act_info = Activity::where('status', 1)->select('id', 'channel_appid', 'channel_secketkey')->get();
            if (empty($act_info)) {
                throw new MyNoLogException('没有可以提交的订单或者活动id配置错误');
            }

            foreach ($act_info as $v) {
                $this->act_list[$v->id] = $v;
            }

            //取出成功或失败不再重试的订单,且订单状态为未推送给业务平台的订单.
            //3 代表已发货 5:失败不再重试
            $orders = $this->getTasks();

            //判断是否有符合条件的订单
            while (count($orders) > 0) {

                //开始给业务系统推送这批订单
                $this->startSend($orders);

                //处理完上一批,再获取下一批符合条件的订单
                $orders = $this->getTasks();
            }

        } catch (MyNoLogException $exc) {
            $msg = $exc->getMessage();
            $this->logger->debug($msg);
        } catch (\Exception $exc) {

            $msg = $exc->getMessage();
            $this->logger->info($msg);
        }

    }

    /**
     * 获取要推送的任务
     */
    private function getTasks() {
        //3 代表已发货 4:失败 。
        return OrderSub::where('status', SysCode::ORDER_SUB_STATUS_5)
            ->where('id', '>', $this->min_id)
            ->whereIn('goods_type', [SysCode::GOODS_TYPE_2, SysCode::GOODS_TYPE_3, SysCode::GOODS_TYPE_5])//卡密、直充、短链接
            ->orderBy('id')
            ->limit($this->once_limit)
            ->get();
    }

    /**
     * 获取最小id
     */
    private function getMinId() {
        $days = intval(config('app.order_submit.days'));
        $begin_time = date('Y-m-d 00:00:00', strtotime(sprintf('-%d day', $days)));
        $min_id_obj = OrderSub::where('status', SysCode::ORDER_SUB_STATUS_5)
            ->where('created_at','>=', $begin_time)
            ->whereIn('goods_type', [SysCode::GOODS_TYPE_2, SysCode::GOODS_TYPE_3, SysCode::GOODS_TYPE_5])//卡密、直充、短链接
            ->orderBy('created_at')
            ->limit(1)
            ->get();

        if (count($min_id_obj) > 0) {
            //取得最小id值
            $this->min_id = $min_id_obj[0]->id - 1;
        } else {
            throw new MyNoLogException('没有可以提交的订单');
        }
    }

    /**
     * 开始提交
     */
    private function startSend($sub_orders) {

        $resubmit_interval = intval(config('app.order_submit.interval'));
        $max_count         = intval(config('app.order_submit.max_count'));

        foreach ($sub_orders as $order) {

            $this->dealSignal();//处理信号

            $this->dealCacheTimeout();

            $this->min_id = $order->id;

            $order_req_count = $order->order_req_count;

            //pay_result 0 为不需要支付的订单  2:为支付成功的订单
            //
            if (!in_array($order->order->pay_result, [0, 2])) {
                continue;
            }

            //如果超过提交次数，则置为失败。
            if ($order_req_count >= $max_count) {
                $order->status = SysCode::ORDER_SUB_STATUS_4;
                $order->save();
                continue;
            }

            if ($order->order_req_time > Carbon::now()->addMinutes(-1 * $resubmit_interval)) {
//                Log::debug('未到重提时间');
                continue;//时间未到，暂不处理
            }

            $activity_id = $order->order->activity_id;

            $appid      = $this->act_list[$activity_id]->channel_appid;
            $secret_key = $this->act_list[$activity_id]->channel_secketkey;

            $this->logger->info(json_encode([
                "opt"             => "resubmit_order",
                "opt_desc"        => "begin deal",
                "id"              => $order->id,
                "sub_order_no"    => $order->sub_order_no,
                "order_req_count" => $order_req_count
            ], JSON_UNESCAPED_SLASHES));

            //先更新状态为处理中
            $order->status          = SysCode::ORDER_SUB_STATUS_2;
            $order->order_req_count = DB::raw("order_req_count + 1");
            $order->order_req_time  = Carbon::now()->format("Y-m-d H:i:s"); //订单请求时间
            $result_update_status   = $order->save();

            //更新主订单状态
            DB::table('orders')->where('id', $order->order_id)->update(['status' => SysCode::ORDER_STATUS_1]);

            if ($result_update_status) {
                //状态更新成功，往渠道提交。
                if (!checkPhone($order->user_mobile) && !in_array($order->ecp_target, ['tel', 'flow'])) {
                    //如果充值账号非手机号，这里user_mobile置为***********，此时如果是recharge大类，则acount不能为空，否则会有错冲的情况。
                    $submit_result = $this->ecp->submitOrder($order->ecp_target . '_' . $order->ecp_pcode, $order->sub_order_no, '***********', $order->charge_account, $appid, $secret_key);
                } else {
                    $submit_result = $this->ecp->submitOrder($order->ecp_target . '_' . $order->ecp_pcode, $order->sub_order_no, $order->user_mobile, $order->charge_account, $appid, $secret_key);
                }

                $this->logger->info(json_encode([
                    "opt"             => "resubmit_order",
                    "opt_desc"        => "ecp response",
                    "id"              => $order->id,
                    "sub_order_no"    => $order->sub_order_no,
                    "order_req_count" => $order_req_count + 1,
                    "ecp_response"    => $submit_result
                ], JSON_UNESCAPED_SLASHES));

                if (empty($submit_result)) {
                    //超时或有参数校验失败
                    if ($submit_result === false) {
                        //此时未提交到ecp，认定为提交失败
                        if ($order_req_count + 1 >= $max_count) {
                            $order->status = SysCode::ORDER_SUB_STATUS_4;
                        } else {
                            $order->status = SysCode::ORDER_SUB_STATUS_5;
                        }

                    } else {
                        //超时，或网络错误，不确定是否已经提交到ecp。保持处理中状态
                    }

                } else {

                    if ($submit_result['code'] == '0000') {
                        //提交成功

                        $order->third_order_no = $submit_result['data']['orderid'];

                        if (in_array($order->ecp_target, ['tel', 'flow', 'qcoins', 'recharge'])) {

                            //异步通知，保持处理中状态。

                        } else {

                            $order->status = SysCode::ORDER_SUB_STATUS_3;

                            if (!empty($submit_result['data']['activation_code'])) {
                                $order->activation_code = $submit_result['data']['activation_code'];
                                $order->sequence_no     = $submit_result['data']['sequence_no'];
                                $order->endtime         = (empty($submit_result['data']['endtime'] || $submit_result['data']['endtime'] == '0000-00-00') ? '1900-01-01' : $submit_result['data']['endtime']);
                            }
                        }

                    } else {
                        //提交失败
                        if ($order_req_count + 1 >= $max_count) {
                            $order->status = SysCode::ORDER_SUB_STATUS_4;
                        } else {
                            $order->status = SysCode::ORDER_SUB_STATUS_5;
                        }
                    }
                }

                if ($order->isDirty()) {
                    if (!$order->save()) {
                        $this->logger->warning(json_encode([
                            "opt"          => "resubmit_order",
                            "opt_desc"     => "after save fail",
                            "id"           => $order->id,
                            "sub_order_no" => $order->sub_order_no,
                            "changes"      => $order->getDirty()
                        ], JSON_UNESCAPED_SLASHES));
                    }
                }
            } else {
                $this->logger->warning(json_encode([
                    "opt"          => "resubmit_order",
                    "opt_desc"     => "pre save fail",
                    "id"           => $order->id,
                    "sub_order_no" => $order->sub_order_no
                ], JSON_UNESCAPED_SLASHES));
            }

        }
    }
}
