<?php

namespace App\Console\Commands;

use App\Libraries\Channel\BaseChannel;
use App\Libraries\Enums;
use App\Libraries\OrderUtils;
use App\Service\Ecp;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Api\SysCode\SysCode;
use GuzzleHttp\Client;
use Exception;
use Carbon\Carbon;
use App\Models\OrderSub;
use App\Models\Activity;
use App\Exceptions\MyNoLogException;

/**
 * 失败订单重新提交。支持多渠道
 */
class ReSubmitOrderV2 extends SignalAndLockedBaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ap:resubmit-order-v2';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '失败订单重提';

    protected $logger;

    protected $min_id = 0;

    protected $once_limit = 200;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->lock_ttl = 3600;
        $this->logger   = Log::channel('submit_order');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handleBusiness()
    {
        try {
            //首先查出符合条件的最小id
            $this->getMinId();

            //取出成功或失败不再重试的订单,且订单状态为未推送给业务平台的订单.
            //3 代表已发货 5:失败不再重试
            $orders = $this->getTasks();

            //判断是否有符合条件的订单
            while (count($orders) > 0) {

                //开始给业务系统推送这批订单
                $this->startSend($orders);

                //处理完上一批,再获取下一批符合条件的订单
                $orders = $this->getTasks();
            }

        } catch (MyNoLogException $exc) {
            $msg = $exc->getMessage();
            $this->logger->debug($msg);
        } catch (\Exception $exc) {

            $msg = $exc->getMessage();
            $this->logger->info($msg);
        }

    }

    /**
     * 获取要推送的任务
     */
    private function getTasks()
    {
        //3 代表已发货 4:失败 。
        return OrderSub::where('status', SysCode::ORDER_SUB_STATUS_5)
            ->where('id', '>', $this->min_id)
            ->whereIn('goods_type', [SysCode::GOODS_TYPE_2, SysCode::GOODS_TYPE_3, SysCode::GOODS_TYPE_5])//卡密、直充、短链接
            ->orderBy('id')
            ->limit($this->once_limit)
            ->get();
    }

    /**
     * 获取最小id
     */
    private function getMinId()
    {
        $days       = intval(config('app.order_submit.days'));
        $begin_time = date('Y-m-d 00:00:00', strtotime(sprintf('-%d day', $days)));
        $min_id_obj = OrderSub::where('status', SysCode::ORDER_SUB_STATUS_5)
            ->where('created_at', '>=', $begin_time)
            ->whereIn('goods_type', [SysCode::GOODS_TYPE_2, SysCode::GOODS_TYPE_3, SysCode::GOODS_TYPE_5])//卡密、直充、短链接
            ->orderBy('created_at')
            ->limit(1)
            ->get();

        if (count($min_id_obj) > 0) {
            //取得最小id值
            $this->min_id = $min_id_obj[0]->id - 1;
        } else {
            throw new MyNoLogException('没有可以提交的订单');
        }
    }

    /**
     * 开始提交
     */
    private function startSend($sub_orders)
    {
        $resubmit_interval = intval(config('app.order_submit.interval'));
        $max_count         = intval(config('app.order_submit.max_count'));

        foreach ($sub_orders as $order) {

            $this->dealSignal();//处理信号

            $this->dealCacheTimeout();

            $this->min_id = $order->id;

            //pay_result 0 为不需要支付的订单  2:为支付成功的订单
            if (!in_array($order->order->pay_result, [0, 2])) {
                continue;
            }

            $order_req_count = $order->order_req_count;

            //如果超过提交次数，则置为失败。
            if ($order_req_count >= $max_count) {
                $order->status = SysCode::ORDER_SUB_STATUS_4;
                $order->save();
                continue;
            }

            if ($order->order_req_time > Carbon::now()->addMinutes(-1 * $resubmit_interval)) {
                continue;//时间未到，暂不处理
            }

            $this->logger->info(json_encode([
                "opt"             => "resubmit_order",
                "opt_desc"        => "begin deal",
                "id"              => $order->id,
                "sub_order_no"    => $order->sub_order_no,
                "order_req_count" => $order_req_count
            ], JSON_UNESCAPED_SLASHES));

            $channel = OrderUtils::getChannel($order->order->activity_id, $order->ecp_target);
            if (empty($channel)) {
                $this->logger->warning(json_encode([
                    "opt"          => "resubmit_order",
                    "opt_desc"     => "activity error",
                    "id"           => $order->id,
                    "req_order_no" => $order->req_order_no,
                ], JSON_UNESCAPED_SLASHES));
                continue;
            }

            //主订单更新为处理中
            if ($order->order->status != SysCode::ORDER_STATUS_1) {
                DB::table('orders')->where('id', $order->order_id)->update(['status' => SysCode::ORDER_STATUS_1]);
            }

            //先更新状态为处理中
            $order->status          = SysCode::ORDER_SUB_STATUS_2;
            $order->req_order_no    = OrderUtils::getNewReqOrderNo($order);//更新请求订单号
            $order->order_req_count = DB::raw("order_req_count + 1");
            $order->order_req_time  = Carbon::now()->format("Y-m-d H:i:s"); //订单请求时间
            $result_update_status   = $order->save();

            if ($result_update_status) {

                $channel_product_code = empty($order->ecp_target) ? $order->ecp_pcode : ($order->ecp_target . '_' . $order->ecp_pcode);
                $submit_result        = $channel->submit($order->req_order_no, $channel_product_code, $order->goods_num, $order->user_mobile, $order->charge_account);

                $this->logger->info(json_encode([
                    "opt"             => "submit_order",
                    "opt_desc"        => "qcp response",
                    "id"              => $order->id,
                    "req_order_no"    => $order->req_order_no,
                    "order_req_count" => $order_req_count + 1,
                    "channel_rsp"     => $submit_result
                ], JSON_UNESCAPED_SLASHES));

                switch ($submit_result['order_status']) {
                    case BaseChannel::ORDER_STATUS_SUCCESS:
                        $order->status         = SysCode::ORDER_SUB_STATUS_3;
                        $order->third_order_no = $submit_result['third_order_no'];
                        if (!empty($submit_result['cost_price'])) {
                            $order->cost_price = $submit_result['cost_price'];
                        }

                        if (!empty($submit_result['cards'])) {
                            $cards                  = $submit_result['cards'][0];
                            $order->sequence_no     = $cards['no'];
                            $order->activation_code = $cards['pwd'];
                            $order->endtime         = (empty($cards['end']) || $cards == '0000-00-00') ? '1900-01-01' : $cards['end'];
                        }
                        break;
                    case BaseChannel::ORDER_STATUS_FAIL:
                        if ($order_req_count + 1 >= $max_count) {
                            $order->status = SysCode::ORDER_SUB_STATUS_4;
                        } else {
                            $order->status = SysCode::ORDER_SUB_STATUS_5;
                        }
                        if (!empty($submit_result['third_order_no'])) {
                            $order->third_order_no = $submit_result['third_order_no'];
                        }
                        break;
                    case BaseChannel::ORDER_STATUS_DEALING:
                        $order->third_order_no = $submit_result['third_order_no'] ?? '';
                        if (!empty($submit_result['cost_price'])) {
                            $order->cost_price = $submit_result['cost_price'];
                        }
                        break;
                    default:
                        break;
                }

                if ($order->isDirty()) {
                    if (!$order->save()) {
                        $this->logger->warning(json_encode([
                            "opt"          => "submit_order",
                            "opt_desc"     => "after save fail",
                            "id"           => $order->id,
                            "req_order_no" => $order->req_order_no,
                            "changes"      => $order->getDirty()
                        ], JSON_UNESCAPED_SLASHES));
                    }

                    if ($order->status != SysCode::ORDER_SUB_STATUS_4 && $order->status != SysCode::ORDER_SUB_STATUS_5) {
                        DB::table('orders')->where('id', $order->order_id)->update(['status' => SysCode::ORDER_STATUS_1]);
                    }
                }
            } else {
                $this->logger->warning(json_encode([
                    "opt"          => "resubmit_order",
                    "opt_desc"     => "pre save fail",
                    "id"           => $order->id,
                    "sub_order_no" => $order->sub_order_no
                ], JSON_UNESCAPED_SLASHES));
            }

        }
    }
}
