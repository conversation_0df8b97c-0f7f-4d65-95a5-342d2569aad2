<?php

namespace App\Console\Commands;

use App\Mail\OpenAccount;
use App\Models\IcbcOpenAccount;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;

/**
 * 发送邮件
 */
class SendEmail extends Command
{

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'open-account:send-email';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '发送邮件';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

        $column_list = ['company_name','license_key','office_location','contact_number','created_at'];
        $start_time = date('Y-m-d 00:00:00',strtotime('-1 day'));
        $end_time = date('Y-m-d 23:59:59',strtotime('-1 day'));
        $day = date('Y-m-d',strtotime('-1 day'));

        $list = IcbcOpenAccount::whereBetween('created_at',[$start_time,$end_time])->select($column_list)->get();

        if (count($list)>0) {

            $export_data = [];
            foreach ($list as $v) {
                $data = [];
                foreach ($column_list as $column) {
                    $data[] = $v->$column;
                }
                $export_data[] = $data;
            }

            $title_data = ['单位名称','开户许可证号','现法人办公地点','联系电话','提交时间'];
            $column_format = [];
            foreach ($column_list as $k =>$column) {
                if(in_array($column, array("license_key","contact_number"))){
                    $column_format[] = $k + 1;
                }
            }

            $file_name = '预约开户信息_' . date('Y-m-d',strtotime('-1 day'));

            $file_options = ['fileType' => 'xlsx', 'format_str' => $column_format];
            // 导出excel
            $file_path = export_excel($title_data, $export_data, $file_name, $file_options);

            $subject = '在线预约开户信息清单,新增' . count($list). '条';

            Mail::send(new OpenAccount($subject, $file_path, '【'.$day.'】新增预约记录'.count($list).'条。'));

        } else {

            Mail::send(new OpenAccount('在线预约开户信息清单,无新增记录', '', '【'.$day.'】无新增预约记录。'));
        }
    }


}
