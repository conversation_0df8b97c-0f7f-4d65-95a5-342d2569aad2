<?php

namespace App\Console\Commands;

use App\Exceptions\MyException;
use App\Exceptions\MySysException;
use App\Service\SignalDealHelper;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Contracts\Cache\LockTimeoutException;
use Illuminate\Support\Facades\Log;

/**
 * 加锁，并且有信号处理的Command基类。锁名称："command-lock:" . $this->signature。锁默认超时时间为600s。
 * 继承本类需实现handleBusiness方法；
 * 并在kernel->schedule中调度本指令时，不要加onOneServer约束。
 */
abstract class SignalAndLockedBaseCommand extends Command
{
    /**
     * 任务锁名称
     * @var string
     */
    public $lock_name = '';

    /**
     * 任务锁超时时间。默认600s。设置为0则永不超时。
     * @var integer
     */
    protected $lock_ttl = 600;

    /**
     * 锁对象
     * @var Object
     */
    protected $lock;

    protected $start_timestamp = 0;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        $this->lock_name = "command-lock:" . explode(' ', trim(str_replace(["\r", "\n"], ' ', $this->signature)))[0];  //spider_db_cache:command-lock:short:locktest1
        $this->start_timestamp = time();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        SignalDealHelper::installSignal(); //注册信号

        $this->lock = Cache::lock($this->lock_name, $this->lock_ttl, gethostname());

        if ($this->lock->get()) {

            try {

                Log::debug("[pid:" . getmypid() . "] "  . $this->signature . " start.");

                $this->handleBusiness();

                optional($this->lock)->release();

            } catch (LockTimeoutException $e) {

                Log::warning("[pid:" . getmypid() . "] lock timeout. key = {$this->lock_name}. ttl = {$this->lock_ttl}");

            } catch (MyException $e) {

                optional($this->lock)->release();

                Log::warning("[pid:" . getmypid() . "] " . $e->getMessage());

            } catch (MySysException $e) {

                optional($this->lock)->release();

                Log::warning("[pid:" . getmypid() . "] " . $e->getMessage());

            } catch (\Exception $e) {

                optional($this->lock)->release();

                Log::error("[pid:" . getmypid() . "] " . $e->getMessage() . "\n" . $e->getTraceAsString());

            }

            Log::debug("[pid:" . getmypid() . "] "  . $this->signature . " end.");

        } else {

            Log::debug("[pid:" . getmypid() . "] lock not released. key = {$this->lock_name}. ttl = {$this->lock_ttl}");

        }

    }

    // 处理信号。子类在适当位置（在此处终止任务不会对业务逻辑造成破坏）插入该方法来处理信号。
    protected function dealSignal(){

        $signal = SignalDealHelper::getSignal();

        if(in_array($signal, [SIGINT, SIGTERM, SIGHUP])){

            throw new MySysException("signal: {$signal} triggered, command {$this->signature} exit.");

        }
    }

    /**
     * 锁超时之前结束任务。
     * @throws MySysException
     */
    protected function dealCacheTimeout($left_time = 60) {
        if (time() - $this->start_timestamp > $this->lock_ttl - $left_time) {
            throw new MySysException("lock will timeout soon, command {$this->signature} exit.");
        }
    }

}
