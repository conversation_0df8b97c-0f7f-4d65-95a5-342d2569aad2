<?php
/**
 * 短信提交
 *
 * @desc : 用于处理类似星点值这样的短信发送需求
 *
 * 1.成功失败都要发短信
 * 2.不同产品对短信中是否有卡号有要求
 */

namespace App\Console\Commands;

use App\Service\Starnet;
use App\Models\SmsResult;
use App\Exceptions\MyException;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Exceptions\MyNoLogException;


class SmsSend extends SignalAndLockedBaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sms-task:send';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '执行从sms_results表中取出待发送和发送失败需要重试的短信任务';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->lock_ttl = 3600;
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handleBusiness()
    {
        $logger = Log::channel('sms_send');
        $opt    = $this->signature;
        $sms_time_range = time() - config('api.sms_send.sms_time_range') * 24 * 3600;

        try {
            # 获取待发送的任务
//            短信状态(1-待发送，2-发送中，3-发送成功，4-发送失败，5-发送失败不再重试)
            //筛选出待发送和发送失败的短信发送任务
            $where     = [SmsResult::SMS_STATUS_1, SmsResult::SMS_STATUS_4];
            $sendTasks = DB::table('sms_results')
                ->whereIn('status', $where)
                ->where('req_count', '<=', config('api.sms_send.send_max'))
                ->where('created_at', '>=', date('Y-m-d H:i:s', $sms_time_range))
                ->orderBy('id')
                ->limit(100)
                ->get();

            $min_id = 0; //加在while循环结束前的查询条件。

            while (count($sendTasks) > 0) {

                foreach ($sendTasks as $task) {

                    $this->dealSignal();//处理信号

                    $this->dealCacheTimeout();

                    $min_id = $task->id;
                    //发送失败的情况
                    if ($task->status = SmsResult::SMS_STATUS_4) {
                        //重试次数是否到了最大值
                        //如果到了则不再重试
                        if ($task->req_count >= config('api.sms_send.send_max')) {
                            DB::table('sms_results')->where('id',
                                $task->id)->update(['status' => SmsResult::SMS_STATUS_5]);
                            continue;
                        }
                        //重试时间未到
                        if (strtotime($task->req_time) > strtotime('-'.config('api.sms_send.send_interval').' min')) {
                            continue;
                        }
                    }

                    //如果短信信息为空，更改状态为失败不再重试。
                    if (empty($task->content)) {
                        # 短信模板为空，记录日志
                        $logger->info(getStdLogMessage($opt, '短信内容为空.', $task));
                        DB::table('sms_results')->where('id', $task->id)
                            ->update([
                                'status'   => SmsResult::SMS_STATUS_5, 'req_count' => DB::raw('req_count + 1'),
                                'req_time' => date('Y-m-d H:i:s')
                            ]);
                        continue;
                    }

                    # 短信发送完成数据
                    $updateData              = [];
                    $updateData['req_count'] = DB::raw('req_count + 1');
                    $updateData['req_time']  = date('Y-m-d H:i:s');

                    # 发送短信
                    $smsObj      = Starnet::getSmsObj();
                    $send_result = $smsObj->send_sms($task->mobile, $task->content);
                    $result      = json_encode($send_result, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
                    $logger->info(getStdLogMessage($opt, 'send sms mobile',
                        [
                            'mobile' => $task->mobile, 'content' => $task->content, 'result' => $result
                        ]));
                    if (!empty($send_result) && ($send_result['result'] == 0)) {
                        $updateData['gateway_seq'] = $send_result['gateway_seq'];
                        //改为发送中
                        //提交成功
                        $updateData['status'] = SmsResult::SMS_STATUS_2;
                    } else {
                        //提交失败
                        //发送失败
                        $updateData['status'] = SmsResult::SMS_STATUS_4;
                    }

                    # 更新短信发送任务状态
                    $updateResult = DB::table('sms_results')->where('id', $task->id)->update($updateData);
                    if (!$updateResult) {
                        $logger->info(getStdLogMessage($opt, '短信结果更新失败',
                            array_merge(['id' => $task->id], $updateData)));
                        continue;
                    }

                } // end sendTasks foreach

                $this->dealSignal();//处理信号

                $this->dealCacheTimeout();

                $sendTasks      = DB::table('sms_results')
                    ->whereIn('status', $where)
                    ->where('req_count', '<=', config('api.sms_send.send_max'))
                    ->where('id', '>', $min_id)
                    ->where('created_at', '>=', date('Y-m-d H:i:s', $sms_time_range))
                    ->orderBy('id')
                    ->limit(100)
                    ->get();

            } // end while

        } catch (MyNoLogException $exc) {
            $logger->debug(getStdLogMessage($opt, $exc->getMessage()));
        } catch (MyException $exc) {
            $logger->error(getStdLogMessage($opt, $exc->getMessage()));
        } catch (\Exception $e) {
            $logger->error(getStdLogMessage($opt, 'error: '.$e->getMessage(), $e->getTrace()));
        }

    }
}
