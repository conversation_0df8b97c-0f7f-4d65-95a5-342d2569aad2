<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Api\SysCode\SysCode;
use Carbon\Carbon;

/**
 * 订单提交
 */
class TmCacheLimitReplenish extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ap:tm-account-repl';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '明苑风华天猫充值充值账号每月限制缓存补全';

    protected $redis;

    protected $min_id = 0;

    protected $once_limit = 200;

    protected $logger;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->logger = Log::channel();
        $this->redis  = app('redis.connection');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        try {
            //3 代表已发货 5:失败不再重试
            $orders = $this->getTasks();
            $this->doTasks($orders);

        } catch (\Exception $exc) {
//            $msg = $exc->getMessage();
//            $this->logger->error($msg);
//            Log::error($msg, $exc->getTrace());
            Log::error($exc);
        }
    }

    /**
     * 获取要推送的任务
     */
    private function getTasks()
    {
        $activity = DB::table('activities')->where('activity_type', 'TianMao')->first();
        if ($activity) {
            //3 代表已发货 4-失败不重试，5:失败重试 .订单状态为未推送给业务平台的订单
            return DB::table('orders')
                ->where('status', SysCode::ORDER_STATUS_2)
                ->where('activity_id', $activity->id)
                ->where('created_at', '>=', Carbon::now()->startOfMonth()->toDateTimeString())
                ->groupBy('charge_account')
                ->select(['charge_account', DB::raw('count(*) num')])
                ->orderBy('num', 'desc')
                ->get();
        } else {
            return [];
        }
    }


    /**
     * 开始处理
     */

    private function doTasks($orders)
    {
        foreach ($orders as $order) {
            $key_m_account = 'tianmao_m_' . md5($order->charge_account);
            $ttl           = Carbon::now()->endOfMonth()->diffInSeconds(Carbon::now());
            $result        = $this->redis->setex($key_m_account, $ttl, $order->num);
            $this->logger->info(json_encode([
                'charge_account' => $order->charge_account,
                'num'            => $order->num,
                'redis_key'      => $key_m_account,
                'ttl'            => $ttl,
                'result'         => $result
            ], JSON_UNESCAPED_UNICODE));
        }
    }
}
