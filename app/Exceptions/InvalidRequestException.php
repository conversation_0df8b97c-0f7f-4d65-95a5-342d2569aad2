<?php

/**
 * 请求参数验证
 * 验证失败会以json格式返回
 */

namespace App\Exceptions;

use Exception;
use Illuminate\Http\Request;

class InvalidRequestException extends Exception
{
    public function __construct(string $message = "", int $code = 400)
    {
        parent::__construct($message, $code);
    }

    public function render(Request $request)
    {

        // if ($request->expectsJson()) {
        //     // json() 方法第二个参数就是 Http 返回码
        //     return response()->json(['msg' => $this->message], $this->code);
        // }

        // return view('pages.error', ['msg' => $this->message]);

        return response()->json(['msg' => $this->message], $this->code);
    }
}
