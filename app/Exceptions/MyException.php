<?php

namespace App\Exceptions;

/**
 * 自定义的一个异常处理类。扩展构造函数第四个参数：$data。
 */
class MyException extends \Exception
{
    private $data = [];

    /**
     * 构造函数。扩展第四个参数：$data。
     * @param string $message
     * @param int $code
     * @param \Throwable|null $previous
     * @param array $data
     */
    public function __construct($message = '', $code = 0, \Throwable $previous = null, $data = null) {
        parent::__construct($message, $code, $previous);
        if (!empty($data)) {
            $this->data = $data;
        }
    }

    public function __toString() {
        //重写父类方法，自定义字符串输出的样式
        return __CLASS__ . ":[" . $this->code . "]:" . $this->message . "  <br>\n [data]: " . json_encode($this->data, JSON_UNESCAPED_UNICODE) . "<br>";
    }

    public function getData() {
        return $this->data;
    }

    //往$data里添加数据
    public function addData(array $data = []) {
        if (empty($this->data)) {
            $this->data = ($data === null ? [] : $data);
        } else {
            if (!empty($data)) {
                foreach ($data as $key => $val) {
                    $this->data[$key] = $val;
                }
            }
        }
    }

}

//
//try { //使用自定义的异常类捕获一个异常，并处理异常
//    $error = '允许抛出这个错误';
//    throw new MyException($error);
//    //创建一个自定义的异常类对象，通过throw语句抛出
//    echo 'Never executed';
//    //从这里开始，try代码块内的代码将不会再被执行
//} catch (MyException $e) {        //捕获自定义的异常对象
//    echo '捕获异常: ' . $e;        //输出捕获的异常消息
//    $e->customFunction();  //通过自定义的异常对象中的方法处理异常
//}
//echo '你好呀';              //程序没有崩溃继续向下执行




