<?php

namespace App\Exceptions;

/**
 * 自定义的一个异常处理类。扩展构造函数第四个参数：$data。
 */
class MySysException extends \Exception
{
    private $data = [];

    /**
     * 构造函数。扩展第四个参数：$data。
     * @param string $message
     * @param int $code
     * @param \Throwable|null $previous
     * @param array $data
     */
    public function __construct($message = '', $code = 0, \Throwable $previous = null, $data = null)
    {
        parent::__construct($message, $code, $previous);
        $this->data = $data;
    }

    public function __toString()
    {
        //重写父类方法，自定义字符串输出的样式
        return __CLASS__ . ":[" . $this->code . "]:" . $this->message . "  <br>\n [data]: " . json_encode($this -> data, JSON_UNESCAPED_UNICODE) . "<br>";
    }

    public function getData(){
        return $this->data;
    }

}


