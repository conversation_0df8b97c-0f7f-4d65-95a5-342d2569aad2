<?php

/**
 * 活动广告列表拉取（公共）
 */

namespace App\Http\Controllers\Api\Common;

use App\Models\ActivityAd;
use Exception;
use App\Http\Controllers\Api\SysCode\SysCode;
use App\Exceptions\MyException;

class ActivityAdController extends AuthController
{
    public function index() {
        $code = SysCode::SYSTEM_ERROR;
        $msg  = SysCode::$resp_msg[$code];
        $data = [];

        try {

            $list = ActivityAd::where([
                'activity_id' => $this->activity_id,
                'status'   => SysCode::COMMON_STATUS_1,
            ])
                ->where('valid_start_at', '<=', date('Y-m-d H:i:s'))
                ->where('valid_end_at', '>=', date('Y-m-d H:i:s'))
                ->orderBy('sort')
                ->get(['show_title', 'show_img_url', 'jump_url'])->map(function ($value) {
                    $value->show_img_url = getImgUrl($value->show_img_url);
                    return $value->toArray();
                });

            $code = SysCode::SUCCESS;
            $msg  = '成功';
            $data = $list->toArray();

        } catch (MyException $exc) {
            if ($exc->getCode() === 0) {
                $code = $exc->getMessage();
                if ($this->logic_obj) {
                    $msg = $this->logic_obj->getMsgByCode($code);
                } elseif (array_key_exists($code, SysCode::$resp_msg)) {
                    $msg = SysCode::$resp_msg[$code];
                } else {
                    $msg = "错误编码：" . $code . "。" . SysCode::$resp_msg[SysCode::UNKNOW_ERROR];
                }
            } else {
                $code = $exc->getCode();
                $msg  = $exc->getMessage();
            }
            $data = $exc->getData();
        } catch (Exception $exc) {
            if (array_key_exists($exc->getMessage(), SysCode::$resp_msg)) {
                $code = $exc->getMessage();
                $msg  = SysCode::$resp_msg[$code];
            } else {
                $code = SysCode::SYSTEM_ERROR;
                $msg  = SysCode::$resp_msg[$code];
                $this->logError(['code' => $code, 'msg' => $exc->getMessage()]);
            }
        }

        $ret = [
            'code' => $code,
            'msg'  => $msg,
            'data' => $data,
        ];
        return $ret;
    }
}
