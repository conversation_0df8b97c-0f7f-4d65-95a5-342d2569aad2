<?php

/**
 * 作为接口的基类
 */

namespace App\Http\Controllers\Api\Common;

use App\Service\Aes;
use App\Models\Activity;
use App\Exceptions\MyException;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Request;
use App\Http\Controllers\Api\SysCode\SysCode;


class ApiController extends Controller
{
    protected $params;
    public    $activity_id; //活动id
//    public $activity_type; //活动标识
    public $logic_obj; //活动逻辑处理实例

    public function __construct()
    {
        $this->params = Request::all();
        //拆分带分组id的商品id 2024-11-06
        if (!empty($this->params['goods_id'])) {
            list($group_id, $goods_id) = exchange_split($this->params['goods_id']);
            $this->params['goods_id'] = $goods_id;
            $this->params['group_id'] = $group_id;
        }

        // 在构造函数中无法处理session的情况，所以用中间件处理。
        $this->middleware(function ($request, $next) {
            //$inst为当前controller实例
            //因为在中间件中用$this直接调用自己的类方法行不通
            //所以使用下面的方式
            $inst = $request->route()->controller;
            //根据act参数 或 session的activity参数获取logic对象
            try {
                $inst->logic_obj = $inst->getLogic();
                if ($inst->logic_obj !== null) {
                    $inst->activity_id = session('activity.id');
                }
                if (!$inst->logic_obj) {
                    throw new MyException(SysCode::ACTIVITY_ERROR);
                }
            } catch (MyException $exc) {
                $code = SysCode::SYSTEM_ERROR;
                $msg  = SysCode::$resp_msg[$code];
                if ($exc->getCode() === 0) {
                    $code = $exc->getMessage();
                    if ($inst->logic_obj) {
                        $msg = $inst->logic_obj->getMsgByCode($code);
                    } elseif (array_key_exists($code, SysCode::$resp_msg)) {
                        $msg = SysCode::$resp_msg[$code];
                    } else {
                        $msg = SysCode::$resp_msg[SysCode::UNKNOW_ERROR];
                    }
                } else {
                    $code = $exc->getCode();
                    $msg  = $exc->getMessage();
                }

                if ($code !== SysCode::SUCCESS) {
                    $request->attributes->set('my_exception', sprintf('%s:%s', str_replace(base_path() . DIRECTORY_SEPARATOR, '', $exc->getFile()), $exc->getLine()));
                }

                return $this->api_response([
                    'code' => $code,
                    'msg'  => $msg,
                ]);
            }

            if ($inst->logic_obj) {
                //中间件中日志记录
                $request->attributes->add([
                    'activity_id' => $inst->activity_id,
                ]);
            }

            return $next($request);
        });

    }

    /**
     * 统一api响应header
     * @param \Illuminate\View\View|string|array|null $content
     * @param int $status
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Http\Response
     */
    protected function api_response(array $content, $status = 200)
    {
        return response($content, $status, ['Content-Type' => 'application/json;charset=utf-8']);
    }

    /**
     * 记录错误日志。
     * @param $data
     * @param int $log_level
     */
    protected function logError($data, $exception = null)
    {
        $req_time = getMillisecond();
        $request  = request();

        // 记录所有请求信息
        $requestMessage = [
            'req_time'     => $req_time,
            'res_time'     => $req_time,
            'process_time' => '0',
            'baseurl'      => $request->getSchemeAndHttpHost(),
            'method'       => $request->method(),
            'ip'           => $request->ip(),
            'path'         => $request->path(),
            'req_params'   => $request->input(),
            'res_params'   => [],
        ];

        if ($request->attributes->count() > 0) {
            $requestMessage['attach'] = $request->attributes->all();
        }

        $requestMessage['session'] = $request->session()->all();

        $requestMessage['error_msg'] = $data;

        Log::channel('api_log')->error(json_encode($requestMessage, JSON_UNESCAPED_UNICODE));
        if ($exception) {
            Log::error($exception);
        }
    }

    /**
     * 具体的活动登录
     * @param $act
     */

    protected function actLogin()
    {
        if ($this->logic_obj === null) {
            throw new MyException(SysCode::$resp_msg[Syscode::ACTIVITY_ERROR], Syscode::ACTIVITY_ERROR);
        }
        $this->logic_obj->login(session('activity.id'));
    }

    /**
     * 根据act参数 或 session的activity参数获取logic对象
     * @throws MyException
     */
    protected function getLogic()
    {
        $request = request();
        $_sess   = session();
        //session中没有activity这个参数,或者有act参数并且这个act的参数和获取到的act不一致,则根据act重新获取logic
        //session中存在activity时还要判断两个act相同.若相同则返回,不同重新生成.
        if (!$_sess->has('activity') || (array_key_exists('act', $this->params) && $_sess->get('activity.act') != $this->params['act'])) {

            //删除session,目的是为了防止同一个客户端访问不同的活动产生session混乱
            $_sess->forget(['activity', 'user_info', 'extra']);
            //所以还要判断传入的参数中是否含有act参数
            //如果没有则返回null
            if (!array_key_exists('act', $this->params) || empty($this->params['act'])) {
                return null;
            }

            //如果act参数不为空 则进行解密并校验
            $this->params['act'] = ucfirst((new Aes(config('api.app_key')))->decrypt($this->params['act'])); //TODO
            //解密失败会返回false

            if (!$this->params['act']) {
                throw new MyException(SysCode::$resp_msg[Syscode::ACTIVITY_ERROR], Syscode::ACTIVITY_ERROR);
            }

            //然后根据act重新获取logic
            $activity_type_info = Activity::where([
                'activity_type' => $this->params['act'],
                'status'        => SysCode::COMMON_STATUS_1
            ])->first();
            if (empty($activity_type_info)) {
                throw new MyException(SysCode::$resp_msg[Syscode::ACTIVITY_ERROR], Syscode::ACTIVITY_ERROR);
            }
            //然后判断这个logic是否存在
            $class_name = 'App\\Logic\\' . $this->params['act'];
            if (!class_exists($class_name)) {
                throw new MyException(SysCode::$resp_msg[Syscode::ACTIVITY_ERROR], Syscode::ACTIVITY_ERROR);
            }

            $_sess->put('activity', [
                'id'         => $activity_type_info->id,
                'type'       => $activity_type_info->activity_type,
                'act'        => $request->input('act'),
                'begin_time' => $activity_type_info->begin_time,
                'end_time'   => $activity_type_info->end_time,
            ]);
            $_sess->save();

        } else {
            //这里的条件：if (session('activity') && (!request()->has('act') || session('activity.act') == request('act'))) { }
            $class_name = 'App\\Logic\\' . $_sess->get('activity.type');
        }

        $obj = new $class_name();

        $obj->activity_id = $_sess->get("activity.id");

        return $obj;
    }
}
