<?php

/**
 * 作为接口的基类
 * 公共方法
 * 继承此类必须是登录状态
 * 需要验证登录的方法集成此类
 */

namespace App\Http\Controllers\Api\Common;

use App\Http\Controllers\Api\SysCode\SysCode;
use Illuminate\Support\Facades\DB;
use App\Exceptions\MyException;
use Illuminate\Support\Facades\Log;

class AuthController extends ApiController
{
    public function __construct()
    {
        parent::__construct();
        $this->middleware(function ($request, $next) {
            $inst = $request->route()->controller;
            try {
                //判断用户是否登录
                if (!session('user_info') || !session('activity.id')) {
                    throw new MyException(SysCode::NOT_LOGIN);
                }
                //活动时间的控制
                //status 为1 是启用状态
                //判断活动是否处于正常状态
//                $act_info = DB::table('activities')->where(['id' => $this->activity_id, 'status' => SysCode::COMMON_STATUS_1])->first();
//                if (empty($act_info)) {
//                    throw new MyException(SysCode::ACTIVITY_ERROR);
//                }
                //查看订单时不需要判断活动时间。
                if (!$inst instanceof OrderRecordController) {
                    //判断活动的兑换时间
                    $inst->logic_obj->verifyActifity(false);

//                    if (strtotime($act_info->begin_time) > time()) {
//                        //兑换时间尚未开启
//                        throw new MyException(SysCode::EXCHANGE_NOT_BEGIN);
//                    }
                    //这里不判断结束时间，下单时判断。以免影响订单查询。
//                    if (strtotime($act_info->end_time) < time()) {
//                        //兑换时间已经结束
//                        throw new MyException(SysCode::EXCHANGE_HAD_END);
//                    }
                }
            } catch (MyException $exc) {
                if ($exc->getCode() === 0) {
                    $code = $exc->getMessage();
                    if (!empty($inst->logic_obj)) {
                        $msg = $inst->logic_obj->getMsgByCode($code);
                    } elseif (array_key_exists($code, SysCode::$resp_msg)) {
                        $msg = SysCode::$resp_msg[$code];
                    } else {
                        $msg = SysCode::$resp_msg[SysCode::UNKNOW_ERROR];
                    }
                } else {
                    $code = $exc->getCode();
                    $msg  = $exc->getMessage();
                }
                return response([
                    'code' => $code,
                    'msg'  => $msg,
                ], 200, ['Content-Type' => 'application/json;charset=utf-8']);
            }
            return $next($request);
        });
    }
}
