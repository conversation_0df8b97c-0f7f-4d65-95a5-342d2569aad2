<?php

namespace App\Http\Controllers\Api\Common;

use App\Exceptions\MyException;
use App\Http\Controllers\Api\SysCode\SysCode;
use App\Models\CebGhUser;
use App\Service\Sms;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * 发送验证码
 */
class CaptchaController extends ApiController
{
    public function index(Request $request)
    {
        $logger = Log::channel('sms_captcha_log');
        try {
            $this->logic_obj->verifyActifity();
            $redis = app('redis.connection');

            $mobile = $request->get('mobile', '');

            $redis_cache_key = 'act_' . session('activity.id', '0') . '_' . $mobile;

            if (empty($mobile) || !check_mobile($mobile)) {
                throw new MyException('手机号错误', SysCode::MOBILE_ERROR);
            }

            if (check_virtual_mobile($mobile) && session('activity.type') == 'TianMao') {
                //明苑风华-农行-天猫，禁用虚拟运营商
                throw new MyException('请使用非虚拟运营商的手机号进行充值', SysCode::MOBILE_ERROR);
            }

            if (session('activity.type') == 'CebGh') {
                //光大银行的手机号不在白名单的不发验证码
                $user = CebGhUser::where(['mobile' => $mobile, 'status' => 1])->first();
                if (!$user) {
                    throw new MyException('未获得活动资格', SysCode::NOT_CHANCE_3019);
                }
                if (Carbon::now() > Carbon::parse('2023-09-15 23:59:59') && $user->department != '总行行长室') {
                    throw new MyException('本活动已结束^_^', SysCode::EXCHANGE_HAD_END);
                }
            }

            $captcha = $request->get('captcha', '');//图形验证码
            if (!in_array(session('activity.type'), ['MengshangJf', 'Aqy', 'AqyCC', 'JkIqy', 'CebGh'])) { //蒙商登录入口没有图形校验码
                if (empty($captcha) || !captcha_check($captcha)) {
                    throw new MyException('校验码错误，请输入正确的校验码。', SysCode::CAPTCHA_IMG_ERROR);
                }
            }

            if (session()->has('sms_captcha') && session('sms_captcha.end') > Carbon::now()->timestamp) {
                $time = session('sms_captcha.end') - Carbon::now()->timestamp;
                if ($time > 0) {
                    throw new MyException('请' . $time . '秒后重试', SysCode::PARAMS_ERROR);
                }
            }

            if ($redis_cache_val = $redis->get($redis_cache_key)) {
                $redis_cache_val = json_decode($redis_cache_val, true);
                $time            = $redis_cache_val['end'] - Carbon::now()->timestamp;
                if ($time > 0) {
                    throw new MyException('请' . $time . '秒后重试', SysCode::PARAMS_ERROR);
                }
            }

            $code              = random_int(100000, 999999);
            $sms_template_code = config('sms.captcha_sms_template_code');
            $expired           = config('sms.captcha_expired');
            $end               = config('sms.captcha_end');
            $signName          = config('sms.signName');

//            $logger->info("验证码：mobile=$mobile, code=$code");

            $sms_setting = config('sms.activity_' . session('activity.type', ''));
            if (empty($sms_setting)) {
                $logger->error("短信配置" . 'sms.activity_' . session('activity.type', '') . '不存在。');
                throw new MyException('验证码发送失败', SysCode::SYSTEM_ERROR);
            }
            $result = Sms::sendSms($mobile, Sms::MSGTYPE_TEMPLATE, ['code' => $code], $sms_setting['captcha_temp_id'],
                $sms_setting['sign_name'], $sms_setting['appid'], $sms_setting['secret_key']);

            $logger->info("验证码：mobile=$mobile, code=$code, sms_result=" . (is_array($result) ? json_encode($result, JSON_UNESCAPED_UNICODE) : $result));

            session()->forget(['sms_captcha']);

            if ($result['code'] == 200) {
                $session_data = [
                    'code'    => $code,
                    'mobile'  => $mobile,
                    'expired' => Carbon::now()->timestamp + $expired,
                    'end'     => Carbon::now()->timestamp + $end,
                ];
                session(['sms_captcha' => $session_data]);
                session()->save();

                $request->attributes->set('sms_captcha_session', $session_data);

                //限制放在redis中，防止技术性刷短信验证码接口
                if ($redis) {
                    $redis->setex($redis_cache_key, $end, json_encode($session_data));
                }

            } else {
                throw new MyException('验证码发送失败', SysCode::SYSTEM_ERROR);
            }

            throw new MyException(SysCode::SUCCESS);

        } catch (MyException $exc) {
            if ($exc->getCode() === 0) {
                $code = $exc->getMessage();
                $msg  = SysCode::$resp_msg[$code];
            } else {
                $code = $exc->getCode();
                $msg  = $exc->getMessage();
            }
            if ($code !== SysCode::SUCCESS) {
                $request->attributes->set('my_exception', sprintf('%s:%s', str_replace(base_path() . DIRECTORY_SEPARATOR, '', $exc->getFile()), $exc->getLine()));
            }
        } catch (\Exception $exc) {
            if (array_key_exists($exc->getMessage(), SysCode::$resp_msg)) {
                $code = $exc->getMessage();
                $msg  = SysCode::$resp_msg[$code];
            } else {
                $code = SysCode::SYSTEM_ERROR;
                $msg  = SysCode::$resp_msg[$code];
            }
            $request->attributes->set('exception', sprintf('%s:%s, %s', str_replace(base_path() . DIRECTORY_SEPARATOR, '', $exc->getFile()), $exc->getLine(), $exc->getMessage()));
            Log::error($exc);
        }

        return [
            'code' => $code,
            'msg'  => $msg,
        ];
    }
}
