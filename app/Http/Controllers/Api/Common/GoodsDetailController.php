<?php

/**
 * 商品详情。（公共）
 */

namespace App\Http\Controllers\Api\Common;

use App\Exceptions\MyException;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Api\SysCode\SysCode;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;


class GoodsDetailController extends ApiController
{
    protected $user;
    private   $goods_info;

    public function index(Request $request)
    {
        $code = SysCode::SYSTEM_ERROR;
        $msg  = SysCode::$resp_msg[$code];

        try {
            //检查参数
            $this->checkParams();
            //返回数据
            $this->getGoodsDetail();

            $this->goods_info = $this->logic_obj->handlerAfterGetGoodsDetail($this->goods_info);

            throw new MyException(SysCode::SUCCESS);

        } catch (MyException $exc) {
            if ($exc->getCode() === 0) {
                $code = $exc->getMessage();
                if ($this->logic_obj) {
                    $msg = $this->logic_obj->getMsgByCode($code);
                } elseif (array_key_exists($code, SysCode::$resp_msg)) {
                    $msg = SysCode::$resp_msg[$code];
                } else {
                    $msg = "错误编码：" . $code . "。" . SysCode::$resp_msg[SysCode::UNKNOW_ERROR];
                }
            } else {
                $code = $exc->getCode();
                $msg  = $exc->getMessage();
            }
            if ($code !== SysCode::SUCCESS) {
                $request->attributes->set('my_exception', sprintf('%s:%s', str_replace(base_path() . DIRECTORY_SEPARATOR, '', $exc->getFile()), $exc->getLine()));
            }
        } catch (Exception $exc) {
            if (array_key_exists($exc->getMessage(), SysCode::$resp_msg)) {
                $code = $exc->getMessage();
                $msg  = SysCode::$resp_msg[$code];
            } else {
                $code = SysCode::SYSTEM_ERROR;
                $msg  = SysCode::$resp_msg[$code];
            }
            $request->attributes->set('exception', sprintf('%s:%s, %s', str_replace(base_path() . DIRECTORY_SEPARATOR, '', $exc->getFile()), $exc->getLine(), $exc->getMessage()));
            Log::error($exc);
        }

        $data = [
            'code' => $code,
            'msg'  => $msg,
            'data' => $code == SysCode::SUCCESS ? $this->goods_info : [],
        ];
        return $data;
    }

    //检查参数
    private function checkParams()
    {
        //接收goods_id
        if (empty($this->params['goods_id'])) {
            throw new MyException(SysCode::PARAMS_ERROR);
        }
    }

    /**
     * 登录成功并返回相应的数据
     */
    private function getGoodsDetail()
    {
        $this->goods_info = DB::table('goods')
            ->join('goods_details', 'goods.id', '=', 'goods_details.goods_id')
            ->where('goods.id', $this->params['goods_id'])
            ->select('goods.id',
                'goods.goods_type',
                'goods.goods_name',
                'goods.goods_attr',
                'goods.pre_verify_type',
                'goods.is_show_card_no',
                'goods.advance_days',
                'goods.service_time',
                'goods_details.goods_imgs',
                'goods_details.goods_desc',
                'goods_details.goods_params',
                'goods_details.goods_instr'
            )->first();

        if (empty($this->goods_info)) {
            throw new MyException(SysCode::GOOD_DETAILS_FAILD);
        }

        $this->goods_info->goods_imgs = collect(json_decode($this->goods_info->goods_imgs, true))->map(function ($v) {
//            return env('OSS_URL') . $v;
            return Storage::disk(config("admin.upload.disk"))->url($v);
        });

        //还原带group_id的goods_id，不判断group_id
        if (!empty($this->params['group_id'])) {
            $this->goods_info->id = $this->params['group_id'] . '-' . $this->goods_info->id;
        }

//        throw new MyException(SysCode::SUCCESS);
    }
}
