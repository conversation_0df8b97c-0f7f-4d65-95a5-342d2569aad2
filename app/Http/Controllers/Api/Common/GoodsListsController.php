<?php

/**
 * 商品列表 (公共)
 * @package App\Http\Controllers\Api
 */

namespace App\Http\Controllers\Api\Common;

use Exception;
use App\Exceptions\MyException;
use App\Http\Controllers\Api\SysCode\SysCode;
use App\Http\Controllers\Api\Common\ApiController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class GoodsListsController extends ApiController
{
    protected $params;
    protected $user;

    public function index(Request $request)
    {
        $code     = SysCode::SYSTEM_ERROR;
        $msg      = SysCode::$resp_msg[$code];
        $ret_data = [];

        try {
            $goods_list = $this->logic_obj->handlerBeforeGetGoodsList();
            if (is_null($goods_list)) {
                $goods_list = $this->getGoodsList();
            }

            $goods_list = $this->logic_obj->handlerAfterGetGoodsList($goods_list);

            $code     = SysCode::SUCCESS;
            $msg      = SysCode::$resp_msg[$code];
            $ret_data = $goods_list;

        } catch (MyException $exc) {
            if ($exc->getCode() === 0) {
                $code = $exc->getMessage();
                if ($this->logic_obj) {
                    $msg = $this->logic_obj->getMsgByCode($code);
                } elseif (array_key_exists($code, SysCode::$resp_msg)) {
                    $msg = SysCode::$resp_msg[$code];
                } else {
                    $msg = SysCode::$resp_msg[SysCode::UNKNOW_ERROR];
                }
            } else {
                $code = $exc->getCode();
                $msg  = $exc->getMessage();
            }
            $ret_data = $exc->getData();
            if ($code !== SysCode::SUCCESS) {
                $request->attributes->set('my_exception', sprintf('%s:%s', str_replace(base_path() . DIRECTORY_SEPARATOR, '', $exc->getFile()), $exc->getLine()));
            }

        } catch (Exception $exc) {
            if (array_key_exists($exc->getMessage(), SysCode::$resp_msg)) {
                $code = $exc->getMessage();
                $msg  = SysCode::$resp_msg[$code];
            } else {
                $code = SysCode::SYSTEM_ERROR;
                $msg  = SysCode::$resp_msg[$code];
            }
            $request->attributes->set('exception', sprintf('%s:%s, %s', str_replace(base_path() . DIRECTORY_SEPARATOR, '', $exc->getFile()), $exc->getLine(), $exc->getMessage()));
            Log::error($exc);
        }

        $data = [
            'code' => $code,
            'msg'  => $msg,
            'data' => $ret_data
        ];

        return $this->api_response($data);
    }

    private function getGoodsList()
    {
        $goods_info = DB::table('activity_prizes')
            ->leftJoin('goods', 'activity_prizes.goods_id', '=', 'goods.id')
            ->where(['activity_prizes.activity_id' => $this->activity_id, "goods.status" => "1"])
//            ->select('goods.id', 'goods.goods_show_img', 'goods.goods_name', 'goods.goods_type','goods.goods_price', 'goods.goods_attr')
            ->select('goods.id', 'goods.goods_show_img', 'goods.goods_name', 'goods.goods_type', 'goods.goods_attr', 'goods.advance_days', 'goods.service_time', 'activity_prizes.prize_level', 'activity_prizes.prize_level2')
            ->orderBy('activity_prizes.order_by')
            ->get();

        if (count($goods_info) == 0) {
            throw new MyException(SysCode::$resp_msg[SysCode::GOOD_LISTS_FAILD], SysCode::GOOD_LISTS_FAILD);
        }
        $goods_list = [];

        $goods_info->map(function ($value) use (&$goods_list) {
//            $value->goods_show_img = Storage::disk(config("admin.upload.disk"))->url($value->goods_show_img);
            if ($value->goods_show_img) {
                $value->goods_show_img = env('OSS_URL') . $value->goods_show_img;
            }

            $goods_list[$value->prize_level][$value->prize_level2][] = $value;

        });

        return $goods_list;
    }

///api/goods-list
//```
//请求参数：
//   无
//
//响应：
//{
//	"code": 200,
//	"msg": "成功",
//	"data": [{
//		"id": 20,
//		"goods_show_img": "https://star0.oss-cn-shanghai.aliyuncs.com/prize/imgs/goods/主图1.png",
//		"goods_name": "苏泊尔智能电饭煲",
//		"is_has_stock": 1 //暂不用这个，根据活动要求，有的活动没有。
//	}]
//}
//
//修正后的：加了礼品级别  data[level1][level2]
//{
//	"code": 200,
//	"msg": "成功",
//	"data": {
//		"1": {
//			"1": {
//				"id": 1,
//				"goods_show_img": "http:\/\/219.141.172.134:8085\/upload\/images\/旅行三件套-800px.png",
//				"goods_name": "测试商品1",
//				"goods_type": 1,
//				"goods_attr": "红色",
//				"prize_level": 1,
//				"prize_level2": 1
//			}
//		}
//	}
//}
}
