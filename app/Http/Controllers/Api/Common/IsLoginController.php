<?php

/**
 * 判断是否登录接口
 */

namespace App\Http\Controllers\Api\Common;

use App\Http\Controllers\Controller;
use Exception;
use App\Http\Controllers\Api\SysCode\SysCode;
use App\Exceptions\MyException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;


class IsLoginController extends Controller
{
    public function index(Request $request)
    {
        $ret_data = [];
        $code     = SysCode::SYSTEM_ERROR;
        $msg      = SysCode::$resp_msg[$code];
        try {
            //判断用户是否登录
            if (!session('user_info') || !session('activity.id')) {
                throw new MyException(SysCode::NOT_LOGIN);
            } else {
                throw new MyException(SysCode::SUCCESS);
            }
        } catch (MyException $exc) {
            if ($exc->getCode() === 0) {
                $code = $exc->getMessage();
                if (array_key_exists($code, SysCode::$resp_msg)) {
                    $msg = SysCode::$resp_msg[$code];
                } else {
                    $msg = SysCode::$resp_msg[SysCode::UNKNOW_ERROR];
                }
            } else {
                $code = $exc->getCode();
                $msg  = $exc->getMessage();
            }
        } catch (Exception $exc) {
            if (array_key_exists($exc->getMessage(), SysCode::$resp_msg)) {
                $code = $exc->getMessage();
                $msg  = SysCode::$resp_msg[$code];
            } else {
                $code = SysCode::SYSTEM_ERROR;
                $msg  = SysCode::$resp_msg[$code];
            }
            $request->attributes->set('exception', sprintf('%s:%s, %s', str_replace(base_path() . DIRECTORY_SEPARATOR, '', $exc->getFile()), $exc->getLine(), $exc->getMessage()));
            Log::error($exc);
        }
        $data = [
            'code' => $code,
            'msg'  => $msg,
        ];

        return response($data, 200, ['Content-Type' => 'application/json;charset=utf-8']);
    }
}
