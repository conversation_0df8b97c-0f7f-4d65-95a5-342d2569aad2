<?php

/**
 * 公共登录接口
 */

namespace App\Http\Controllers\Api\Common;

use Exception;
use App\Http\Controllers\Api\SysCode\SysCode;
use App\Exceptions\MyException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class LoginController extends ApiController
{
    public function index(Request $request)
    {
        $ret_data = [];
        $code     = SysCode::SYSTEM_ERROR;
        $msg      = SysCode::$resp_msg[$code];
        try {
            //对act参数进行处理
            $this->handleAct();
            //按照不同act进行登录
            $this->actLogin();
        } catch (MyException $exc) {
            if ($exc->getCode() === 0) {
                $code = $exc->getMessage();
                if ($this->logic_obj) {
                    $msg = $this->logic_obj->getMsgByCode($code);
                } elseif (array_key_exists($code, SysCode::$resp_msg)) {
                    $msg = SysCode::$resp_msg[$code];
                } else {
                    $msg = SysCode::$resp_msg[SysCode::UNKNOW_ERROR];
                }
            } else {
                $code = $exc->getCode();
                $msg  = $exc->getMessage();
            }
            $ret_data = $exc->getData();
            if ($code !== SysCode::SUCCESS) {
                $request->attributes->set('my_exception', sprintf('%s:%s, %s', str_replace(base_path() . DIRECTORY_SEPARATOR, '', $exc->getFile()), $exc->getLine(), $exc->getMessage()));
            }
        } catch (Exception $exc) {
            if (array_key_exists($exc->getMessage(), SysCode::$resp_msg)) {
                $code = $exc->getMessage();
                $msg  = SysCode::$resp_msg[$code];
            } else {
                $code = SysCode::SYSTEM_ERROR;
                $msg  = SysCode::$resp_msg[$code];
            }
            $request->attributes->set('exception', sprintf('%s:%s, %s', str_replace(base_path() . DIRECTORY_SEPARATOR, '', $exc->getFile()), $exc->getLine(), $exc->getMessage()));
            Log::error($exc);
        }
        $data = [
            'code' => $code,
            'msg'  => $msg,
            'data' => $ret_data
        ];

        return $this->api_response($data);
    }

    private function handleAct()
    {
        //普通登录携带act参数的情况
        //如果没有act参数则提示错误
        if (!isset($this->params['act']) || empty($this->params['act'])) {
            throw new MyException(SysCode::$resp_msg[SysCode::ACTIVITY_ERROR], SysCode::ACTIVITY_ERROR);
        }
    }
}
