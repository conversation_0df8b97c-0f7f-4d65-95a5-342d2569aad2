<?php

/**
 * 登录之前首先调用这个接口（只用于e生活内获取e生活登录参数）
 * 公共方法
 * Desc : 如果在e生活中登录,依靠e生活环境
 *
 */

namespace App\Http\Controllers\Api\Common;

use App\Exceptions\MyException;
use Exception;
use App\Service\DecryptLoginParams;
use App\Http\Controllers\Api\SysCode\SysCode;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;


class LoginParamsController extends ApiController
{
    private $decryped_params;

    public function index(Request $request)
    {
        $code = SysCode::SYSTEM_ERROR;
        $msg  = SysCode::$resp_msg[$code];
        try {
            //验证参数
            $this->checkParams();
            //解析出来的参数
            $this->decrypt();
            //将e生活的手机号存入session
            $this->saveMobileNo();
            //对act参数进行处理
            $this->handleAct();
            //根据act参数选择登录哪个活动
            $this->actLogin();
        } catch (MyException $exc) {
            if ($exc->getCode() === 0) {
                $code = $exc->getMessage();
                if ($this->logic_obj) {
                    $msg = $this->logic_obj->getMsgByCode($code);
                } elseif (array_key_exists($code, SysCode::$resp_msg)) {
                    $msg = SysCode::$resp_msg[$code];
                } else {
                    $msg = "错误编码：" . $code . "。" . SysCode::$resp_msg[SysCode::UNKNOW_ERROR];
                }
            } else {
                $code = $exc->getCode();
                $msg  = $exc->getMessage();
            }
            if ($code !== SysCode::SUCCESS) {
                $request->attributes->set('my_exception', sprintf('%s:%s', str_replace(base_path() . DIRECTORY_SEPARATOR, '', $exc->getFile()), $exc->getLine()));
            }
        } catch (Exception $exc) {
            if (array_key_exists($exc->getMessage(), SysCode::$resp_msg)) {
                $code = $exc->getMessage();
                $msg  = SysCode::$resp_msg[$code];
            } else {
                $code = SysCode::SYSTEM_ERROR;
                $msg  = SysCode::$resp_msg[$code];
            }
            $request->attributes->set('exception', sprintf('%s:%s, %s', str_replace(base_path() . DIRECTORY_SEPARATOR, '', $exc->getFile()), $exc->getLine(), $exc->getMessage()));
            Log::error($exc);
        }

        if ($code != SysCode::SUCCESS) {
            //登录失败的，清除session，以防以前登录成功的数据继续有效。
            session()->forget('user');
        }
        $data = [
            'code' => $code,
            'msg'  => $msg,
        ];
        return $data;
    }

    //获取参数
    private function checkParams()
    {
        if (empty($this->params['loginParams'])) {
            throw new MyException(Syscode::PARAMS_ERROR);
        }
    }

    private function decrypt()
    {
        $this->decryped_params = DecryptLoginParams::decrypt($this->params['loginParams']);
        if (!$this->decryped_params) {
            throw new MyException(SysCode::DECRYPT_ERROR);
        }
        $this->params['decryped_params'] = $this->decryped_params;
    }

    private function saveMobileNo()
    {
        session()->put('user.mobile', $this->decryped_params['phone']);
        session()->save();
    }

    private function handleAct()
    {
        //依靠e生活登录,并且携带act参数的情况
        //如果没有act参数则不处理, 直接返回成功兼容之前逻辑
        if (!isset($this->params['act']) || empty($this->params['act'])) {
            throw new MyException(SysCode::SUCCESS);
        }
    }
}
