<?php

/**
 * 订单状态回调 QCP1(已弃用)
 * 公共方法
 * 接收QCP 订单状态的回调
 */

namespace App\Http\Controllers\Api\Common;

use App\Exceptions\MyException;
use App\Http\Controllers\Controller;
use App\Libraries\OrderUtils;
use App\Models\OrderSub;
use Exception;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Api\SysCode\SysCode;
use App\Http\Requests\Api\CallbackRequest;
use Illuminate\Support\Facades\Log;
use App\Models\Activity;
use Illuminate\Support\Facades\Request;

class OrderCallbackController extends Controller
{
    protected $params;

    public function __construct()
    {
        $this->params = Request::all();
    }

    public function index()
    {
        try {
            $this->handelOrder();
        } catch (Exception $exc) {
            $code = $exc->getMessage();
            if (!array_key_exists($code, SysCode::$resp_msg)) {
                $code = SysCode::SYSTEM_ERROR;
                Log::channel('order_callback')->warning($exc->getMessage());
            }
        }
        $data = [
            'request'  => $this->params,
            'response' => SysCode::$resp_msg[$code],
        ];

        //记日志
        $this->write_log($data);
        return $code == SysCode::SUCCESS ? 'success' : 'false';
    }


    private function handelOrder()
    {
        //判断接收的参数
        if (empty($this->params['requestid'])
            || empty($this->params['orderid'])
            || !in_array($this->params['state'], array(1, 2))) {
            throw new MyException(SysCode::PARAMS_ERROR);
        }

        $order_sub = DB::table('order_subs')->where('sub_order_no', OrderUtils::getSubOrderNo($this->params['requestid']))->first();

        if (!$order_sub || $order_sub->third_order_no != $this->params['orderid']) {
            throw new MyException(SysCode::ORDER_NOT_EXIST);
        }

        $max_count = intval(config('app.order_submit.max_count'));

        switch ($this->params['state']) {
            //1成功
            case 1:
                //activation_code：激活码，卡密产品有效
                // sequence_no：卡号，卡密产品有效
                // endtime：有效期，卡密产品有效
                // logistics_sn：物流单号。实物产品有效
                // logistics_company：物流公司。实物产品有效
                $update_data = [
                    'status'                => SysCode::ORDER_SUB_STATUS_3, //已发货
                    'deliver_complete_time' => Carbon::now()->format("Y-m-d H:i:s"),
                ];
                $result      = true;
                break;
            case 2://失败
                $update_data = [
                    'status'                => $order_sub->order_req_count >= $max_count ? SysCode::ORDER_SUB_STATUS_4 : SysCode::ORDER_SUB_STATUS_5, //发货失败
                    'deliver_complete_time' => Carbon::now()->format("Y-m-d H:i:s"),
                ];
                $result      = true;
                break;
            default:
                $result = false;
                break;
        }

        if ($result) {

            if ($order_sub->status != $update_data['status']) {
                DB::table('order_subs')
                    ->where('id', $order_sub->id)
                    ->update($update_data);
            }

            throw new MyException(SysCode::SUCCESS);

        } else {
            throw new MyException(SysCode::SYSTEM_ERROR);
        }
    }


    private function write_log($resp_info)
    {
        $logger   = Log::channel('order_callback');
        $log_data = [
            'request'  => $this->params,
            'response' => $resp_info
        ];
        $logger->info(json_encode($log_data));
    }
}
