<?php

/**
 * 订单状态回调 第二版，支持v1和v2
 * 接收QCP 订单状态的回调
 */

namespace App\Http\Controllers\Api\Common;

use App\Exceptions\MyException;
use App\Http\Controllers\Controller;
use App\Libraries\Channel\BaseChannel;
use App\Libraries\Enums;
use App\Libraries\OrderUtils;
use App\Models\OrderSub;
use Exception;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Api\SysCode\SysCode;
use Illuminate\Support\Facades\Log;

class OrderCallbackV2Controller extends Controller
{
    protected $params;
    protected $logger;
    protected $channel;
    protected $callback_params = [];

    public function __construct()
    {
        $this->logger = Log::channel('order_callback');
        $this->params = request()->all();
    }

    public function index(Request $request, $activity_id = 0)
    {
        try {
            $channel_type  = empty($this->params['method']) ? Enums::QCPV1 : Enums::QCPV2;
            $this->channel = OrderUtils::getChannel($activity_id, $channel_type);
            if (empty($this->channel)) {
                //活动id不正确，找不到活动
                throw new MyException(SysCode::PARAMS_ERROR);
            }

            //同一渠道默认appid切换成新的appid时，可能会产生回调签名验证失败的错误，这里是为了处理这种问题
            //如果产生签名错误，则用默认渠道参数再处理一遍
            try {
                $this->callback_params = $this->channel->callbackParamsDeal($request);
            } catch (MyException $exc) {
                if ($exc->getCode() === 0) {
                    $code = $exc->getMessage();
                } else {
                    $code = $exc->getCode();
                }
                if ($code == BaseChannel::APPID_ERROR || $code == BaseChannel::SIGN_ERROR) {
                    $this->channel         = OrderUtils::getDefaultChannel($activity_id, $channel_type);
                    $this->callback_params = $this->channel->callbackParamsDeal($request);
                } else {
                    throw $exc;
                }
            }

            $order_sub = OrderSub::where('req_order_no', $this->callback_params['req_order_no'])->first();

            //如果没找到，且请求订单号中没有下划线，则用sub_order_no再查找一次，用于新旧版本交替时兼容性操作
            if (!$order_sub && !str_contains($this->callback_params['req_order_no'], '_')) {
                $order_sub = OrderSub::where('sub_order_no', $this->callback_params['req_order_no'])->first();
            }

            if (!$order_sub) {
                throw new MyException(SysCode::ORDER_NOT_EXIST);
            }

            switch ($this->callback_params['order_status']) {
                case BaseChannel::ORDER_STATUS_SUCCESS:
                    $order_sub->status         = SysCode::ORDER_SUB_STATUS_3;
                    $order_sub->third_order_no = $this->callback_params['third_order_no'];
                    if (!empty($this->callback_params['cost_price'])) {
                        $order_sub->cost_price = $this->callback_params['cost_price'];
                    }
                    if (!empty($this->callback_params['finish_time'])) {
                        $order_sub->deliver_complete_time = $this->callback_params['finish_time'];
                    } else {
                        $order_sub->deliver_complete_time = Carbon::now()->format("Y-m-d H:i:s");
                    }
                    if (!empty($this->callback_params['cards'])) {
                        $card                       = $this->callback_params['cards'][0];
                        $order_sub->sequence_no     = $card['no'];
                        $order_sub->activation_code = $card['pwd'];
                        $order_sub->endtime         = (empty($card['end']) || $card['end'] == '0000-00-00') ? '2099-12-31' : $card['end'];
                    }
                    break;
                case BaseChannel::ORDER_STATUS_FAIL:
                    if ($order_sub->order_req_count >= intval(config('app.order_submit.max_count'))) {
                        $order_sub->status = SysCode::ORDER_SUB_STATUS_4;
                    } else {
                        $order_sub->status = SysCode::ORDER_SUB_STATUS_5;
                    }
                    if (!empty($this->callback_params['third_order_no'])) {
                        $order_sub->third_order_no = $this->callback_params['third_order_no'];
                    }
                    if (!empty($this->callback_params['finish_time'])) {
                        $order_sub->deliver_complete_time = $this->callback_params['finish_time'];
                    } else {
                        $order_sub->deliver_complete_time = Carbon::now()->format("Y-m-d H:i:s");
                    }
                    break;
                case BaseChannel::ORDER_STATUS_DEALING:
                    $order_sub->third_order_no = $this->callback_params['third_order_no'] ?? '';
                    if (!empty($this->callback_params['cost_price'])) {
                        $order_sub->cost_price = $this->callback_params['cost_price'];
                    }
                    break;
                default:
                    break;
            }

            if ($order_sub->isDirty()) {
                if (!$order_sub->save()) {
                    $this->write_log('warning', [
                        "opt"          => "order_callback",
                        "opt_desc"     => "save fail",
                        "id"           => $order_sub->id,
                        "req_order_no" => $order_sub->req_order_no,
                        "changes"      => $order_sub->getDirty()
                    ]);
                    throw new MyException(SysCode::SYSTEM_ERROR);
                }
            }

            return $this->channel->callbackResponse($request, $this->callback_params);

        } catch (Exception $exc) {
            $code = $exc->getCode() === 0 ? $exc->getMessage() : $exc->getCode();
            $this->write_log('warning', ['error' => $exc->getMessage(), 'error_msg' => BaseChannel::$callback_msg[$code] ?? (SysCode::$resp_msg[$code]) ?? $exc->getMessage()]);
            return $this->channel->callbackResponse($request, $this->callback_params, $exc);
        }
    }

    private function write_log($level, $logger_data)
    {
        $this->logger->$level(json_encode(array_merge([
            'request'         => $this->params,
            'callback_params' => $this->callback_params,
        ], $logger_data), JSON_UNESCAPED_UNICODE));
    }
}
