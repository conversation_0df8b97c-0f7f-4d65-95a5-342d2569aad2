<?php

/**
 * 公共生成订单
 *
 * desc: 所有兑换类活动下订单的公共部分
 * 可以根据不同情况在Logic文件夹下面
 * 写活动的特殊逻辑
 */

namespace App\Http\Controllers\Api\Common;

use App\Models\ExchangeGroup;
use App\Models\Goods;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Api\SysCode\SysCode;
use App\Exceptions\MyException;
use Illuminate\Support\Facades\Log;

class OrderController extends AuthController
{
    protected $goods_info;
    protected $group_info;
    protected $activity_user_id;
    protected $prize_level  = 0;
    protected $prize_level2 = 0;

    public function index(Request $request)
    {
        $code = SysCode::SYSTEM_ERROR;
        $msg  = SysCode::$resp_msg[$code];
        try {
            //检查参数
            $this->checkParams();
            //附加日志。
            request()->attributes->add([
                'activity_id'      => $this->activity_id,
                'activity_user_id' => $this->activity_user_id,
            ]);

            //开始兑换
            $this->startExchange();

        } catch (MyException $exc) {
            if ($exc->getCode() === 0) {
                $code = $exc->getMessage();
                if ($this->logic_obj) {
                    $msg = $this->logic_obj->getMsgByCode($code);
                } elseif (array_key_exists($code, SysCode::$resp_msg)) {
                    $msg = SysCode::$resp_msg[$code];
                } else {
                    $msg = SysCode::$resp_msg[SysCode::UNKNOW_ERROR];
                }
            } else {
                $code = $exc->getCode();
                $msg  = $exc->getMessage();
            }
            if ($code !== SysCode::SUCCESS) {
                $request->attributes->set('my_exception', sprintf('%s:%s', str_replace(base_path() . DIRECTORY_SEPARATOR, '', $exc->getFile()), $exc->getLine()));
            }
        } catch (Exception $exc) {
            if (array_key_exists($exc->getMessage(), SysCode::$resp_msg)) {
                $code = $exc->getMessage();
                $msg  = SysCode::$resp_msg[$code];
            } else {
                $code = SysCode::SYSTEM_ERROR;
                $msg  = SysCode::$resp_msg[$code];
            }
            $request->attributes->set('exception', sprintf('%s:%s, %s', str_replace(base_path() . DIRECTORY_SEPARATOR, '', $exc->getFile()), $exc->getLine(), $exc->getMessage()));
            Log::error($exc);
        }

        $data = [
            'code' => $code,
            'msg'  => $msg,
            'data' => [
                'rediret_url' => $this->logic_obj->rediret_url ?? ''
            ]
        ];

        if ($code == SysCode::SUCCESS) {
            $data['data']['goods_name'] = $this->goods_info->goods_name;
        }

        return $data;
    }

    //检查参数
    private function checkParams()
    {
        //接收goods_id
        if (empty($this->params['goods_id'])) {
            throw new MyException("请选择要兑换的商品", SysCode::PARAMS_ERROR);
        }

        //$this->goods_info = DB::table('goods')->where('id', $this->params['goods_id'])->first();
        //改为以下实现，联查activity_prizes，并附加prize_level prize_level2到商品信息，不用再判断商品在不在活动商品配置列表
        $this->goods_info = Goods::leftJoin('activity_prizes', function (\Illuminate\Database\Query\JoinClause $join) {
            $join->on('goods.id', '=', 'activity_prizes.goods_id')
                ->where('activity_prizes.activity_id', $this->activity_id);
        })
            ->where('goods.id', $this->params['goods_id'])
            ->where('activity_prizes.status', SysCode::COMMON_STATUS_1)
            ->select(['goods.*', 'activity_prizes.prize_level', 'activity_prizes.prize_level2'])
            ->first();

        if (empty($this->goods_info) || $this->goods_info->status == SysCode::COMMON_STATUS_0) {
            throw new MyException(SysCode::GOOD_LISTS_EMPTY);
        }

        if ($this->goods_info->prize_leve && !array_key_exists($this->goods_info->prize_level . "_" . $this->goods_info->prize_level2, session("user_info.prize_level"))) {
            throw new MyException(SysCode::$resp_msg[SysCode::NOT_CHANCE_3019], SysCode::NOT_CHANCE_3019);
        }

        // 为1的时候是实物商品
        if ($this->goods_info->goods_type == SysCode::GOODS_TYPE_1) {
            //实物类
            // consignee_name:"收货人姓名"
            // consignee_phone："收货人联系方式"
            // consignee_address:"收货地址"
            if (empty($this->params['consignee_name']) || empty($this->params['consignee_phone']) || empty($this->params['consignee_address'])) {
                throw new MyException('收货人、电话、收货地址不能为空', SysCode::PARAMS_ERROR);
            }

            if (!check_mobile($this->params['consignee_phone'])) {
                throw new MyException('收货电话格式不正确，请重新填写', SysCode::MOBILE_ERROR);
            }

        } elseif ($this->goods_info->goods_type == SysCode::GOODS_TYPE_4) {
            //实物和虚拟
            if (
                empty($this->params['consignee_name']) || empty($this->params['consignee_phone']) || empty($this->params['consignee_address']) ||
                empty($this->params['charge_account'])
            ) {
                throw new MyException(SysCode::PARAMS_ERROR);
            }

            if (!checkPhone($this->params['consignee_phone'])) {
                throw new MyException(SysCode::MOBILE_ERROR);
            }

            // 虚拟直充
            $this->checkChargeAccount();

        } elseif ($this->goods_info->goods_type == SysCode::GOODS_TYPE_2) {
//            //虚拟卡密类 卡密类下单不需要填写充值账号
//            if (empty($this->params['charge_account'])) {
//                throw new MyException(SysCode::PARAMS_ERROR);
//            }
//            if (!checkPhone($this->params['charge_account'])) {
//                throw new MyException(SysCode::MOBILE_ERROR);
//            }

        } elseif ($this->goods_info->goods_type == SysCode::GOODS_TYPE_3) {
            // 虚拟直充
            $this->checkChargeAccount();

        } elseif ($this->goods_info->goods_type == SysCode::GOODS_TYPE_5) {
            //短链接类
            if (empty($this->params['charge_account'])) {
                throw new MyException(SysCode::PARAMS_ERROR);
            }
            if (!checkPhone($this->params['charge_account'])) {
                throw new MyException(SysCode::MOBILE_ERROR);
            }
        } elseif ($this->goods_info->goods_type == SysCode::GOODS_TYPE_6) {
            //家政服务类

            if (empty($this->params['service_date']) || empty($this->params['service_time'])) {
                throw new MyException(SysCode::PARAMS_ERROR);
            }

            //特殊日期不提供服务判断
            $exclude_date_settings = config('api.jiazheng_exclude_date');
            $now                   = strtotime($this->params['service_date']);
            foreach ($exclude_date_settings as $date_setting) {
                if ($now >= strtotime($date_setting['begin']) && $now <= strtotime($date_setting['end'] . ' 23:59:59')) {
                    throw new MyException($date_setting['msg'], SysCode::SERVICE_DATE_ERROR);
                }
            }

            if (empty($this->params['consignee_name']) || empty($this->params['consignee_phone']) || empty($this->params['consignee_address'])) {
                throw new MyException(SysCode::PARAMS_ERROR);
            }

            if (!checkPhone($this->params['consignee_phone'])) {
                throw new MyException(SysCode::MOBILE_ERROR);
            }

        } else {
            //不存在的类别
            if (empty($this->params['charge_account'])) {
                throw new MyException(SysCode::PARAMS_ERROR);
            }
            if (!checkPhone($this->params['charge_account'])) {
                throw new MyException(SysCode::MOBILE_ERROR);
            }
        }

        if (!empty($this->params['group_id'])) {
            $this->group_info = ExchangeGroup::find($this->params['group_id']);
            if (empty($this->group_info) || $this->group_info->status != SysCode::COMMON_STATUS_1) {
                throw new MyException(SysCode::EXCHANGE_GOODS_NOT_PERMIT); //分组信息不存在或禁用
            }
        }

        //活动自定义验证。验证是否符合条件
        $verify_return          = $this->logic_obj->verifyBeforeCreateOrder($this->goods_info, $this->group_info);
        $this->activity_user_id = $verify_return['activity_user_id'];
        $this->prize_level      = $verify_return['prize_level'];
        $this->prize_level2     = $verify_return['prize_level2'];
    }

    /**
     * 开始兑换
     */
    private function startExchange()
    {
        $order_no = create_order_no();
        $now_time = date("Y-m-d H:i:s");
        $mobile   = session('user.mobile', session('extra.mobile', ''));
        //status 1 启用状态
        $act_info = DB::table('activities')->where(['id' => $this->activity_id, 'status' => SysCode::COMMON_STATUS_1])->first();

        if (empty($act_info)) {
            throw new MyException(SysCode::ACTIVITY_ERROR);
        }
        if (empty($this->activity_user_id)) {
            throw new MyException(SysCode::EXCHANGE_NOT_ALLOW);
        }

        //user_mobile 获取顺序: 接口提交过来的user_mobile参数，登录手机号，充值账号，收货人联系方式
        if (!empty($this->params['user_mobile'])) {
            $user_mobile = $this->params['user_mobile'];
        } elseif (!empty($mobile)) {
            $user_mobile = $mobile;
        } elseif (!empty($this->params['charge_account']) && checkPhone($this->params['charge_account'])) {
            $user_mobile = $this->params['charge_account'];
        } else {
            $user_mobile = $this->params['consignee_phone'] ?? '';
        }

        $order_data = [
            'order_time'         => $now_time,
            'activity_id'        => $this->activity_id,
            'activity_user_id'   => $this->activity_user_id,
            'prize_level'        => $this->prize_level,
            'prize_level2'       => $this->prize_level2,
            'order_no'           => $order_no,
            'status'             => SysCode::ORDER_STATUS_1,
            'sms_status'         => $act_info->is_send_sms, //0不发送短信 1:发送短信
            'goods_id'           => $this->goods_info->id,
            'goods_type'         => $this->goods_info->goods_type,
            'goods_no'           => $this->goods_info->goods_no,
            'goods_price'        => $this->goods_info->goods_price,
            //新增cost_price ,tax_rate , goods_tax_rate
            'goods_tax_rate'     => $this->goods_info->goods_tax_rate,
            'cost_price'         => $this->goods_info->cost_price,
            'cost_tax_rate'      => $this->goods_info->cost_tax_rate,
            'goods_name'         => $this->goods_info->goods_name,
            'goods_attr'         => $this->goods_info->goods_attr,
            'goods_num'          => 1,
            'mobile'             => $mobile, //登录手机号  例 工行的登录
            'user_mobile'        => $user_mobile,
            'charge_account'     => $this->params['charge_account'] ?? '',
            'consignee_name'     => $this->params['consignee_name'] ?? '',
            'consignee_phone'    => $this->params['consignee_phone'] ?? '',
            'consignee_address'  => $this->params['consignee_address'] ?? '',
            'order_remark'       => $this->params['order_remark'] ?? '',
            'exchange_detail_id' => session('extra.exchange_detail_id', 0),//兑换码id
            'exchange_code'      => session('extra.exchange_code', ''),//兑换码
            'created_at'         => $now_time,
            'updated_at'         => $now_time,
        ];

        if (isset($this->params['service_date'])) {
            $order_data['service_date'] = $this->params['service_date'];
            $order_data['service_time'] = $this->params['service_time'] ?? '';
        }

        // 实物的 将order_notify_sms_status 置为1
        // if ($this->goods_info->goods_type == SysCode::GOODS_TYPE_1) {
        //     $order_data['order_notify_sms_status'] = SysCode::IS_SEND_MSG_1;
        // }

        $goods_list = [];
        if ($this->goods_info->combin_type == Goods::COMBIN_TYPE_SINGLE) {
            $goods_list[] = $this->goods_info->toArray();
        } else if ($this->goods_info->combin_type == Goods::COMBIN_TYPE_COMBIN) {
            DB::table('goods_combins')
                ->leftJoin('goods', 'goods_combins.child_goods_id', '=', 'goods.id')
                ->where('goods_combins.goods_id', $this->goods_info->id)
                //->where('goods.status', '1')//这里不加该条件，因为如果把禁用的商品排除掉，组合产品就不完整了。可以在禁用商品的时候，判断该商品是不是组合商品的子商品。TODO: 商品禁用逻辑是否要变更，适配组合商品。
                ->select(DB::raw('goods.*'))
                ->get()
                ->map(function ($goods) use (&$goods_list) {
                    $goods_list[] = (array)$goods;
                });
        }

        if (empty($goods_list)) {
            throw new MyException(SysCode::GOOD_SETTING_ERROR);//礼品配置错误
        }

        $order_sub_data = [];
        foreach ($goods_list as $k => $g) {
            $sub = [
                'order_no'          => $order_no,
                'sub_order_no'      => $order_no . ($k + 1),
                'goods_id'          => $g['id'],
                'goods_type'        => $g['goods_type'],
                'goods_name'        => $g['goods_name'],
                'goods_no'          => $g['goods_no'],
                'goods_price'       => $g['goods_price'],
                //新增 cost_price,goods_tax_rate
                'goods_tax_rate'    => $g['goods_tax_rate'],
                'cost_price'        => $g['cost_price'],
                'cost_tax_rate'     => $g['cost_tax_rate'],
                'goods_attr'        => $g['goods_attr'],
                'goods_num'         => 1,
                'ecp_target'        => $g['ecp_target'],
                'ecp_pcode'         => $g['ecp_pcode'],
                'user_mobile'       => $order_data['user_mobile'],
                'charge_account'    => $this->params['charge_account'] ?? '',
                'consignee_name'    => $this->params['consignee_name'] ?? '',
                'consignee_phone'   => $this->params['consignee_phone'] ?? '',
                'consignee_address' => $this->params['consignee_address'] ?? '',
                'status'            => SysCode::ORDER_SUB_STATUS_1,  //订单状态
                'created_at'        => $now_time,
                'updated_at'        => $now_time,
                'is_show_card_no'   => $g['is_show_card_no'],
            ];

            if (isset($this->params['service_date'])) {
                $sub['service_date'] = $this->params['service_date'];
                $sub['service_time'] = $this->params['service_time'] ?? '';
            }

            $order_sub_data[] = $sub;
        }

        DB::transaction(function () use ($order_data, $order_sub_data) {
            $order_id = DB::table('orders')->insertGetId($order_data);
            if (!$order_id) {
                throw new MyException(SysCode::$resp_msg[SysCode::SYSTEM_ERROR], SysCode::SYSTEM_ERROR);
            }

            foreach ($order_sub_data as $key => $val) {
                $order_sub_data[$key]['order_id'] = $order_id;
            }

            $order_data['id'] = $order_id;

            $insert_order_sub = DB::table('order_subs')->insert($order_sub_data);
            if (!$insert_order_sub) {
                throw new MyException(SysCode::$resp_msg[SysCode::SYSTEM_ERROR], SysCode::SYSTEM_ERROR);
            }

            $order_data['order_sub'] = $order_sub_data;

            $this->logic_obj->handleAfterCreateOrder($this->activity_user_id, $order_data, $this->goods_info, $this->group_info);
        });

        throw new MyException(SysCode::SUCCESS);
    }

    /**
     * 验证虚拟直充类商品充值账号格式
     * @throws MyException
     * Date: 11/26/21
     */
    protected function checkChargeAccount()
    {
        if (empty($this->params['charge_account'])) {
            throw new MyException(SysCode::CHARGE_ACCOUNT_ERROR);
        }

        $this->logic_obj->checkChargeAccount($this->goods_info, $this->params['charge_account']);

//        $setting = config('api');
//
//        // 判断商品是否在特殊配置的商品中
//        if (key_exists($this->goods_info->id, $setting['goods_setting'])) {
//
//            $key    = $setting['goods_setting'][$this->goods_info->id];
//            $regexp = $setting['charge_account'][$key]['regexp'];
//            if (!preg_match($regexp, $this->params['charge_account'])) {
//                throw new MyException($setting[$key]['error_msg'], SysCode::PARAMS_ERROR);
//            }
//        } else {
//            if (!preg_match('/^\S{1,64}$/u', $this->params['charge_account'])) {
//                throw new MyException(SysCode::CHARGE_ACCOUNT_ERROR);
//            }
//        }
    }
}
