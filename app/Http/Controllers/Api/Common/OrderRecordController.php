<?php

/**
 * 订单记录
 * 公共方法
 *
 * desc: 根据session里面的活动的信息
 * 查找相应的订单记录
 *
 */

namespace App\Http\Controllers\Api\Common;

use App\Models\Goods;
use App\Models\Order;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Api\SysCode\SysCode;
use App\Exceptions\MyException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;


class OrderRecordController extends AuthController
{
    protected $user;
    protected $goods_info;
    protected $exchange_record = [];

    protected static $order_status = [
        SysCode::ORDER_STATUS_DELETED => '已取消',
        SysCode::ORDER_STATUS_0       => '已取消',
        SysCode::ORDER_STATUS_1       => '处理中',
        SysCode::ORDER_STATUS_2       => '成功',
        SysCode::ORDER_STATUS_3       => '处理中',//部分发货
        SysCode::ORDER_STATUS_4       => '发货失败',//发货失败
    ];

    public function index(Request $request)
    {
        $code = SysCode::SYSTEM_ERROR;
        $msg  = SysCode::$resp_msg[$code];
        try {
            $this->getExchangeRecord();
        } catch (MyException $exc) {
            if ($exc->getCode() === 0) {
                $code = $exc->getMessage();
                if ($this->logic_obj) {
                    $msg = $this->logic_obj->getMsgByCode($code);
                } elseif (array_key_exists($code, SysCode::$resp_msg)) {
                    $msg = SysCode::$resp_msg[$code];
                } else {
                    $msg = SysCode::$resp_msg[SysCode::UNKNOW_ERROR];
                }
            } else {
                $code = $exc->getCode();
                $msg  = $exc->getMessage();
            }
            $ret_data = $exc->getData();
            if ($code !== SysCode::SUCCESS) {
                $request->attributes->set('my_exception', sprintf('%s:%s', str_replace(base_path() . DIRECTORY_SEPARATOR, '', $exc->getFile()), $exc->getLine()));
            }
        } catch (Exception $exc) {
            if (array_key_exists($exc->getMessage(), SysCode::$resp_msg)) {
                $code = $exc->getMessage();
                $msg  = SysCode::$resp_msg[$code];
            } else {
                $code = SysCode::SYSTEM_ERROR;
                $msg  = SysCode::$resp_msg[$code];
            }
            $request->attributes->set('exception', sprintf('%s:%s, %s', str_replace(base_path() . DIRECTORY_SEPARATOR, '', $exc->getFile()), $exc->getLine(), $exc->getMessage()));
            Log::error($exc);
        }

        $data = [
            'code' => $code,
            'msg'  => $msg,
            'data' => $this->exchange_record ?? ''
        ];
        return $data;
    }

    private function getExchangeRecord()
    {
        $user_id = session('user_info.id');

        $this->exchange_record = Order::where([
            'activity_user_id' => $user_id,
            'activity_id'      => $this->activity_id,
        ])
            ->where('orders.status', '>=', '0')
            ->leftJoin('goods', 'orders.goods_id', '=', 'goods.id')
            ->leftJoin('goods_details', 'orders.goods_id', '=', 'goods_details.goods_id')
            ->with([
                'order_subs' => function ($query) {
                    $query->select([
                        'order_id', 'goods_type', 'goods_no', 'goods_name',
                        //'charge_account', 'consignee_name', 'consignee_phone', 'consignee_address',
                        'activation_code', 'sequence_no', 'endtime', 'logistics_sn', 'logistics_company'
                    ]);
                },
            ])
            ->select([
                'orders.id', 'orders.order_no', 'orders.order_time',
                'orders.goods_no', 'orders.goods_name', 'orders.goods_attr',
                'orders.charge_account', 'orders.consignee_name', 'orders.consignee_phone', 'orders.consignee_address',
                'orders.service_date', 'orders.service_time', 'orders.exchange_code',
                'orders.status', 'goods.goods_show_img', 'goods_details.goods_imgs', 'orders.goods_type',
                'orders.user_mobile'
            ])
            ->orderByDesc('orders.id')
            ->get();

        if (count($this->exchange_record) == 0) {
            throw new MyException(SysCode::EXCHANGE_RECORD_EMPTY);
        }

        //orders->status: 状态。 1-处理中，2-已发货，3-部分发货，4-发货失败
        //order_subs->status: 状态。 1-未处理 2-处理中，3-已发货，4-发货失败，5-失败重提
        $this->exchange_record->map(function ($value) {
            if ($value->goods_type == 1 && $value->status == 2) {
                $value->status = '已发货';
            } else {
                if ($value->status == SysCode::ORDER_STATUS_4 && $value->goods_type == SysCode::GOODS_TYPE_3) {
                    $value->status = '充值失败';
                } else {
                    $value->status = OrderRecordController::$order_status[$value->status];
                }
            }

            $value->goods_show_img = getImgUrl($value->goods_show_img);

            $tmp = json_decode($value->goods_imgs, true);
            if (!empty($tmp)) {
                $value->goods_imgs = getImgUrl($tmp[0]);
            } else {
                $value->goods_imgs = '';
            }

            unset($value->id);
            if (!$value->exchange_code) {
                unset($value->exchange_code);
            }

            // 如果充值账号是加密账号,则显示他的充值手机号  (针对天猫充值 ********)
            if (strpos($value->charge_account, 'RAzN8') === 0) {
                $value->charge_account = $value->user_mobile;
            }

            $value->order_subs->map(function ($sub_v) {
                unset($sub_v->order_id);
                switch ($sub_v->goods_type) {
                    case 1: //实物
                        unset($sub_v->activation_code, $sub_v->sequence_no, $sub_v->endtime);
                        break;
                    case 2://卡密
                        unset($sub_v->logistics_sn, $sub_v->logistics_company);
                        break;
                    case 3://直充
                        unset($sub_v->activation_code, $sub_v->sequence_no, $sub_v->endtime, $sub_v->logistics_sn, $sub_v->logistics_company);
                        break;
                    case 4://实物+虚拟
                        break;
                    case 5://短链接
                        break;
                    case 6://家政
                        unset($sub_v->activation_code, $sub_v->sequence_no, $sub_v->endtime, $sub_v->logistics_sn, $sub_v->logistics_company);
                        break;
                    default:
                        break;
                }
            });
        });

        throw new MyException(SysCode::SUCCESS);

    }
}
