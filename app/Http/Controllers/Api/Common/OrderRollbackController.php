<?php

/**
 * 取消订单（根据订单号重置兑换码）
 *
 * desc: 根据订单号重置兑换码，客户操作重置后重新兑换
 *
 * @date 2024-05-10
 */

namespace App\Http\Controllers\Api\Common;

use App\Models\ExchangeDetail;
use App\Models\Order;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Api\SysCode\SysCode;
use App\Exceptions\MyException;
use Illuminate\Support\Facades\Log;


class OrderRollbackController extends AuthController
{
    protected $data = [];

    public function index(Request $request)
    {
        $code = SysCode::SYSTEM_ERROR;
        $msg  = SysCode::$resp_msg[$code];

        try {
            $order_no = $request->get('order_no');

            if (empty($order_no)) {
                throw new MyException('参数错误！', SysCode::PARAMS_ERROR);
            }

            $order = Order::where('order_no', $order_no)->first();

            if (empty($order)) {
                throw new MyException('参数错误！', SysCode::PARAMS_ERROR);
            }

            if ($order->activity_id != $this->activity_id || $order->activity_user_id != session('user_info.id')) {
                throw new MyException('参数错误！', SysCode::PARAMS_ERROR);
            }

            if ($order->status == SysCode::ORDER_STATUS_DELETED) {
                $request->attributes->set('order_status', $order->status);
                throw new MyException('操作成功', SysCode::SUCCESS);
            } elseif ($order->status != SysCode::ORDER_STATUS_4) {
                throw new MyException('该订单不是充值失败状态，不能重新兑换！', SysCode::PARAMS_ERROR);
            }

            try {
                $ret     = Order::exchangeRollback($order);
                $message = $ret['error_msg'];
                $request->attributes->set('exchangeRollback_result', $ret);
            } catch (MyException $e) {
                $message = $e->getMessage();
            }

            if (mb_strpos($message, '处理成功') !== false) {
                throw new MyException('操作成功！', SysCode::SUCCESS);
            } elseif (mb_strpos($message, '已过期') !== false) {
                throw new MyException('该兑换码已过期，不能重新兑换', SysCode::PARAMS_ERROR);
            } else {
                throw new MyException('重置失败，请联系客服咨询', SysCode::PARAMS_ERROR);
            }

        } catch (MyException $exc) {
            if ($exc->getCode() === 0) {
                $code = $exc->getMessage();
                if ($this->logic_obj) {
                    $msg = $this->logic_obj->getMsgByCode($code);
                } elseif (array_key_exists($code, SysCode::$resp_msg)) {
                    $msg = SysCode::$resp_msg[$code];
                } else {
                    $msg = SysCode::$resp_msg[SysCode::UNKNOW_ERROR];
                }
            } else {
                $code = $exc->getCode();
                $msg  = $exc->getMessage();
            }
            $this->data = $exc->getData();

        } catch (Exception $exc) {
            if (array_key_exists($exc->getMessage(), SysCode::$resp_msg)) {
                $code = $exc->getMessage();
                $msg  = SysCode::$resp_msg[$code];
            } else {
                $code = SysCode::SYSTEM_ERROR;
                $msg  = SysCode::$resp_msg[$code];
            }
            $request->attributes->set('exception', sprintf('%s:%s, %s', str_replace(base_path() . DIRECTORY_SEPARATOR, '', $exc->getFile()), $exc->getLine(), $exc->getMessage()));
            Log::error($exc);
        }


        if ($code == SysCode::SUCCESS) {
            $this->getRetData();
        }

        return [
            'code' => $code,
            'msg'  => $msg,
            'data' => $this->data,
        ];
    }

    //获取跳转的商品详情或列表页面url及附加参数
    protected function getRetData()
    {
        try {
            if ($exchange_detail_id = session('extra.exchange_detail_id')) {
                $exchange_detail = ExchangeDetail::find($exchange_detail_id);

                $goods_info = DB::table('exchange_goods')
                    ->leftJoin('goods', 'exchange_goods.goods_id', '=', 'goods.id')
                    ->leftJoin('goods_details', 'goods_details.goods_id', '=', 'exchange_goods.goods_id')
                    ->where(['exchange_goods.exchange_batch_id' => $exchange_detail->exchange_batch_id])
                    ->select(['exchange_goods.goods_id', 'goods.goods_name', 'goods.goods_type', 'goods.goods_show_img', 'goods.pre_verify_type', 'goods_details.goods_imgs', 'goods_details.goods_desc'])
                    ->get();

                $goods = null;
                if (count($goods_info) == 1) {
                    $goods = $goods_info->first();
                    if ($goods->goods_type == SysCode::GOODS_TYPE_6) {
                        $rdr_url = str_replace('{id}', $goods->goods_id, $this->logic_obj->jiazheng_rdr_url);
                    } else {
                        $rdr_url = str_replace('{id}', $goods->goods_id, $this->logic_obj->single_goods_rdr_url);
                    }
                } else {
                    $rdr_url = str_replace('{s}', session('extra.exchange_code', ''), $this->logic_obj->goods_list_rdr_url);
                }

                $data = [
                    'exchange_state' => $exchange_detail->status . '',
                    'rdr_url'        => $rdr_url,
                ];

                if ($goods) {
                    $data['goods_id']        = $goods->goods_id;
                    $data['goods_name']      = $goods->goods_name;
                    $data['goods_type']      = $goods->goods_type;
                    $data['pre_verify_type'] = $goods->pre_verify_type;//充值验证类型。0-无，1-短信验证码(京东），2-淘宝手机号充值
                    //$data['goods_show_img']  = getImgUrl($goods->goods_show_img);
                    $goods_imgs = json_decode($goods->goods_imgs ?? '', true);
                    if ($goods_imgs) {
                        if (!empty($goods_imgs)) {
                            $data['goods_img'] = getImgUrl($goods_imgs[0]);
                        } else {
                            $data['goods_img'] = getImgUrl($goods->goods_show_img);
                        }
                    }
                    $data['goods_desc'] = $goods->goods_desc ?? '';
                }

                $this->data = $data;
            }

        } catch (\Exception $e) {
            Log::error($e);
        }
    }

}
