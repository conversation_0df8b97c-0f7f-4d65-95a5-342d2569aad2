<?php

/**
 * 公共方法
 * 接收支付网关回调
 */

namespace App\Http\Controllers\Api\Common;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PayCallbackController extends Controller {

    public function index(Request $request) {

        $code = '200';
        $logger = Log::channel('trade_callback');
        try{

            $params = Request::all();
            $logger->info('notify-params:'. json_encode($params));
            //验证签名
            $checkResult = $this->checkSign($params);
            if ($checkResult === FALSE) {
                throw new \Exception('验签失败！');
            }
            //查询交易记录
            $trades = DB::table('trades')->where('gateway_orderid', $params['orderid'])->select()->get()->toArray();
            if (empty($trades)) {
                throw new \Exception('查询不到交易记录！');
            }
            $trade = $trades[0];
            $payResult = $params['code'] === '0000' ? '2' : '3';
            $tradeData['callback_code'] = $params['code'];
            $tradeData['platform_orderid'] = $params['platform_orderid'] ?? '';
            $tradeData['real_pay_fee'] = $params['amount'];
            $tradeData['shop_reduction_discount'] = $params['mer_disc_amt'] ?? 0; //优惠立减金额
            $tradeData['card_discount'] = $params['bank_disc_amt'] ?? 0;          //银行补贴金额
            $tradeData['points'] = $params['point_amt'] ?? 0;                     //积分抵扣金额
            $tradeData['discount'] = $params['ecoupon_amt'] ?? 0;                 //电子券抵扣金额
            $tradeData['real_pay_fee'] = $params['payment_amt'] ?? 0;             //用户实际扣减金额
            $tradeData['pay_card_no'] = $params['card_no'] ?? '';                 //实际支付卡号
            $tradeData['pay_result'] = $payResult;
            $tradeData['updated_at'] = Carbon::now()->format('Y-m-d H:i:s');

            $orderData['pay_result'] = $payResult;
            $orderData['updated_at'] = Carbon::now()->format('Y-m-d H:i:s');

            try {
                DB::transaction(function () use($tradeData, $orderData, $trade) {

                    $tradeResult = DB::table('trades')->where(['id'=>$trade->id,'pay_result'=>'1'])->update($tradeData);
                    if (!$tradeResult) {
                        throw new \Exception('更新支付信息表支失败！order_no:'. $trade->order_no);
                    }
                    if (!empty($trade->pay_from_table_name)){
                        $orderResult = DB::table($trade->pay_from_table_name)->where(['order_no'=>$trade->order_no, 'pay_result'=>'1'])->update($orderData);
                        if (!$orderResult) {
                            throw new \Exception('更新主订单支付状态失败！order_no:'. $trade->order_no);
                        }
                    }
                    DB::table('orders')->where(['order_no'=>$trade->order_no, 'pay_result'=>'1'])->update($orderData);
                });
            } catch (\Exception $ex) {
                throw $ex;
            }

        } catch (\Exception $e) {
            $logger->error($e->getMessage());
            $code = '300';
        }

        echo $code;
    }

    private function checkSign ($data) {
        $ret = FALSE;
        if (!empty($data) && is_array($data)) {
            $old_sign = $data['sign'];
            unset($data['sign']);
            $string = '';
            ksort($data);
            foreach ($data as $k => $v) {
                $string .= $k . "=" . $v . "&";
            }
            $string = $string . "secret=" . config('activity.icbc_autumn_activity.pay_trade_secret');
            $string = md5($string);
            $string = strtoupper($string);
            if ($string === $old_sign) {
                $ret = TRUE;
            }
        }
        return $ret;
    }
}
