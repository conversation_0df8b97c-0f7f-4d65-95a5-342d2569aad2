<?php

/**
 * 短信回调
 */

namespace App\Http\Controllers\Api\Common;

use App\Exceptions\MyException;
use Exception;
use Carbon\Carbon;
use App\Models\OrderSub;
use App\Models\SmsResult;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Request;


class SmsCallbackController extends Controller
{
    protected $params;

    public function __construct()
    {
        $this->params = Request::all();
    }

    public function index()
    {
        try {

            if (empty($this->params['gateway_seq'])) {
                throw new MyException(SmsResult::GATEWAY_SEQ_EMPTY);
            }
            $sms_info = SmsResult::where('gateway_seq', $this->params['gateway_seq'])->first();
            if (empty($sms_info)) {
                //这笔短信不存在
                throw new MyException(SmsResult::GATEWAY_SEQ_NOT_EXIST);
            }

            //sms_status的状态是 order_items表中短信的状态
            //短信状态(1-未知，2-成功，3-失败)
            if ($this->params['submit_result'] == 0 && $this->params['report_result'] == 0) {
                $update_column['status']            = SmsResult::SMS_STATUS_3;
                $order_sub_sms_status['sms_status'] = 2; //成功
            } else {
                $update_column['status']            = SmsResult::SMS_STATUS_4;
                $order_sub_sms_status['sms_status'] = 3; //失败
            }
//            $update_column['status']        = ($this->params['submit_result'] == 0 && $this->params['report_result'] == 0) ?
//                SmsResult::SMS_STATUS_3 : SmsResult::SMS_STATUS_4;
            $update_column['callback_time']        = $this->params['report_at'] ?: Carbon::now()->format("Y-m-d H:i:s");
            $order_sub_sms_status['callback_time'] = $this->params['report_at'] ?: Carbon::now()->format("Y-m-d H:i:s");

            DB::enableQueryLog();
            $is_update_success  = SmsResult::where('id', $sms_info->id)->update($update_column);
            $is_sms_status_succ = OrderSub::where('id', $sms_info->order_sub_id)->update($order_sub_sms_status);
            $sql                = DB::getQueryLog();
            if (!$is_update_success || !$is_sms_status_succ) {
                $logger = Log::channel('sms_callback');
                $logger->info($sql);
                throw new MyException(SmsResult::SYSTEM_ERROR);
            }

            throw new MyException(SmsResult::SUCCESS);
        } catch (Exception $exc) {
            $code = $exc->getMessage();
            if (!array_key_exists($code, SmsResult::$resp_msg)) {
                $code = SmsResult::SYSTEM_ERROR;
            }
        }

        $data = [
            'timestamp' => time(),
            'code'      => $code,
            'msg'       => SmsResult::$resp_msg[$code],
        ];
        $this->write_log($data);
        return $data;
    }


    private function write_log($resp_info)
    {
        $logger   = Log::channel('sms_callback');
        $log_data = [
            'request'  => $this->params,
            'response' => $resp_info
        ];
        $logger->info(json_encode($log_data));
    }
}
