<?php

/**
 * 短信回调
 * 公共方法
 * Desc : 接收订单网关 短信状态的回调
 */

namespace App\Http\Controllers\Api\Common;

use App\Exceptions\MyException;
use Exception;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Api\SysCode\SysCode;
use App\Http\Requests\Api\CallbackRequest;
use Illuminate\Support\Facades\Log;
use App\Models\Activity;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Request;

class SmsEactCallbackController extends Controller
{
    protected $params;

    public function __construct()
    {
        $this->params = Request::all();
    }

    public function index(CallbackRequest $request)
    {
        try {
            $this->handelSms();
            throw new MyException(SysCode::SUCCESS_0000);
        } catch (Exception $exc) {
            $code = $exc->getMessage();
            if (!array_key_exists($code, SysCode::$resp_msg)) {
                $code = SysCode::SYSTEM_ERROR;
            }
        }

        $data = [
            'serial_no' => session_create_id(),
            'appid' => config('activity.icbc_e_activity.appid'),
            'timestamp' => time(),
            'code' => $code,
            'msg' => SysCode::$resp_msg[$code],
        ];
        $this->write_log($data);
        return $data;
    }


    private function handelSms()
    {

        //获取本次活动id
        $activity_id = env('E_ACTIVITY_ID');
        //根据活动id查出 appid secret_key is_sms
        // DB::table()
        $act_info = Activity::find($activity_id);
        if (empty($act_info)) {
            $logger = Log::channel('sms_callback');
            $logger->info('活动id配置错误!');
            return;
        }

        $appid = $this->params['appid'];
        $secret_key = $act_info->order_gateway_secketkey;

        if ($appid != $act_info->order_gateway_appid) {
            throw new MyException(SysCode::APPID_ERROR);
        }

        $callback_sign = $this->params['sign'];
        $local_sign = createSign($appid . $this->params['biz_data']  . $this->params['serial_no'] . $this->params['timestamp'] . $secret_key);

        //验证签名
        if ($callback_sign != $local_sign) {
            throw new MyException(SysCode::SIGN_ERROR);
        }

        //解析数据

        $biz_data = json_decode($this->params['biz_data'], true);

        switch ($biz_data['sms_status']) {
                //4成功
            case 4:
                $update_data = [
                    'sms_status' => SysCode::SMS_STATUS_2, //成功
                    'sms_callback_time' => Carbon::now()->format("Y-m-d H:i:s"),
                    'sms_gateway_seq' => $biz_data['sms_gateway_seq'] ?? '',
                    'sequence_no' => $biz_data['sequence_no'] ?? '',
                ];
                $result = true;
                break;
            case 6:
                $update_data = [
                    'sms_status' => SysCode::SMS_STATUS_3, //失败
                    'sms_callback_time' => Carbon::now()->format("Y-m-d H:i:s"),
                ];
                $result = true;
                break;
            default:
                $result = false;
                break;
        }

        if ($result) {
            DB::table('order_subs')->where('order_no', $biz_data['order_no'])->update($update_data);
        }
    }

    private  function write_log($resp_info)
    {
        $logger = Log::channel('sms_callback');
        $biz_data = json_decode($this->params['biz_data'], true);
        $log_data = [
            'request' => [
                'serial_no' => $this->params['serial_no'],
                'appid' => $this->params['appid'],
                'timestamp' => $this->params['timestamp'],
                'biz_data' => $biz_data,
                'sign' => $this->params['sign'],
            ],
            'response' => $resp_info
        ];
        $logger->info(json_encode($log_data));
    }
}
