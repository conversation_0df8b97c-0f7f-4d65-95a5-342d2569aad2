<?php

namespace App\Http\Controllers\Api\Common;

use Exception;
use Carbon\Carbon;
use App\Exceptions\MyException;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Api\SysCode\SpeCode;


/**
 * spe
 */
class SpeController extends Controller
{
    protected $app_id_info;
    protected $logger;
    protected $app_id;
    protected $request;
    protected $bizContent;//业务参数
    protected $is_blacklist     = false;
    protected $settlement_price = 0;

    public function __construct()
    {
        $this->logger  = Log::channel('spe_log');
        $this->request = \request();
    }

    public function index()
    {
        $code = SpeCode::SYSTEM_ERROR;
        $msg  = SpeCode::$resp_msg[$code];
        try {
            $this->checkBaseParams();
            //验签
            $this->checkSign();
            //检测是否在黑名单中
            $this->checkIsInBlackList();
        } catch (MyException $exc) {
            // 业务级别
            $code = $exc->getMessage();
            if (array_key_exists($code, SpeCode::$resp_msg)) {
                $msg = SpeCode::$resp_msg[$code];
            } else {
                $msg = SpeCode::$resp_msg[SpeCode::SYSTEM_ERROR];
            }

        } catch (Exception $exc) {
            //系统类异常
            $code = $exc->getMessage();
            if (strpos($code, 'bl_records.u_orderno') !== FALSE) {
                // 幂等  如果bl_records表中存在次订单,会抛出唯一索引错误, 捕获之后按照成功返回
                $code               = SpeCode::SUCCESS;
                $msg                = SpeCode::$resp_msg[$code];
                $this->is_blacklist = true;
            } elseif (array_key_exists($code, SpeCode::$resp_msg)) {
                $msg = SpeCode::$resp_msg[$code];
            } else {
                $msg = SpeCode::$resp_msg[SpeCode::SYSTEM_ERROR];
            }
            $this->logger->error($exc);
        }
        $data = [
            'code' => $code,
            'msg'  => $msg,
            'data' => [
                'is_blacklist'     => $this->is_blacklist,
                'settlement_price' => $this->settlement_price
            ]
        ];

        return response()->json($data, SpeCode::SUCCESS, [], JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
    }

    /**
     * @return void
     * @throws Exception
     * 检测基础参数
     */
    protected function checkBaseParams()
    {
        //判断接口参数是否完整
        if (empty($this->request->get('appId')) || empty($this->request->get('sign'))
            || empty($this->request->get('t')) || empty($this->request->get('bizContent'))) {
            throw new MyException(SpeCode::BASE_PARAMS_ERROR);
        }

        $app_id            = $this->request->get('appId');
        $this->app_id_info = DB::table('bl_api_settings')->where(['appid' => $app_id, 'status' => SpeCode::COMMON_STATUS_ON])->first();
        if (empty($this->app_id_info)) {
            throw new MyException(SpeCode::APP_ID_ERROR);
        }
    }

    /**
     * @return void
     * @throws MyException
     * 验签
     */
    protected function checkSign()
    {
        $origin_sign = $this->getSignOrigin();
        $req_sign    = $this->request->get('sign');
        if (strcmp($origin_sign, $req_sign) != 0) {
            throw new MyException(SpeCode::SIGN_ERROR);
        }
    }

    /**
     * @return string
     * 获取请求参数签名原串
     */
    protected function getSignOrigin()
    {
        $req_params['appId']      = $this->request->get("appId");
        $req_params['bizContent'] = $this->request->get("bizContent");
        $req_params['t']          = $this->request->get("t");

        return self::getSign($req_params, $this->app_id_info->secret_key);

    }


    protected static function getSign($params, $secret_key = '')
    {
        ksort($params);
        $sign_str = '';
        foreach ($params as $k => $v) {
            $sign_str .= $k . '=' . $v . '&';
        }
        $sign_str = trim($sign_str, '&');
        return md5($sign_str . $secret_key);
    }


    /**
     * 验证是否是在黑名单中
     */
    protected function checkIsInBlackList()
    {
        $biz_content      = $this->request->get('bizContent');
        $this->bizContent = json_decode($biz_content, true);
        $this->request->attributes->set('req_bizContent', $this->bizContent);
        if (empty($this->bizContent['project_no']) || empty($this->bizContent['product_code']) || empty($this->bizContent['charge_account'])) {
            throw new MyException(SpeCode::BIZ_CONTENT_ERROR);
        }


        // 去项目商品关系表中查出相应的信息

        $where_relation           = [
            'project_no'   => $this->bizContent['project_no'],
            'product_code' => $this->bizContent['product_code'],
            'status'       => SpeCode::COMMON_STATUS_ON, // 状态正常
        ];
        $project_product_relation = DB::table('bl_project_products')->where($where_relation)->first();

        if (empty($project_product_relation)) {
            //不存在该项目和商品之间的关系
            $this->is_blacklist = false;
            throw new MyException(SpeCode::SUCCESS);
        }

        $where = [
            'project_id'     => $project_product_relation->project_id, //项目id
            'product_id'     => $project_product_relation->product_id, //商品id
            'charge_account' => $this->bizContent['charge_account'],
            'status'         => SpeCode::COMMON_STATUS_ON, //
        ];

        $is_in_blacklist = DB::table('bl_greylists')->where($where)->first();
        if ($is_in_blacklist) {
            //在,则将这笔订单记录
            $insertData = [
                'project_id'       => $is_in_blacklist->project_id,
                'api_setting_id'   => $this->app_id_info->id,
                'order_no'         => $this->bizContent['order_no'],
                'out_trade_no'     => $this->bizContent['out_trade_no'] ?? '',
                'product_id'       => $is_in_blacklist->product_id,
                'charge_account'   => $this->bizContent['charge_account'],
                'settlement_price' => $project_product_relation->settlement_price,
                'created_at'       => Carbon::now()->format('Y-m-d H:i:s'),
                'updated_at'       => Carbon::now()->format('Y-m-d H:i:s'),
            ];

            if (!(DB::table('bl_records')->insert($insertData))) {
                $this->logger->error('【入记录表异常】:' . json_encode($insertData));
                throw new MyException(SpeCode::SYSTEM_ERROR);
            } else {
                $this->is_blacklist     = true;
                $this->settlement_price = $project_product_relation->settlement_price;
                throw new MyException(SpeCode::SUCCESS);
            }

        } else {
            //不在黑名单中
            $this->is_blacklist = false;
            throw new MyException(SpeCode::SUCCESS);
        }
    }

    /**
     * @param $data
     */
    protected function logError($data)
    {
        $req_time = millisecond();
        $request  = request();

        // 记录所有请求信息
        $requestMessage = [
            'req_time'     => $req_time,
            'res_time'     => $req_time,
            'process_time' => '0',
            'baseurl'      => $request->getSchemeAndHttpHost(),
            'method'       => $request->method(),
            'ip'           => $request->ip(),
            'path'         => $request->path(),
            'req_params'   => $request->input(),
            'res_params'   => [],
        ];

        if ($request->attributes->count() > 0) {
            $requestMessage['attach'] = $request->attributes->all();
        }

        $requestMessage['error_msg'] = $data;

        $this->logger->error(json_encode($requestMessage, JSON_UNESCAPED_UNICODE));
    }
}
