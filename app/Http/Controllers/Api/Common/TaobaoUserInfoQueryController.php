<?php

/**
 * 淘宝系充值，拉取淘宝账号列表的接口
 */

namespace App\Http\Controllers\Api\Common;

use Exception;
use App\Http\Controllers\Api\SysCode\SysCode;
use App\Exceptions\MyException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TaobaoUserInfoQueryController extends AuthController
{
    public function index(Request $request)
    {
        $code = SysCode::SYSTEM_ERROR;
        $msg  = SysCode::$resp_msg[$code];

        try {
            //三个参数： goods_id, mobile, verify_code(可选）
            if (empty($this->params['goods_id']) || empty($this->params['mobile'])) {
                throw new MyException(SysCode::PARAMS_ERROR);
            }
            $goods_info = DB::table('goods')->where('id', $this->params['goods_id'])->first();
            if (empty($goods_info) || $goods_info->status == SysCode::COMMON_STATUS_0) {
                throw new MyException(SysCode::GOOD_LISTS_EMPTY);
            }

            $activity_prize = DB::table('activity_prizes')->where([
                'goods_id'    => $this->params['goods_id'],
                'activity_id' => $this->activity_id
            ])->first();

            if (empty($activity_prize)) {
                throw new MyException(SysCode::GOOD_LISTS_EMPTY);
            }
            $query_result = $this->logic_obj->taobaoUserInfoQuery($goods_info->ecp_pcode, $this->params['mobile'] ?? '', $this->params['verify_code'] ?? '');
            if ($query_result['code'] != 200) {
                $this->throwMyException(SysCode::CHANNEL_DEAL_ERROR, $query_result['return_msg']);
            }
            unset($query_result['code'], $query_result['return_code'], $query_result['return_msg']);
            return [
                'code' => SysCode::SUCCESS,
                'msg'  => 'success',
                'data' => $query_result,
            ];
        } catch (MyException $exc) {
            if ($exc->getCode() === 0) {
                $code = $exc->getMessage();
                if ($this->logic_obj) {
                    $msg = $this->logic_obj->getMsgByCode($code);
                } elseif (array_key_exists($code, SysCode::$resp_msg)) {
                    $msg = SysCode::$resp_msg[$code];
                } else {
                    $msg = "错误编码：" . $code . "。" . SysCode::$resp_msg[SysCode::UNKNOW_ERROR];
                }
            } else {
                $code = $exc->getCode();
                $msg  = $exc->getMessage();
            }
        } catch (Exception $exc) {
            if (array_key_exists($exc->getMessage(), SysCode::$resp_msg)) {
                $code = $exc->getMessage();
                $msg  = SysCode::$resp_msg[$code];
            } else {
                $code = SysCode::SYSTEM_ERROR;
                $msg  = SysCode::$resp_msg[$code];
            }
            $request->attributes->set('exception', sprintf('%s:%s, %s', str_replace(base_path() . DIRECTORY_SEPARATOR, '', $exc->getFile()), $exc->getLine(), $exc->getMessage()));
            Log::error($exc);
        }

        $result = [
            'code' => $code,
            'msg'  => $msg,
        ];
        return $result;
    }
}
