<?php

namespace App\Http\Controllers\Api\Statistics;

use App\Exceptions\MyException;
use App\Http\Controllers\Controller;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Request;

/**
 * 宁德专用
 * 昨天核销数量统计
 */
class GuangDaController extends Controller
{

    private   $params;
    protected $yesterday_begin;
    protected $yesterday_end;

    public function __construct()
    {
        $this->logger          = Log::channel('ningde_count');
        $this->yesterday_begin = \Carbon\Carbon::yesterday()->toDateTimeString();
        $this->yesterday_end   = Carbon::yesterday()->format('Y-m-d 23:59:59');
    }


    public function index()
    {
        $data = [];
        try {
            $data['gray_list'] = $this->blackList();
            throw new MyException("成功", "200");
        } catch (Exception $exc) {
            $code = $exc->getCode();
            $msg  = $exc->getMessage();
        }

        $resp = [
            'code' => $code,
            'msg'  => $msg,
            'data' => $data
        ];

        echo json_encode($resp);

    }

    /**
     * @return \Illuminate\Support\Collection
     * 黑名单部分
     */
    private function blackList()
    {
        $result_yesterday = DB::table('bl_records')
            ->leftJoin('bl_projects', 'bl_records.project_id', '=', 'bl_projects.id')
            ->leftJoin('bl_products', 'bl_records.product_id', '=', 'bl_products.id')
            ->whereBetween('bl_records.created_at', [$this->yesterday_begin, $this->yesterday_end])
            ->where('bl_products.status', '=', 1)
            ->select('bl_projects.project_name', 'bl_projects.project_no', 'bl_products.product_name', 'bl_products.product_code',
                DB::raw('count(*) as yesterday_num')
            )->groupBy("bl_projects.project_name")
            ->groupBy('bl_projects.project_no')
            ->groupBy('bl_products.product_name')
            ->groupBy('bl_products.product_code')
            ->get();

        // 总数从 2024-04-08 00:00:00 这一天开始
        $result_total = DB::table('bl_records')
            ->leftJoin('bl_projects', 'bl_records.project_id', '=', 'bl_projects.id')
            ->leftJoin('bl_products', 'bl_records.product_id', '=', 'bl_products.id')
            ->where('bl_products.status', '=', 1)
            ->where('bl_records.created_at', '>=', '2024-04-08 00:00:00')
            ->select('bl_projects.project_name', 'bl_projects.project_no', 'bl_products.product_name', 'bl_products.product_code',
                DB::raw('count(*) as total_num')
            )->groupBy("bl_projects.project_name")
            ->groupBy('bl_products.product_name')
            ->groupBy('bl_projects.project_no')
            ->groupBy('bl_products.product_code')
            ->orderBy('bl_products.product_name')
            ->get();

        $bl_result = [];

        foreach ($result_yesterday as $v) {
            $bl_result[$v->project_name][$v->product_name] = $v->yesterday_num;
        }

        return collect($result_total)->map(function ($value, $key) use ($bl_result) {
            $value->yesterday = isset($bl_result[$value->project_name][$value->product_name]) ? $bl_result[$value->project_name][$value->product_name] : 0;
            return $value;
        });
    }
}
