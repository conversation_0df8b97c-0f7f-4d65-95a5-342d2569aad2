<?php

namespace App\Http\Controllers\Api\Statistics;

use App\Exceptions\MyException;
use App\Http\Controllers\Api\SysCode\SysCode;
use App\Http\Controllers\Controller;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Request;

/**
 * 宁德专用
 * 昨天核销数量统计
 */
class NingDeController extends Controller
{

    private   $params;
    protected $yesterday_begin;
    protected $yesterday_end;

    public function __construct()
    {
        $this->logger          = Log::channel('ningde_count');
        $this->yesterday_begin = \Carbon\Carbon::yesterday()->toDateTimeString();
        $this->yesterday_end   = Carbon::yesterday()->format('Y-m-d 23:59:59');
    }


    public function index()
    {
        $data = [];
        try {
            //接口权限验证
            //$this->checkAuth();
            $confirm    = $this->ningDe();
            $users_info = DB::table("admin_users")->whereNotIn('username', ['admin', 'zhous'])->pluck('username', 'id')->toArray();
            $str        = '';
            foreach ($confirm as $key => $val) {
                $confirm_num       = isset($val['confirm_num']) ? $val['confirm_num'] : '0';
                $total_confirm_num = isset($val['total_confirm_num']) ? $val['total_confirm_num'] : '0';
                if (isset($users_info[$key])) {
                    $str .= $users_info[$key] . ',' . $confirm_num . ',' . $total_confirm_num . "\r\n";
                }
            }
            $data['hx']        = $str;
            $data['gray_list'] = $this->blackList();
            throw new MyException("成功", 200);
        } catch (Exception $exc) {
            $code = $exc->getCode();
            $msg  = $exc->getMessage();
        }

        $resp = [
            'code' => $code,
            'msg'  => $msg,
            'data' => $data
        ];

        echo json_encode($resp);

    }

    private function checkAuth()
    {
        $this->params = Request::all();
        if (empty($this->params['app_id']) || empty($this->params['ts']) || empty($this->params['sign'])) {
            throw new MyException("参数错误", 9998);
        }

        $app_id = config("api.statistics.order_sale_app_id");
        if ($this->params['app_id'] != $app_id) {
            throw new MyException("App_id错误", SysCode::APPID_ERROR);
        }

        $api_sign = $this->params['sign'];
        $sign     = $this->getSign();
        if (strcmp($api_sign, $sign) != 0) {
            throw new MyException("签名错误", 201);
        }

    }

    private function getSign()
    {
        $secret_key = config("api.statistics.secret_key");
        return md5($this->params['app_id'] . $this->params['ts'] . $secret_key);
    }

    /**
     * @return array
     * 核销部分
     */
    private function ningDe()
    {
        $result_yesterday = DB::table('exchange_detail_forbiddens')->whereBetween('created_at', [$this->yesterday_begin, $this->yesterday_end])
            ->select('created_by',
                DB::raw('count(*) as confirm_num')
            )->groupBy("created_by")->get();

        $total_confirm = DB::table('exchange_detail_forbiddens')->where('created_at', '<=', $this->yesterday_end)
            ->select('created_by',
                DB::raw('count(*) as total_confirm_num')
            )->groupBy("created_by")->get();

        $confirm = [];

        foreach ($total_confirm as $v) {
            $confirm[$v->created_by]['total_confirm_num'] = $v->total_confirm_num;
        }
        foreach ($result_yesterday as $v) {
            $confirm[$v->created_by]['confirm_num'] = $v->confirm_num;
        }
        return $confirm;
    }

    /**
     * @return \Illuminate\Support\Collection
     * 黑名单部分
     */
    private function blackList()
    {
        $result_yesterday = DB::table('bl_records')
            ->leftJoin('bl_projects', 'bl_records.project_id', '=', 'bl_projects.id')
            ->leftJoin('bl_products', 'bl_records.product_id', '=', 'bl_products.id')
            ->whereBetween('bl_records.created_at', [$this->yesterday_begin, $this->yesterday_end])
            ->where('bl_products.status', '=', 1)
            ->select('bl_projects.project_name', 'bl_projects.project_no', 'bl_products.product_name', 'bl_products.product_code',
                DB::raw('count(*) as yesterday_num')
            )->groupBy("bl_projects.project_name")
            ->groupBy('bl_projects.project_no')
            ->groupBy('bl_products.product_name')
            ->groupBy('bl_products.product_code')
            ->get();

        $result_total = DB::table('bl_records')
            ->leftJoin('bl_projects', 'bl_records.project_id', '=', 'bl_projects.id')
            ->leftJoin('bl_products', 'bl_records.product_id', '=', 'bl_products.id')
            ->where('bl_products.status', '=', 1)
            ->select('bl_projects.project_name', 'bl_projects.project_no', 'bl_products.product_name', 'bl_products.product_code',
                DB::raw('count(*) as total_num')
            )->groupBy("bl_projects.project_name")
            ->groupBy('bl_products.product_name')
            ->groupBy('bl_projects.project_no')
            ->groupBy('bl_products.product_code')
            ->get();

        $bl_result = [];

        foreach ($result_yesterday as $v) {
            $bl_result[$v->project_name][$v->product_name] = $v->yesterday_num;
        }

        return collect($result_total)->map(function ($value, $key) use ($bl_result) {
            $value->yesterday = isset($bl_result[$value->project_name][$value->product_name]) ? $bl_result[$value->project_name][$value->product_name] : 0;
            return $value;
        });
    }
}
