<?php

namespace App\Http\Controllers\Api\Statistics;

use App\Exceptions\MyException;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Exception;
use Illuminate\Support\Facades\Request;
use Carbon\Carbon;

/**
 * 订单销售统计
 */
class OperateDataController extends Controller
{

    private $params;
    private $except_activity;
    private $special_activity;

    public function index()
    {
        $order_data = [];
        try {
            //接口权限验证
            $this->checkAuth();
            //获取所有项目所有商品的总订单数量 按照项目和商品分组
            $order_data = $this->getData();
            throw new MyException("成功", 200);
        } catch (Exception $exc) {
            $code = $exc->getCode();
            $msg  = $exc->getMessage();
        }

        if ($code == 200) {
            echo json_encode($order_data, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
        } else {
//            echo $msg;
            echo '';
        }
    }

    private function checkAuth()
    {
        $this->params = Request::all();

        if (!isset($this->params['start_date']) || empty($this->params['start_date'])) {
            //如果没有起始时间,就从昨天凌晨开始
            $this->params['start_date'] = Carbon::yesterday()->toDateTimeString();
        }

        if (!isset($this->params['end_date']) || empty($this->params['end_date'])) {
            //如果没有结束时间,就从今天凌晨开始
            $this->params['end_date'] = Carbon::today()->toDateTimeString();
        }

        //  11:蒙商银行图书家政   14:爱奇艺兑换
        $this->except_activity = [11, 14];
        // 24:明苑华夏美团,25 明苑爱奇艺 26:明苑天猫
        // 这三个活动不统计制码部分,在gift只记录成本,  在订单平台记录订单金额
        $this->special_activity = [24, 25, 26];
    }

    private function getData()
    {
//        if ($this->params['flag'] == 'exchange_batches') {
//            $exchange_code = $this->getExchangeData();
//            return $exchange_code;
//
//        } else {
//            $order_data       = $this->getOrderData();
//            $forbidden_orders = $this->getNingDeData();
//            $orders           = array_merge($order_data, $forbidden_orders);
//            return $orders;
//        }

        $exchange_code    = $this->getExchangeData();
        $order_data       = $this->getOrderData();
        $forbidden_orders = $this->getNingDeData();

        $orders = array_merge($exchange_code, $order_data, $forbidden_orders);
        return $orders;
    }


    private function getOrderData()
    {
        //需要统计的商品
//        $goods_no = ['zxbc001', 'zxbc002', 'zxbc003', 'zxbc004', 'zxbc005', 'zxbc006', 'zxbc007', 'zxbc008', 'zxbc009', 'zxbc010', 'zxbc011', 'zxbc012', 'zxbc013', 'zxbc014', 'zxbc015', 'zxbc016', 'zxbc017', 'zxbc018', 'zxbc019', 'zxbc020', 'MYFHTM010', 'MYFHTM020', 'MYFHTM050', 'JSYH001', 'JSYH002', 'JSYH003', 'JSYH004', 'JSYH006', 'JSYH007', 'JSYH007', 'JSYH008', 'QYDH0001', 'cx001', 'cx002', 'cx003', 'tongyong1'];
        //获取每个项目中每个商品销售的总数量

        //acitvity_id  11 :蒙商银行图书家政
        //acitvity_id  14 :爱奇艺兑换(光炫科技)
        //等蒙商银行大有财富上线之后 就不再限制activity_id,蒙商大有财富的订单就不在卡库上提取, 统一在gift提取
        $order_data = DB::table("orders")
            ->leftJoin("activities", "orders.activity_id", '=', "activities.id")
            ->leftJoin("exchange_details", "orders.exchange_detail_id", '=', "exchange_details.id")
            ->leftJoin("exchange_batches", "exchange_details.exchange_batch_id", '=', "exchange_batches.id")
            ->leftJoin('customers', 'activities.customer_id', '=', 'customers.id')
            //状态。 1-处理中，2-已发货，3-部分发货，4-发货失败
            ->where('orders.status', '2')
            ->where('exchange_batches.status', '1')
            ->where('orders.created_at', '>=', $this->params['start_date'])
            ->where('orders.created_at', '<', $this->params['end_date'])
            ->whereNotIn('activities.id', $this->except_activity)
            ->select(
                DB::raw("DATE_FORMAT(orders.created_at,'%Y-%m-%d') as every_day"),
                'activity_name as project_name',
                'exchange_batches.package_name as package_name',
                'exchange_batches.batch_no as batch_no',
                'orders.goods_name',
                'orders.goods_no',
                'customers.cust_name as customer',
                DB::raw('count(*) as orders_num'),
                DB::raw('sum(goods_num*orders.cost_price) as purchase_amount'), //成本金额
                DB::raw('sum((goods_num*orders.cost_price/(1+cost_tax_rate))) as purchase_amount_without_tax') //成本金额(未税)
//            )->groupBy('every_day', 'orders.activity_id', 'activities.activity_name', 'orders.goods_id', 'orders.goods_name')
            )->groupBy('every_day', 'customers.cust_name', 'project_name', 'orders.goods_name'
                , 'exchange_batches.package_name', 'orders.goods_no', 'exchange_batches.batch_no')
            ->get();

        return json_decode(json_encode($order_data), true);
    }

    private function getExchangeData()
    {
        //还有兑换码要统计 兑换码生成(销售额 收入)
        /**
         * 因为业务形式是， 我们生成一批码，然后会一次性的收到付款
         * 然后客户再一笔一笔的消耗
         * 不能拆成每一笔都有售价和成本价，所以：收到的总价-消耗的笔数（成本价）*数量=收入
         *
         * 在这里统计 总收入，和消耗 两者相减
         */

        $exchange_code = DB::table('exchange_batches')
            ->leftJoin("activities", "exchange_batches.activity_id", '=', "activities.id")
            ->leftJoin('customers', 'activities.customer_id', '=', 'customers.id')
            ->where('exchange_batches.status', '1')
            ->where('exchange_batches.created_at', '>=', $this->params['start_date'])
            ->where('exchange_batches.created_at', '<', $this->params['end_date'])
            ->whereNotIn('exchange_batches.activity_id', $this->except_activity)
            ->whereNotIn('exchange_batches.activity_id', $this->special_activity)
            ->select(
                DB::raw("DATE_FORMAT(exchange_batches.created_at,'%Y-%m-%d') as every_day"),
                'activity_name as project_name',
//                'activity_id',
                'package_name',
                'batch_no',
                'customers.cust_name as customer',
                DB::raw("sum(goods_price*total_num) as orders_amount"),
                DB::raw("sum(goods_price*total_num/(1+tax_rate)) as orders_amount_without_tax"),
                DB::raw('count(*) as orders_num')
            )->groupBy('every_day', 'customers.cust_name', 'project_name', 'package_name', 'batch_no')
            ->get();

        return json_decode(json_encode($exchange_code), true);
    }

    private function getNingDeData()
    {
        //统计宁德核销的   兑换码核销(记作成本)
        //exchange_detail_forbiddens  表获取已核销的
        // activity_id=11 表示蒙商积分
        $forbidden_orders = DB::table("exchange_detail_forbiddens")
            ->leftJoin("activities", "exchange_detail_forbiddens.activity_id", '=', "activities.id")
            ->leftJoin('customers', 'activities.customer_id', '=', 'customers.id')
            ->leftjoin('exchange_batches', "exchange_detail_forbiddens.exchange_batch_id", '=', "exchange_batches.id")
            ->whereNotIn('exchange_detail_forbiddens.activity_id', $this->except_activity)
            ->where('exchange_detail_forbiddens.created_at', '>=', $this->params['start_date'])
            ->where('exchange_detail_forbiddens.created_at', '<', $this->params['end_date'])
            ->where('exchange_detail_forbiddens.forbidden_status', 1)
            ->where('exchange_batches.status', 1)
            ->select(
                DB::raw("DATE_FORMAT(exchange_detail_forbiddens.created_at,'%Y-%m-%d') as every_day"),
                'activity_name as project_name',
//                'activity_id',
//                'exchange_batch_id',
                'customers.cust_name as customer',
                'exchange_batches.package_name as goods_name',
                'exchange_batches.package_name as package_name',
                'exchange_batches.batch_no as batch_no',
                DB::raw('count(*) as orders_num'),
                DB::raw("sum(exchange_detail_forbiddens.cost_price) as purchase_amount"),
                DB::raw("sum(exchange_detail_forbiddens.cost_price/(1+exchange_detail_forbiddens.cost_tax_rate)) as purchase_amount_without_tax")
            )->groupBy('every_day', 'customers.cust_name', 'project_name', 'goods_name', 'exchange_batches.batch_no')
            ->get();
        return json_decode(json_encode($forbidden_orders), true);

    }

}
