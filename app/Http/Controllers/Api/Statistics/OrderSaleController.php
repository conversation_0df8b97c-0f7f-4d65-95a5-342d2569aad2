<?php

namespace App\Http\Controllers\Api\Statistics;

use App\Exceptions\MyException;
use App\Http\Controllers\Api\SysCode\SysCode;
use App\Http\Controllers\Controller;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Exception;
use Illuminate\Support\Facades\Request;

/**
 * 订单销售统计
 */
class OrderSaleController extends Controller
{

    private $params;

    public function index()
    {
        $data = [];
        try {
            //接口权限验证
            $this->checkAuth();
            //获取所有项目所有商品的总订单数量 按照项目和商品分组
            $order_total_data = $this->getOrderTotal();
            //获取每个商品前天的销售数量  [goods_id=>"num"]
            $order_yesterday_data = $this->getYesterdayData();
            //整合数据
            $data = $this->handelData($order_total_data, $order_yesterday_data);
            throw new MyException("成功", 200);


        } catch (Exception $exc) {
            $code = $exc->getCode();
            $msg  = $exc->getMessage();
        }

        $resp = [
            'code' => $code,
            'msg'  => $msg,
            'data' => $data
        ];

        echo json_encode($resp);

    }

    private function checkAuth()
    {
        $this->params = Request::all();
        if (empty($this->params['app_id']) || empty($this->params['ts']) || empty($this->params['sign'])) {
            throw new MyException("参数错误", 9998);
        }

        $app_id = config("api.statistics.order_sale_app_id");
        if ($this->params['app_id'] != $app_id) {
            throw new MyException("App_id错误",SysCode::APPID_ERROR);
        }

        $api_sign = $this->params['sign'];
        $sign     = $this->getSign();
        if (strcmp($api_sign, $sign) != 0) {
            throw new MyException("签名错误", 201);
        }

    }

    private function getSign()
    {
        $secret_key = config("api.statistics.secret_key");
        return md5($this->params['app_id'] . $this->params['ts'] . $secret_key);
    }

    private function getOrderTotal()
    {
        //获取每个项目中每个商品销售的总数量
//        $order_data = DB::table("orders")->leftJoin('order_subs', 'orders.id', '=', 'order_subs.order_id')->leftJoin("activities", "orders.activity_id", '=', "activities.id")->groupBy('orders.activity_id', 'orders.goods_id', 'orders.goods_name', 'order_subs.ecp_pcode', 'activities.activity_name')->select('activities.activity_name', 'orders.activity_id', 'orders.goods_id', 'orders.goods_name', 'order_subs.ecp_pcode', DB::raw('count(*) as total'))->get();

        // activity_id
        // 10:青柠礼品兑换平台
        // 14:爱奇艺兑换
        // 15:九一畅想集采
        // 16: 乌鲁木齐分行基金客户答谢礼
        // 17 :中信城支行客户答谢
        // 18:光大银行项目
        // 19:泰隆银行权益兑换
        // 21:奈雪的茶
        // 22:瑞幸咖啡
        // 24:明苑华夏美团
        // 25:明苑华夏爱奇艺
        // 26:明苑华夏天猫
        // 27: 微信立减金
        //29:光大工会全员视听会员活动
        // 31:泰隆银行单品兑换
        //32:佰联权益商城
        //33:佰联权益商城组合商品
        // 34:百联权益商城预付卡组合商品
        // 35:百联权益商城预付卡单品
        $order_data = DB::table("orders")->leftJoin("activities", "orders.activity_id", '=', "activities.id")
            ->whereNotIn('activities.id', [10, 14, 15, 16, 17, 18, 19, 21, 22, 24, 25, 26, 27, 29, 31, 32, 33, 34, 35])
            //状态。 1-处理中，2-已发货，3-部分发货，4-发货失败
            ->where('orders.status', '2')
            ->where('activities.status', '1')
            ->groupBy('orders.activity_id', 'orders.goods_id', 'orders.goods_name', 'activities.activity_name')
            ->orderBy('activities.activity_name', 'desc')
            ->orderBy('orders.goods_name')
            ->select('activities.activity_name',
                'orders.activity_id',
                'orders.goods_id',
                'orders.goods_name',
                DB::raw('count(*) as total'),
                DB::raw('sum(goods_price) as total_price')
            )
            ->get();

        if (count($order_data) <= 0) {
            throw new MyException('数据为空', 501);
        }

        return $order_data;
    }

    private function getYesterdayData()
    {
        //获取
        $yesterday_begin  = Carbon::now()->yesterday();
        $yesterday_end    = Carbon::now()->today();
        $result_yesterday = DB::table("orders")
            ->whereBetween('created_at', [$yesterday_begin, $yesterday_end])
            ->whereNotIn('activity_id', [10, 15, 16, 17, 18, 21, 22, 24, 25, 26, 27, 29])
            ->where('status', '2')
            ->groupBy('activity_id')
            ->groupBy("goods_id")
            ->select(
                'activity_id',
                "goods_id",
                DB::raw("count(*) as yesterday_num"),
                DB::raw('sum(goods_price) as yesterday_price')
            )->get();

        if (count($result_yesterday) <= 0) {
            return [];
        }

        $yesterday_data = [];
        foreach ($result_yesterday as $k => $v) {
            $yesterday_data[$v->activity_id][$v->goods_id]['yesterday_num']   = $v->yesterday_num;
            $yesterday_data[$v->activity_id][$v->goods_id]['yesterday_price'] = $v->yesterday_price;
        }

        return $yesterday_data;
    }

    private function handelData($order_total_data, $order_yesterday_data)
    {
        $processed_data = collect($order_total_data)->map(function ($value, $key) use ($order_yesterday_data) {
            $value->yesterday       = isset($order_yesterday_data[$value->activity_id][$value->goods_id]['yesterday_num']) ? $order_yesterday_data[$value->activity_id][$value->goods_id]['yesterday_num'] : 0;
            $value->yesterday_price = isset($order_yesterday_data[$value->activity_id][$value->goods_id]['yesterday_price']) ? $order_yesterday_data[$value->activity_id][$value->goods_id]['yesterday_price'] : 0;
            return $value;
        });

        $merged_data = [];
        $to_remove   = [];

        foreach ($processed_data as $item) {
            if ($item->goods_id == 562 || $item->goods_id == 570) {
                $key = $item->activity_id;
                if (!isset($merged_data[$key])) {
                    $merged_data[$key] = (object)[
                        'activity_name'   => $item->activity_name,
                        'activity_id'     => $item->activity_id,
                        'goods_id'        => 570,
                        'goods_name'      => $item->goods_id == 570 ? $item->goods_name : '中石化加油卡100元',
                        'total'           => 0,
                        'total_price'     => 0,
                        'yesterday'       => 0,
                        'yesterday_price' => 0
                    ];
                }
                $merged_data[$key]->total           += $item->total;
                $merged_data[$key]->total_price     += $item->total_price;
                $merged_data[$key]->yesterday       += $item->yesterday;
                $merged_data[$key]->yesterday_price += $item->yesterday_price;

                if ($item->goods_id == 562) {
                    $to_remove[] = $key . '-562';
                }
            } else {
                $merged_data[$item->activity_id . '-' . $item->goods_id] = $item;
            }
        }

        // Remove the original items with goods_id 562
        foreach ($to_remove as $key) {
            unset($merged_data[$key]);
        }

        return array_values($merged_data);

    }
}
