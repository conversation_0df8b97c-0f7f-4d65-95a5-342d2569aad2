<?php

namespace App\Http\Controllers\Api\SysCode;

class SpeCode
{
    //通用状态
    const SUCCESS = 200;

    //验证类
    const SIGN_ERROR   = 3000;
    const APP_ID_ERROR = 3001;
    const BASE_PARAMS_ERROR = 3002;
    const BIZ_CONTENT_ERROR = 3003;


    //系统级错误
    const SYSTEM_ERROR = 9999;

    static public $resp_msg = [
        self::SUCCESS      => '成功',
        self::SIGN_ERROR   => '签名错误',
        self::APP_ID_ERROR => 'APP_ID错误',
        self::BASE_PARAMS_ERROR => '基础参数错误',
        self::BIZ_CONTENT_ERROR => '业务参数错误',


        // 系统级错误
        self::SYSTEM_ERROR => '系统繁忙,请稍后再试!',
    ];

    const COMMON_STATUS_ON  = 1;
    const COMMON_STATUS_OFF = 0;

    public static $common_status = [
        self::COMMON_STATUS_OFF => '禁用',
        self::COMMON_STATUS_ON  => '启用',
    ];

}
