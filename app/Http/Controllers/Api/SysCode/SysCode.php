<?php

namespace App\Http\Controllers\Api\SysCode;

class SysCode
{
    //通用状态
    const SUCCESS      = 200;
    const SUCCESS_0000 = '0000'; //用于回调
    const FAILED       = 202; //失败

    //登录
    const NOT_LOGIN = 201;

    //验证类
    const DECRYPT_ERROR         = 3000;
    const NAME_EMPTY            = 3001;
    const CARD_NO_ERROR         = 3002;
    const NOT_FIT               = 3003;
    const PARAMS_ERROR          = 3004;
    const PRIZE_LEVEL_ERROR     = 3005;
    const GOOD_LISTS_EMPTY      = 3007;
    const CUSTOMER_ONCE         = 3008;
    const GOOD_LISTS_FAILD      = 3009;
    const GOOD_DETAILS_FAILD    = 3010;
    const EXCHANGE_NOT_ALLOW    = 3011;
    const CHARGE_ACCOUNT_ERROR  = 3012;
    const EXCHANGE_RECORD_EMPTY = 3013;
    const CARD_PWD_ERROR        = 3014;
    const CARD_PWD_EXPIRED      = 3025;

    //回调验证
    const APPID_ERROR       = 3015;
    const SIGN_ERROR        = 3016;
    const MOBILE_ERROR      = 3017;
    const ACTIVITY_ERROR    = 3018;
    const NOT_CHANCE_3019   = 3019;
    const EXCHANGE_LOG_3020 = 3020;
    const MOBILE_REPEAT     = 3021;
    const UPLOAD_IMG_ERROR  = 3022;

    const EXCHANGE_FIRST = 3023;
    const CAPTCHA_ERROR  = 3024;

    const EXCHANGE_GOODS_REPEAT     = 3026;//不能重复兑换同样的商品。
    const EXCHANGE_GOODS_NOT_PERMIT = 3027;//没有兑换该礼品的权限，请选择其它礼品
    const EXCHANGE_GOODS_NOT_EXIST  = 3036;//兑换商品不存在

    const EXCHANGE_CYCLE_ERROR = 3028; // 兑换周期内重复兑换。

    const CAPTCHA_IMG_ERROR = 3029;//图形校验码校验失败

    const EXCHANGE_MAX_LIMIT = 3030; //超过当日最大限制

    const GOOD_SETTING_ERROR = 3031; //商品配置错误

    const SERVICE_DATE_ERROR = 3032; //预约日期不在可预约范围

    const EXCHANGE_RIGHT_ERROR = 3033; //无兑换权限。

    const EXCHANGE_WRONG_MOBILE = 3034; //手机号和兑换码不匹配(已兑换过的)

    const EXCHANGE_ERROR = 3035;//兑换失败，请稍后重试

    const MULTI_USERS = 4001; //手机号对应的账号有多个。比如淘宝手机号充值，对应多个淘宝账号的情况

    const CHANNEL_DEAL_ERROR = 4002; //比如上游渠道返回失败


    //验证活动兑换时间
    const EXCHANGE_NOT_BEGIN = 3100;
    const EXCHANGE_HAD_END   = 3101;
    //
    const EXCHANGE_TOP_LIMIT_3104 = 3104;  //

    const ERROR_LIMIT  = 9995;//超出错误次数限制
    const UNKNOW_ERROR = 9998;//未知错误
    const SYSTEM_ERROR = 9999;

    //以下为订单相关错误
    const ORDER_NOT_EXIST = 6003;//订单不存在


    // static
    static public $resp_msg = [
        self::SUCCESS      => '成功',
        self::SUCCESS_0000 => '接收成功',
        self::FAILED       => '领取失败',

        self::NOT_LOGIN          => '未登录！',
        self::DECRYPT_ERROR      => '解析错误！',
        self::NAME_EMPTY         => '名称不能为空！',
        self::CARD_NO_ERROR      => '卡号错误！',
        self::NOT_FIT            => '对不起!您暂未获得领奖资格！',
        self::PARAMS_ERROR       => '参数错误！',
        self::PRIZE_LEVEL_ERROR  => '礼品等级错误！',
        self::GOOD_LISTS_EMPTY   => '无对应的礼品信息！',
        self::CUSTOMER_ONCE      => '您已经领过奖品了！',
        self::GOOD_LISTS_FAILD   => '礼品列表获取失败!',
        self::GOOD_DETAILS_FAILD => '礼品详情获取失败!',
        self::EXCHANGE_FIRST     => '请先兑换！',
        self::CAPTCHA_ERROR      => '验证码不正确！',
        self::CAPTCHA_IMG_ERROR  => '校验码不正确！',

        self::EXCHANGE_NOT_ALLOW    => '还未获得该礼品的兑换资格',
        self::CHARGE_ACCOUNT_ERROR  => '充值账号不正确！',
        self::EXCHANGE_RECORD_EMPTY => '暂无兑换记录！',
        self::CARD_PWD_ERROR        => '卡密错误！',
        self::CARD_PWD_EXPIRED      => '卡密已过期！',
        self::MOBILE_REPEAT         => '手机号重复！',
        self::MOBILE_ERROR          => '手机号不正确！',
        self::ACTIVITY_ERROR        => '活动信息有误，请联系活动方！',
        self::NOT_CHANCE_3019       => '对不起!您暂未获得兑换资格！',
        self::EXCHANGE_LOG_3020     => '您已经领过礼品了,请在页面底部查询领取记录。',
        self::UPLOAD_IMG_ERROR      => '上传图片失败！',

        self::GOOD_SETTING_ERROR      => '礼品配置有误！',


        //活动兑换时间
        self::EXCHANGE_NOT_BEGIN      => '本活动尚未开始，请耐心等待。',
        self::EXCHANGE_HAD_END        => '本活动已结束^_^',
        //
        self::EXCHANGE_TOP_LIMIT_3104 => '兑奖名额已领完。',

        self::EXCHANGE_GOODS_REPEAT    => '不能重复兑换同样的礼品，请选择其它礼品^_^',
        self::EXCHANGE_GOODS_NOT_EXIST => '该礼品暂时无法兑换，请选择其它礼品^_^',

        self::EXCHANGE_GOODS_NOT_PERMIT => '您没有兑换该礼品的权限，请选择其它礼品^_^',
        self::EXCHANGE_CYCLE_ERROR      => '本{0}已领取，请下{0}再来^_^',
        self::EXCHANGE_MAX_LIMIT        => '已达今日充值上限，请明天再试。',
        self::EXCHANGE_WRONG_MOBILE     => '该手机号与兑换码不匹配。',
        self::SERVICE_DATE_ERROR        => '特殊节假日期间，家政服务暂停预约，请您选择其它时间进行预约，给您造成的不便敬请谅解。',  //预约日期不在可预约范围
        self::EXCHANGE_ERROR            => '领取失败，请稍后重试',

        self::EXCHANGE_RIGHT_ERROR => '无兑换权限，请重新登录。',

        self::MULTI_USERS => "手机号码对应账号数量大于1", //手机号对应的账号有多个。比如淘宝手机号充值，对应多个淘宝账号的情况

        self::CHANNEL_DEAL_ERROR => "处理失败，请稍后重试", //比如上游渠道返回失败


        //回调
        self::APPID_ERROR        => 'appid错误。',
        self::SIGN_ERROR         => '签名错误。',
        self::ERROR_LIMIT        => '操作过于频繁，请稍后再试^_^',
        self::SYSTEM_ERROR       => '系统开小差中...请稍后再试^_^',
        self::UNKNOW_ERROR       => '未知错误，请联系服务商。^_^',

        self::ORDER_NOT_EXIST => '订单不存在',

    ];


    const PRIZE_LEVEL_1 = 1;
    const PRIZE_LEVEL_2 = 2;

    public static $prize_level = [
        self::PRIZE_LEVEL_1 => '一重礼',
        self::PRIZE_LEVEL_2 => '二重礼',
    ];

    const GOODS_TYPE_1 = 1;
    const GOODS_TYPE_2 = 2;
    const GOODS_TYPE_3 = 3;
    const GOODS_TYPE_4 = 4;
    const GOODS_TYPE_5 = 5;
    const GOODS_TYPE_6 = 6;


    public static $goods_type = [
        self::GOODS_TYPE_1 => '实物商品',
        self::GOODS_TYPE_2 => '虚拟商品卡密类',
        self::GOODS_TYPE_3 => '虚拟商品-异步直充类',
        self::GOODS_TYPE_4 => '实物+虚拟',
        self::GOODS_TYPE_5 => '短连接',
        self::GOODS_TYPE_6 => '家政服务',
    ];

    const ORDER_SUB_STATUS_DELETED = -1;
    const ORDER_SUB_STATUS_0       = 0;
    const ORDER_SUB_STATUS_1       = 1;
    const ORDER_SUB_STATUS_2       = 2;
    const ORDER_SUB_STATUS_3       = 3;
    const ORDER_SUB_STATUS_4       = 4;
    const ORDER_SUB_STATUS_5       = 5;

    public static $order_sub_status = [
        self::ORDER_SUB_STATUS_DELETED => '无效订单', //兑换码订单撤销后，置为该状态。用户订单表中不显示。
        self::ORDER_SUB_STATUS_0       => '已取消',   //预留
        self::ORDER_SUB_STATUS_1       => '未处理',
        self::ORDER_SUB_STATUS_2       => '处理中',
        self::ORDER_SUB_STATUS_3       => '已发货',
        self::ORDER_SUB_STATUS_4       => '发货失败',
        self::ORDER_SUB_STATUS_5       => '发货失败，可重提',
    ];

    const ORDER_STATUS_DELETED = -1; //无效订单
    const ORDER_STATUS_0       = 0;  //已取消
    const ORDER_STATUS_1       = 1;
    const ORDER_STATUS_2       = 2;
    const ORDER_STATUS_3       = 3;
    const ORDER_STATUS_4       = 4;

    public static $order_status = [
        self::ORDER_STATUS_DELETED => '无效订单', //兑换码订单撤销后，置为该状态。用户订单表中不显示。
        self::ORDER_STATUS_0       => '已取消',   //预留
        self::ORDER_STATUS_1       => '处理中',
        self::ORDER_STATUS_2       => '已发货',
        self::ORDER_STATUS_3       => '部分发货', //暂不用该状态。
        self::ORDER_STATUS_4       => '发货失败',
    ];

    const SMS_STATUS_1 = 1;
    const SMS_STATUS_2 = 2;
    const SMS_STATUS_3 = 3;

    public static $sms_status = [
        self::SMS_STATUS_1 => '未知',
        self::SMS_STATUS_2 => '成功',
        self::SMS_STATUS_3 => '失败',
    ];

    const COMMON_STATUS_0 = 0;
    const COMMON_STATUS_1 = 1;
    const COMMON_STATUS_2 = 2;

    public static $common_status = [
        self::COMMON_STATUS_0 => '禁用',
        self::COMMON_STATUS_1 => '启用',
        self::COMMON_STATUS_2 => '已领取',
    ];

    //用于实物订单的短信发送
    //入库即需要发送短信

    const IS_SEND_MSG_0 = 0;
    const IS_SEND_MSG_1 = 1;
    const IS_SEND_MSG_2 = 2;
    const IS_SEND_MSG_3 = 3;
    const IS_SEND_MSG_4 = 4;

    public static $is_send_msg = [
        self::IS_SEND_MSG_0 => '不发短信',
        self::IS_SEND_MSG_1 => '未处理',
        self::IS_SEND_MSG_2 => '处理中',
        self::IS_SEND_MSG_3 => '成功',
        self::IS_SEND_MSG_4 => '失败',
    ];
}
