<?php

/**
 * 测试
 *
 */

namespace App\Http\Controllers\Api;

use App\Models\ExchangeBatch;
use App\Models\ExchangeDetail;
use App\Models\ExchangeRecord;
use Carbon\Carbon;
use Exception;
use App\Service\Aes;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Api\SysCode\SysCode;
use GuzzleHttp\Client;

class TestController extends Controller
{
    public function __construct()
    {
    }

    public function resonse_200()
    {

        return response([
            'code' => 200,
            'msg'  => 'Success',
        ], 200, ['Content-Type' => 'application/json;charset=utf-8']);

    }

    public function resonse_500()
    {

        return response([
            'code' => 500,
            'msg'  => '失败',
        ], 200, ['Content-Type' => 'application/json;charset=utf-8']);

    }

    public function black_test()
    {
//        return response([
//            'code' => 200,
//            'msg'  => '成功',
//        ], 200, ['Content-Type' => 'application/json;charset=utf-8']);

        return response([
            'code' => 500,
            'msg'  => '失败',
        ], 200, ['Content-Type' => 'application/json;charset=utf-8']);

    }

    protected function api_response(array $content, $status = 200)
    {
        return response($content, $status, ['Content-Type' => 'application/json;charset=utf-8']);
    }

    public function my_test()
    {

//        dd(Carbon::now()->startOfDay()->format('Y-m-d H:i:s'));

        $detail = ExchangeDetail::where('expired', 0)
            ->whereIn('status', [ExchangeDetail::STATUS_NO_EXCHANGE, ExchangeDetail::STATUS_EXCHANGEING])
            ->where('endtime', '<', Carbon::now()->startOfDay())
            ->orderBy('id')
            ->first();

        dd($detail);

        dd(time() > strtotime(ExchangeDetail::find(1)->endtime . "23:59:59"));
        $request = request();
        $ip      = $request->ip();
//        $url = request()->getPathInfo();
        $url = request()->path();

        $black_cache_key = config('cache.black_key_pre') . ":${ip}:${url}";

        dd($black_cache_key);
//        $charid = strtoupper(md5(uniqid(mt_rand(), true)));
//        $uuid =
//            substr($charid, 2, 2).
//            substr($charid,12, 2).
//            substr($charid,20, 4);
        dd(getRandStr(8));
        session(['aa' => ['query_only' => true]]);
        var_dump(!empty(session('aa.query_only')));
        exit();
//        dd(ExchangeRecord::findOrFail(1)); //Illuminate\Database\Eloquent\ModelNotFoundException
//        dd(ExchangeRecord::where('id',1)->get()->first());
        dd(ExchangeDetail::find(1)->exchange_batch);
    }

    public function encrypt()
    {

        $aes = new Aes(env("APP_KEY"));
        $aa  = $aes->encrypt('WxCoupon');
        dd($aa);
//         $bb = $aes->decrypt("8iWm+3BPsot23RtO/+Rs8A==");
//         $bb = $aes->decrypt("Uk6mT6aqI8E57G7mkkkc8A==");
//         $bb = $aes->decrypt("qgLc+QN1bTuOQdwY2DufLQ==");
        $bb = $aes->decrypt("z8Rgt/xN2kn8FpMoayO7Ew==");
        dd($bb);
//        dd($aa);
        // $bb = $aes->encrypt('XKDX');

        // var_dump($bb);
    }

    public function log()
    {
        //        'api_log' => [
        //            'driver' => 'daily',
        //            'tap' => [App\Logging\EsFormatter::class],
        //            'path' => storage_path('logs/api_log.log'),
        //            'level' => 'info',
        //        ],

        //        Log::stack(['api_log','whq_log'])->info( json_encode(array('req_time'=> date('Y-m-d H:i:s'))));
        Log::stack(['api_log', 'whq_log'])->info('normal string');
        echo "success";
    }

    public function print_req()
    {
        $data = [];
        $code = '';
        try {
            $request = request();
            $request->setTrustedProxies($request->getClientIps(), \Illuminate\Http\Request::HEADER_X_FORWARDED_FOR);
            $data['client_ip']  = $request->ip();
            $data['client_ips'] = $request->ips();
            $data['request']    = $request->all();
            $data['server']     = $request->server->all();
            $code               = SysCode::SUCCESS;
        } catch (Exception $exc) {
            $code = $exc->getMessage();
        }
        echo response_info($code, $data);
    }

    public function print_sess()
    {
        $data = [];
        $code = '';
        try {
            $data['session'] = request()->session()->all();
            $code            = SysCode::SUCCESS;
        } catch (Exception $exc) {
            $code = $exc->getMessage();
        }
        echo response($data);
    }

    public function forget_sess()
    {
//        request()->session()->forget(['activity', 'user', 'user_info']);
        request()->session()->flush();
//        request()->session()->invalidate();
        echo response("success");
    }

    public function req_and_rsp(\Illuminate\Http\Request $request)
    {
        $log_data           = [];
        $ret                = '';
        $log_data['method'] = $request->getMethod();
        $log_data['url']    = $request->url();
        $log_data['header'] = $request->header();
        $log_data['req']    = $request->all();
        if ($request->exists("rsp")) {
            $ret = $request->get("rsp");
        } else {
            $ret = 'success';
        }

        $log_data['rsp'] = $ret;
        Log::info(json_encode($log_data, JSON_UNESCAPED_UNICODE));
        return $ret;
    }

    public function test_xdz_issue()
    {
        $issue_obj = new Issue();
        $params    = [
            'chantype'   => 102,
            'oapp'       => 'F-PBMS',
            'serialno'   => '0502001900439012',
            'trxtype'    => 2,
            'zoneno'     => '00101',
            'brno'       => '00000',
            'tellerno'   => '30',
            'workdate'   => '2022-10-01',
            'worktime'   => '12:00:00',
            'custId'     => '020079000034816',
            'custPhone'  => '13400202020',
            'serviceId'  => '20220930000001079',
            'channel'    => '4',
            'orderid'    => '1111',
            'isBookFlag' => 0,
            'expireDate' => "2022-12-31",


        ];
        $a         = $issue_obj->index($params);
        dd($a);
    }

    public function test_xdz_result_confirm()
    {
        $result_confirm_obj = new ResultConfirm();
        $params             = [
            'chantype'    => "102",
            'serialno'    => '010202002181004021622443800',
            'oapp'        => 'F-PBMS',
            'trxtype'     => "2",
            'zoneno'      => '00101',
            'brno'        => '00000',
            'tellerno'    => '',
            'workdate'    => '2022-10-01',
            'worktime'    => '09:31:53',
            'revtranf'    => 0,
            'issueStatus' => 2,
            'svcIssueId'  => 'SRVISSUE20221001000001291',
            'orderResult' => "0"

        ];
        $a                  = $result_confirm_obj->index($params);
        dd($a);
    }

    public function aaa()
    {
        $appid        = "wx24b546e1c992508a";
        $redirect_url = "http://gift-backend.dev.limeb.cn/api/test/getCode";
        $scope        = "snsapi_base"; // 或者 snsapi_userinfo
        $state        = "123abc";

        $url = "https://open.weixin.qq.com/connect/oauth2/authorize?";
        $url .= "appid={$appid}&redirect_uri={$redirect_url}&response_type=code&scope={$scope}&state={$state}#wechat_redirect";

        header("Location: {$url}");
    }

    public function getCode()
    {
        $code   = $_GET["code"];
        $appid  = 'wx24b546e1c992508a';
        $secret = '786ac6c61f7a6c075e40add8d79c5757';
        $url    = "https://api.weixin.qq.com/sns/oauth2/access_token?";
        $url    .= "appid={$appid}&secret={$secret}&code={$code}&grant_type=authorization_code";

        $client = new Client();
        $response = $client->request('GET',$url);

        $resp_json = $response->getBody()->getContents();

        $resp = json_decode($resp_json, true);

        var_dump($resp);

//        $result = $this->sendCurl($url, [], 'get');
//
//        var_dump($result);
//
//        $result = json_decode($result, true);
//
//        var_dump($result);

//        $access_token = $result["access_token"];
//        $openid = $result["openid"];
//        $url
    }

    function sendCurl($url, array $params = array(), $mode = 'post', $time = 30)
    {
        $curlHandle = curl_init();
        curl_setopt($curlHandle, CURLOPT_TIMEOUT, $time); //最大响应时间
        curl_setopt($curlHandle, CURLOPT_RETURNTRANSFER, TRUE);
        if ($mode == 'post') {
            curl_setopt($curlHandle, CURLOPT_HTTPHEADER, array('Expect:'));
            curl_setopt($curlHandle, CURLOPT_POST, TRUE);
            //curl_setopt($curlHandle, CURLOPT_TIMEOUT, '20');
            curl_setopt($curlHandle, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_0); //强制使用哪个版本
            curl_setopt($curlHandle, CURLOPT_POSTFIELDS, http_build_query($params));
        } else {
            $url .= (strpos($url, '?') === FALSE ? '?' : '&') . http_build_query($params);
        }
        curl_setopt($curlHandle, CURLOPT_URL, $url);
        if (substr($url, 0, 5) == 'https') {
            curl_setopt($curlHandle, CURLOPT_SSL_VERIFYPEER, FALSE);
            curl_setopt($curlHandle, CURLOPT_SSL_VERIFYHOST, FALSE);
        }

        $result     = curl_exec($curlHandle);
        $curl_errno = curl_errno($curlHandle);
        $curl_error = curl_error($curlHandle);
        // if ($curl_errno > 0) {
        //     write_log(LOG_LEVEL_ERROR, 'curl_error', $url . ',' . json_encode($params, JSON_UNESCAPED_UNICODE) . ',' . "cURL Error ($curl_errno): $curl_error");
        // }
        curl_close($curlHandle);
        return $result;
    }
}
