<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;


class UploadsController extends Controller
{

    protected $storage;
//    // 存储到本地方法，支持多图片上传
//    public function uploadImg(Request $request) {
//
//        $return = [];
//        $files = $request->file("upImages");
//        if (!empty($files)) {
//            if ($len = count($files) > 20) {
//                return response()->json(['ResultData' => 6, 'info' => '最多可以上传20张图片']);
//            }
//            $succeed = $fail = 0;
//            for ($i = 0; $i <= $len; $i++) {
//
//                if ($files[$i]->isValid()) {
//
//                    if (in_array(strtolower($files[$i]->extension()), ['jpeg', 'jpg', 'gif', 'png'])) {
//
//                        $ext = $files[$i]->getClientOriginalExtension();
//                        $filePath = public_path() . DIRECTORY_SEPARATOR . 'upload/images';
//                        $filename = time() . \Str::random(6) . "." . $ext;
//                        if ($files[$i]->move($filePath, $filename)) {
//                            $newFileName = '/upload/images/' . $filename;
//                            $succeed = $succeed + 1;
//                        } else {
//                            $fail = $fail + 1;
//                        }
//                        $msg = $succeed . ' 张图片上传成功' . $fail . ' 张图片上传失败。<br>';
//                        $return[] = ['ResultData' => 0, 'info' => $msg, 'newFileName' => $newFileName];
//                    } else {
//                        return response()->json(['ResultData' => 3, 'info' => '第' . ($i+1) . ' 张图片后缀名不合法!<br/>' . '只支持jpeg/jpg/png/gif格式。']);
//                    }
//                } else {
//                    return response()->json(['ResultData' => 1, 'info' => '第' . ($i+1) . ' 张图片超过最大限制!<br/>' . '图片最大支持2M。']);
//                }
//            }
//        } else {
//            return response()->json(['ResultData' => 5, 'info' => '请选择文件']);
//        }
//
//        return $return;
//    }


    // 上传到阿里云服务器，不支持多图片上传。
    public function uploadImg(Request $request) {

        $this->storage = Storage::disk('oss');
        $return = [];
        $file = $request->file("upImage");
        if (!empty($file)) {

            if ($file->isValid()) {

                if (in_array(strtolower($file->extension()), ['jpeg', 'jpg', 'gif', 'png'])) {

                    $ext = $file->getClientOriginalExtension();
                    $filename = time() . \Str::random(6) . "." . $ext;
                    $newFileName = '';

                    $path = $this->storage->putFileAs(env('WANG_EDITOR_DIR_OF_OSS'), $file, $filename);
                    $msg = ' 图片上传成功<br>';
                    if ($path) {
                        $newFileName = env('OSS_URL') . $path;
                    } else {
                        $msg = '图片上传失败<br>';
                    }

                    $return[] = ['ResultData' => 0, 'info' => $msg, 'newFileName' => $newFileName];
                } else {
                    return response()->json(['ResultData' => 3, 'info' => '图片后缀名不合法!<br/>只支持jpeg/jpg/png/gif格式。']);
                }
            } else {
                return response()->json(['ResultData' => 1, 'info' => '图片超过最大限制!<br/>图片最大支持2M。']);
            }
        }

        return $return;
    }


//    public function uploadImg(Request $request)
//    {
//
//        $return = [];
//
//        $this->storage = Storage::disk(config("admin.upload.disk"));
//        $img_base_path = config("admin.upload.directory.image");
//
//        $files = $request->file("upImages");
//
//        if (!empty($files)) {
//            if ($len = count($files) > 10) {
//                return response()->json(['ResultData' => 6, 'info' => '最多可以上传10张图片']);
//            }
//            $succeed = $fail = 0;
//            for ($i = 0; $i <= $len; $i++) {
//
//                if ($files[$i]->isValid()) {
//
//                    if (in_array(strtolower($files[$i]->extension()), ['jpeg', 'jpg', 'gif', 'png'])) {
//
//                        $ext      = $files[$i]->getClientOriginalExtension();
//                        $filePath = public_path() . DIRECTORY_SEPARATOR . 'upload/images';
//
//                        $filename = time() . \Str::random(6) . "." . $ext;
//
//                        if($files[$i]->storeAs($img_base_path, $filename, ['disk' => config("admin.upload.disk")])){
//                            $succeed     = $succeed + 1;
//                        }else{
//                            $fail = $fail + 1;
//                        }
//                        $newFileName = "/upload/${img_base_path}/${filename}";
//
//                        $msg      = $succeed . ' 张图片上传成功' . $fail . ' 张图片上传失败。<br>';
//                        $return[] = ['ResultData' => 0, 'info' => $msg, 'newFileName' => $newFileName];
//                    } else {
//                        return response()->json(['ResultData' => 3, 'info' => '第' . ($i + 1) . ' 张图片后缀名不合法!<br/>' . '只支持jpeg/jpg/png/gif格式。']);
//                    }
//                } else {
//                    return response()->json(['ResultData' => 1, 'info' => '第' . ($i + 1) . ' 张图片超过最大限制!<br/>' . '图片最大支持2M。']);
//                }
//            }
//        } else {
//            return response()->json(['ResultData' => 5, 'info' => '请选择文件']);
//        }
//
//        return $return;
//    }

}
