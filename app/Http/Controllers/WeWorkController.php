<?php

namespace App\Http\Controllers;


use Illuminate\Support\Facades\Log;

class WeWorkController extends Controller
{

    /**
     * 处理企业微信的请求消息
     *
     * @return string
     */
    public function serve()
    {
        Log::info('request arrived.'); # 注意：Log 为 Laravel 组件，所以它记的日志去 Laravel 日志看，而不是 EasyWeChat 日志

//        $config = config('wechat.work.default');
//
//        $work = \EasyWeChat\Factory::work($config);

        $work = app('wechat.work');

        $work->server->push(function ($message) {
            // $message['FromUserName'] // 消息来源
            // $message['MsgType'] // 消息类型：event ....

            return 'Hello easywechat.';
        });

        $response = $work->server->serve();

        $response->send();
    }
}
