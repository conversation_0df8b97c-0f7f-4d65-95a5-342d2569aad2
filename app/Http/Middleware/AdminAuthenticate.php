<?php

namespace App\Http\Middleware;

use Closure;
use Encore\Admin\Facades\Admin;

class AdminAuthenticate extends \Encore\Admin\Middleware\Authenticate
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     *
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        \config(['auth.defaults.guard' => 'admin']);

        $redirectTo = admin_base_path(config('admin.auth.redirect_to', 'auth/login'));

        if (Admin::guard()->guest() && !$this->shouldPassThrough($request)) {
            $login_extra = session('admin_login_extra', '');//?s=lime
            return redirect()->to($redirectTo . $login_extra);
        }

        return $next($request);
    }
}
