<?php

/**
 * 限流
 * 秒级别
 * 黑名单功能
 * 规定时间内超过访问次数加入黑名单
 */

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Response;
use RuntimeException;
use \Illuminate\Routing\Middleware\ThrottleRequests;

class BlackListRequests extends ThrottleRequests
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */

    public function handle($request, Closure $next, $maxAttempts = 60, $decayMinutes = 1)
    {

        $key = $this->resolveRequestSignature($request);

        $maxAttempts = $this->resolveMaxAttempts($request, $maxAttempts);

        if ($this->limiter->tooManyAttempts($key, $maxAttempts)) {
            return $this->buildException($key, $maxAttempts);
        }

        $this->limiter->hit($key, $decayMinutes);
        $response = $next($request);
        return $this->addHeaders(
            $response,
            $maxAttempts,
            $this->calculateRemainingAttempts($key, $maxAttempts)
        );
    }

    protected function buildException($key, $maxAttempts)
    {
        $retryAfter = $this->limiter->availableIn($key);

        //要返回的数据
        $message = json_encode([
            'code' => 429,
            'msg' => '您的请求太频繁，已被限制请求',
            'data' => '',
            'retryAfter' => $retryAfter,
        ], 320);

        $response = new Response($message, 200);

        return $this->addHeaders(
            $response,
            $maxAttempts,
            $this->calculateRemainingAttempts($key, $maxAttempts, $retryAfter),
            $retryAfter
        );
    }

    protected function addHeaders(\Symfony\Component\HttpFoundation\Response $response, $maxAttempts, $remainingAttempts, $retryAfter = null)
    {
        $response->headers->add(
            ['Content-Type' => 'application/json;charset=utf-8']
        );
        return parent::addHeaders($response, $maxAttempts, $remainingAttempts, $retryAfter);
    }

    /**
     * Resolve request signature.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return string
     *
     * @throws \RuntimeException
     */
    protected function resolveRequestSignature($request)
    {
        //问题一 : 如果不用ip 或者 domain  未登录的情况怎么获取唯一key
        if ($user = $request->user()) {
            return sha1($user->getAuthIdentifier());
        }

        if ($route = $request->route()) {
            return sha1($route->getDomain() . '|' . $request->ip());
        }

        //这个抛出  稍后改为api形式
        throw new RuntimeException('Unable to generate the request signature. Route unavailable.');
    }
}
