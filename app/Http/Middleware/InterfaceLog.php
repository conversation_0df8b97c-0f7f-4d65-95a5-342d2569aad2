<?php

/**
 * desc 用中间件的形式做接口日志的记录
 */

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;

class InterfaceLog
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     * @param string $log_channel 自定义日志配置名称。
     * @return mixed
     */
    public function handle($request, Closure $next, $log_channel = '')
    {
//        $request->attributes->add(['mid_params' => 'this is mid_params']);//通过$request从controller传值给中间件。
//        $mid_params = $request->get('mid_params');//中间件获取参数。
//        获取所有参数： $all_attr = $request->attributes->all();

        $request->setTrustedProxies($request->getClientIps(), \Illuminate\Http\Request::HEADER_X_FORWARDED_FOR);  //获取真实ip，需添加该语句。

        $req_time = $this->getMillisecond();
        $response = $next($request);
        $res_time = $this->getMillisecond();

        // 记录所有请求信息
        $requestMessage = [
            'req_time'     => $req_time,
            'res_time'     => $res_time,
            'process_time' => $res_time - $req_time,
            'req_params'   => $request->input(),
            'req_content'  => $request->getContent(),
            'res_params'   => $response->original, //返回结果
            'ip'           => $request->ip(),
            'path'         => $request->path(),
            'method'       => $request->method(),
            'baseurl'      => $request->getSchemeAndHttpHost(),
            'referer'      => $request->header('referer'),
            'content-type' => $request->header('content-type'),
            'accept'       => $request->header('accept'),
            'user-agent'   => $request->header('user-agent'),
            //'headers'       => $request->header(),
        ];

        if ($request->attributes->count() > 0) {
            $requestMessage['attach'] = $request->attributes->all();
        }

        if ($request->file()) {
            // 文件内容不做日志记录，使用<file>做标识
            $requestMessage['body'] = '<file>';
        }
        if (Session::isStarted()) {
            $requestMessage['session'] = $this->getSessions($request);
        }

        // 控制器中可以根据log_channel控制日志记录到那个文件。
        $controller_log_channel = $request->attributes->get('log_channel', '');
        if (!empty($controller_log_channel)) {
            Log::channel($controller_log_channel)->info(json_encode($requestMessage, JSON_UNESCAPED_UNICODE));
        } else {
            // 要根据 config/logging.php  中的配置  自己命名 暂定 business_log
            Log::channel(empty($log_channel) ? 'api_log' : $log_channel)->info(json_encode($requestMessage, JSON_UNESCAPED_UNICODE));
        }
        return $response;
    }

    private function getMillisecond()
    {
        list($t1, $t2) = explode(' ', microtime());
        return (int)(((float)$t1 + (float)$t2) * 1000);
    }

    private function getSessions($request)
    {
        $data = $request->session()->all();
        unset($data['_previous'], $data['_flash']);
        return $data;
    }
}
