<?php

/**
 * desc 用中间件做接口请求错误次数限制(自然日/周/月/年)。
 */

namespace App\Http\Middleware;

use App\Exceptions\MyException;
use Carbon\Carbon;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;
use function PHPSTORM_META\type;

class WrongLimitEntire
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     * @param string $period_type 限制周期类型 h-小时/d-天/w-周/m-月/y-年。
     * @param string $maxAttempts 错误最大次数。
     * @param string $err_code 错误码结合，用|隔开。为空则非200均会计算到错误里
     * @param string $black_key 黑名单存储的键的组成，例如：{ip}_{path}_{mobile}。仅支持这三个元素。mobile从请求参数中取（参数名为mobile）。
     * @param string $black_msg 如果达到最大错误次数响应的消息内容
     * @return mixed
     */
    public function handle(Request $request, Closure $next, $period_type = 'd', $maxAttempts = 10, $err_code = '', $black_key = '{ip}_{path}', $black_msg = '操作过于频繁，请稍后重试！')
    {
        $request->setTrustedProxies($request->getClientIps(), \Illuminate\Http\Request::HEADER_X_FORWARDED_FOR);  //获取真实ip，需添加该语句。

        $ip   = $request->ip();
        $path = $request->path();

        $redis       = null;
        $black_count = null;

        try {
            $redis = app('redis.connection');
        } catch (\RedisException $e) {

            Log::error('RedisException: ' . $e->getMessage());

            //加了限制的目的是错误控制，redis服务器如果挂了，干脆都返回错误
            $rsp = [
                'code' => 9999,
                'msg'  => '系统开小差中...请稍后再试^_^',
            ];

            $request->attributes->add(['err_msg' => 'RedisException: ' . $e->getMessage()]);

            return response($rsp, 200, ['Content-Type' => 'application/json;charset=utf-8']);
        }

        $black_cache_key = $this->get_black_key($request, $black_key);

        $request->attributes->add(['black_cache_key' => $black_cache_key]);

        //判断是否够错误次数。
        if ($redis) {
            $black_count = $redis->get($black_cache_key);
            if (!empty($black_count) && $black_count >= $maxAttempts) {

                //大于等于指定次数，返回指定错误信息
                $rsp = $this->get_black_resp($request, $maxAttempts, $black_msg);

                $log_data = [
                    'middleware'      => static::class,
                    'req_time'        => $this->getMillisecond(),
                    'ip'              => $ip,
                    'method'          => $request->method(),
                    'baseurl'         => $request->getSchemeAndHttpHost(),
                    'path'            => $path,
                    'req_params'      => $request->input(),
                    'res_params'      => $rsp,
                    'black_cache_key' => $black_cache_key,
                    'black_count'     => $black_count,
                ];

                Log::channel('black_log')->info(json_encode($log_data, JSON_UNESCAPED_UNICODE));

                return response($rsp, 200, ['Content-Type' => 'application/json;charset=utf-8']);
            }
        }

        $response = $next($request);

        try {
            $rsp = $response->getOriginalContent(); //返回结果

//            response: {"message":"Server Error"}

//            Log::info('type response->original: ' . gettype($response->original));
//            Log::info('type response->getOriginalContent: ' . gettype($rsp));
//            if (is_string($rsp)) {
//                Log::info('response->original(str): ' . $rsp);
//            } elseif (is_array($rsp)) {
//                Log::info('response->original(arr): ' . json_encode($rsp, true));
//            }
//            Log::info('response->getStatusCode: ' . $response->getStatusCode());
//            Log::info('response->statusText: ' . \Symfony\Component\HttpFoundation\Response::$statusTexts[$response->getStatusCode()]);
//            Log::info('response->getContent: ' . $response->getContent());
//            Log::info('response->getOriginalContent: ' . json_encode($response->getOriginalContent()));
//            Log::info('response: ' . $response);

            if ($response->getStatusCode() == 500) {
                //这里代码启用的话，不会返回给客户端500错误
//                $rsp = [
//                    'code' => 9999,
//                    'msg'  => '系统开小差中...请稍后再试^_^',
//                ];
//                return response($rsp, 200, ['Content-Type' => 'application/json;charset=utf-8']);

            } else {
                if ((empty($err_code) && $rsp['code'] != 200) || (!empty($err_code) && in_array($rsp['code'], explode('|', $err_code)))) {
                    if ($redis) {
                        //黑名单错误次数+1，从错误次数达到$maxAttempts时开始计时。
                        $val = $redis->incr($black_cache_key);
                        $redis->expire($black_cache_key, $this->get_ttl($period_type));
                        $response->original['remain_count'] = $maxAttempts - $val;
                    }
                }
            }

        } catch (\Exception $e) {
            Log::error($e->getMessage(), $e->getTrace());
        }

        return $response;

    }

    protected function get_ttl($period_type)
    {
        $period_type = strtolower($period_type);
        switch ($period_type) {
            case 'h':
                return Carbon::now()->endOfHour()->timestamp - time();
            case 'd':
                return Carbon::now()->endOfDay()->timestamp - time();
            case 'w':
                return Carbon::now()->endOfWeek()->timestamp - time();
            case 'm':
                return Carbon::now()->endOfMonth()->timestamp - time();
            case 'y':
                return Carbon::now()->endOfYear()->timestamp - time();
            default:
                return Carbon::now()->endOfMonth()->timestamp - time();//配置错误，则按月算
        }
    }

    //获取黑名单key
    protected function get_black_key(Request $request, $black_key)
    {
        $black_key = str_replace(['{ip}', '{path}', '{mobile}'], [$request->ip(), $request->path(), $request->get('mobile', '')], $black_key);
        return config('cache.black_key_pre') . ':' . $black_key;
    }

    //获取超过最大错误次数响应内容
    protected function get_black_resp($request, $maxAttempts, $black_msg)
    {
        //前置中间件拿不到附加在$request中的数据，因为还么处理该请求。可以通过session、redis等来设定响应信息。
        //$msg = str_replace('{decayMinutes}', $decayMinutes, $black_msg);
        return [
            'code' => 9995,
            'msg'  => $black_msg,
        ];
    }

    private function getMillisecond()
    {
        list($t1, $t2) = explode(' ', microtime());
        return (int)(((float)$t1 + (float)$t2) * 1000);
    }
}
