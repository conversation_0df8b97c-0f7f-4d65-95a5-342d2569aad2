<?php

namespace App\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CallbackOrderRequest extends FormRequest
{

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'requestid' => 'required',
            'orderid' => 'required',
            'status' => ['required', Rule::in([1, 2])]
        ];
    }

    public function messages()
    {
        return [
            'in' => ':attribute状态只能是 0,1,2其中的一个',
        ];
    }
}
