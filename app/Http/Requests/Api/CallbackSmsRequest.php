<?php

namespace App\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CallbackSmsRequest extends FormRequest
{

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'gateway_seq' => 'required',
            'submit_result' => 'required',
            'report_result' => 'required',
            'report_at' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'in' => ':attribute状态只能是 0,1,2其中的一个',
        ];
    }
}
