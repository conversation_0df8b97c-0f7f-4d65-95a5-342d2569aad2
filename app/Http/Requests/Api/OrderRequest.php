<?php

namespace App\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;

class OrderRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'serial_no' => 'required|max:32',
            'appid' => 'required',
            'req_time' => 'required',
            'biz_data' => 'required|json',
            'sign' => 'required|string',
        ];
    }
}
