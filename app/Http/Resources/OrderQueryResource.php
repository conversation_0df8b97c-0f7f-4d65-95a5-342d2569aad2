<?php
namespace App\Http\Resources;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderQueryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'status' => $this->status,
            'goods_type' => $this->goods_type,
            'complete_time' => $this->complete_time,
            'activation_code' => $this->activation_code,
            'sequence_no' => $this->sequence_no,
            'endtime' => $this->endtime,
            'logistics_sn' => $this->logistics_sn,
            'logistics_company' => $this->logistics_company,
            'sms_status' => $this->sms_status,
            'sms_gateway_seq' => $this->sms_gateway_seq,
            'sms_complete_time' => $this->sms_complete_time,
        ];
    }
}
