<?php

namespace App\Libraries;

class Aes
{
    static  $aes         = [];
    private $cipher_algo = 'AES-256-CBC';
    private $key;
    private $iv;

    public static function getAes($key, $iv, $cipher_algo = 'AES-256-CBC'): Aes
    {
        $key = sprintf('%s%s%s', $key, $iv, $cipher_algo);
        if (!array_key_exists($key, static::$aes)) {
            static::$aes[$key] = new Aes($key, $iv, $cipher_algo);
        }
        return static::$aes[$key];
    }

    /**
     * @param $key         string 16/32位密钥base64编码的密钥。根据加密算法来选择是16位，还是32位。
     * @param $iv          string 16位密钥base64编码的向量
     * @param $cipher_algo string AES-256-CBC
     */
    public function __construct($key, $iv, $cipher_algo = 'AES-256-CBC')
    {
        $this->key         = base64_decode($key);//32位
        $this->iv          = base64_decode($iv);//16位
        $this->cipher_algo = $cipher_algo;
    }

    /**
     * 设置加解密算法。
     * @param $cipher_algo string 加解密算法。比如：AES-256-CBC/AES-128-CBC
     * @return void
     */
    public function setCipherAlgo($cipher_algo)
    {
        $this->cipher_algo = $cipher_algo;
    }

    /**
     * 设置密钥
     * @param $key string 32位密钥base64编码的密钥。根据加密算法来选择是16位，还是32位。
     */
    public function setKey($key)
    {
        $this->key = base64_decode($key);
    }

    /**
     * 设置向量
     * @param $iv string 16位密钥base64编码的向量
     */
    public function setIv($iv)
    {
        $this->iv = base64_decode($iv);
    }

    /**
     * 加密
     */
    public function encrypt($str)
    {
        $data = openssl_encrypt($str, $this->cipher_algo, $this->key, OPENSSL_RAW_DATA, $this->iv);
        return base64_encode($data);
    }

    /**
     * 解密
     */
    public function decrypt($str)
    {
        return openssl_decrypt(base64_decode($str), $this->cipher_algo, $this->key, OPENSSL_RAW_DATA, $this->iv);
    }

    /**
     * 生成base64随机key
     * @param $len int 长度
     * @return string
     * @throws \Exception
     */
    public static function generateKey($len = 16)
    {
        return base64_encode(random_bytes($len));
    }

//    /**
//     * 十六进制转字符串
//     * @param $hex
//     * @return string
//     */
//    public function hexToStr($hex)
//    {
//        $string = '';
//        for ($i = 0; $i < strlen($hex) - 1; $i += 2) {
//            $string .= chr(hexdec($hex[$i] . $hex[$i + 1]));
//        }
//        return $string;
//    }
//
//    public function hash256($str)
//    {
//        return hash('sha256', $str, false);
//    }
//
//    public function stringToBytes($string)
//    {
//        return unpack('C*', $string);
//    }

    public function javaCode()
    {
        return <<<EOT
package com.example.demo.utils;

//<dependency><groupId>org.apache.directory.studio</groupId><artifactId>org.apache.commons.codec</artifactId><version>1.8</version><type>pom</type></dependency>

import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

public class Aes {
    private static final String aesKey = "Sg1UbrltSMyrxiL3yMyTuiWOZ0EeLM/EVgXZtNssDoA=";
    private static final String initVector = "hIbeamJVhtmkfq81NnDJbA==";


    public static String encrypt(String value) {
        try {
            IvParameterSpec iv = new IvParameterSpec(Base64.decodeBase64(initVector));
            SecretKeySpec skeySpec = new SecretKeySpec(Base64.decodeBase64(aesKey), "AES");

            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5PADDING");
            cipher.init(Cipher.ENCRYPT_MODE, skeySpec, iv);

            byte[] encrypted = cipher.doFinal(value.getBytes());
            return Base64.encodeBase64String(encrypted);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return null;
    }

    public static String decrypt(String encrypted) {
        try {
            IvParameterSpec iv = new IvParameterSpec(Base64.decodeBase64(initVector));
            SecretKeySpec skeySpec = new SecretKeySpec(Base64.decodeBase64(aesKey), "AES");

            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5PADDING");
            cipher.init(Cipher.DECRYPT_MODE, skeySpec, iv);
            byte[] original = cipher.doFinal(Base64.decodeBase64(encrypted));

            return new String(original);
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        return null;
    }

    public static void main(String[] args) {
        try {
            String originalString = "Hello World，你好！Current time is 09:23:48。";
            System.out.println("Original String to encrypt - " + originalString);
            System.out.println("Original String length - " + originalString.length());
            System.out.println("Original String byte length - " + originalString.getBytes("UTF-8").length);
            String encryptedString = encrypt(originalString);
            System.out.println("Encrypted String - " + encryptedString);
            String decryptedString = decrypt(encryptedString);
            System.out.println("After decryption - " + decryptedString);

            String enstr = "MrpvM/IfmDQuiTvsGiUAllJoUBSnHDJI66zbJ7HYUKxTCYeMdW+3XeH9WlBndopEJL1y8Fl8Fc1rwMSCKxx9WfkWbSVUtDzOh4ZHn9KkpHK/alp7XDfYt0qKyV9FlMy+W2mVKPlGEU1/0cEijQ8yymEsDi8kKV/gTq7IAuVOpGc=";
            decryptedString = decrypt(enstr);
            System.out.println("Another decryption - " + decryptedString);
            System.out.println("Another decryption length - " + decryptedString.length());
            System.out.println("Another decryption byte length - " + decryptedString.getBytes("UTF-8").length);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }
}

EOT;
    }
}

