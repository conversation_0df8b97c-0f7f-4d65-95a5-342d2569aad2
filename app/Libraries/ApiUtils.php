<?php

namespace App\Libraries;

class ApiUtils
{
    //获取签名
    public static function getSign($data, $secret_key, $sign_type = 'md5')
    {
        unset($data['sign']);
        $data['secret_key'] = $secret_key;
        ksort($data);
        $arr = [];
        foreach ($data as $key => $value) {
            $arr[] = sprintf('%s=%s', $key, $value);
        }
        $str  = implode('&', $arr);
        $sign = '';
        if (strtolower($sign_type) === 'md5') {
            $sign = md5($str);
        } elseif (strtolower($sign_type) === 'sha256') {
            $sign = hash('sha256', $str);
        }
        return $sign;
    }

    //请求业务参数解密
    public static function decrypt($en_str, $encryptKey, $encryptIv = '', $encrypt_type = 'aes')
    {
        $decrypt_str = $en_str;
        if (strtolower($encrypt_type) == 'aes') {
            $aes         = Aes::getAes($encryptKey, $encryptIv);
            $decrypt_str = $aes->decrypt($en_str);
        }
        return $decrypt_str;
    }

    //响应业务参数加密
    public static function encrypt($str, $encryptKey, $encryptIv = '', $encrypt_type = 'aes')
    {
        $encrypt_str = $str;
        if (strtolower($encrypt_type) == 'aes') {
            $aes         = Aes::getAes($encryptKey, $encryptIv);
            $encrypt_str = $aes->encrypt($str);
        }
        return $encrypt_str;
    }
}

