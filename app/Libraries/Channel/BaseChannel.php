<?php

namespace App\Libraries\Channel;

use App\Exceptions\MyException;
use App\Exceptions\UnImplementedException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

abstract class BaseChannel
{
    //用于供应商接口的订单状态定义的统一封装，供上层服务做逻辑判断使用
    //const ORDER_STATUS_WAIT       = 1; //忽略本次提交，当没提交过，订单恢复成原处理状态。渠道余额不足，ip不在白名单，会再次提交，子订单状态置为：1-待处理（走首次提交任务）
    const ORDER_STATUS_DEALING = 2; //处理中。提交成功、超时、订单号已存在
    const ORDER_STATUS_SUCCESS = 3; //成功。渠道返回订单成功（非接收成功）
    const ORDER_STATUS_FAIL    = 4; //失败。明确失败，不再重提
    //const ORDER_STATUS_FAIL_RETRY = 5; //失败需重提。
    //const ORDER_STATUS_MANUAL     = 6; //需人工处理。渠道返回需人工处理状态时。

    //描述
    public static $order_status_desc = [
        //self::ORDER_STATUS_WAIT       => '待提交',
        self::ORDER_STATUS_DEALING => '处理中',
        self::ORDER_STATUS_SUCCESS => '成功',
        self::ORDER_STATUS_FAIL    => '失败',
        //self::ORDER_STATUS_FAIL_RETRY => '失败待重试',
        //self::ORDER_STATUS_MANUAL     => '待人工处理',
    ];

    //供应商回调处理时用到的错误码
    const CLIENT_IP_ERR      = 401;
    const PARAMS_ERROR       = 402;
    const CHANNEL_ERROR      = 403;
    const APPID_ERROR        = 404;
    const SIGN_ERROR         = 405;
    const DENCRYPT_ERROR     = 406;
    const ORDER_NOT_EXIST    = 407;
    const NO_DEALING_ORDER   = 408;
    const CALLBACK_DEAL_FAIL = 409;

    public static $callback_msg = [
        self::CLIENT_IP_ERR      => 'IP不在白名单',
        self::PARAMS_ERROR       => '参数错误',
        self::CHANNEL_ERROR      => '渠道错误',
        self::APPID_ERROR        => 'APP_ID错误',
        self::SIGN_ERROR         => '签名错误',
        self::DENCRYPT_ERROR     => '解密失败',
        self::ORDER_NOT_EXIST    => '订单不存在',
        self::NO_DEALING_ORDER   => '订单状态不为处理中',
        self::CALLBACK_DEAL_FAIL => 'deal error',
    ];

    //供应商api配置表信息
    protected $api_setting;

    private $log_channels = [];

    public function __construct($api_setting = null)
    {
//        $example_api_setting = [
//              "appid" => "", "secret_key" => "", "encrypt_key" => "", "base_url" => "", "submit_url" => "", "query_url" => "", "product_url" => "", "balance_url" => ""
//        ];
        if ($api_setting) {
            $this->api_setting = $api_setting;
        }
    }

    public function getChannelType()
    {
        return str_replace('App\\Libraries\\Channel\\', '', static::class);
    }

    public function getAppid()
    {
        return $this->api_setting['appid'];
    }

    public function setApiSetting($api_setting)
    {
        $this->api_setting = $api_setting;
    }

    /**
     * 提交订单
     * @param string $req_order_no
     * @param string $channel_product_code
     * @param int $amount
     * @param string $user_mobile
     * @param string $charge_account
     * @param array $ext_order_info 扩展的订单信息，比如身份证号、身份证姓名等。order_from, attach
     * @return array {"return_code":"", "return_msg":"", "order_status":1, "third_order_no":"", "cost_price":"", "total_cost_money":"", "cards":[{"no":"","pwd":"","end":""}],"operator_serial_no":""}
     *        说明：
     *        return_code：       必须。渠道返回的响应码（只做记录）
     *        return_msg:         必须。渠道响应码描述（只做记录）
     *        order_status:       必须。订单状态。1-忽略本次提交, 2-处理中，3-成功，4-失败(不会进行订单重提), 5-失败需重提, 6-需人工处理。渠道返回需人工处理状态时.
     *        req_order_no:        非必须。请求订单号
     *        third_order_no:      非必须。渠道方订单编号
     *        cost_price:          非必须。渠道返回的价格，单位：元
     *        total_cost_money:    非必须。渠道返回的订单扣款金额，单位：元
     *        cards:               非必须。卡密信息（如有），二维数组。卡密属性：no,pwd,end
     *        operator_serial_no:  非必须。品牌方流水号
     *        finish_time:         非必须。订单成功时间，渠道方返回
     * @author: liujq
     * @Time  : 2022/5/31 02:07
     */
    abstract public function submit($req_order_no, $channel_product_code, $amount, $user_mobile, $charge_account, $ext_order_info);

    /**
     * 查询订单状态
     * @param string $req_order_no
     * @param string $channel_product_code
     * @param array $ext_order_info
     * @return array {"return_code":"", "return_msg":"", "order_status":1, "third_order_no":"", "cost_price":"", "total_cost_money":"", "cards":[{"no":"","pwd":"","end":""}],"operator_serial_no":""}
     *         说明：
     *         return_code：       必须。渠道返回的响应码
     *         return_msg:         必须。渠道响应码描述
     *         order_status:       必须。订单状态。1-处理中，2-成功，3-失败
     *         req_order_no:        非必须。平台订单编号
     *         third_order_no:      非必须。渠道方订单编号
     *         cost_price:          非必须。渠道返回的价格，，单位：元
     *         total_cost_money:    非必须。渠道返回的订单扣款金额，，单位：元
     *         cards:               非必须。卡密信息（如有），二维数组
     *         operator_serial_no:  非必须。品牌方流水号
     *         finish_time:         非必须。订单成功时间，渠道方返回
     * @author: liujq
     * @Time  : 2022/5/31 02:07
     */
    abstract public function query($req_order_no, $channel_product_code, $ext_order_info);

    /**
     * 获取商品列表
     * @return array
     * @author: liujq
     * @Time  : 2022/5/31 02:07
     */
    abstract public function getPorducts();

    /**
     * 获取渠道余额
     * @return array {"return_code":"", "return_msg":"", "balance":1}
     *         说明：
     *         return_code： 必须。渠道返回的响应码，超时为28，失败为http_code；
     *         return_msg:   必须。渠道响应码描述
     *         balance:       非必须。请求失败则没有该字段。余额。单位厘
     * @author: liujq
     * @Time  : 2022/5/31 02:07
     */
    abstract public function getBalance();

    /**
     * 统一回调参数处理
     * @param Request $request
     * @return array {"req_order_no":"", "order_status":1,  "third_order_no":"", "cost_price":"", "total_cost_money":"", "cards":[{"no":"","pwd":"","end":""}],"operator_serial_no":""}
     *         说明：
     *         req_order_no:           必须。请求订单号
     *         order_status:           必须。订单状态。1-处理中，2-成功，3-失败
     *         finish_time:             非必须。订单成功时间，渠道方返回
     *         third_order_result:      非必须。渠道返回的订单处理结果
     *         third_order_result_desc: 非必须。渠道返回的订单处理结果描述
     *         third_order_no:          非必须。渠道方订单编号
     *         cost_price:              非必须。渠道返回的价格，单位需转换成厘
     *         total_cost_money:        非必须。渠道返回的订单扣款金额，单位需转换成厘
     *         cards:                   非必须。卡密信息（如有），二维数组，例如： [{"no":"","pwd":"","end":""}]
     *         operator_serial_no:      非必须。品牌方流水号
     *         charge_account:          非必须。充值账号
     * @author: liujq
     * @Time  : 2022/5/31 02:08
     */
    abstract public function callbackParamsDeal(Request $request);

    /**
     * 响应回调结果给供应商
     * @param Request $request
     * @param array $callbackParams $this->callbackParamsDeal()返回的结果
     * @param \Exception $exception
     * @return mixed
     * @author: liujq
     * @Time  : 2022/6/29 20:26
     * \Illuminate\Database\Eloquent\Collection|\App\Models\OrderVirtualItem
     */
    abstract public function callbackResponse(Request $request, array $callbackParams, \Exception $exception);

    public function verify($supplier_product_code, $charge_account, $verify_code = null)
    {
//        return ['code' => 200, 'return_code' => 200, 'return_msg' => ''];
        throw new UnImplementedException('not support verify method');
    }

    public function userInfoQueryForTaobao($product_code, $mobile, $verify_code)
    {
        throw new UnImplementedException('not support userInfoQueryForTaobao method');
    }

    public function sendVerifyCodeForTaobao($product_code, $mobile)
    {
        throw new UnImplementedException('not support sendVerifyCodeForTaobao method');
    }

    //返回xml参考如下代码：
    /*$xml = new SimpleXMLElement('<?xml version="1.0" encoding="UTF-8" ?><websites></websites>');*/
    //$website = $xml->addChild('result','success');
    //$website->addChild('desc', '成功');
    //$content = $xml->asXML();
    //return response($content)->header('Content-Type', 'text/xml');


    protected function log_submit(string $level, $url, $req_data, $result, $timestamp_start = 0, $timestamp_end = 0)
    {
        $this->log('submit_order', $level, 'channel_submit', static::class . '_' . $this->api_setting['appid'],
            $url, $req_data, $result, $timestamp_start, $timestamp_end);
    }

    protected function log_query($level, $url, $req_data, $result, $timestamp_start = 0, $timestamp_end = 0)
    {
        $this->log('query_order', $level, 'channel_query', static::class . '_' . $this->api_setting['appid'],
            $url, $req_data, $result, $timestamp_start, $timestamp_end);
    }

    protected function log_balance(string $level, $url, $req_data, $result, $timestamp_start = 0, $timestamp_end = 0)
    {
        $this->log('channel_balance', $level, 'channel_balance', static::class . '_' . $this->api_setting['appid'],
            $url, $req_data, $result, $timestamp_start, $timestamp_end);
    }

    protected function log_callback(string $level, $url, $req_data, $result, $timestamp_start = 0, $timestamp_end = 0)
    {
        $this->log('order_callback', $level, 'channel_callback', static::class . '_' . $this->api_setting['appid'],
            $url, $req_data, $result, $timestamp_start, $timestamp_end);
    }

    protected function log($channel, $level, $opt, $msg, $url, $req_data, $result, $timestamp_start = 0, $timestamp_end = 0)
    {
        if ($timestamp_end === 0) {
            $timestamp_end = time();
        }
        $log_data = getStdLogMessage($opt, $msg, ['url' => $url, 'req_data' => $req_data, 'result' => $result], $timestamp_start, $timestamp_end);
        $logger   = $this->getLogger($channel);
        $logger->$level($log_data);
    }

    protected function getLogger($channel)
    {
        if (!array_key_exists($channel, $this->log_channels)) {
            $this->log_channels[$channel] = Log::channel($channel);
        }
        return $this->log_channels[$channel];
    }
}
