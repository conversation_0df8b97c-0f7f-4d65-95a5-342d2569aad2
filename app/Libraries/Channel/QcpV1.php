<?php

namespace App\Libraries\Channel;

use App\Exceptions\MyException;
use App\Exceptions\UnImplementedException;
use Carbon\Carbon;
use Illuminate\Http\Request;

//use SimpleXMLElement;

/**
 * Qcp渠道例子
 * @author: liujq
 * @Time  : 2022/5/31 02:02
 */
class QcpV1 extends BaseChannel
{
    //api_setting需要配置的参数: appid, submit_url, secret_key, query_url, balance_url
    public function submit($req_order_no, $channel_product_code, $amount, $user_mobile = '', $charge_account = '', $ext_order_info = [])
    {
        $timestamp1 = time();
        $req_data   = [];
        $result     = [];
        try {
            $url          = $this->api_setting['submit_url'];
            $qcp_code_arr = explode('_', $channel_product_code);
            $req_data     = array(
                'username'   => $this->api_setting['appid'],
                'pcode'      => $channel_product_code,
                'requestid'  => $req_order_no,
                'timestamp'  => time(),
                'msgencrypt' => 1,
                'telphone'   => $user_mobile,
                'secretkey'  => $this->api_setting["secret_key"],
            );

            if ($qcp_code_arr[0] == 'qcoins') {
                $req_data['qq'] = $charge_account;
            } elseif ($qcp_code_arr[0] == 'recharge') {
                $req_data['recharge_no'] = $charge_account;
            } elseif (in_array($qcp_code_arr[0], array('tel', 'flow'))) {
                $req_data['telphone'] = $charge_account;
            } else {
                //卡密
                if (!empty($account) && check_mobile($account)) {
                    //卡券类里的同步直充账号处理，比如爱奇艺直充
                    $req_data['telphone'] = $account;
                }
            }

            if (empty($req_data['telphone']) && !in_array($qcp_code_arr[0], ['tel', 'flow'])) {
                if (isset($req_data['recharge_no']) && checkPhone($req_data['recharge_no'])) {
                    $req_data['telphone'] = $req_data['recharge_no'];
                } else {
                    $req_data['telphone'] = '***********';
                }
            }

            //把请求数组按键名排序
            ksort($req_data);
            $req_data['sign'] = md5(implode("", $req_data));
            unset($req_data['secretkey']);

            $http_response = http_request_send($url, $req_data);

            if ($http_response['code'] == 200) {
                //请求正常响应
                $supplier_result       = json_decode($http_response['data'], true);
                $result['return_code'] = $supplier_result['retcode'];
                $result['return_msg']  = $supplier_result['retmsg'];
                //处理并转换结果
                if ($supplier_result['retcode'] == '0000') {
                    $result['third_order_no'] = $supplier_result['retinfo']['orderid'];
                    if (in_array($qcp_code_arr[0], ['tel', 'flow', 'recharge', 'qcoins'])) {
                        $result['order_status'] = static::ORDER_STATUS_DEALING;
                    } else {
                        $result['order_status'] = static::ORDER_STATUS_SUCCESS;
                    }

                    //卡密。包括no、pwd、end等字段
                    if (!empty($supplier_result['retinfo']['activation_code'])) {
                        $result['cards'] = [
                            [
                                'no'  => $supplier_result['retinfo']['sequence_no'],
                                'pwd' => $supplier_result['retinfo']['activation_code'],
                                'end' => empty($supplier_result['retinfo']['endtime'] || $supplier_result['retinfo']['endtime'] == '0000-00-00') ? '1900-01-01' : $supplier_result['retinfo']['endtime'],
                            ],
                        ];
                    }
                } else {
                    $result['order_status'] = static::ORDER_STATUS_FAIL;
                }

            } elseif ($http_response['code'] == 28) {
                $result['return_code']  = 'http:28';
                $result['return_msg']   = $http_response['error_msg'];
                $result['order_status'] = static::ORDER_STATUS_DEALING;
            } else {
                $result['return_code']  = "http:" . $http_response['code'];
                $result['return_msg']   = $http_response['error_msg'] ?? '';
                $result['order_status'] = static::ORDER_STATUS_FAIL;
            }

//            $result['supplier_response'] = $http_response['data'];//暂时注释掉，需要的话再调整。

            $this->log_submit('info', $url, $req_data, $result, $timestamp1);

        } catch (MyException $exc) {
            $result['return_code']  = 'error:600';
            $result['return_msg']   = $exc->getMessage();
            $result['order_status'] = static::ORDER_STATUS_FAIL;
            $this->log_query('error', $url, $req_data, $result, $timestamp1);
        }

        return $result;
    }

    public function query($req_order_no, $channel_product_code, $ext_order_info = [])
    {
        $timestamp1 = time();
        $req_data   = [];
        $result     = [];
        $url        = $this->api_setting['query_url'];
        try {
            $qcp_code_arr = explode('_', $channel_product_code);
            $req_data     = array(
                'username'   => $this->api_setting['appid'],
                'pcode'      => $qcp_code_arr[0],
                'request_id' => $req_order_no,
                'timestamp'  => time(),
                'msgencrypt' => 1,
                'secretkey'  => $this->api_setting["secret_key"],
            );

            //把请求数组按键名排序
            ksort($req_data);
            $req_data['appsign'] = md5(implode("", $req_data));
            unset($req_data['secretkey']);

            $http_response = http_request_send($url, $req_data);
            //{"retcode":"0000","retinfo":{
            //   "request_id":"T15090916320701765","order_id":"10g15090916320721728","show_price":"10000","sell_price":"11000",
            //   "ctime":"2015-09-09 16:32:07","pay_time":"0000-00-00 00:00:00","mobileno":"13436554542","operator":"1",
            //   "belong_to":"北京移动","reason":"","is_pay":0,"is_success":0,"is_refund":0
            //  }
            //,"retmsg":"请求成功"}

            if ($http_response['code'] == 200) {

                //请求正常响应
                $supplier_result       = json_decode($http_response['data'], true);
                $result['return_code'] = $supplier_result['retcode'];
                $result['return_msg']  = $supplier_result['retmsg'];
                //处理并转换结果
                if ($supplier_result['retcode'] == '0000') {
                    $result['third_order_no'] = $supplier_result['retinfo']['order_id'];
                    //是否成功。0处理中 1成功 2失败
                    if ($supplier_result['retinfo']['is_success'] == 1) {
                        $result['order_status'] = static::ORDER_STATUS_SUCCESS;
                    } elseif ($supplier_result['retinfo']['is_success'] == 2) {
                        $result['order_status'] = static::ORDER_STATUS_FAIL;
                    } else {
                        $result['order_status'] = static::ORDER_STATUS_DEALING;
                    }

                    if (isset($supplier_result['retinfo']['sell_price'])) {
                        $result['cost_price'] = rmb_li_to_yuan($supplier_result['retinfo']['sell_price']);
                    }
                } elseif ($supplier_result['retcode'] == '9998') {
                    //TODO: 暂时先置为处理中，待多渠道版本稳定后可改为失败。
                    //$result['order_status'] = static::ORDER_STATUS_FAIL;
                    $result['order_status'] = static::ORDER_STATUS_DEALING;
                } else {
                    $result['order_status'] = static::ORDER_STATUS_DEALING;
                }

            } else {
                $result['return_code']  = 'http:' . $http_response['code'];
                $result['return_msg']   = $http_response['error_msg'];
                $result['order_status'] = static::ORDER_STATUS_DEALING;
            }

            $this->log_query('info', $url, $req_data, $result, $timestamp1);

        } catch (\Exception $exc) {
            $result['return_code']  = 'error:600';
            $result['return_msg']   = $exc->getMessage() . ':' . $exc->getLine();
            $result['order_status'] = static::ORDER_STATUS_DEALING;
            $this->log_query('error', $url, $req_data, $result, $timestamp1);
        }

        return $result;
    }

    public function getPorducts()
    {
        throw new UnImplementedException();
    }

    public function getBalance()
    {
        $timestamp1 = time();
        $req_data   = [];
        $result     = [];
        $url        = $this->api_setting['balance_url'];
        try {
            $req_data = array(
                'username'   => $this->api_setting['appid'],
                'timestamp'  => time(),
                'msgencrypt' => 1,
                'secretkey'  => $this->api_setting["secret_key"],
            );

            //把请求数组按键名排序
            ksort($req_data);
            $req_data['appsign'] = md5(implode("", $req_data));
            unset($req_data['secretkey']);

            $http_response = http_request_send($url, $req_data);

            if ($http_response['code'] == 200) {
                $rsp_data = json_decode($http_response['data'], true);

                $result['return_code'] = $rsp_data['retcode'];
                $result['return_msg']  = $rsp_data['retmsg'];

                if ($rsp_data['retcode'] == '0000') {
                    $result['balance'] = rmb_li_to_yuan($rsp_data['retinfo']['account']);
                }
            } else {
                $result['return_code'] = 'http:' . $http_response['code'];
                $result['return_msg']  = $http_response['error_msg'];
            }

            $this->log_balance('info', $url, $req_data, $result, $timestamp1);

        } catch (MyException $exc) {
            $result['return_code'] = 'error:600';
            $result['return_msg']  = $exc->getMessage() . ':' . $exc->getLine();
            $this->log_balance('error', $url, $req_data, $result, $timestamp1);
        }

        return $result;
    }

    public function callbackParamsDeal(Request $request)
    {
        $timestamp1 = time();
        $result     = [];
        $params     = $request->all();

        $this->log_callback('info', $request->url(), ['req_data' => $params], [], $timestamp1);

        if (empty($params['requestid'])
            || empty($params['orderid'])
            || !in_array($params['state'], array(1, 2))) {
            throw new MyException(static::PARAMS_ERROR);
        }

        $result['third_order_result'] = $params['state'];
        switch ($params['state']) {
            //1成功
            case 1:
                $result['req_order_no']   = $params['requestid'];
                $result['order_status']   = static::ORDER_STATUS_SUCCESS;
                $result['third_order_no'] = $params['orderid'];
                $result['finish_time']    = Carbon::now()->format("Y-m-d H:i:s");
                break;
            case 2://失败
                $result['req_order_no']   = $params['requestid'];
                $result['order_status']   = static::ORDER_STATUS_FAIL;
                $result['third_order_no'] = $params['orderid'];
                break;
            default:
                $result['order_status'] = static::ORDER_STATUS_DEALING;
                break;
        }

        return $result;
    }

    public function callbackResponse(Request $request, array $callbackParams = null, \Exception $exception = null)
    {
        $this->log_callback('info', $request->url(), ['req_data' => $request->all(), 'callbackParams' => $callbackParams], 0);

        if (!$exception) {
            return 'success';
        } else {
            if ($exception->getCode() === 0) {
                $code = intval($exception->getMessage());
                if (array_key_exists($code, static::$callback_msg)) {
                    $msg = static::$callback_msg[$code];
                } else {
                    $msg = 'error:' . $code;
                }
            } else {
                $code = $exception->getCode();
                $msg  = $exception->getMessage();
            }
            if ($code == static::NO_DEALING_ORDER) {
                return 'success';
            } else {
                return $msg;
            }
        }
    }
}
