<?php

namespace App\Libraries\Channel;

use App\Exceptions\MyException;
use App\Exceptions\UnImplementedException;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

/**
 * Qcpv2渠道例子
 */
class QcpV2 extends BaseChannel
{
    //api_setting需要配置的参数: appid, base_url, encrypt_type, sign_type, secret_key, encrypt_key, encrypt_iv, callback_url

    protected $encrypt_type = 'aes';
    protected $sign_type    = 'sha256';
    protected $version      = '1.0';
    protected $format       = 'json';
    protected $charset      = 'utf-8';
    protected $aes;

    public function __construct($api_setting)
    {
        parent::__construct($api_setting);

        if (isset($this->api_setting['encrypt_type'])) {
            $this->encrypt_type = $this->api_setting['encrypt_type'];
        }
        if (isset($this->api_setting['sign_type'])) {
            $this->sign_type = $this->api_setting['sign_type'];
        }
        $this->aes = \App\Libraries\Aes::getAes($this->api_setting['encrypt_key'], $this->api_setting['encrypt_iv']);
    }

    public function submit($req_order_no, $channel_product_code, $amount, $user_mobile = '', $charge_account = '', $ext_order_info = [])
    {
        $timestamp1 = time();
        $req_data   = [];
        $result     = [];
        $url        = $this->api_setting['base_url'] ?? '';
        try {
            if ($amount > 1) {
                throw new MyException('数量不能大于1');
            }

            $biz_data = [
                'product_code'   => $channel_product_code,
                'out_trade_no'   => $req_order_no,
                'amount'         => $amount,
                'user_mobile'    => $user_mobile,
                'charge_account' => $charge_account,
                'order_from'     => $ext_order_info['order_from'] ?? '',
                'attach'         => $ext_order_info['attach'] ?? '',
                'notify_url'     => $this->api_setting['callback_url'],
            ];

            $req_data         = [
                "appid"        => $this->api_setting['appid'],
                "method"       => "order.virtual.create",
                "timestamp"    => Carbon::now()->toDateTimeString(),
                "version"      => $this->version,
                "format"       => $this->format,
                "charset"      => $this->charset,
                "encrypt_type" => $this->encrypt_type,
                "sign_type"    => $this->sign_type,
                "biz_content"  => $this->aes->encrypt(json_encode($biz_data)),
            ];
            $req_data['sign'] = \App\Libraries\ApiUtils::getSign($req_data, $this->api_setting['secret_key'], $this->sign_type);

            $http_response = http_request_send($url, $req_data);

            if ($http_response['code'] == 200) {
                //请求正常响应
                $supplier_result = json_decode($http_response['data'], true);
                //响应验签，可以不进行验签
                if (!empty($supplier_result['sign'])) {
                    $my_sign = \App\Libraries\ApiUtils::getSign($supplier_result, $this->api_setting['secret_key'], $this->sign_type);
                    if ($my_sign != $supplier_result['sign']) {
                        //这里不抛出错误，只记录下警告日志以便查询
                        $this->log_submit('warning', $url, $req_data, array_merge($result, [
                            'rsp_sign'  => $supplier_result['sign'],
                            'my_sign'   => $my_sign,
                            'error_msg' => '响应结果验签不正确！',
                        ]), $timestamp1);
                    }
                }

                $result['return_code'] = $supplier_result['code'];
                $result['return_msg']  = $supplier_result['message'];

                if ($supplier_result['code'] == 200) {
                    //解密
                    if (!empty($this->encrypt_type)) {
                        $rsp_biz_content = $this->aes->decrypt($supplier_result['rsp_biz_content']);
                        if (empty($rsp_biz_content)) {
                            //解密失败当处理中
                            $result['return_msg']   = '响应结果解密失败！';
                            $result['order_status'] = static::ORDER_STATUS_DEALING;
                            throw new MyException('响应结果解密失败！');
                        }
                    }
                    $supplier_result['rsp_biz_content'] = json_decode($rsp_biz_content, true);

                    $result['third_order_no'] = $supplier_result['rsp_biz_content']['order_id'];
                    if ($supplier_result['rsp_biz_content']['order_state'] == 1) {
                        $result['order_status'] = static::ORDER_STATUS_SUCCESS;
                        $result['finish_time']  = $supplier_result['rsp_biz_content']['finish_time'];
                        if (!empty($supplier_result['rsp_biz_content']['cards'])) {
                            foreach ($supplier_result['rsp_biz_content']['cards'] as $card) {
                                $result['cards'][] = [
                                    'no'  => $card['cardno'],
                                    'pwd' => $card['cardpwd'],
                                    'end' => $card['endtime'] ?? '',
                                ];
                            }
                        }
                    } elseif ($supplier_result['rsp_biz_content']['order_state'] == 2) {
                        $result['order_status'] = static::ORDER_STATUS_FAIL;
                    } else {
                        $result['order_status'] = static::ORDER_STATUS_DEALING;
                    }

                } elseif ($supplier_result['code'] == 1302) {
                    //1302	商户订单号重复，接口支持幂等，不会返回该错误码
                    $result['order_status'] = static::ORDER_STATUS_DEALING;
                } else {
                    $result['order_status'] = static::ORDER_STATUS_FAIL;
                }
            } elseif ($http_response['code'] == 28) {
                $result['return_code']  = 'http:28';
                $result['return_msg']   = $http_response['error_msg'];
                $result['order_status'] = static::ORDER_STATUS_DEALING;
            } else {
                $result['return_code']  = "http:" . $http_response['code'];
                $result['return_msg']   = $http_response['error_msg'] ?? '';
                $result['order_status'] = static::ORDER_STATUS_FAIL;
            }

            $this->log_submit('info', $url, $req_data, $result, $timestamp1);

        } catch (MyException $exc) {
            //抛出MyException错误时，可提前设置$result['return_code']，$result['return_msg'] ，$result['order_status']
            //否则当处理中
            if (!isset($result['order_status'])) {
                $result['order_status'] = static::ORDER_STATUS_DEALING;
            }
            if (!isset($result['return_code'])) {
                $result['return_code'] = 'waring:600';
            }
            if (!isset($result['return_msg'])) {
                $result['return_msg'] = $exc->getMessage();
            }
            $this->log_submit('error', $url, $req_data, $result, $timestamp1);
        } catch (\Exception $exc) {
            $result['return_code']  = 'error:600';
            $result['return_msg']   = $exc->getMessage();
            $result['order_status'] = static::ORDER_STATUS_FAIL;
            $this->log_submit('error', $url, $req_data, $result, $timestamp1);
        }

        return $result;
    }

    public function query($req_order_no, $channel_product_code, $ext_order_info = [])
    {
        $timestamp1 = time();
        $req_data   = [];
        $result     = [];
        $url        = $this->api_setting['base_url'];
        try {
            $biz_data         = [
                'out_trade_no' => $req_order_no,
            ];
            $req_data         = [
                "appid"        => $this->api_setting['appid'],
                "method"       => "order.virtual.query",
                "timestamp"    => Carbon::now()->toDateTimeString(),
                "version"      => $this->version,
                "format"       => $this->format,
                "charset"      => $this->charset,
                "encrypt_type" => $this->encrypt_type,
                "sign_type"    => $this->sign_type,
                "biz_content"  => $this->aes->encrypt(json_encode($biz_data)),
            ];
            $req_data['sign'] = \App\Libraries\ApiUtils::getSign($req_data, $this->api_setting['secret_key'], $this->sign_type);

            $http_response = http_request_send($url, $req_data);

            if ($http_response['code'] == 200) {
                //请求正常响应
                $supplier_result = json_decode($http_response['data'], true);

                $result['return_code'] = $supplier_result['code'];
                $result['return_msg']  = $supplier_result['message'];

                if (!empty($supplier_result['sign'])) {
                    //验签
                    $my_sign = \App\Libraries\ApiUtils::getSign($supplier_result, $this->api_setting['secret_key'], $this->sign_type);
                    if ($my_sign != $supplier_result['sign']) {
                        $this->log_query('warning', $url, $req_data, array_merge($result, [
                            'rsp_sign'  => $supplier_result['sign'],
                            'my_sign'   => $my_sign,
                            'error_msg' => '响应结果验签不正确！',
                        ]), $timestamp1);
                    }
                }

                if ($supplier_result['code'] == 200) {
                    //解密
                    if (!empty($this->encrypt_type)) {
                        $rsp_biz_content = $this->aes->decrypt($supplier_result['rsp_biz_content']);
                        if (empty($rsp_biz_content)) {
                            $result['return_msg']   = '响应结果解密失败！';
                            $result['order_status'] = static::ORDER_STATUS_DEALING;//响应结果解密失败 置为处理中
                            throw new MyException('响应结果解密失败！');
                        }
                    }
                    $supplier_result['rsp_biz_content'] = json_decode($rsp_biz_content, true);

                    $result['third_order_no'] = $supplier_result['rsp_biz_content']['order_id'];
                    if ($supplier_result['rsp_biz_content']['order_state'] == 1) {
                        $result['order_status'] = static::ORDER_STATUS_SUCCESS;
                        $result['finish_time']  = $supplier_result['rsp_biz_content']['finish_time'];
                        if (!empty($supplier_result['rsp_biz_content']['cards'])) {
                            foreach ($supplier_result['rsp_biz_content']['cards'] as $card) {
                                $result['cards'][] = [
                                    'no'  => $card['cardno'],
                                    'pwd' => $card['cardpwd'],
                                    'end' => $card['endtime'] ?? '',
                                ];
                            }
                        }
                    } elseif ($supplier_result['rsp_biz_content']['order_state'] == 2) {
                        $result['order_status'] = static::ORDER_STATUS_FAIL;
                    } else {
                        $result['order_status'] = static::ORDER_STATUS_DEALING;
                    }

                } elseif ($supplier_result['code'] == 1401) {
                    //1401-商户订单号不存在。TODO: 暂时先置为处理中，待多渠道版本稳定后可改为失败。
                    //$result['order_status'] = static::ORDER_STATUS_FAIL;
                    $result['order_status'] = static::ORDER_STATUS_DEALING;

                } else {
                    $result['order_status'] = static::ORDER_STATUS_DEALING;
                }
            } else {
                $result['return_code']  = "http:" . $http_response['code'];
                $result['return_msg']   = $http_response['error_msg'] ?? '';
                $result['order_status'] = static::ORDER_STATUS_DEALING;//返回http错误时，置为处理中
            }

            $this->log_query('info', $url, [
                'req_data'    => $req_data,
                'biz_content' => $biz_data
            ], [
                'http_response'   => $http_response,
                'supplier_result' => $supplier_result ?? [],
                'result'          => $result,
            ], $timestamp1);

        } catch (MyException $exc) {
            //抛出MyException错误时，可提前设置$result['return_code']，$result['return_msg'] ，$result['order_status']
            //否则当处理中
            if (!isset($result['order_status'])) {
                $result['order_status'] = static::ORDER_STATUS_DEALING;
            }
            if (!isset($result['return_code'])) {
                $result['return_code'] = 'waring:600';
            }
            if (!isset($result['return_msg'])) {
                $result['return_msg'] = $exc->getMessage();
            }
            $this->log_query('error', $url, $req_data, $result, $timestamp1);
        } catch (\Exception $exc) {
            $result['return_code']  = 'error:600';
            $result['return_msg']   = $exc->getMessage() . ':' . $exc->getLine();
            $result['order_status'] = static::ORDER_STATUS_DEALING;
            $this->log_query('error', $url, $req_data, $result, $timestamp1);
        }

        return $result;
    }

    public function getPorducts()
    {
        throw new UnImplementedException();
    }

    public function getBalance()
    {
        $timestamp1 = time();
        $req_data   = [];
        $result     = [];
        $url        = $this->api_setting['base_url'];
        try {
            $req_data         = [
                "appid"        => $this->api_setting['appid'],
                "method"       => "cust.balance.query",
                "timestamp"    => Carbon::now()->toDateTimeString(),
                "version"      => $this->version,
                "format"       => $this->format,
                "charset"      => $this->charset,
                "encrypt_type" => $this->encrypt_type,
                "sign_type"    => $this->sign_type,
            ];
            $req_data['sign'] = \App\Libraries\ApiUtils::getSign($req_data, $this->api_setting['secret_key'], $this->sign_type);

            $http_response = http_request_send($url, $req_data);

            if ($http_response['code'] == 200) {
                //请求正常响应
                $supplier_result = json_decode($http_response['data'], true);
                //验签
                if (!empty($supplier_result['sign'])) {
                    $my_sign = \App\Libraries\ApiUtils::getSign($supplier_result, $this->api_setting['secret_key'], $this->sign_type);
                    if ($my_sign != $supplier_result['sign']) {
                        $this->log_balance('warning', $url, $req_data, array_merge($result, [
                            'rsp_sign'  => $supplier_result['sign'],
                            'my_sign'   => $my_sign,
                            'error_msg' => '响应结果验签不正确！',
                        ]), $timestamp1);
                    }
                }

                $result['return_code'] = $supplier_result['code'];
                $result['return_msg']  = $supplier_result['message'];

                if ($supplier_result['code'] == 200) {
                    //解密
                    if (!empty($supplier_result['rsp_biz_content'])) {
                        if (!empty($this->encrypt_type)) {
                            $rsp_biz_content = $this->aes->decrypt($supplier_result['rsp_biz_content']);
                            if (empty($rsp_biz_content)) {
                                throw new MyException('响应结果解密失败！');
                            }
                        }
                        $supplier_result['rsp_biz_content'] = json_decode($rsp_biz_content, true);
                    }

                    $result['balance'] = $supplier_result['rsp_biz_content']['balance'];
                }
            }

            $this->log_balance('info', $url, $req_data, $result, $timestamp1);

        } catch (MyException $exc) {
            $result['return_code'] = 'error:600';
            $result['return_msg']  = $exc->getMessage() . ':' . $exc->getLine();
            $this->log_balance('error', $url, $req_data, $result, $timestamp1);
        }

        return $result;
    }

    public function callbackParamsDeal(Request $request)
    {
        $timestamp1 = time();
        $result     = [];
        $params     = $request->all();

        $this->log_callback('info', $request->url(), ['req_data' => $params], [], $timestamp1);

        $validator = Validator::make($params, [
            'appid'       => ['required', Rule::in([$this->api_setting['appid']])],
            'method'      => ['required', Rule::in(['order.virtual.callback'])],
            'timestamp'   => 'required',
            'version'     => 'required',
            'format'      => 'required',
            'charset'     => 'required',
            'sign_type'   => 'required',
            'biz_content' => 'required',
            'sign'        => 'required',
        ], [
            'appid.required'       => static::APPID_ERROR,
            'appid.in'             => static::APPID_ERROR,
            'method.required'      => static::PARAMS_ERROR,
            'method.in'            => static::PARAMS_ERROR,
            'timestamp.required'   => static::PARAMS_ERROR,
            'version.required'     => static::PARAMS_ERROR,
            'format.required'      => static::PARAMS_ERROR,
            'charset.required'     => static::PARAMS_ERROR,
            'sign_type.required'   => static::PARAMS_ERROR,
            'biz_content.required' => static::PARAMS_ERROR,
            'sign.required'        => static::PARAMS_ERROR,
        ]);

        if ($validator->fails()) {
            $errors   = $validator->errors();
            $err_code = $errors->first();
            throw new MyException($err_code);
        }

        $my_sign = \App\Libraries\ApiUtils::getSign($params, $this->api_setting['secret_key'], $params['sign_type']);
        if ($my_sign != $params['sign']) {
            throw new MyException(static::SIGN_ERROR);//验签失败
        }
        //解密业务参数
        if (!empty($this->encrypt_type)) {
            $biz_content = $this->aes->decrypt($params['biz_content']);
            if (empty($biz_content)) {
                //解密失败当处理中
                throw new MyException(static::DENCRYPT_ERROR);
            }
        } else {
            $biz_content = $params['biz_content'];
        }
        $biz_content = json_decode($biz_content, true);

        if ($biz_content['order_state'] == 1) {
            $result['order_status'] = static::ORDER_STATUS_SUCCESS;
            if (!empty($biz_content['cards'])) {
                foreach ($biz_content['cards'] as $card) {
                    $result['cards'][] = [
                        'no'  => $card['cardno'],
                        'pwd' => $card['cardpwd'],
                        'end' => $card['endtime'] ?? '',
                    ];
                }
            }
        } elseif ($biz_content['order_state'] == 2) {
            $result['order_status'] = static::ORDER_STATUS_FAIL;
        } else {
            throw new MyException(static::PARAMS_ERROR);
        }

        $result['third_order_result']      = $biz_content['order_state'];
        $result['third_order_result_desc'] = 'callback-' . $biz_content['order_state'];
        $result['req_order_no']            = $biz_content['out_trade_no'];
        $result['third_order_no']          = $biz_content['order_id'];
        if (!empty($biz_content['finish_time'])) {
            $result['finish_time'] = $biz_content['finish_time'];
        }
        if (!empty($biz_content['operator_serial_number'])) {
            $result['operator_serial_no'] = $biz_content['operator_serial_number'];
        }

        return $result;
    }

    public function callbackResponse(Request $request, array $callbackParams = null, \Exception $exception = null)
    {
        $code = '';
        $msg  = '';
        if (!$exception) {
            $code = 200;
            $msg  = 'success';
        } else {
            if ($exception->getCode() === 0) {
                $code = intval($exception->getMessage());
                if (array_key_exists($code, BaseChannel::$callback_msg)) {
                    $msg = BaseChannel::$callback_msg[$code];
                } else {
                    $msg = '';
                }
            } else {
                $code = $exception->getCode();
                $msg  = $exception->getMessage();
            }
            if ($code == BaseChannel::NO_DEALING_ORDER) {
                $code = 200;
                $msg  = 'success';
            }
        }

        $rsp_arr         = [
            'code'      => $code,
            'message'   => $msg,
            'timestamp' => Carbon::now()->toDateTimeString(),
        ];
        $rsp_arr['sign'] = \App\Libraries\ApiUtils::getSign($rsp_arr, $this->api_setting['secret_key'], $request['sign_type']);

        $this->log_callback('info', $request->url(), ['req_data' => $request->all(), 'callbackParams' => $callbackParams], $rsp_arr);

        return $rsp_arr;
    }

    //@return array ["code"=>200, "return_code"=>"", "return_msg"=>""]
    public function verify($supplier_product_code, $charge_account, $verify_code = null)
    {
        $timestamp1 = time();
        $req_data   = [];
        $result     = [];
        $url        = $this->api_setting['base_url'];
        try {
            $biz_data = [
                'product_code'   => $supplier_product_code,
                'charge_account' => $charge_account,
            ];
            if ($verify_code) {
                $biz_data['verify_code'] = $verify_code;
            }
            $req_data         = [
                "appid"        => $this->api_setting['appid'],
                "method"       => "order.virtual.pre_verify",
                "timestamp"    => Carbon::now()->toDateTimeString(),
                "version"      => $this->version,
                "format"       => $this->format,
                "charset"      => $this->charset,
                "encrypt_type" => $this->encrypt_type,
                "sign_type"    => $this->sign_type,
                "biz_content"  => $this->aes->encrypt(json_encode($biz_data)),
            ];
            $req_data['sign'] = \App\Libraries\ApiUtils::getSign($req_data, $this->api_setting['secret_key'], $this->sign_type);

            $http_response = http_request_send($url, $req_data);

            if ($http_response['code'] == 200) {
                //请求正常响应
                $supplier_result = json_decode($http_response['data'], true);
                //验签
                if (!empty($supplier_result['sign'])) {
                    $my_sign = \App\Libraries\ApiUtils::getSign($supplier_result, $this->api_setting['secret_key'], $this->sign_type);
                    if ($my_sign != $supplier_result['sign']) {
                        $this->log_query('warning', $url, $req_data, array_merge($result, [
                            'rsp_sign'  => $supplier_result['sign'],
                            'my_sign'   => $my_sign,
                            'error_msg' => '响应结果验签不正确！',
                        ]), $timestamp1);
                    }
                }
                $result['code']        = $supplier_result['code'];
                $result['return_code'] = $supplier_result['code'];
                $result['return_msg']  = $supplier_result['message'];

                if ($supplier_result['code'] == 200) {
                    //解密
                    if (!empty($supplier_result['rsp_biz_content'])) {
                        if (!empty($this->encrypt_type)) {
                            $rsp_biz_content = $this->aes->decrypt($supplier_result['rsp_biz_content']);
                            if (empty($rsp_biz_content)) {
                                throw new MyException('响应结果解密失败！');
                            }
                        }
                        $supplier_result['rsp_biz_content'] = json_decode($rsp_biz_content, true);
                    }

                    if ($supplier_result['rsp_biz_content']['result'] == 1) {
                        $result['code'] = 200;
                    } else {
                        $result['code'] = 300;
                    }
                    $result['return_code'] = $supplier_result['rsp_biz_content']['result'];
                    $result['return_msg']  = $supplier_result['rsp_biz_content']['result_desc'];
                }
            } elseif ($http_response['code'] == 28) {
                $result['code']        = $http_response['code'];
                $result['return_code'] = 'http:' . $http_response['code'];
                $result['return_msg']  = '请求超时，请稍后重试！';
            } else {
                $result['code']        = $http_response['code'];
                $result['return_code'] = 'http:' . $http_response['code'];
                $result['return_msg']  = $http_response['error_msg'] ?? 'failed';
            }
            $this->log_query('info', $url, $req_data, $result, $timestamp1);
        } catch (\Exception $exc) {
            $result['code']        = 500;
            $result['return_code'] = 'error:500';
            $result['return_msg']  = $exc->getMessage() . ':' . $exc->getLine();
            $this->log_query('error', $url, $req_data, $result, $timestamp1);
        }

        return $result;
    }

    /**
     * @param string $mobile 手机号
     * @param string $verify_code 验证码。可选。为空则查询该手机号绑定淘宝账号数量
     * @return array ["code"=>200, "return_code"=>"", "return_msg"=>"", "user_num"=>2,  "user_infos" => [
     * 0 => array:4 [
     * "active" => true
     * "buyer_display_nick_mask" => "苏*3"
     * "buyer_nick_mask" => "苏*3"
     * "obs_buyer_id" => "RAzN8HWUvKgcM2fUjMowQvgLHiLH5kv6F7wDwh8uLPE4MDaRXqd"
     * ]
     * 1 => array:4 [
     * "active" => false
     * "buyer_display_nick_mask" => "t*0"
     * "buyer_nick_mask" => "t*0"
     * "obs_buyer_id" => "RAzN8HWNgjUGdtnZwasrGmemEdSW6NtC2NAkTXoTCwo2Yyr4VrA"
     * ]
     * ]
     * ]
     * err_code:VERIFY_CODE_VALIDATE_FAIL   err_msg:验证码有误，请核对后重试
     *
     * @author: liujq
     * @date: 2023/10/16
     */
    public function userInfoQueryForTaobao($product_code, $mobile, $verify_code = '')
    {
        $timestamp1 = time();
        $req_data   = [];
        $result     = [];
        $url        = $this->api_setting['base_url'];
        try {
            $biz_data         = [
                'product_code' => $product_code,
                'mobile'       => $mobile,
                'verify_code'  => $verify_code,
            ];
            $req_data         = [
                "appid"        => $this->api_setting['appid'],
                "method"       => "taobao.user_info.query",
                "timestamp"    => Carbon::now()->toDateTimeString(),
                "version"      => $this->version,
                "format"       => $this->format,
                "charset"      => $this->charset,
                "encrypt_type" => $this->encrypt_type,
                "sign_type"    => $this->sign_type,
                "biz_content"  => $this->aes->encrypt(json_encode($biz_data)),
            ];
            $req_data['sign'] = \App\Libraries\ApiUtils::getSign($req_data, $this->api_setting['secret_key'], $this->sign_type);

            $http_response = http_request_send($url, $req_data);

            if ($http_response['code'] == 200) {
                //请求正常响应
                $supplier_result = json_decode($http_response['data'], true);
                //验签
                if (!empty($supplier_result['sign'])) {
                    $my_sign = \App\Libraries\ApiUtils::getSign($supplier_result, $this->api_setting['secret_key'], $this->sign_type);
                    if ($my_sign != $supplier_result['sign']) {
                        $this->log_query('warning', $url, $req_data, array_merge($result, [
                            'rsp_sign'  => $supplier_result['sign'],
                            'my_sign'   => $my_sign,
                            'error_msg' => '响应结果验签不正确！',
                        ]), $timestamp1);
                    }
                }

                $result['code']        = $supplier_result['code'];
                $result['return_code'] = $supplier_result['code'];
                $result['return_msg']  = $supplier_result['message'];

                if ($supplier_result['code'] == 200) {
                    //解密
                    if (!empty($supplier_result['rsp_biz_content'])) {
                        if (!empty($this->encrypt_type)) {
                            $rsp_biz_content = $this->aes->decrypt($supplier_result['rsp_biz_content']);
                            if (empty($rsp_biz_content)) {
                                throw new MyException('响应结果解密失败！');
                            }
                        }
                        $supplier_result['rsp_biz_content'] = json_decode($rsp_biz_content, true);
                    }

                    if ($supplier_result['rsp_biz_content']['success'] == 1) {
                        $result['code']     = 200;
                        $result['user_num'] = $supplier_result['rsp_biz_content']['user_num'];
                        if (!empty($supplier_result['rsp_biz_content']['user_infos'])) {
                            $result['user_infos'] = $supplier_result['rsp_biz_content']['user_infos'];
                        }
                    } else {
                        $result['code'] = 300;
                    }
                    $result['return_code'] = $supplier_result['rsp_biz_content']['err_code'];
                    $result['return_msg']  = $supplier_result['rsp_biz_content']['err_msg'];
                }
            } elseif ($http_response['code'] == 28) {
                $result['code']        = $http_response['code'];
                $result['return_code'] = 'http:' . $http_response['code'];
                $result['return_msg']  = '请求超时，请稍后重试！';
            } else {
                $result['code']        = $http_response['code'];
                $result['return_code'] = 'http:' . $http_response['code'];
                $result['return_msg']  = $http_response['error_msg'] ?? 'failed';
            }
            $this->log_query('info', $url, $req_data, $result, $timestamp1);
        } catch (\Exception $exc) {
            $result['code']        = 500;
            $result['return_code'] = 'error:500';
            $result['return_msg']  = $exc->getMessage() . ':' . $exc->getLine();
            $this->log_query('error', $url, $req_data, $result, $timestamp1);
        }

        return $result;
    }

    /**
     * 权益外放手机号码验证码发放
     * @param $mobile
     * @return array ["code"=>200, "return_code"=>"", "return_msg"=>""]
     * err_code:QUERY_USER_INFO_NUM_NOT_MORE_THAN_ONE   err_msg:手机号码对应淘宝用户数量未超过1
     *          VERIFY_CODE_SYSTEM_ERROR                        验证码系统繁忙，请稍后重试  (3分钟内再次发送验证码会，会返回这个错误)
     * @author: liujq
     * @date: 2023/10/16
     */
    public function sendVerifyCodeForTaobao($product_code, $mobile)
    {
        $timestamp1 = time();
        $req_data   = [];
        $result     = [];
        $url        = $this->api_setting['base_url'];
        try {
            $biz_data         = [
                'product_code' => $product_code,
                'mobile'       => $mobile,
            ];
            $req_data         = [
                "appid"        => $this->api_setting['appid'],
                "method"       => "taobao.verify_code.send",
                "timestamp"    => Carbon::now()->toDateTimeString(),
                "version"      => $this->version,
                "format"       => $this->format,
                "charset"      => $this->charset,
                "encrypt_type" => $this->encrypt_type,
                "sign_type"    => $this->sign_type,
                "biz_content"  => $this->aes->encrypt(json_encode($biz_data)),
            ];
            $req_data['sign'] = \App\Libraries\ApiUtils::getSign($req_data, $this->api_setting['secret_key'], $this->sign_type);

            $http_response = http_request_send($url, $req_data);

            if ($http_response['code'] == 200) {
                //请求正常响应
                $supplier_result = json_decode($http_response['data'], true);
                //验签
                if (!empty($supplier_result['sign'])) {
                    $my_sign = \App\Libraries\ApiUtils::getSign($supplier_result, $this->api_setting['secret_key'], $this->sign_type);
                    if ($my_sign != $supplier_result['sign']) {
                        $this->log_query('warning', $url, $req_data, array_merge($result, [
                            'rsp_sign'  => $supplier_result['sign'],
                            'my_sign'   => $my_sign,
                            'error_msg' => '响应结果验签不正确！',
                        ]), $timestamp1);
                    }
                }

                $result['code']        = $supplier_result['code'];
                $result['return_code'] = $supplier_result['code'];
                $result['return_msg']  = $supplier_result['message'];

                if ($supplier_result['code'] == 200) {
                    //解密
                    if (!empty($supplier_result['rsp_biz_content'])) {
                        if (!empty($this->encrypt_type)) {
                            $rsp_biz_content = $this->aes->decrypt($supplier_result['rsp_biz_content']);
                            if (empty($rsp_biz_content)) {
                                throw new MyException('响应结果解密失败！');
                            }
                        }
                        $supplier_result['rsp_biz_content'] = json_decode($rsp_biz_content, true);
                    }

                    if ($supplier_result['rsp_biz_content']['success'] == 1) {
                        $result['code'] = 200;
                    } else {
                        $result['code'] = 300;
                    }
                    $result['return_code'] = $supplier_result['rsp_biz_content']['err_code'];
                    $result['return_msg']  = $supplier_result['rsp_biz_content']['err_msg'];
                }
            } elseif ($http_response['code'] == 28) {
                $result['code']        = $http_response['code'];
                $result['return_code'] = 'http:' . $http_response['code'];
                $result['return_msg']  = '请求超时，请稍后重试！';
            } else {
                $result['code']        = $http_response['code'];
                $result['return_code'] = 'http:' . $http_response['code'];
                $result['return_msg']  = $http_response['error_msg'] ?? 'failed';
            }
            $this->log_query('info', $url, $req_data, $result, $timestamp1);
        } catch (\Exception $exc) {
            $result['code']        = 500;
            $result['return_code'] = 'error:500';
            $result['return_msg']  = $exc->getMessage() . ':' . $exc->getLine();
            $this->log_query('error', $url, $req_data, $result, $timestamp1);
        }

        return $result;
    }
}
