<?php

namespace App\Libraries;

/**
 * 导出csv，直接下载，服务器不保存文件
 * @author: liujq
 * @Time: 2023/7/3
 */
class CsvExporterHelper
{
    protected $fp;

    public function init($filename, $headArray = [])
    {
        ini_set('memory_limit', '256M');
        ini_set('max_execution_time', 600);
        ob_end_clean();
        ob_start();
        header("Content-Type: text/csv;charset=utf-8");
//        header("Content-type:text/csv;charset=gbk");//application/vnd.ms-excel
        header("Content-Disposition:filename=" . $filename . '.csv');
//        header("Pragma: no-cache"); // 禁止缓存
        header('Pragma:public');
        header("Expires: 0");// 有效期时间
        header('Cache-Control:must-revalidate,post-check=0,pre-check=0,max-age=0');
        $this->fp = fopen('php://output', 'w');
        fwrite($this->fp, chr(0xEF) . chr(0xBB) . chr(0xBF));
        if (!empty($headArray)) {
            fputcsv($this->fp, $headArray);
            ob_flush();
            flush();
        }
    }

    public function write($dataArray = [])
    {
        $i = 0;
        foreach ($dataArray as $item) {
            fputcsv($this->fp, $item);
            $i++;
            if ($i === 100) {
                $this->flush();
                $i = 0;
            }
            unset($item, $key, $value);
        }
        if ($i !== 0) {
            $this->flush();
        }
        unset($dataArray);
    }

    //刷新缓存，将PHP的输出缓存输出到浏览器上
    public function flush()
    {
        ob_flush();
        flush();
    }

    //关闭输出流
    public function finish()
    {
        if ($this->fp) {
            fclose($this->fp);
        }
        ob_end_clean();
        exit;
    }
}
