<?php

namespace App\Libraries;

use App\Exceptions\MyNoLogException;
use App\Libraries\Channel\BaseChannel;
use App\Models\Activity;

class OrderUtils
{
    private static $channels         = [];
    private static $act_api_settings = [];

    public static function getActApiSetting($activity_id)
    {
        if (!array_key_exists($activity_id, static::$act_api_settings)) {
            if ($activity_id > 0) {
                $act_info = Activity::where('id', $activity_id)
                    ->select('id', 'channel_appid', 'channel_secketkey', 'channel2_appid', 'channel2_secret_key', 'channel2_encrypt_key', 'channel2_encrypt_iv')
                    ->first();
                if (empty($act_info)) {
                    return null;
                }
                static::$act_api_settings[$activity_id] = $act_info->getChannelApiSettings();
            } else {
                static::$act_api_settings[$activity_id] = [Enums::QCPV1 => config('app.QcpV1'), Enums::QCPV2 => config('app.QcpV2')];
            }

            if (str_contains(static::$act_api_settings[$activity_id][Enums::QCPV2]['callback_url'], '%s')) {
                static::$act_api_settings[$activity_id][Enums::QCPV2]['callback_url'] = sprintf(static::$act_api_settings[$activity_id][Enums::QCPV2]['callback_url'], $activity_id);
            }
        }
        return static::$act_api_settings[$activity_id];
    }

    /**
     * 获取渠道类实例
     * @param int $activity_id 活动id。如果为0则取默认QcpV1和QcpV2参数配置
     * @param string $ecp_target 商品大类配置，或者渠道标识（QcpV1/QcpV2）。根据大类是否为空来判断走QcpV1或QcpV2
     * @return BaseChannel
     * @author: liujq
     * @Time: 2023/7/14
     */
    public static function getChannel(int $activity_id, string $ecp_target): ?BaseChannel
    {
        if (array_key_exists($ecp_target, Enums::$channels)) {
            $channel_type = $ecp_target;
        } else {
            $channel_type = empty($ecp_target) ? Enums::QCPV2 : Enums::QCPV1;
        }
        $key = sprintf('%s_%s', $activity_id, $channel_type);
        if (!array_key_exists($key, static::$channels)) {
            $channel_class   = '\\App\\Libraries\\Channel\\' . $channel_type;
            $act_api_setting = static::getActApiSetting($activity_id);
            if (empty($act_api_setting)) {
                //找不到活动
                return null;
            }
            static::$channels[$key] = new $channel_class($act_api_setting[$channel_type]);
        }
        return static::$channels[$key];
    }

    /**
     * 获取默认渠道参数配置
     * @param int $activity_id
     * @param string $ecp_target
     * @return BaseChannel|null
     * @author: liujq
     * @date: 2023/8/22
     */
    public static function getDefaultChannel(int $activity_id, string $ecp_target): ?BaseChannel
    {
        if (array_key_exists($ecp_target, Enums::$channels)) {
            $channel_type = $ecp_target;
        } else {
            $channel_type = empty($ecp_target) ? Enums::QCPV2 : Enums::QCPV1;
        }

        $channel_class = '\\App\\Libraries\\Channel\\' . $channel_type;
        if ($channel_type == Enums::QCPV1) {
            $act_api_setting = config('app.QcpV1');
        } elseif ($channel_type == Enums::QCPV2) {
            $act_api_setting = config('app.QcpV2');
            if (str_contains($act_api_setting['callback_url'], '%s')) {
                $act_api_setting['callback_url'] = sprintf($act_api_setting['callback_url'], $activity_id);
            }
        }
        if (empty($act_api_setting)) {
            return null;
        }
        return new $channel_class($act_api_setting);
    }

    public static function getNewReqOrderNo($sub_order)
    {
        if (empty($sub_order->req_order_no)) {
            return $sub_order->sub_order_no;
        } else {
            if (($pos = strripos($sub_order->req_order_no, '_')) !== false) {
                $suffix = substr($sub_order->req_order_no, $pos + 1);
                return $sub_order->sub_order_no . '_' . ($suffix + 1);
            } else {
                return $sub_order->sub_order_no . '_1';
            }
        }
    }

    //order_subs子订单表的sub_order_no中不包含"_"，所以这么拆解没问题
    public static function getSubOrderNo($req_order_no)
    {
        if (empty($req_order_no)) {
            return '';
        } else {
            if (($pos = strripos($req_order_no, '_')) !== false) {
                return substr($req_order_no, 0, $pos);
            } else {
                return $req_order_no;
            }
        }
    }
}
