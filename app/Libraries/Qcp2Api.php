<?php

namespace App\Libraries;

use GuzzleHttp\Exception\ConnectException;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Exception\InvalidArgumentException;
use Carbon\Carbon;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * 用于后台管理系统调用spider_api清除缓存功能
 */
class Qcp2Api
{
    static $inst;

    protected $appid;
    protected $logger_http;
    protected $url;
    protected $encrypt_type;
    protected $sign_type;
    protected $encrypt_key;
    protected $encrypt_iv;
    protected $secret_key;

    public function __construct()
    {
        $this->logger_http  = Log::channel('submit_qcp2');
        $this->appid        = config('app.qcp2.appid');
        $this->encrypt_type = config('app.qcp2.encrypt_type');
        $this->sign_type    = config('app.qcp2.sign_type');
        $this->encrypt_key  = config('app.qcp2.encrypt_key');
        $this->encrypt_iv   = config('app.qcp2.encrypt_iv');
        $this->url          = config('app.qcp2.url');
        $this->secret_key   = config('app.qcp2.secret_key');

    }

    public static function getInst()
    {
        if (is_null(static::$inst)) {
            static::$inst = new Qcp2Api();
        }
        return static::$inst;
    }

    //
    public function commonRequest($method, $out_trade_no, $product_code, $user_mobile, $charge_account)
    {

        $biz_data = [
            'product_code'   => $product_code,
            'out_trade_no'   => $out_trade_no,
            'amount'         => '1',
            'charge_account' => $charge_account,
            'user_mobile'    => $user_mobile,
            'order_from'     => null,
            'attach'         => $this->appid,
            'notify_url'     => '',
        ];
        $data     = [
            "appid"        => $this->appid,
            "method"       => $method,
            "timestamp"    => Carbon::now()->toDateTimeString(),
            "version"      => "1.0",
            "format"       => "json",
            "charset"      => "utf-8",
            "encrypt_type" => $this->encrypt_type,
            "sign_type"    => $this->sign_type,
            "biz_content"  => Aes::getAes($this->encrypt_key, $this->encrypt_iv)->encrypt(json_encode($biz_data)),
        ];

        $data['sign'] = ApiUtils::getSign($data, $this->secret_key, $this->sign_type);
        $rsp_arr      = $this->doPost($this->url, $data);
//        $their_sign = $rsp_arr['sign'];
//        $my_sign    = ApiUtils::getSign($rsp_arr,  $this->secret_key, $this->sign_type);
        return $rsp_arr;


    }

    protected function doPost($url, $data, $eventType = '', $timeout = 30)
    {

        $client      = new Client();
        $reqTime     = millisecond();
        $rsp_content = '';
        $resp        = [];

        try {
            $response = $client->request('POST', $url, [
                'form_params' => $data,
                'timeout'     => $timeout,
                'headers'     => [
                    'Content-type' => 'application/x-www-form-urlencoded;charset=utf-8'
                ]
            ]);

            $rsp_content = $response->getBody()->getContents();

            $resp = json_decode($rsp_content, true);
            if (isset($resp['rsp_biz_content'])) {
                $resp['rsp_biz_content'] = Aes::getAes($this->encrypt_key, $this->encrypt_iv)->decrypt($resp['rsp_biz_content']);
                if (!empty($resp['rsp_biz_content'])) {
                    $resp['rsp_biz_content'] = json_decode($resp['rsp_biz_content'], true);
                }
            }

        } catch (\Exception $e) {

            //"cURL error 28: Operation timed out after 3114 milliseconds with 0 bytes received"
            if (strpos($e->getMessage(), 'timed out after') !== FALSE) {
                $resp = ['code' => 28];//超时
            } else {
                $resp = ['code' => 500];
            }
            $this->logger_http->error($e);
        }

        $rspTime = millisecond();

        $log_data = [
            'serviceId' => config('app.name'),
            'eventType' => $eventType,
            'reqTime'   => $reqTime,
            'rspTime'   => $rspTime,
            'procTime'  => $rspTime - $reqTime,
            'url'       => $url,
            'reqData'   => $data,
            'rspData'   => !empty($resp) ? $resp : $rsp_content,
        ];

        $this->logger_http->info(json_encode($log_data, JSON_UNESCAPED_UNICODE));

        return $resp;
    }
}
