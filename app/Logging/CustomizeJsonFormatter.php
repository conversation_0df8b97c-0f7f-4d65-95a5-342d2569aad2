<?php

/**
 * @Description: 日志记录
 *
 */

namespace App\Logging;

use Monolog\Formatter\JsonFormatter;

class CustomizeJsonFormatter extends JsonFormatter
{
    // 重构
    public function format(array $record): string
    {
        //去掉空数据，节省空间。
        if (empty($record['context'])) {
            unset($record['context']);
        }
        if (empty($record['extra'])) {
            unset($record['extra']);
        }

        //整理数据顺序
        $record_datetime = $record['datetime']->format('Y-m-d H:i:s');
        $level_name = $record['level_name'];
        unset($record['datetime']);
        unset($record['level_name']);

        //message字段转换为数组
        if(is_string($record['message'])){
            $arr_message = json_decode($record['message'], true);
            if(!empty($arr_message)){
                $record['message'] = $arr_message;
            }
        }
        $record = array_merge(array(
            'datetime' => $record_datetime,
            'level_name' => $level_name,
            ), $record);

        $json = $this->toJson($this->normalize($record), true) . ($this->appendNewline ? "\n" : '');

        return $json;
    }
}
