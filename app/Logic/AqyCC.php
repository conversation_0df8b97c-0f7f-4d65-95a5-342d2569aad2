<?php

namespace App\Logic;

use App\Http\Controllers\Api\SysCode\SysCode;
use App\Exceptions\MyException;
use App\Models\Order;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

//爱奇艺兑换
class AqyCC extends ExchangeBaseV2
{
    protected $redis;
    protected $pre_key         = 'aqycc_'; //每天每账号订单计数redis key前缀
    protected $max_order_limit = 36;  //每天每账号最大订单数

    public function __construct()
    {
        parent::__construct();

        $this->redis = app('redis.connection');

        $this->single_goods_rdr_url = '/aqycc/index.html'; //单品跳转地址
        $this->jiazheng_rdr_url     = ''; //家政服务跳转地址
        $this->goods_list_rdr_url   = ''; //商品列表跳转地址
    }

    //默认登录方法。
    public function login($activity_id = null)
    {
        $mobile      = request('mobile');
        $sms_captcha = request('sms_captcha');

        if (!check_mobile($mobile)) {
            throw new MyException(SysCode::MOBILE_ERROR);
        }

        if (!empty($sms_captcha)) {
            $this->checkSmsCaptcha($mobile, $sms_captcha);
        }

        try {

            $user = DB::table('aqy_users')->where('mobile', $mobile)->first();

            if (!$user) {
                $activity_user_id = DB::table('aqy_users')->insertGetId(['mobile' => $mobile, 'created_at' => Carbon::now()]);
            } else {
                $activity_user_id = $user->id;
            }

            $extra = ['mobile' => $mobile];
            if (empty($sms_captcha)) {
                $extra['query_only'] = true;
            }

            $this->exchange_login(false, $activity_user_id, $extra);

        } catch (MyException $e) {

            if ($e->getCode() == SysCode::SUCCESS) {

                session(['user' => ['mobile' => $mobile]]);
                session()->save();
            }

            throw $e;
        }
    }

    public function verifyBeforeCreateOrder($goods_info, $group_info = null)
    {
        //判断是否超过下单次数限制。
        $key         = $this->pre_key . md5(request('charge_account'));
        $order_count = $this->redis->get($key);
        if (!empty($order_count) && $order_count >= $this->max_order_limit) {
            $this->throwMyException(SysCode::EXCHANGE_MAX_LIMIT);
        }

        return parent::verifyBeforeCreateOrder($goods_info, $group_info);
    }

    //处理自定义下订单
    // 入各个活动的兑换表
    public function handleAfterCreateOrder($activity_user_id, $order_data, $goods_info, $group_info = null)
    {
        parent::handleAfterCreateOrder($activity_user_id, $order_data, $goods_info, $group_info);

        //添加下单计数
        $key         = $this->pre_key . md5(request('charge_account'));
        $order_count = $this->redis->incr($key);
        $this->redis->expire($key, Carbon::now()->endOfDay()->timestamp - Carbon::now()->timestamp);
        if ($order_count > $this->max_order_limit) {
            $this->throwMyException(SysCode::EXCHANGE_MAX_LIMIT);
        }
    }

    /**
     * 取消订单后执行的操作。主要是对各活动下单频次限制条件的更新
     * @param Order $order 订单信息
     * @date: 2024/11/8
     */
    public function handlerAfterCancelOrder($order)
    {
        parent::handlerAfterCancelOrder($order);

        if ($order->charge_account && $this->order_cancel_result === 1) {
            try {
                $key         =  $this->pre_key . md5($order->charge_account);
                $order_count = $this->redis->get($key);
                if (!empty($order_count) && $order_count > 0) {
                    $this->redis->decr($key);
                    Log::info(sprintf("order id: %d, %s limit_key=%s decr.", $order->id, static::class, $key));
                }
            } catch (\Exception $e) {
                Log::error("order id: {$order->id}, decr error. " . $e->getMessage(), $e->getTrace());
            }
        }
    }
}
