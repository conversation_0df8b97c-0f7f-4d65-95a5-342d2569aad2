<?php

namespace App\Logic;

use App\Exceptions\MyException;
use App\Http\Controllers\Api\SysCode\SysCode;
use App\Libraries\OrderUtils;
use App\Models\Activity;
use App\Models\Goods;
use App\Models\Order;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * 活动逻辑处理基类
 * @package App\Logic
 */
class BaseLogic implements LogicInterface
{
    public $activity_id = 0;
//    public $activity_type = '';

    public $rediret_url = ''; //下单成功后返回的url。默认为空，各活动自己处理。

    protected $query_only = false; //判断用户是否只能查询订单而不能做下单、更新信息等操作。

    public $jiazheng_rdr_url     = '/ms/jzindex.html?id={id}'; //家政服务跳转地址
    public $single_goods_rdr_url = '/detail.html?id={id}'; //单品跳转地址
    public $goods_list_rdr_url   = '/goods.html'; //商品列表跳转地址

    protected $my_syscode = [

    ];

    public function getMsgByCode($code)
    {
        if (array_key_exists($code, $this->my_syscode)) {
            return $this->my_syscode[$code];
        } elseif (array_key_exists($code, SysCode::$resp_msg)) {
            return SysCode::$resp_msg[$code];
        } else {
            return SysCode::$resp_msg[SysCode::UNKNOW_ERROR];
        }
    }

    public function login($activity_id)
    {
        throw new MyException(SysCode::$resp_msg[SysCode::SYSTEM_ERROR], SysCode::SYSTEM_ERROR);
    }

    /**
     * 活动内验证登录成功后，调用本方法写入session。
     * @param mixed $activity_user_id 活动用户id及级别信息。[prize_level=>id,prize_level2=>id2] or id
     * @param int $activity_id 活动id
     * @param array $prize_levels 奖品级别数组
     * @param array $extra 存入业务所需的session信息。query_only = true-只能查询订单，不能下单。
     */
    protected function doLogin($activity_user_id, $activity_id, $prize_levels = [], $extra = [])
    {
        //登录成功
        session([
            'user_info' => [
                'id'          => $activity_user_id, //int。
                'prize_level' => $prize_levels,//["1_1"=> [1, 1],"1_2"=> [1, 2]]
            ],
            // 这里不再写入activity信息，因为已经在ApiController里的getLogic方法里写过了。
            'extra'     => $extra,
        ]);
        session()->save();
    }

    public function verifyBeforeCreateOrder($goods_info, $group_info = null)
    {
        throw new MyException(SysCode::$resp_msg[SysCode::SYSTEM_ERROR], SysCode::SYSTEM_ERROR);
    }

    public function handleAfterCreateOrder($activity_user_id, $order_id, $goods_info, $group_info = null)
    {
        throw new MyException(SysCode::$resp_msg[SysCode::SYSTEM_ERROR], SysCode::SYSTEM_ERROR);
    }

    /**
     * 取消订单后执行的操作。主要是对各活动下单频次限制条件的更新
     * @param Order $order 订单信息
     * @date: 2024/11/8
     */
    public function handlerAfterCancelOrder($order)
    {

    }

    /**
     * 活动资格验证（是否有领取礼品资格）
     * @param $goods_id int 礼品id
     * @return mixed
     * @throws MyException
     */
    public function isPermit($goods_id = null, $group_id = null)
    {
        throw new MyException(SysCode::$resp_msg[SysCode::SYSTEM_ERROR], SysCode::SYSTEM_ERROR);
    }

    /**
     * 获取商品列表前的操作
     * @return mixed|null
     */
    public function handlerBeforeGetGoodsList()
    {
        return null;
    }

    /**
     * 处理获取兑换商品列表逻辑处理
     * @param $goods_list
     * @return mixed
     */
    public function handlerAfterGetGoodsList($goods_list)
    {
        return $goods_list;
    }

    public function handlerAfterGetGoodsDetail($goods_info)
    {
        return $goods_info;
    }

    /**
     * 活动及时间判断
     * @param int $activity_id
     * @param bool $is_verify_end_time 是否验证活动结束时间。查询订单时不需要验证。
     * @throws MyException
     */
    public function verifyActifity($is_verify_end_time = true)
    {
        if (empty(session('activity.begin_time')) || empty(session('activity.end_time'))) {
            $activity = Activity::find($this->activity_id);
            if (!$activity || $activity->status == '0') {
                throw new MyException(SysCode::$resp_msg[SysCode::ACTIVITY_ERROR], SysCode::ACTIVITY_ERROR);
            }
            session()->put('activity', [
                'begin_time' => $activity->begin_time,
                'end_time'   => $activity->end_time,
            ]);
            session()->save();
        }

        if (time() < strtotime(session('activity.begin_time'))) {
            throw new MyException(SysCode::$resp_msg[SysCode::EXCHANGE_NOT_BEGIN], SysCode::EXCHANGE_NOT_BEGIN);
        }

        if ($is_verify_end_time && time() > strtotime(session('activity.end_time'))) {
            throw new MyException(SysCode::$resp_msg[SysCode::EXCHANGE_HAD_END], SysCode::EXCHANGE_HAD_END);
        }
    }

    //淘宝用户账号查询
    //return_code:VERIFY_CODE_VALIDATE_FAIL   err_msg:验证码有误，请核对后重试
    public function taobaoUserInfoQuery($product_code, $mobile, $verify_code = '')
    {
        $channel = OrderUtils::getChannel($this->activity_id, '');//获取qcp2的配置
        $result  = $channel->userInfoQueryForTaobao($product_code, $mobile, $verify_code);
        if ($result['code'] != 200) {
            $this->throwMyException(SysCode::CHANNEL_DEAL_ERROR, $result['return_msg']);
        }
        return $result;
    }

    //发送验证码
    //return_code:QUERY_USER_INFO_NUM_NOT_MORE_THAN_ONE   err_msg:手机号码对应淘宝用户数量未超过1
    //return_code:VERIFY_CODE_SYSTEM_ERROR    err_msg:验证码系统繁忙，请稍后重试
    public function taobaoVerifyCodeSend($product_code, $mobile)
    {
        $channel = OrderUtils::getChannel($this->activity_id, '');//获取qcp2的配置
        return $channel->sendVerifyCodeForTaobao($product_code, $mobile);
    }

    //前置验证
    //return_code:QUERY_USER_INFO_NUM_NOT_MORE_THAN_ONE   err_msg:手机号码对应淘宝用户数量未超过1
    //return_code:VERIFY_CODE_SYSTEM_ERROR    err_msg:验证码系统繁忙，请稍后重试
    public function preVerify($product_code, $charge_account, $verify_code = '')
    {
        $channel = OrderUtils::getChannel($this->activity_id, '');//获取qcp2的配置
        return $channel->verify($product_code, $charge_account, $verify_code);
    }

    /**
     * 验证充值账号格式（如果配置了格式约束，则验证格式，否则只验证是否为空）
     * @param $goods_info
     * @param string $charge_account
     * @throws MyException
     * @date: 2024/11/7
     */
    public function checkChargeAccount($goods_info, $charge_account)
    {
        if (empty($charge_account)) {
            $this->throwMyException(SysCode::CHARGE_ACCOUNT_ERROR);
        }

        if ($goods_info->input_pattern_ids) {
            if (is_string($goods_info->input_pattern_ids)) {
                $goods_info->input_pattern_ids = array_filter(json_decode($goods_info->input_pattern_ids, true));
            }

            if ($goods_info->input_pattern_ids) {
                //配置了格式，则做格式判断，支持多个格式，有一个通过即可
                $input_patterns = DB::table('input_patterns')
                    ->whereIn('id', $goods_info->input_pattern_ids)
                    ->where('form_name', 'charge_account')
                    ->select(['name', 'pattern', 'input_mark'])
                    ->get();
                if (count($input_patterns) > 0) {
                    $valid_success = false;//验证结果
                    $mess          = [];
                    foreach ($input_patterns as $input_pattern) {
                        $mess[] = $input_pattern->name;
                        if (preg_match($input_pattern->pattern, $charge_account)) {
                            $valid_success = true;
                            break;
                        }
                    }
                    if (!$valid_success) {
                        $this->throwMyException(SysCode::CHARGE_ACCOUNT_ERROR, '请输入正确的' . implode('/', $mess));
                    }
                }
            }
        }
    }

    /**
     * 判断是否只能查询而不能下单
     * @return bool
     */
    protected function isQueryOnly()
    {
        return !empty(session('extra.query_only'));
    }

    //抛出自定义错误
    protected function throwMyException($code, $msg = '', \Throwable $previous = null, $data = null)
    {
        if (empty($msg)) {
            $msg = $this->getMsgByCode($code);
        }

        throw new MyException($msg, $code, $previous, $data);
    }

    /**
     * 校验短信验证码
     * @param $mobile string 用户输入的手机号
     * @param $sms_captcha string 用户输入的短信验证码
     * @param $remove_session_if_pass bool 校验成功是否删除短信验证码相关session项。默认为true
     * @param $throw_if_verify_fail bool 校验失败是否抛出错误。默认为true
     * @return bool 返回true表示校验成功，false-校验失败
     * @throws MyException SysCode::CAPTCHA_ERROR
     * Date: 11/25/21
     */
    protected function checkSmsCaptcha($mobile, $sms_captcha, $remove_session_if_pass = true, $throw_if_verify_fail = true)
    {
        if (empty($sms_captcha)) {
            if ($throw_if_verify_fail) {
                throw new MyException(SysCode::CAPTCHA_ERROR);
            } else {
                return false;
            }
        }
        if ($sms_captcha == '123331' && (strpos(request()->url(), 'http://gift-backend.dev.limeb.cn') === 0)) {
            return true;
        } else {
            //兑换时需输入验证码
            $cattcha_arr = session('sms_captcha');
            if ($cattcha_arr && $cattcha_arr['code'] == $sms_captcha && $cattcha_arr['mobile'] == $mobile && $cattcha_arr['expired'] > time()) {
                //通过
                if ($remove_session_if_pass) {
                    session()->forget('sms_captcha');//可以不删，减少短信发送量
                }
                return true;
            } else {
                Log::channel('sms_captcha_log')->warning('验证失败。' . json_encode(['mobile' => $mobile, 'sms_captcha' => $sms_captcha, 'session_sms_captcha' => $cattcha_arr]));
                if ($throw_if_verify_fail) {
                    throw new MyException(SysCode::CAPTCHA_ERROR);
                }
            }
        }

        return false;
    }

    /**
     * 删除短信验证码session
     * @author: liujq
     * @Time: 2023/7/10
     */
    protected function removeSmsCaptchaFromSession()
    {
        session()->forget('sms_captcha');
    }

}
