<?php

namespace App\Logic;

use App\Http\Controllers\Api\SysCode\SysCode;
use App\Exceptions\MyException;

//建行工会观影活动。2021-11-09
class CcbGhGy extends ExchangeBaseV2
{
    public function __construct()
    {
        parent::__construct();

        $this->single_goods_rdr_url = '/jhghgy/goods.html'; //单品跳转地址
        $this->jiazheng_rdr_url     = '/ms/jzindex.html?id={id}'; //家政服务跳转地址
        $this->goods_list_rdr_url   = '/jhghgy/goods.html'; //商品列表跳转地址
    }

    //默认登录方法。
    public function login($activity_id = null)
    {
        try {

            $this->exchange_login();

        } catch (MyException $e) {

            if ($e->getCode() == SysCode::CARD_PWD_ERROR) {

                $card_pwd = request('card_pwd');

                //不区分大小写
                if (stripos($card_pwd, 'I') !== false || stripos($card_pwd, 'l') !== false) {
                    throw new MyException($this->getMsgByCode(SysCode::CARD_PWD_ERROR) . "，请注意区分字母“L”和“I”并重新尝试", SysCode::CARD_PWD_ERROR);
                }
            }

            throw $e;
        }
    }
}
