<?php

namespace App\Logic;

use App\Exceptions\MySysException;
use App\Models\CebGhUser;
use App\Models\Duihuan1User;
use App\Models\ExchangeGroup;
use App\Models\Goods;
use App\Models\Iqy20210910Record;
use App\Models\Iqy20210910User;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Api\SysCode\SysCode;
use App\Exceptions\MyException;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

//光大银行工会活动-20230710
//白名单模式，按手机号判断
class CebGh extends BaseLogic
{
    //public $jiazheng_rdr_url     = '/ms/jzindex.html?id={id}'; //家政服务跳转地址
    //public $single_goods_rdr_url = '/gdyh/detail.html?id={id}'; //单品跳转地址
    //public $goods_list_rdr_url   = '/gdyh/goods.html'; //商品列表跳转地址

    public $my_syscode = [
        //SysCode::CARD_PWD_ERROR    => '兑换码错误',
        SysCode::PARAMS_ERROR      => '手机号或验证码错误',
        SysCode::EXCHANGE_LOG_3020 => '您已经领过礼品了,请在页面底部查询兑换记录。',
    ];

    public function login($activity_id)
    {
        $validator = Validator::make(request()->all(), [
            'mobile'      => 'required|min:11',
            'sms_captcha' => 'required|min:6',
        ], [
            'mobile.required'      => SysCode::MOBILE_ERROR,
            'mobile.min'           => SysCode::MOBILE_ERROR,
            'sms_captcha.required' => SysCode::CAPTCHA_ERROR,
            'sms_captcha.min'      => SysCode::CAPTCHA_ERROR,
        ]);

        if ($validator->fails()) {
            $errors   = $validator->errors();
            $err_code = $errors->first();
            throw new MyException($this->getMsgByCode($err_code), $err_code);
        }

        $mobile      = request('mobile');
        $sms_captcha = request('sms_captcha');
        if (!check_mobile($mobile)) {
            throw new MyException(SysCode::MOBILE_ERROR);
        }

        $this->checkSmsCaptcha($mobile, $sms_captcha);

        $this->verifyActifity();

        $user = CebGhUser::where(['mobile' => $mobile, 'status' => 1])->first();
        if (!$user) {
            throw new MyException('未获得活动资格', SysCode::NOT_CHANCE_3019);
        }
        if (Carbon::now() > Carbon::parse('2023-09-15 23:59:59') && $user->department != '总行行长室') {
            throw new MyException('本活动已结束^_^', SysCode::EXCHANGE_HAD_END);
        }

        $this->doLogin($user->id, $activity_id, ['1_1' => [1, 1]], ['mobile' => $mobile]);

        $this->removeSmsCaptchaFromSession();//删除短信验证码相关session值

        throw new MyException($this->getMsgByCode(SysCode::SUCCESS), SysCode::SUCCESS, null, [
            //'rediret_url' => $this->goods_list_rdr_url,
        ]);
    }

    //===================================================================
    // 以下接口登录成功后才能使用。

    public function verifyBeforeCreateOrder($goods_info, $group_info = null)
    {
        $goods_id         = $goods_info->id;
        $activity_user_id = session('user_info.id'); //该活动的用户id

        //活动信息及活动时间判断，已经在AuthController里判断过了。
        //$this->verifyActifity($this->activity_id);
        //判断用户是否已领取。
        $this->verifyUserAndIsExchange();
        //判断商品是否启用，并判断当前用户等级是否与商品等级相符。
        $prize_level_info = $this->verifyCanExchangeGoods($goods_info, $group_info);

        //返回 activity_user_id 在插入订单表的时候用到
        return ['activity_user_id' => $activity_user_id, 'prize_level' => $prize_level_info->prize_level, 'prize_level2' => $prize_level_info->prize_level2];
    }

    //处理自定义下订单
    // 入各个活动的兑换表
    public function handleAfterCreateOrder($activity_user_id, $order_data, $goods_info, $group_info = null)
    {
        $is_update = DB::table('ceb_gh_users')->where(['id' => $activity_user_id, 'get_status' => CebGhUser::GET_STATUS_NO])
            ->update([
                'get_status'     => CebGhUser::GET_STATUS_YES,
                'get_time'       => date('Y-m-d H:i:s'),
                "goods_id"       => $goods_info->id,
                "goods_name"     => $goods_info->goods_name,
                "order_id"       => $order_data['id'],
                "charge_account" => $order_data['charge_account'],
                "goods_price"    => $order_data['goods_price'],
                "updated_at"     => date('Y-m-d H:i:s'),
            ]);
        if (!$is_update) {
            throw new MyException(SysCode::$resp_msg[SysCode::CUSTOMER_ONCE], SysCode::CUSTOMER_ONCE);
        }
    }

    //如果传递了$goods_id参数，则判断商品是否满足兑换条件，否则只判断活动及用户状态
    public function isPermit($goods_id = null, $group_id = null)
    {
        //活动信息及活动时间判断，已经在AuthController里判断过了。
        //判断用户是否已领取。
        $this->verifyUserAndIsExchange();
        //判断商品是否启用，并判断当前用户等级是否与商品等级相符。
        if (!is_null($goods_id)) {
            $goods_info = Goods::find($goods_id);
            $this->verifyCanExchangeGoods($goods_info, empty($group_id) ? null : ExchangeGroup::find($group_id));
        }

        throw new MyException(SysCode::$resp_msg[SysCode::SUCCESS], SysCode::SUCCESS);
    }

    /**
     * 根据领取状态，判断用户是否已领取。因活动而已。本活动没有礼品等级概念
     * @return Iqy20210910User
     * @throws MyException
     */
    private function verifyUserAndIsExchange()
    {
        $activity_user_info = CebGhUser::find(session('user_info.id'));

        //用户状态
        if (!$activity_user_info || $activity_user_info->status == CebGhUser::STATUS_DISABLE) {
            throw new MyException(SysCode::$resp_msg[SysCode::NOT_CHANCE_3019], SysCode::NOT_CHANCE_3019);
        }

        //验证是否领取过
        if ($activity_user_info->get_status == CebGhUser::GET_STATUS_YES) {
            throw new MyException(SysCode::$resp_msg[SysCode::EXCHANGE_LOG_3020], SysCode::EXCHANGE_LOG_3020); //领取过
        }

        return $activity_user_info;
    }

    /**
     * 判断商品是否启用，并判断当前用户等级是否与商品等级相符。
     * @param $goods_id
     * @return activity_prizes
     * @throws MyException
     */
    protected function verifyCanExchangeGoods($goods_info, $group_info = null)
    {
        $prize_info = DB::table('activity_prizes')->where([
            'goods_id'    => $goods_info->id,
            'activity_id' => $this->activity_id
        ])->first();

        if (!$prize_info) {
            throw new MyException(SysCode::$resp_msg[SysCode::NOT_CHANCE_3019], SysCode::NOT_CHANCE_3019);
        }

        if (!$goods_info || $goods_info->status == SysCode::COMMON_STATUS_0) {
            throw new MyException(SysCode::$resp_msg[SysCode::GOOD_LISTS_EMPTY], SysCode::GOOD_LISTS_EMPTY);
        }

//        if (!array_key_exists($prize_info->prize_level . "_" . $prize_info->prize_level2, session("user_info.prize_level"))) {
//            throw new MyException(SysCode::$resp_msg[SysCode::NOT_CHANCE_3019], SysCode::NOT_CHANCE_3019);
//        }

        return $prize_info;
    }

    public function handlerAfterGetGoodsDetail($goods_info)
    {
        $prize_info = DB::table('activity_prizes')->where([
            'goods_id'    => $goods_info->id,
            'activity_id' => $this->activity_id
        ])->first();

        if (!$prize_info) {
            throw new MyException(SysCode::$resp_msg[SysCode::GOOD_LISTS_EMPTY], SysCode::GOOD_LISTS_EMPTY);
        }
        return $goods_info;
    }

}
