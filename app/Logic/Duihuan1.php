<?php

namespace App\Logic;

use App\Http\Controllers\Api\SysCode\SysCode;
use App\Exceptions\MyException;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

//通用兑换页面  2022-07-26
class Duihuan1 extends Normal
{
    public function __construct()
    {
        parent::__construct();
        $this->single_goods_rdr_url = '/qn/detail.html?id={id}';   //单品跳转地址
//        $this->jiazheng_rdr_url     = '/n/jzindex.html?id={id}';  //家政服务跳转地址   ？？？
        $this->goods_list_rdr_url = '/qn/goods.html';            //商品列表跳转地址
    }
}
