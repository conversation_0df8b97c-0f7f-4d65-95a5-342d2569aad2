<?php

namespace App\Logic;

use App\Models\ExchangeBatch;
use App\Models\ExchangeDetail;
use App\Models\ExchangeGoods;
use App\Models\ExchangeGroup;
use App\Models\ExchangeRecord;
use App\Models\Goods;
use Carbon\Carbon;
use Encore\Admin\Form\Field\Datetime;
use Exception;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Api\SysCode\SysCode;
use App\Exceptions\MyException;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

//兑换码逻辑。用于蒙商积分兑换处理家政类和图书订单。2021-10-15
class Exchange extends BaseLogic
{
    private $exchange_detail;
    private $exchange_batch;

    public function __construct()
    {

        $this->single_goods_rdr_url = '/blue/detail.html?id={id}'; //单品跳转地址
        $this->jiazheng_rdr_url     = '/ms/jzindex.html?id={id}'; //家政服务跳转地址
        $this->goods_list_rdr_url   = '/blue/goods.html'; //商品列表跳转地址
    }

    public $my_syscode = [
        SysCode::CARD_PWD_ERROR   => '兑换码错误',
        SysCode::CARD_PWD_EXPIRED => '兑换码已过期',
        SysCode::PARAMS_ERROR     => '兑换码或手机号错误',
    ];

    public function login($activity_id)
    {
        $validator = Validator::make(request()->all(), [
            'card_pwd' => 'required|min:8|max:32',
            'mobile'   => 'required|size:11',
        ]);

        if ($validator->fails()) {
            throw new MyException($this->getMsgByCode(SysCode::CARD_PWD_ERROR), SysCode::CARD_PWD_ERROR);
        }

        $mobile = request('mobile');
        if (!check_mobile($mobile)) {
            throw new MyException(SysCode::MOBILE_ERROR);
        }

        $goods_id   = 0;
        $goods_name = '';
        $goods_type = 0;
        $rdr_url    = '';

        $is_query_only = false;
        $captcha       = request('sms_captcha', request('captcha'));//因为其它的地方短信验证码改为了sms_captcha参数。过渡期间这里改为两个都尝试取一次。

        if ($captcha) {
            $this->verifyActifity();
            $this->checkSmsCaptcha($mobile, $captcha);
        } else {
            $is_query_only = true;
            $this->verifyActifity(false);
        }

        $this->exchange_detail = ExchangeDetail::where([
            'code'   => request('card_pwd'),
            'enable' => ExchangeDetail::ENABLE_YES,
        ])->get()->first();

        if (!$this->exchange_detail) {
            throw new MyException($this->getMsgByCode(SysCode::CARD_PWD_ERROR), SysCode::CARD_PWD_ERROR);
        }

        if ($this->exchange_detail->expired) {
            throw new MyException($this->getMsgByCode(SysCode::CARD_PWD_EXPIRED), SysCode::CARD_PWD_EXPIRED);
        }

        $this->exchange_batch = $this->exchange_detail->exchange_batch;

        //批次禁用
        if ($this->exchange_batch->status == 0) {
            throw new MyException($this->getMsgByCode(SysCode::CARD_PWD_ERROR), SysCode::CARD_PWD_ERROR);
        }

        //如果不是该活动的兑换码，提示兑换码错误。
        if ($this->activity_id != $this->exchange_batch->activity_id) {
            throw new MyException($this->getMsgByCode(SysCode::CARD_PWD_ERROR), SysCode::CARD_PWD_ERROR);
        }

        $exchange_records = $this->exchange_detail->exchange_records;//兑换记录

        if ($captcha) {

            if ($this->exchange_detail->status == ExchangeDetail::STATUS_EXCHANGE_COMPLATE) {
                throw new MyException($this->getMsgByCode(SysCode::EXCHANGE_LOG_3020), SysCode::EXCHANGE_LOG_3020); //领取过
            }

            $this->checkExpired();

            $this->checkCycle(); //判断兑换周期

            //如果有兑换记录了，那手机号需与之前一致。
            if (count($exchange_records) > 0 && request('mobile') != $exchange_records->first()->mobile) {
                throw new MyException($this->getMsgByCode(SysCode::MOBILE_ERROR), SysCode::MOBILE_ERROR);
            }

            //兑换，查出商品信息。
            $goods_info = DB::table('exchange_goods')
                ->leftJoin('goods', 'exchange_goods.goods_id', '=', 'goods.id')
                ->where(['exchange_goods.exchange_batch_id' => $this->exchange_detail->exchange_batch_id])
                ->select(['exchange_goods.goods_id', 'goods.goods_name', 'goods.goods_type'])
                ->get();

            if (count($goods_info) == 0) {
                throw new MyException($this->getMsgByCode(SysCode::GOOD_LISTS_FAILD), SysCode::GOOD_LISTS_FAILD);
            }

            if (count($goods_info) == 1) {
                $goods_id   = $goods_info->first()->goods_id;
                $goods_name = $goods_info->first()->goods_name;
                $goods_type = $goods_info->first()->goods_type;
                if ($goods_type == SysCode::GOODS_TYPE_6) {
                    $rdr_url = str_replace('{id}', $goods_info->first()->goods_id, $this->jiazheng_rdr_url);
                } else {
                    $rdr_url = str_replace('{id}', $goods_info->first()->goods_id, $this->single_goods_rdr_url);
                }

            } else {
                $rdr_url = str_replace('{s}', $this->exchange_detail->code, $this->goods_list_rdr_url);
            }
        } else {
            //查询订单时
            if (count($exchange_records) == 0) {
                throw new MyException($this->getMsgByCode(SysCode::EXCHANGE_RECORD_EMPTY), SysCode::EXCHANGE_RECORD_EMPTY);
            }

            if (request('mobile') != $exchange_records->first()->mobile) {
                throw new MyException($this->getMsgByCode(SysCode::MOBILE_ERROR), SysCode::MOBILE_ERROR);
            }
        }

        session(['user.mobile' => request('mobile')]);

        //把兑换码表当成用户表。这里没有用户的概念。
        $extra = [
            'mobile'             => request('mobile'),
            'exchange_detail_id' => $this->exchange_detail->id,
            'exchange_code'      => $this->exchange_detail->code,
        ];

        if ($is_query_only) {
            $extra['query_only'] = true;
        }
        $this->doLogin($this->exchange_detail->id, $activity_id, ['1_1' => [1, 1]], $extra);

        $data = [
            'rdr_url' => $rdr_url,
            //            $user->prize_level . '_' . $user->prize_level2 => [$user->prize_level, $user->prize_level2],
        ];
        if ($goods_id) {
            $data['goods_id']   = $goods_id;
            $data['goods_name'] = $goods_name;
            $data['goods_type'] = $goods_type;
        }

        throw new MyException($this->getMsgByCode(SysCode::SUCCESS), SysCode::SUCCESS, null, $data);

    }

    //===================================================================
    // 以下接口登录成功后才能使用。

    public function verifyBeforeCreateOrder($goods_info, $group_info = null)
    {
        $goods_id         = $goods_info->id;
        $activity_user_id = session('user_info.id'); //该活动的用户id，即兑换码的id

        //活动信息及活动时间判断，在AuthController里不判断结束时间，改为这里判断。
        $this->verifyActifity();
        //判断用户是否已领取。
        $this->verifyUserAndIsExchange($goods_id);

        //批次禁用
        if ($this->exchange_batch->status == 0) {
            throw new MyException($this->getMsgByCode(SysCode::CARD_PWD_ERROR), SysCode::CARD_PWD_ERROR);
        }

        $this->checkExpired();//判断卡是否过期

        //判断商品是否启用，并判断当前用户等级是否与商品等级相符。
        $prize_level_info = $this->verifyCanExchangeGoods($goods_info, $group_info);

        $this->checkExchangeing($goods_id);

        $this->checkCycle();//判断兑换周期

        //返回 activity_user_id 在插入订单表的时候用到
        return ['activity_user_id' => $activity_user_id, 'prize_level' => $prize_level_info->prize_level, 'prize_level2' => $prize_level_info->prize_level2];

    }

    //处理自定义下订单
    // 入各个活动的兑换表
    public function handleAfterCreateOrder($activity_user_id, $order_data, $goods_info, $group_info = null)
    {
        if (!$this->exchange_detail) {
            $this->exchange_detail = ExchangeDetail::find(session('user_info.id'));
        }

        if (!$this->exchange_batch) {
            $this->exchange_batch = $this->exchange_detail->exchange_batch;
        }

        DB::table("exchange_records")->insert([
            'exchange_batch_id'  => $this->exchange_detail->exchange_batch_id,
            'exchange_group_id'  => request('exchange_group_id') ?? 0,
            'exchange_detail_id' => $this->exchange_detail->id,
            'goods_id'           => $goods_info->id,
            'exchange_status'    => ExchangeRecord::EXCHANGE_STATUS_SUCC,
            'mobile'             => session('user.mobile', session('extra.mobile', '')),
            'order_id'           => $order_data['id'],
            "created_at"         => Carbon::now()->format('Y-m-d H:i:s'),
            "updated_at"         => Carbon::now()->format('Y-m-d H:i:s'),
        ]);

        $exchanged_goods   = empty($this->exchange_detail->exchanged_goods) ? [] : explode(',', $this->exchange_detail->exchanged_goods);
        $exchanged_goods[] = $goods_info->id;

        $update_date = [
            "exchanged_times"    => DB::raw('exchanged_times + 1'),
            "bind_by"            => session('user.mobile', session('extra.mobile', '')),
            "exchanged_goods"    => implode(',', $exchanged_goods),
            "last_exchange_time" => date("Y-m-d H:i:s"),
            "updated_at"         => date("Y-m-d H:i:s"),
        ];

        if (!$this->exchange_detail->bind_at) {
            $update_date['bind_at'] = date("Y-m-d H:i:s");
        }

        if ($this->exchange_batch->type == ExchangeBatch::TYPE_LIPINKA) {

            //礼品卡多选一。
            $update_date["status"] = ExchangeDetail::STATUS_EXCHANGE_COMPLATE;

        } elseif ($this->exchange_batch->type == ExchangeBatch::TYPE_JICIKA_REP || $this->exchange_batch->type == ExchangeBatch::TYPE_JICIKA) {

            //可重复选择
            if (count($exchanged_goods) >= $this->exchange_batch->max_times) {
                $update_date["status"] = ExchangeDetail::STATUS_EXCHANGE_COMPLATE;
            } else {
                $update_date["status"] = ExchangeDetail::STATUS_EXCHANGEING;
            }

        } elseif ($this->exchange_batch->type == ExchangeBatch::TYPE_LIBAOKA) {

            //礼包卡（多组选一组），该组里的礼品都可以领取，但不能重复。
            //TODO:逻辑先不做。需要下单接口端配合增加组编号。数量判断还不太准确。

            $update_date["exchanged_group_id"] = request('exchange_group_id');
            if (count($exchanged_goods) >= $this->exchange_batch->max_times) {
                $update_date["status"] = ExchangeDetail::STATUS_EXCHANGE_COMPLATE;
            } else {
                $update_date["status"] = ExchangeDetail::STATUS_EXCHANGEING;
            }
        }

        $is_update = DB::table("exchange_details")
            ->where('id', $this->exchange_detail->id)
            ->update($update_date);

        if (!$is_update) {
            throw new MyException($this->getMsgByCode(SysCode::SYSTEM_ERROR), SysCode::SYSTEM_ERROR);
        }

    }

    //如果传递了$goods_id参数，则判断商品是否满足兑换条件，否则只判断活动及用户状态
    public function isPermit($goods_id = null, $group_id = null)
    {
        //活动信息及活动时间判断，已经在AuthController里判断过了。
//        $this->verifyActifity($this->activity_id);
        //判断用户是否已领取。
        $this->verifyUserAndIsExchange($goods_id);
        //判断商品是否启用，并判断当前用户等级是否与商品等级相符。
        if (!is_null($goods_id)) {
            $goods_info = Goods::find($goods_id);
            $this->verifyCanExchangeGoods($goods_info, empty($group_id) ? null : ExchangeGroup::find($group_id));
        }

        throw new MyException(SysCode::$resp_msg[SysCode::SUCCESS], SysCode::SUCCESS);
    }

    /**
     * 根据兑换卡状态、类型、兑换周期等配置，判断用户是否已领取或是否达到领取条件。
     * @return ExchangeDetail
     * @throws MyException
     */
    private function verifyUserAndIsExchange($goods_id = null)
    {
        if (!$this->exchange_detail) {
            $this->exchange_detail = ExchangeDetail::find(session('user_info.id'));
        }

        //兑换码状态
        if (!$this->exchange_detail || $this->exchange_detail->enable != ExchangeDetail::ENABLE_YES || $this->exchange_detail->expired == ExchangeDetail::EXPIRED_YES) {
            throw new MyException($this->getMsgByCode(SysCode::NOT_CHANCE_3019), SysCode::NOT_CHANCE_3019);
        }

        //验证是否领取过
        if ($this->exchange_detail->status == ExchangeDetail::STATUS_EXCHANGE_COMPLATE) {
            throw new MyException($this->getMsgByCode(SysCode::EXCHANGE_LOG_3020), SysCode::EXCHANGE_LOG_3020); //领取过
        }

        if (!$this->exchange_batch) {
            $this->exchange_batch = $this->exchange_detail->exchange_batch;
        }

        //验证该兑换码是否包含了该商品。
        if ($goods_id && !in_array($goods_id, $this->exchange_batch->get_goods_ids())) {

            throw new MyException(SysCode::$resp_msg[SysCode::EXCHANGE_GOODS_NOT_PERMIT], SysCode::EXCHANGE_GOODS_NOT_PERMIT);
        }


        return $this->exchange_detail;
    }

    public function handlerBeforeGetGoodsList()
    {

        if (!$this->exchange_detail) {
            $this->exchange_detail = ExchangeDetail::find(session('user_info.id'));
        }

        $goods = DB::table('exchange_goods')
            ->leftJoin('exchange_groups', 'exchange_goods.exchange_group_id', '=', 'exchange_groups.id')
            ->leftJoin('goods', 'exchange_goods.goods_id', '=', 'goods.id')
            ->where('exchange_goods.exchange_batch_id', $this->exchange_detail->exchange_batch_id)
            ->orderBy('exchange_goods.sort')
            ->select(['goods.id', 'goods.goods_show_img', 'goods.goods_name', 'goods.goods_type', 'goods.goods_attr', 'goods.advance_days', 'goods.service_time', 'exchange_goods.exchange_group_id', DB::raw('exchange_groups.sort group_sort'), 'exchange_groups.group_name'])
            ->get();

        $ret = [];
        $goods->map(function ($value) use (&$ret) {
//            $value->goods_show_img = Storage::disk(config("admin.upload.disk"))->url($value->goods_show_img);
            $value->goods_show_img = env('OSS_URL') . $value->goods_show_img;
            $ret[1][1][]           = $value;

        });
        return $ret;
    }

    /**
     * 判断兑换卡领取周期是否满足领取条件。
     * @throws MyException
     */
    private function checkCycle()
    {

        if (!$this->exchange_batch || !$this->exchange_detail) {
            throw new MyException($this->getMsgByCode(SysCode::UNKNOW_ERROR), SysCode::UNKNOW_ERROR);
        }

        if ($this->exchange_batch->exchange_cycle == ExchangeBatch::EXCHANGE_CYCLE_MONTH) {
            if (date('Y-m', strtotime($this->exchange_detail->last_exchange_time)) == date('Y-m')) {
                throw new MyException(str_replace('{0}', '月', $this->getMsgByCode(SysCode::EXCHANGE_CYCLE_ERROR)), SysCode::EXCHANGE_CYCLE_ERROR);
            }
        } elseif ($this->exchange_batch->exchange_cycle == ExchangeBatch::EXCHANGE_CYCLE_WEEK) {
            if (date('W', strtotime($this->exchange_detail->last_exchange_time)) == date('W')) {
                throw new MyException(str_replace('{0}', '周', $this->getMsgByCode(SysCode::EXCHANGE_CYCLE_ERROR)), SysCode::EXCHANGE_CYCLE_ERROR);
            }
        } elseif ($this->exchange_batch->exchange_cycle == ExchangeBatch::EXCHANGE_CYCLE_DAY) {
            if (date('Y-m-d', strtotime($this->exchange_detail->last_exchange_time)) == date('Y-m-d')) {
                $msg = str_replace('本{0}', '今天', $this->getMsgByCode(SysCode::EXCHANGE_CYCLE_ERROR));
                $msg = str_replace('下{0}', '明天', $msg);
                throw new MyException($msg, SysCode::EXCHANGE_CYCLE_ERROR);
            }
        }
    }

    //检查兑换中的礼品卡的兑换资格
    private function checkExchangeing($goods_id)
    {

        if (!$this->exchange_batch || !$this->exchange_detail) {
            throw new MyException($this->getMsgByCode(SysCode::UNKNOW_ERROR), SysCode::UNKNOW_ERROR);
        }

        if ($this->exchange_detail->status == ExchangeDetail::STATUS_EXCHANGEING) {

            //礼包卡需判断是否领取的已经领取的分组里的其它商品。

            //兑换码类型。1-礼品卡（多选一），2-计次卡（多选N，可多次选同一个商品），3-不重复计次卡（多选N，不能重复），4-礼包卡（多组选一组）
            if ($this->exchange_batch->type == ExchangeBatch::TYPE_LIPINKA) {

                //礼品卡多选一，不存在这个问题。

            } elseif ($this->exchange_batch->type == ExchangeBatch::TYPE_JICIKA_REP) {

                //可重复选择，也不存在这个问题。

            } elseif ($this->exchange_batch->type == ExchangeBatch::TYPE_JICIKA) {

                //不能重复选择同一商品，传了goods_id才能判断。
                if ($goods_id) {

                    $goods_ids = explode(',', $this->exchange_detail->exchanged_goods);
                    if (in_array($goods_id, $goods_ids)) {
                        throw new MyException($this->getMsgByCode(SysCode::EXCHANGE_GOODS_REPEAT), SysCode::EXCHANGE_GOODS_REPEAT);
                    }
                }

            } elseif ($this->exchange_batch->type == ExchangeBatch::TYPE_LIBAOKA) {

                //礼包卡（多组选一组），该组里的礼品都可以领取，但不能重复。
                //request('exchange_group_id')

                if (empty(request('exchange_group_id'))) {
                    throw new MyException($this->getMsgByCode(SysCode::PARAMS_ERROR) . '11', SysCode::PARAMS_ERROR);
                }

                if (request('exchange_group_id') != $this->exchange_detail->exchanged_group_id) {
                    throw new MyException($this->getMsgByCode(SysCode::EXCHANGE_GOODS_NOT_PERMIT), SysCode::EXCHANGE_GOODS_NOT_PERMIT);
                }

                $group_goods_ids = $this->exchange_batch->get_group_goods_ids;
                if (!array_key_exists(request('exchange_group_id'), $group_goods_ids) || !in_array($goods_id, $group_goods_ids[request('exchange_group_id')])) {
                    throw new MyException($this->getMsgByCode(SysCode::EXCHANGE_GOODS_NOT_PERMIT), SysCode::EXCHANGE_GOODS_NOT_PERMIT);
                }

                $goods_ids = explode(',', $this->exchange_detail->exchanged_goods);
                if (in_array($goods_id, $goods_ids)) {
                    throw new MyException($this->getMsgByCode(SysCode::EXCHANGE_GOODS_REPEAT), SysCode::EXCHANGE_GOODS_REPEAT);
                }

            }

        }
    }

    //检查卡是否过期
    private function checkExpired()
    {
        if ($this->exchange_detail->expired == ExchangeDetail::EXPIRED_YES
            || time() > strtotime($this->exchange_detail->endtime . " 23:59:59")) {
            throw new MyException($this->getMsgByCode(SysCode::CARD_PWD_EXPIRED), SysCode::CARD_PWD_EXPIRED);
        }
    }

    /**
     * 判断商品是否启用，并判断当前用户等级是否与商品等级相符。
     * @param $goods_id
     * @return  activity_prizes
     * @throws MyException
     */
    protected function verifyCanExchangeGoods($goods_info, $group_info = null)
    {
        $prize_info = DB::table('activity_prizes')->where([
            'goods_id'    => $goods_info->id,
            'activity_id' => $this->activity_id
        ])->first();

        if (!$prize_info) {
            throw new MyException(SysCode::$resp_msg[SysCode::NOT_CHANCE_3019], SysCode::NOT_CHANCE_3019);
        }

        if (!$goods_info || $goods_info->status == SysCode::COMMON_STATUS_0) {
            throw new MyException(SysCode::$resp_msg[SysCode::GOOD_LISTS_EMPTY], SysCode::GOOD_LISTS_EMPTY);
        }

        if (!array_key_exists($prize_info->prize_level . "_" . $prize_info->prize_level2, session("user_info.prize_level"))) {
            throw new MyException(SysCode::$resp_msg[SysCode::NOT_CHANCE_3019], SysCode::NOT_CHANCE_3019);
        }

        return $prize_info;

    }

}
