<?php

namespace App\Logic\Exchange;

use App\Exceptions\MyException;
use App\Http\Controllers\Api\SysCode\SysCode;
use App\Libraries\Enums;
use App\Models\ExchangeBatch;
use App\Models\ExchangeDetail;
use App\Models\ExchangeGroup;
use App\Models\ExchangeRecord;
use App\Models\Goods;
use App\Models\Order;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

abstract class Base
{
    /**
     * 兑换时，获取兑换码待更新的数据项（status, exchanged_goods）
     * @param ExchangeBatch $exchange_batch
     * @param ExchangeDetail $exchange_detail
     * @param ExchangeGroup|null $exchange_group
     * @param $goods_id
     * @return array ['status' => xx,'exchanged_goods' => xx]
     * @date: 2024/10/25
     */
    abstract protected function get_detail_update_for_exchange($exchange_batch, $exchange_detail, $exchange_group, $goods_id);

    /**
     * 回退后，获取新的已兑换商品id数组（即exchange_details表中exchange_goods列值的数组形式）
     * @param $detail
     * @param $record
     * @return array ['1-1','2-1','2-2'] 或 [1,2,3]
     * @date: 2024/10/25
     */
    abstract protected function get_new_exchange_goods_for_rollback($detail, $record);

    /**
     * 获取兑换码商品可兑换状态
     * @param ExchangeBatch $exchange_batch
     * @param ExchangeDetail $exchange_detail
     * @param array $exchange_goods_ids 已兑换商品列表，按","拆分后的数组
     * @param int $goods_id
     * @param ExchangeGroup|null $group_info
     * @return bool
     * @date: 2024/11/5
     */
    abstract protected function get_available($exchange_batch, $exchange_detail, $exchange_goods_ids, $goods_id, $group_info);

    abstract public function get_remain_times($exchange_batch, $exchange_detail, $group_info);

    /**
     * 判断是否符合兑换条件，借用了子类方法get_detail_update_for_exchange，并返回其返回值。如果不满足兑换条件，则抛出MyException错误
     * @param ExchangeBatch $exchange_batch
     * @param ExchangeDetail $exchange_detail
     * @param ExchangeGroup|null $exchange_group
     * @param int|null $goods_id
     * @return array|bool $update_data or true
     * @throws MyException
     * @date: 2024/11/7
     */
    public function can_exchange($exchange_batch, $exchange_detail, $exchange_group, $goods_id = null)
    {
        if ($exchange_batch->status != SysCode::COMMON_STATUS_1) {
            throw new MyException(SysCode::CARD_PWD_ERROR);
        }

        $needGroup = ExchangeBatch::hasGroups($exchange_batch->type);

        if ($needGroup && (!$exchange_group || $exchange_group->status != 1)) {
            throw new MyException(SysCode::EXCHANGE_GOODS_NOT_PERMIT); //分组信息不存在或禁用
        }

        if (!$needGroup && $exchange_group) {
            throw new MyException(SysCode::EXCHANGE_GOODS_NOT_PERMIT); //不需要分组信息
        }

        //验证兑换码状态（status, enable）
        $this->checkExchangeDetailStatus($exchange_detail);

        //检查兑换码是否过期
        $this->checkExpired($exchange_detail);

        //验证兑换周期
        $this->checkCycle($exchange_batch, $exchange_detail);

        if ($goods_id) {
            //需在上层判断商品状态
            //批次或分组是否包含该产品，在get_detail_update_for_exchange方法中判断
            $update_data = $this->get_detail_update_for_exchange($exchange_batch, $exchange_detail, $exchange_group, $goods_id);
        }

        return $update_data ?? true;
    }

    /**
     * 兑换
     * @param ExchangeBatch $exchange_batch
     * @param ExchangeDetail $exchange_detail
     * @param ExchangeGroup|null $exchange_group
     * @param $goods_id
     * @param $activity_user_id
     * @param $mobile
     * @param int $order_id
     * @throws MyException
     * @date: 2024/10/25
     */
    public function exchange($exchange_batch, $exchange_detail, $exchange_group, $goods_id, $activity_user_id, $mobile, $order_id)
    {
        //放在此处获取$update_data
        $update_data = $this->can_exchange($exchange_batch, $exchange_detail, $exchange_group, $goods_id);

        DB::table("exchange_records")->insert([
            'exchange_batch_id'  => $exchange_detail->exchange_batch_id,
            'exchange_group_id'  => $exchange_group->id ?? 0,
            'exchange_detail_id' => $exchange_detail->id,
            'goods_id'           => $goods_id,
            'exchange_status'    => ExchangeRecord::EXCHANGE_STATUS_SUCC,
            'mobile'             => $mobile, //session('user.mobile', session('extra.mobile', ''))
            'order_id'           => $order_id,
            "created_at"         => date('Y-m-d H:i:s'),
        ]);

        $update_data["exchanged_times"]    = DB::raw('exchanged_times + 1');
        $update_data["last_exchange_time"] = date("Y-m-d H:i:s");
        $update_data["updated_at"]         = date("Y-m-d H:i:s");

        if (!$exchange_detail->bind_by && !empty($mobile)) {
            $update_date['bind_by'] = $mobile;
            $update_date['bind_at'] = date("Y-m-d H:i:s");
        }

        $is_update = DB::table("exchange_details")
            ->where('id', $exchange_detail->id)
            ->where('status', $exchange_detail->status)
            ->where('exchanged_times', $exchange_detail->exchanged_times)
            ->where('exchanged_goods', $exchange_detail->exchanged_goods)
            ->update($update_data);

        if (!$is_update) {
            throw new MyException(SysCode::EXCHANGE_ERROR);
        }
    }

    /**
     * 回退操作
     * @param Order $order
     * @param ExchangeDetail $detail
     * @return int 1-回退成功，0-已回退，失败抛出错误
     * @throws MyException
     * @date: 2024/10/25
     */
    public function order_cancel($order, $detail)
    {
//        if ($order->status != SysCode::ORDER_STATUS_4) {
//            throw new MyException('该订单不是失败状态，不能重置！');
//        }

        if (!$detail) {
            throw new MyException('异常：兑换码不存在！', SysCode::SYSTEM_ERROR);
        }
        if ($detail->status == ExchangeDetail::STATUS_NO_EXCHANGE) {
            //不会走到该分支
            throw new MyException('重置失败，请稍后重试！', SysCode::SYSTEM_ERROR);
        }

        if (strtotime($detail->endtime) < Carbon::now()->startOfDay()->timestamp) {
            throw new MyException('该兑换码已过期，重置后无法再次兑换！', SysCode::SYSTEM_ERROR);
        }

        $batch = $detail->exchange_batch;

        if ($batch->status != 1) {
            throw new MyException('该兑换码已禁用，重置后无法再次兑换！', SysCode::SYSTEM_ERROR);
        }

        $record = ExchangeRecord::where(['order_id' => $order->id, 'exchange_detail_id' => $detail->id])->first();
        if (empty($record)) {
            throw new MyException('无此兑换订单！', SysCode::PARAMS_ERROR);
        } elseif ($record->exchange_status == ExchangeRecord::EXCHANGE_STATUS_CANCEL) {
            //throw new MyException('该兑换订单已重置过了，请刷新！', SysCode::PARAMS_ERROR);
            //此处已重置过了，直接返回true，不再抛出错误。
            return 0;
        } elseif ($record->exchange_status != ExchangeRecord::EXCHANGE_STATUS_SUCC) {
            //不会走到该分支
            throw new MyException('兑换订单状态异常，请刷新后重试！', SysCode::PARAMS_ERROR);
        }

        $new_exchange_goods_arr = $this->get_new_exchange_goods_for_rollback($detail, $record);

        $last_exchange_time = null;
        if (count($new_exchange_goods_arr) > 0) {
            $status            = ExchangeDetail::STATUS_EXCHANGEING;
            $valid_last_record = ExchangeRecord::where(['exchange_detail_id' => $record->exchange_detail_id, 'exchange_status' => ExchangeRecord::EXCHANGE_STATUS_SUCC])
                ->where('id', '<>', $record->id)
                ->orderBy('id', 'desc')
                ->first();
            if ($valid_last_record) {
                $last_exchange_time = $valid_last_record->created_at;
            }
        } else {
            $status = ExchangeDetail::STATUS_NO_EXCHANGE;
        }

        DB::beginTransaction();
        try {
            //这里只管处理跟兑换码有关的记录，订单记录在上层处理
            //更新兑换记录表兑换状态为取消订单。exchange_status=2
            $effect_rows = DB::table('exchange_records')
                ->where([
                    'order_id'        => $record->order_id,
                    'exchange_status' => ExchangeRecord::EXCHANGE_STATUS_SUCC
                ])
                ->update([
                    'exchange_status' => ExchangeRecord::EXCHANGE_STATUS_CANCEL,
                    'updated_at'      => date('Y-m-d H:i:s')
                ]);
            if (!$effect_rows) {
                throw new MyException('重置失败，请稍后重试！', SysCode::SYSTEM_ERROR);
            }

            $detail_update_data = [
                'status'             => $status,
                'exchanged_times'    => DB::raw('exchanged_times-1'),
                'exchanged_goods'    => implode(',', $new_exchange_goods_arr),
                'last_exchange_time' => $last_exchange_time,
            ];
            //这里没有取消 bind_by 的绑定关系

            $effect_rows = DB::table('exchange_details')
                ->where('id', $detail->id)
                ->where('status', $detail->status)
                ->where('exchanged_times', $detail->exchanged_times)
                ->where('exchanged_goods', $detail->exchanged_goods)
                ->update($detail_update_data);

            if (!$effect_rows) {
                throw new MyException('重置失败，请稍后重试！', SysCode::SYSTEM_ERROR);
            }

            DB::commit();

            return 1;

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 商品列表附加商品可兑换状态字段
     * @param ExchangeBatch $exchange_batch
     * @param ExchangeDetail $exchange_detail
     * @param int|array $goods_list 商品id / 商品列表，需包含goods_id属性或id属性（商品id）
     * @param ExchangeGroup|array|null $group ExchangeGroup / group_id / 以group_id为键的数组
     * @return array|bool $goods_list如果不是数组
     * @date: 2024/11/5
     */
    public function with_available($exchange_batch, $exchange_detail, $goods_list, $group = null)
    {
        $exchange_goods_ids = array_filter(explode(',', $exchange_detail->exchanged_goods));
        if ($group && is_int($group)) {
            foreach ($exchange_batch->exchange_groups as $item) {
                if ($item->id == $group) {
                    $group = $item;
                    break;
                }
            }
        }
        if (is_array($goods_list)) {
            foreach ($goods_list as $idx => $goods) {
                $goods_id = $goods['goods_id'] ?? $goods['id'];
                $_group   = !$group ? $group : ($group instanceof ExchangeGroup ? $group : ($group[$goods['exchange_group_id']] ?? null));
                //
                $goods_list[$idx]['available'] = $this->get_available($exchange_batch, $exchange_detail, $exchange_goods_ids, $goods_id, $_group);
            }
            return $goods_list;
        } else {
            return $this->get_available($exchange_batch, $exchange_detail, $exchange_goods_ids, $goods_list, $group);
        }
    }


    /**
     * 把已兑换商品列表按分组编号分组
     * @param array $exchanged_goods example: ['1-1','1-2','2-1','2-3']
     * @return array  example: ['1'=>[1,2],'2'=>[1,3]]
     * @date: 2024/10/28
     */
    public function grouping(array $exchanged_goods): array
    {
        $ret = [];
        foreach ($exchanged_goods as $item) {
            list($group_id, $goods_id) = exchange_split($item);
            $ret[$group_id][] = $goods_id;
        }
        return $ret;
    }

    /**
     * 判断兑换卡领取周期是否满足领取条件。
     * @throws MyException
     */
    public function checkCycle($exchange_batch, $exchange_detail)
    {
        if ($exchange_batch->exchange_cycle == ExchangeBatch::EXCHANGE_CYCLE_MONTH) {
            if (date('Y-m', strtotime($exchange_detail->last_exchange_time)) == date('Y-m')) {
                throw new MyException(str_replace('{0}', '月', SysCode::$resp_msg[SysCode::EXCHANGE_CYCLE_ERROR]), SysCode::EXCHANGE_CYCLE_ERROR);
            }
        } elseif ($exchange_batch->exchange_cycle == ExchangeBatch::EXCHANGE_CYCLE_WEEK) {
            if (date('W', strtotime($exchange_detail->last_exchange_time)) == date('W')) {
                throw new MyException(str_replace('{0}', '周', SysCode::$resp_msg[SysCode::EXCHANGE_CYCLE_ERROR]), SysCode::EXCHANGE_CYCLE_ERROR);
            }
        } elseif ($exchange_batch->exchange_cycle == ExchangeBatch::EXCHANGE_CYCLE_DAY) {
            if (date('Y-m-d', strtotime($exchange_detail->last_exchange_time)) == date('Y-m-d')) {
                $msg = str_replace('本{0}', '今天', SysCode::$resp_msg[SysCode::EXCHANGE_CYCLE_ERROR]);
                $msg = str_replace('下{0}', '明天', $msg);
                throw new MyException($msg, SysCode::EXCHANGE_CYCLE_ERROR);
            }
        }
    }

    /**
     * 判断兑换码状态是否可用(status, enable，不涉及过期）
     * @param ExchangeDetail $exchange_detail
     * @throws MyException
     * @date: 2024/11/7
     */
    public function checkExchangeDetailStatus(ExchangeDetail $exchange_detail)
    {
        if ($exchange_detail->status == ExchangeDetail::STATUS_EXCHANGE_COMPLATE) {
            throw new MyException(SysCode::EXCHANGE_LOG_3020);
        }

        if ($exchange_detail->enable != ExchangeDetail::ENABLE_YES) {
            throw new MyException(SysCode::CARD_PWD_ERROR);
        }
    }

    /**
     * 检查兑换码是否过期
     * @param $exchange_detail
     * @throws MyException
     * @date: 2024/11/7
     */
    public function checkExpired($exchange_detail)
    {
        if ($exchange_detail->expired == ExchangeDetail::EXPIRED_YES || time() > strtotime($exchange_detail->endtime . " 23:59:59")) {
            throw new MyException(SysCode::CARD_PWD_EXPIRED);
        }
    }

}
