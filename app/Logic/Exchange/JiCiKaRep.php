<?php

namespace App\Logic\Exchange;

use App\Exceptions\MyException;
use App\Http\Controllers\Api\SysCode\SysCode;
use App\Models\ExchangeBatch;
use App\Models\ExchangeDetail;
use App\Models\ExchangeGroup;

/**
 * 可重复兑换计次卡（可重复兑换同一商品）
 * @date: 2024/10/25
 */
class JiCiKaRep extends Base
{
    protected function get_detail_update_for_exchange($exchange_batch, $exchange_detail, $exchange_group, $goods_id)
    {
        $exchanged_goods = empty($exchange_detail->exchanged_goods) ? [] : explode(',', $exchange_detail->exchanged_goods);

        if ($exchange_detail->exchanged_times >= $exchange_batch->max_times || count($exchanged_goods) >= $exchange_batch->max_times) {
            throw new MyException(SysCode::EXCHANGE_LOG_3020);
        }

        //验证该兑换码是否包含了该商品。
        if (!in_array($goods_id, $exchange_batch->get_goods_ids(true))) {
            throw new MyException(SysCode::EXCHANGE_GOODS_NOT_PERMIT);
        }

        $exchanged_goods[] = $goods_id;

        $update_date = [
            "exchanged_goods" => implode(',', $exchanged_goods),
        ];

        if (count($exchanged_goods) >= $exchange_batch->max_times) {
            $update_date["status"] = ExchangeDetail::STATUS_EXCHANGE_COMPLATE;
        } else {
            $update_date["status"] = ExchangeDetail::STATUS_EXCHANGEING;
        }

        return $update_date;
    }

    protected function get_new_exchange_goods_for_rollback($detail, $record)
    {
        //计次卡，不重复
        $goods_ids     = array_filter(explode(',', $detail->exchanged_goods));
        $drop_num      = 0;
        $new_goods_ids = array_filter($goods_ids, function ($value) use ($record, &$drop_num) {
            if ($drop_num === 0 && $value == $record->goods_id) {
                $drop_num += 1;
                return false;
            }
            return true;
        });
        unset($drop_num);
        $new_goods_ids = array_values($new_goods_ids);

        if (count($new_goods_ids) == count($goods_ids)) {
            throw new MyException('兑换记录异常，重置失败！', SysCode::SYSTEM_ERROR);
        }

        return $new_goods_ids;
    }

    protected function get_available($exchange_batch, $exchange_detail, $exchange_goods_ids, $goods_id, $group)
    {
        if ($exchange_detail->status == ExchangeDetail::STATUS_EXCHANGE_COMPLATE) return false;

        return $exchange_detail->exchanged_times < $exchange_batch->max_times;
    }

    public function get_remain_times($exchange_batch, $exchange_detail, $group = null)
    {
        return $exchange_batch->max_times - $exchange_detail->exchanged_times;
    }
}
