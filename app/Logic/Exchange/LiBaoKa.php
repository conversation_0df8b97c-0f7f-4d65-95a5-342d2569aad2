<?php

namespace App\Logic\Exchange;

use App\Exceptions\MyException;
use App\Http\Controllers\Api\SysCode\SysCode;
use App\Models\ExchangeBatch;
use App\Models\ExchangeDetail;
use App\Models\ExchangeGroup;

/**
 * 礼包卡（多组选一组）
 * @date: 2024/10/25
 */
class LiBaoKa extends Base
{
    protected function get_detail_update_for_exchange($exchange_batch, $exchange_detail, $exchange_group, $goods_id)
    {
        if (!in_array($goods_id, $exchange_group->get_goods_ids(true))) {
            throw new MyException(SysCode::EXCHANGE_GOODS_NOT_PERMIT); //分组没有该商品
        }

        $exchanged_goods = empty($exchange_detail->exchanged_goods) ? [] : explode(',', $exchange_detail->exchanged_goods);

        if ($exchanged_goods) {
            $grouping_exchanged_goods = $this->grouping($exchanged_goods);

            if ($exchange_group->id != array_keys($grouping_exchanged_goods)[0]) {
                throw new MyException(SysCode::EXCHANGE_GOODS_NOT_PERMIT);//兑换商品不在已选择的分组
            }

            if (count($grouping_exchanged_goods[$exchange_group->id]) >= $exchange_group->exchange_times) {
                throw new MyException(SysCode::EXCHANGE_LOG_3020);//超出该分组兑换次数
            }

            if (!$exchange_group->allow_repeat && in_array($goods_id, $grouping_exchanged_goods[$exchange_group->id])) {
                throw new MyException(SysCode::EXCHANGE_GOODS_REPEAT);//重复兑换同一商品
            }
        } else {
            $grouping_exchanged_goods = [];
        }

        $exchanged_goods[] = $exchange_group->id . '-' . $goods_id;

        $grouping_exchanged_goods[$exchange_group->id][] = $goods_id;

        $update_date = [
            "exchanged_goods" => implode(',', $exchanged_goods),
        ];

        if (count($exchanged_goods) >= $exchange_group->exchange_times) {
            $update_date["status"] = ExchangeDetail::STATUS_EXCHANGE_COMPLATE;
        } else {
            $update_date["status"] = ExchangeDetail::STATUS_EXCHANGEING;
        }

        return $update_date;
    }

    protected function get_new_exchange_goods_for_rollback($detail, $record)
    {
        $goods_ids     = array_filter(explode(',', $detail->exchanged_goods));
        $drop_num      = 0;
        $new_goods_ids = array_filter($goods_ids, function ($value) use ($record, &$drop_num) {
            if ($drop_num === 0 && $value == $record->exchange_group_id . '-' . $record->goods_id) {
                $drop_num += 1;
                return false;
            }
            return true;

        });
        unset($drop_num);
        $new_goods_ids = array_values($new_goods_ids);

        if (count($new_goods_ids) == count($goods_ids)) {
            throw new MyException('兑换记录异常，重置失败！', SysCode::SYSTEM_ERROR);
        }

        return $new_goods_ids;
    }

    protected function get_available($exchange_batch, $exchange_detail, $exchange_goods_ids, $goods_id, $group)
    {
        if ($exchange_detail->status == ExchangeDetail::STATUS_EXCHANGE_COMPLATE) return false;

        if ($group->status != SysCode::COMMON_STATUS_1) return false;

        if (empty($exchange_goods_ids)) return true;

        $arr = explode('-', $exchange_goods_ids[0]);

        if ($group->id != $arr[0]) return false;

        return $group->allow_repeat || !in_array($group->id . '-' . $goods_id, $exchange_goods_ids);
    }

    public function get_remain_times($exchange_batch, $exchange_detail, $group)
    {
        $exchange_goods_ids = array_filter(explode(',', $exchange_detail->exchanged_goods));

        if ($group && is_int($group)) {
            foreach ($exchange_batch->exchange_groups as $item) {
                if ($item->id == $group) {
                    $group = $item;
                    break;
                }
            }
        }

        if (empty($exchange_goods_ids)) return $group->exchange_times;

        $arr = explode('-', $exchange_goods_ids[0]);

        if ($group->id != $arr[0]) return 0;

        return $group->exchange_times - count($exchange_goods_ids);
    }
}
