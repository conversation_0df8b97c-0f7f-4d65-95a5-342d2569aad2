<?php

namespace App\Logic\Exchange;

use App\Exceptions\MyException;
use App\Http\Controllers\Api\SysCode\SysCode;
use App\Models\ExchangeBatch;
use App\Models\ExchangeDetail;
use App\Models\ExchangeGroup;

/**
 * 礼品卡，只能兑换一次
 * @date: 2024/10/25
 */
class LiPinKa extends Base
{
    protected function get_detail_update_for_exchange($exchange_batch, $exchange_detail, $exchange_group, $goods_id)
    {
        $exchanged_goods = empty($exchange_detail->exchanged_goods) ? [] : explode(',', $exchange_detail->exchanged_goods);

        if ($exchanged_goods) {
            throw new MyException(SysCode::EXCHANGE_LOG_3020);
        }

        //验证该兑换码是否包含了该商品。
        if (!in_array($goods_id, $exchange_batch->get_goods_ids(true))) {
            throw new MyException(SysCode::EXCHANGE_GOODS_NOT_PERMIT);
        }

        $exchanged_goods[] = $goods_id;

        $update_date = [
            "exchanged_goods" => implode(',', $exchanged_goods),
            "status"          => ExchangeDetail::STATUS_EXCHANGE_COMPLATE,
        ];

        return $update_date;
    }

    protected function get_new_exchange_goods_for_rollback($detail, $record)
    {
        return [];
    }

    //商品是否可以兑换
    protected function get_available($exchange_batch, $exchange_detail, $exchange_goods_ids, $goods_id, $group)
    {
        if ($exchange_detail->status == ExchangeDetail::STATUS_EXCHANGE_COMPLATE) return false;

        return $exchange_detail->exchanged_times < 1;
    }

    public function get_remain_times($exchange_batch, $exchange_detail, $group = null)
    {
        return $exchange_batch->max_times - $exchange_detail->exchanged_times;
    }

}
