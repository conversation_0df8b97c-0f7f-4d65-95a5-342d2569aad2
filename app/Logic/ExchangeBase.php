<?php

namespace App\Logic;

use App\Libraries\OrderUtils;
use App\Models\ExchangeBatch;
use App\Models\ExchangeDetail;
use App\Models\ExchangeGoods;
use App\Models\ExchangeRecord;
use App\Models\Goods;
use Carbon\Carbon;
use Encore\Admin\Form\Field\Datetime;
use Exception;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Api\SysCode\SysCode;
use App\Exceptions\MyException;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use League\Flysystem\NotSupportedException;

//兑换码逻辑。抽象整理
class ExchangeBase extends BaseLogic
{
    protected $exchange_detail;
    protected $exchange_batch;

    public function __construct()
    {
        $this->single_goods_rdr_url = '/blue/detail.html?id={id}'; //单品跳转地址
        $this->jiazheng_rdr_url     = '/ms/jzindex.html?id={id}'; //家政服务跳转地址
        $this->goods_list_rdr_url   = '/blue/goods.html'; //商品列表跳转地址
    }

    protected $my_syscode = [
        SysCode::CARD_PWD_ERROR    => '兑换码错误',
        SysCode::CARD_PWD_EXPIRED  => '兑换码已过期',
        SysCode::PARAMS_ERROR      => '兑换码或手机号错误',
        SysCode::EXCHANGE_LOG_3020 => '此兑换码已被使用。',
    ];

    //默认登录方法。
    public function login($activity_id = null)
    {
        $this->exchange_login();
    }


    /**
     * 兑换码登录
     * @param bool $isVerifyCanExchange 是否验证兑换资格
     * @param int $activity_user_id 登录用户id，如果为0，则设置为兑换码的id
     * @param array $extra 存储到session扩展字段的数据
     * @throws MyException
     *                                  Date: 11/10/21
     */
    public function exchange_login($isVerifyCanExchange = true, $activity_user_id = 0, $extra = [])
    {
        $validator = Validator::make(request()->all(), [
            'card_pwd' => 'required|min:6|max:32',
        ]);

        if ($validator->fails()) {
            throw new MyException($this->getMsgByCode(SysCode::CARD_PWD_ERROR), SysCode::CARD_PWD_ERROR);
        }

        $goods   = null;
        $rdr_url = '';

        $this->verifyActifity(false);

        $this->exchange_detail = ExchangeDetail::where([
            'code' => str_replace(' ', '', request('card_pwd')),  //去掉误输入的空格
            //'enable' => ExchangeDetail::ENABLE_YES,//因核销的单独返回错误码，注释掉该行，然后在下面判断。2022-10-29
        ])->get()->first();

        //核销的单独提示，主要针对明苑天猫券 2022-10-29
        if (static::class == 'App\Logic\TianMao' && $this->exchange_detail && $this->exchange_detail->enable == ExchangeDetail::ENABLE_HEXIAO) {
            throw new MyException($this->getMsgByCode(SysCode::EXCHANGE_WRONG_MOBILE), SysCode::EXCHANGE_WRONG_MOBILE);
        }

        if (!$this->exchange_detail || $this->exchange_detail->enable != ExchangeDetail::ENABLE_YES) {
            throw new MyException($this->getMsgByCode(SysCode::CARD_PWD_ERROR), SysCode::CARD_PWD_ERROR);
        }

        $this->exchange_batch = $this->exchange_detail->exchange_batch;

        //批次禁用
        if ($this->exchange_batch->status == 0) {
            throw new MyException($this->getMsgByCode(SysCode::CARD_PWD_ERROR), SysCode::CARD_PWD_ERROR);
        }

        //如果不是该活动的兑换码，提示兑换码错误。
        if ($this->activity_id != $this->exchange_batch->activity_id) {
            throw new MyException($this->getMsgByCode(SysCode::CARD_PWD_ERROR), SysCode::CARD_PWD_ERROR);
        }

        $data = [
            'exchange_state' => $this->exchange_detail->status . '',
            'endtime'        => $this->exchange_detail->endtime,
        ];

        //兑换，查出商品信息。
        $goods_info = DB::table('exchange_goods')
            ->leftJoin('goods', 'exchange_goods.goods_id', '=', 'goods.id')
            ->leftJoin('goods_details', 'exchange_goods.goods_id', '=', 'goods_details.goods_id')
            ->where(['exchange_goods.exchange_batch_id' => $this->exchange_detail->exchange_batch_id])
            ->select(['exchange_goods.goods_id', 'goods.goods_name', 'goods.goods_type', 'goods.goods_show_img', 'goods.pre_verify_type', 'goods_details.goods_imgs', 'goods_details.goods_desc'])
            ->orderBy('exchange_goods.sort')
            ->get();

        if (count($goods_info) == 1) {
            $goods = $goods_info->first();
            if ($goods->goods_type == SysCode::GOODS_TYPE_6) {
                $data['rdr_url'] = str_replace('{id}', $goods->goods_id, $this->jiazheng_rdr_url);
            } else {
                $data['rdr_url'] = str_replace('{id}', $goods->goods_id, $this->single_goods_rdr_url);
            }

            $data['goods_id']        = $goods->goods_id;
            $data['goods_name']      = $goods->goods_name;
            $data['goods_type']      = $goods->goods_type;
            $data['pre_verify_type'] = $goods->pre_verify_type;//充值验证类型。0-无，1-短信验证码(京东），2-淘宝手机号充值
            $data['goods_show_img']  = getImgUrl($goods->goods_show_img);
            $goods_imgs              = json_decode($goods->goods_imgs, true);
            if (!empty($goods_imgs)) {
                $data['goods_img'] = getImgUrl($goods_imgs[0]);
            } else {
                $data['goods_img'] = getImgUrl($goods->goods_show_img);
            }
            $data['goods_desc'] = $goods->goods_desc ?? '';
            $data['available']  = $this->getAvailableStatus($goods->goods_id, $this->exchange_detail, $this->exchange_batch);

        } else {
            $data['rdr_url'] = str_replace('{s}', $this->exchange_detail->code, $this->goods_list_rdr_url);
            $data['goods']   = [];
            foreach ($goods_info as $goods) {
                $item       = [
                    'goods_id'        => $goods->goods_id,
                    'goods_name'      => $goods->goods_name,
                    'goods_type'      => $goods->goods_type,
                    'pre_verify_type' => $goods->pre_verify_type,//充值验证类型。0-无，1-短信验证码(京东），2-淘宝手机号充值
                    'goods_show_img'  => getImgUrl($goods->goods_show_img),
                ];
                $goods_imgs = json_decode($goods->goods_imgs, true);
                if (!empty($goods_imgs)) {
                    $item['goods_img'] = getImgUrl($goods_imgs[0]);
                } else {
                    $item['goods_img'] = getImgUrl($goods->goods_show_img);
                }
                $item['goods_desc'] = $goods->goods_desc ?? '';
                $item['available']  = $this->getAvailableStatus($goods->goods_id, $this->exchange_detail, $this->exchange_batch);

                $data['goods'][] = $item;
            }
        }

        //返回批次展示图和兑换说明 2024-08-06
        if ($this->exchange_batch->instr) {
            $data['show_img'] = getImgUrl($this->exchange_batch->instr->show_img);
            $data['instr']    = $this->exchange_batch->instr->instr;
        } else {
            $data['show_img'] = '';
            $data['instr']    = '';
        }

        if ($this->exchange_detail->expired) {
            throw new MyException($this->getMsgByCode(SysCode::CARD_PWD_EXPIRED), SysCode::CARD_PWD_EXPIRED, null, $data);
        }

        //如果只有查询权限，判断手机号和激活码是否匹配。
        if (!empty($extra['query_only']) && !empty($extra['mobile'])) {
            if ($this->exchange_detail->bind_by != $extra['mobile']) {
                throw new MyException('兑换码或手机号错误！', SysCode::PARAMS_ERROR);
            }
        }

        //如果已兑换，则判断登录手机号与兑换码绑定的手机号是否一致
        if (!empty($extra['mobile']) && !empty($this->exchange_detail->bind_by) && $this->exchange_detail->bind_by != $extra['mobile']) {
            throw new MyException($this->getMsgByCode(SysCode::EXCHANGE_WRONG_MOBILE), SysCode::EXCHANGE_WRONG_MOBILE);
        }

        if ($isVerifyCanExchange) {
            try {
                $this->verifyCanExchange();
            } catch (MyException $e) {
                if ($this->exchange_detail) {
                    $data['exchange_state'] = $this->exchange_detail->status . '';
                    $e->addData($data);
                }
                throw $e;
            }
        }

        $extra['exchange_detail_id'] = $this->exchange_detail->id;//把兑换码id写入扩展字段。
        $extra['exchange_code']      = $this->exchange_detail->code;//把兑换码写入扩展字段。
        $extra['exchange_endtime']   = $this->exchange_detail->endtime;//把兑换码过期时间写入扩展字段。

        //如果参数为0，则把兑换码表当成用户表。
        $this->doLogin($activity_user_id > 0 ? $activity_user_id : $this->exchange_detail->id, $this->activity_id, ['1_1' => [1, 1]], $extra);


        //如果商品为多个，则不通过该接口来拉取商品，而是通过商品列表接口来拉取。

        throw new MyException($this->getMsgByCode(SysCode::SUCCESS), SysCode::SUCCESS, null, $data);

    }

    //===================================================================
    // 以下接口登录成功后才能使用。

    public function verifyBeforeCreateOrder($goods_info, $group_info = null)
    {
        $goods_id         = $goods_info->id;
        $activity_user_id = session('user_info.id'); //该活动的用户id，即兑换码的id

        //活动信息及活动时间判断，在AuthController里不判断结束时间，改为这里判断。
        $this->verifyActifity();

        if ($this->isQueryOnly()) {
            throw new MyException($this->getMsgByCode(SysCode::EXCHANGE_RIGHT_ERROR), SysCode::EXCHANGE_RIGHT_ERROR);
        }

        //判断商品是否启用，并判断当前用户等级是否与商品等级相符。
        $prize_level_info = $this->verifyGoods($goods_info);

        //判断是否可兑换。
        $this->verifyCanExchange($goods_id);

        //返回 activity_user_id 在插入订单表的时候用到
        return ['activity_user_id' => $activity_user_id, 'prize_level' => $prize_level_info->prize_level, 'prize_level2' => $prize_level_info->prize_level2];

    }

    //处理自定义下订单
    // 入各个活动的兑换表
    public function handleAfterCreateOrder($activity_user_id, $order_data, $goods_info, $group_info = null)
    {
        if (!$this->exchange_detail) {
            $this->exchange_detail = ExchangeDetail::find(session('extra.exchange_detail_id'));
        }

        if (!$this->exchange_batch) {
            $this->exchange_batch = $this->exchange_detail->exchange_batch;
        }

        DB::table("exchange_records")->insert([
            'exchange_batch_id'  => $this->exchange_detail->exchange_batch_id,
            'exchange_group_id'  => request('exchange_group_id', 0),
            'exchange_detail_id' => $this->exchange_detail->id,
            'goods_id'           => $goods_info->id,
            'exchange_status'    => ExchangeRecord::EXCHANGE_STATUS_SUCC,
            'mobile'             => session('user.mobile', session('extra.mobile', '')),
            'order_id'           => $order_data['id'],
            "created_at"         => Carbon::now()->format('Y-m-d H:i:s'),
            "updated_at"         => Carbon::now()->format('Y-m-d H:i:s'),
        ]);

        $exchanged_goods   = empty($this->exchange_detail->exchanged_goods) ? [] : explode(',', $this->exchange_detail->exchanged_goods);
        $exchanged_goods[] = $goods_info->id;

        $update_date = [
            "exchanged_times"    => DB::raw('exchanged_times + 1'),
            "bind_by"            => session('user.mobile', session('extra.mobile', '')),
            "exchanged_goods"    => implode(',', $exchanged_goods),
            "last_exchange_time" => date("Y-m-d H:i:s"),
            "updated_at"         => date("Y-m-d H:i:s"),
        ];

        if (!$this->exchange_detail->bind_at) {
            $update_date['bind_at'] = date("Y-m-d H:i:s");
        }

        if ($this->exchange_batch->type == ExchangeBatch::TYPE_LIPINKA) {

            //礼品卡多选一。
            $update_date["status"] = ExchangeDetail::STATUS_EXCHANGE_COMPLATE;

        } elseif ($this->exchange_batch->type == ExchangeBatch::TYPE_JICIKA_REP || $this->exchange_batch->type == ExchangeBatch::TYPE_JICIKA) {

            if (count($exchanged_goods) > $this->exchange_batch->max_times) {
                //异常状况。这时卡状态应该是已完成，抛出异常。
                //这里更新数据库会被回滚，没用。
//                DB::table("exchange_details")
//                    ->where('id', $this->exchange_detail->id)
//                    ->update(['status' => ExchangeDetail::STATUS_EXCHANGE_COMPLATE]);
                throw new MyException($this->getMsgByCode(SysCode::EXCHANGE_LOG_3020), SysCode::EXCHANGE_LOG_3020); //领取过
            } elseif (count($exchanged_goods) == $this->exchange_batch->max_times) {
                $update_date["status"] = ExchangeDetail::STATUS_EXCHANGE_COMPLATE;
            } else {
                $update_date["status"] = ExchangeDetail::STATUS_EXCHANGEING;
            }

        } elseif ($this->exchange_batch->type == ExchangeBatch::TYPE_LIBAOKA) {

            //礼包卡（多组选一组），该组里的礼品都可以领取，但不能重复。
            //TODO:逻辑先不做。需要下单接口端配合增加组编号。数量判断还不太准确。

            $update_date["exchanged_group_id"] = request('exchange_group_id');
            if (count($exchanged_goods) > $this->exchange_batch->max_times) {
                throw new MyException($this->getMsgByCode(SysCode::EXCHANGE_LOG_3020), SysCode::EXCHANGE_LOG_3020); //领取过
            } elseif (count($exchanged_goods) == $this->exchange_batch->max_times) {
                $update_date["status"] = ExchangeDetail::STATUS_EXCHANGE_COMPLATE;
            } else {
                $update_date["status"] = ExchangeDetail::STATUS_EXCHANGEING;
            }
        }

        $is_update = DB::table("exchange_details")
            ->where('id', $this->exchange_detail->id)
            ->update($update_date);

        if (!$is_update) {
            throw new MyException($this->getMsgByCode(SysCode::SYSTEM_ERROR), SysCode::SYSTEM_ERROR);
        }

    }

    //如果传递了$goods_id参数，则判断商品是否满足兑换条件，否则只判断活动及用户状态
    public function isPermit($goods_id = null, $group_id = null)
    {
        //判断商品是否启用，并判断当前用户等级是否与商品等级相符。
        if (!is_null($goods_id)) {
            $this->verifyGoods($goods_id);
        }

        //判断用户是否已领取。
        $this->verifyCanExchange($goods_id);

        throw new MyException(SysCode::$resp_msg[SysCode::SUCCESS], SysCode::SUCCESS);
    }

    public function handlerBeforeGetGoodsList()
    {

        if (!$this->exchange_detail) {
            $this->exchange_detail = ExchangeDetail::find(session('extra.exchange_detail_id'));
        }

//        if (!$this->exchange_batch) {
//            $this->exchange_batch = $this->exchange_detail->exchange_batch;
//        }

        $goods = DB::table('exchange_goods')
            ->leftJoin('exchange_groups', 'exchange_goods.exchange_group_id', '=', 'exchange_groups.id')
            ->leftJoin('goods', 'exchange_goods.goods_id', '=', 'goods.id')
            ->where('exchange_goods.exchange_batch_id', $this->exchange_detail->exchange_batch_id)
            ->orderBy('exchange_goods.sort')
            ->select(['goods.id', 'goods.goods_show_img', 'goods.goods_name', 'goods.goods_type', 'goods.goods_attr', 'goods.advance_days', 'goods.service_time', 'exchange_goods.exchange_group_id', DB::raw('exchange_groups.sort group_sort'), 'exchange_groups.group_name'])
            ->get();

        //TODO: 增加活动礼品配置筛选。
//        $prize_info = DB::table('activity_prizes')->where([
//            'activity_id' => $this->activity_id
//        ])->get('goods_id');

        $ret = [];
        $goods->map(function ($value) use (&$ret) {

            if ($value->goods_show_img) {
                $value->goods_show_img = env('OSS_URL') . $value->goods_show_img;
            }
            //可用状态
            $value->available = $this->getAvailableStatus($value->id, $this->exchange_detail, $this->exchange_batch ?? $this->exchange_detail->exchange_batch);

            $ret[1][1][] = $value;
        });
        return $ret;
    }


    /**
     * 根据兑换卡状态、类型、兑换周期等配置，判断用户是否已领取或是否达到领取条件。
     * @return ExchangeDetail
     * @throws MyException
     */
    protected function verifyCanExchange($goods_id = null)
    {
        if (!$this->exchange_detail) {
            $this->exchange_detail = ExchangeDetail::find(session('extra.exchange_detail_id'));
        }

        //兑换码状态
        if (!$this->exchange_detail || $this->exchange_detail->enable != ExchangeDetail::ENABLE_YES) {
            throw new MyException($this->getMsgByCode(SysCode::CARD_PWD_ERROR), SysCode::CARD_PWD_ERROR);
        }

        //验证是否领取过
        if ($this->exchange_detail->status == ExchangeDetail::STATUS_EXCHANGE_COMPLATE) {
            throw new MyException($this->getMsgByCode(SysCode::EXCHANGE_LOG_3020), SysCode::EXCHANGE_LOG_3020); //领取过
        }

        $this->checkExpired();

        if (!$this->exchange_batch) {
            $this->exchange_batch = $this->exchange_detail->exchange_batch;
        }

        if ($this->exchange_batch->status == 0) {
            throw new MyException($this->getMsgByCode(SysCode::CARD_PWD_ERROR), SysCode::CARD_PWD_ERROR);
        }

        //验证该兑换码是否包含了该商品。
        if ($goods_id && !in_array($goods_id, $this->exchange_batch->get_goods_ids())) {

            throw new MyException(SysCode::$resp_msg[SysCode::EXCHANGE_GOODS_NOT_PERMIT], SysCode::EXCHANGE_GOODS_NOT_PERMIT);
        }

        $this->checkCycle();

        if ($goods_id) {
            $this->checkExchangeing($goods_id);
        }

    }


    /**
     * 判断兑换卡领取周期是否满足领取条件。
     * @throws MyException
     */
    protected function checkCycle()
    {

        if (!$this->exchange_batch || !$this->exchange_detail) {
            throw new MyException($this->getMsgByCode(SysCode::UNKNOW_ERROR), SysCode::UNKNOW_ERROR);
        }

        if ($this->exchange_batch->exchange_cycle == ExchangeBatch::EXCHANGE_CYCLE_MONTH) {
            if (date('Y-m', strtotime($this->exchange_detail->last_exchange_time)) == date('Y-m')) {
                throw new MyException(str_replace('{0}', '月', $this->getMsgByCode(SysCode::EXCHANGE_CYCLE_ERROR)), SysCode::EXCHANGE_CYCLE_ERROR);
            }
        } elseif ($this->exchange_batch->exchange_cycle == ExchangeBatch::EXCHANGE_CYCLE_WEEK) {
            if (date('W', strtotime($this->exchange_detail->last_exchange_time)) == date('W')) {
                throw new MyException(str_replace('{0}', '周', $this->getMsgByCode(SysCode::EXCHANGE_CYCLE_ERROR)), SysCode::EXCHANGE_CYCLE_ERROR);
            }
        } elseif ($this->exchange_batch->exchange_cycle == ExchangeBatch::EXCHANGE_CYCLE_DAY) {
            if (date('Y-m-d', strtotime($this->exchange_detail->last_exchange_time)) == date('Y-m-d')) {
                $msg = str_replace('本{0}', '今天', $this->getMsgByCode(SysCode::EXCHANGE_CYCLE_ERROR));
                $msg = str_replace('下{0}', '明天', $msg);
                throw new MyException($msg, SysCode::EXCHANGE_CYCLE_ERROR);
            }
        }
    }

    //检查兑换中的礼品卡的兑换资格
    protected function checkExchangeing($goods_id)
    {

        if (!$this->exchange_batch || !$this->exchange_detail) {
            throw new MyException($this->getMsgByCode(SysCode::UNKNOW_ERROR), SysCode::UNKNOW_ERROR);
        }

        if ($this->exchange_detail->status == ExchangeDetail::STATUS_EXCHANGEING) {

            //礼包卡需判断是否领取的已经领取的分组里的其它商品。

            //兑换码类型。1-礼品卡（多选一），2-计次卡（多选N，可多次选同一个商品），3-不重复计次卡（多选N，不能重复），4-礼包卡（多组选一组）
            if ($this->exchange_batch->type == ExchangeBatch::TYPE_LIPINKA) {

                //礼品卡多选一，不存在这个问题。

            } elseif ($this->exchange_batch->type == ExchangeBatch::TYPE_JICIKA_REP) {

                //可重复选择，也不存在这个问题。

            } elseif ($this->exchange_batch->type == ExchangeBatch::TYPE_JICIKA) {

                //不能重复选择同一商品，传了goods_id才能判断。
                if ($goods_id) {

                    $goods_ids = explode(',', $this->exchange_detail->exchanged_goods);
                    if (in_array($goods_id, $goods_ids)) {
                        throw new MyException($this->getMsgByCode(SysCode::EXCHANGE_GOODS_REPEAT), SysCode::EXCHANGE_GOODS_REPEAT);
                    }
                }

            } elseif ($this->exchange_batch->type == ExchangeBatch::TYPE_LIBAOKA) {

                //礼包卡（多组选一组），该组里的礼品都可以领取，但不能重复。
                //request('exchange_group_id')

                if (empty(request('exchange_group_id'))) {
                    throw new MyException($this->getMsgByCode(SysCode::PARAMS_ERROR) . '11', SysCode::PARAMS_ERROR);
                }

                if (request('exchange_group_id') != $this->exchange_detail->exchanged_group_id) {
                    throw new MyException($this->getMsgByCode(SysCode::EXCHANGE_GOODS_NOT_PERMIT), SysCode::EXCHANGE_GOODS_NOT_PERMIT);
                }

                $group_goods_ids = $this->exchange_batch->get_group_goods_ids();
                if (!array_key_exists(request('exchange_group_id'), $group_goods_ids) || !in_array($goods_id, $group_goods_ids[request('exchange_group_id')])) {
                    throw new MyException($this->getMsgByCode(SysCode::EXCHANGE_GOODS_NOT_PERMIT), SysCode::EXCHANGE_GOODS_NOT_PERMIT);
                }

                $goods_ids = explode(',', $this->exchange_detail->exchanged_goods);
                if (in_array($goods_id, $goods_ids)) {
                    throw new MyException($this->getMsgByCode(SysCode::EXCHANGE_GOODS_REPEAT), SysCode::EXCHANGE_GOODS_REPEAT);
                }

            }

        }
    }

    //检查卡是否过期
    protected function checkExpired()
    {
        if ($this->exchange_detail->expired == ExchangeDetail::EXPIRED_YES
            || time() > strtotime($this->exchange_detail->endtime . " 23:59:59")) {
            throw new MyException($this->getMsgByCode(SysCode::CARD_PWD_EXPIRED), SysCode::CARD_PWD_EXPIRED);
        }
    }

    /**
     * 判断商品是否启用，并判断当前用户等级是否与商品等级相符。
     * @param $goods_id int/object 商品id或商品信息Model
     * @return  activity_prizes
     * @throws MyException
     */
    protected function verifyGoods($goods_id)
    {

        if (is_object($goods_id)) {
            $goods_info = $goods_id;
            $goods_id   = $goods_id->id;
        } else {
            $goods_info = Goods::find($goods_id);
        }

        if (!$goods_info || $goods_info->status == SysCode::COMMON_STATUS_0) {
            throw new MyException(SysCode::$resp_msg[SysCode::GOOD_LISTS_EMPTY], SysCode::GOOD_LISTS_EMPTY);
        }

        $prize_info = DB::table('activity_prizes')->where([
            'goods_id'    => $goods_id,
            'activity_id' => $this->activity_id
        ])->first();

        if (!$prize_info) {
            throw new MyException(SysCode::$resp_msg[SysCode::NOT_CHANCE_3019], SysCode::NOT_CHANCE_3019);
        }

        if (!array_key_exists($prize_info->prize_level . "_" . $prize_info->prize_level2, session("user_info.prize_level"))) {
            throw new MyException(SysCode::$resp_msg[SysCode::NOT_CHANCE_3019], SysCode::NOT_CHANCE_3019);
        }

        return $prize_info;

    }

    //判断是否领取过
    protected function isExchanged()
    {

        if (!$this->exchange_detail) {
            $this->exchange_detail = ExchangeDetail::find(session('extra.exchange_detail_id'));
            if (!$this->exchange_detail) {
                throw new MyException(SysCode::NOT_LOGIN);
            }
        }

        //验证是否领取过
        if (in_array($this->exchange_detail->status, [ExchangeDetail::STATUS_EXCHANGE_COMPLATE, ExchangeDetail::STATUS_EXCHANGEING])) {
            return true;
        } else {
            return false;
        }
    }

    //判断是否全部领取完成
    protected function isExchangeComplate()
    {
        if (!$this->exchange_detail) {
            $this->exchange_detail = ExchangeDetail::find(session('extra.exchange_detail_id'));
            if (!$this->exchange_detail) {
                throw new MyException(SysCode::NOT_LOGIN);
            }
        }

        //验证是否领取过
        if (in_array($this->exchange_detail->status, [ExchangeDetail::STATUS_EXCHANGE_COMPLATE])) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 获取可兑换状态
     * @param int $goods_id
     * @param ExchangeDetail $exchange_detail
     * @param ExchangeBatch $exchange_batch
     * @return bool
     */
    protected function getAvailableStatus($goods_id, $exchange_detail, $exchange_batch = null)
    {
        if ($exchange_detail->status == ExchangeDetail::STATUS_EXCHANGE_COMPLATE
            || $this->exchange_detail->expired == ExchangeDetail::EXPIRED_YES
            || time() > strtotime($this->exchange_detail->endtime . " 23:59:59")
        ) {
            return false;
        }

        if (!$exchange_batch) {
            $exchange_batch = $exchange_detail->exchange_batch;
        }

        $max_times       = $exchange_batch->max_times;
        $type            = $exchange_batch->type;
        $exchanged_times = $exchange_detail->exchanged_times;
        $exchanged_goods = explode(',', $exchange_detail->exchanged_goods);

        //兑换码类型。1-礼品卡（多选一），2-计次卡（多选N，可多次选同一个商品），3-不重复计次卡（多选N，不能重复），4-礼包卡（多组选一组）
        if ($type == ExchangeBatch::TYPE_LIPINKA) {
            return true;
        } elseif ($type == ExchangeBatch::TYPE_JICIKA_REP) {
            return $exchanged_times < $max_times;
        } elseif ($type == ExchangeBatch::TYPE_JICIKA) {
            return $exchanged_times < $max_times && !in_array($goods_id, $exchanged_goods);
        } elseif ($type == ExchangeBatch::TYPE_LIBAOKA) {
            throw new NotSupportedException('暂不支持该类型');
        } else {
            throw new NotSupportedException('暂不支持该类型');
        }
    }

}
