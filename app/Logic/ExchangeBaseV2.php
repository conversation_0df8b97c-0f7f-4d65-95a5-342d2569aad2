<?php

namespace App\Logic;

use App\Models\ExchangeBatch;
use App\Models\ExchangeDetail;
use App\Models\ExchangeGroup;
use App\Models\Goods;
use App\Models\Order;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Api\SysCode\SysCode;
use App\Exceptions\MyException;
use Illuminate\Support\Facades\Validator;

//兑换码逻辑。抽象整理，支持礼包卡和分组计次卡
class ExchangeBaseV2 extends BaseLogic
{
    protected $exchange_detail;
    protected $exchange_batch;
    protected $exchange_group;
    protected $order_cancel_result = -1;//订单取消结果

    public function __construct()
    {
        $this->single_goods_rdr_url = '/blue/detail.html?id={id}'; //单品跳转地址
        $this->jiazheng_rdr_url     = '/ms/jzindex.html?id={id}'; //家政服务跳转地址
        $this->goods_list_rdr_url   = '/blue/goods.html'; //商品列表跳转地址
    }

    protected $my_syscode = [
        SysCode::CARD_PWD_ERROR    => '兑换码错误',
        SysCode::CARD_PWD_EXPIRED  => '兑换码已过期',
        SysCode::PARAMS_ERROR      => '兑换码或手机号错误',
        SysCode::EXCHANGE_LOG_3020 => '此兑换码已被使用。',
    ];

    //默认登录方法。
    public function login($activity_id = null)
    {
        $this->exchange_login();
    }

    /**
     * 兑换码登录
     * @param bool $isVerifyCanExchange 是否验证兑换资格
     * @param int $activity_user_id 登录用户id，如果为0，则设置为兑换码的id
     * @param array $extra 存储到session扩展字段的数据
     * @throws MyException
     * @Date: 11/10/21
     */
    public function exchange_login($isVerifyCanExchange = true, $activity_user_id = 0, $extra = [])
    {
        $validator = Validator::make(request()->all(), [
            'card_pwd' => 'required|min:6|max:32',
        ]);

        if ($validator->fails()) {
            throw new MyException($this->getMsgByCode(SysCode::CARD_PWD_ERROR), SysCode::CARD_PWD_ERROR);
        }

        $this->verifyActifity(false);

        $this->exchange_detail = ExchangeDetail::where([
            'code' => str_replace(' ', '', request('card_pwd')),  //去掉误输入的空格
            //'enable' => ExchangeDetail::ENABLE_YES,//因核销的单独返回错误码，注释掉该行，然后在下面判断。2022-10-29
        ])->get()->first();

        //核销的单独提示，主要针对明苑天猫券 2022-10-29
        if (static::class == 'App\Logic\TianMao' && $this->exchange_detail && $this->exchange_detail->enable == ExchangeDetail::ENABLE_HEXIAO) {
            throw new MyException($this->getMsgByCode(SysCode::EXCHANGE_WRONG_MOBILE), SysCode::EXCHANGE_WRONG_MOBILE);
        }

        if (!$this->exchange_detail || $this->exchange_detail->enable != ExchangeDetail::ENABLE_YES) {
            throw new MyException($this->getMsgByCode(SysCode::CARD_PWD_ERROR), SysCode::CARD_PWD_ERROR);
        }

        $this->exchange_batch = $this->exchange_detail->exchange_batch;

        //批次禁用
        if ($this->exchange_batch->status == 0) {
            throw new MyException($this->getMsgByCode(SysCode::CARD_PWD_ERROR), SysCode::CARD_PWD_ERROR);
        }

        //如果不是该活动的兑换码，提示兑换码错误。
        if ($this->activity_id != $this->exchange_batch->activity_id) {
            throw new MyException($this->getMsgByCode(SysCode::CARD_PWD_ERROR), SysCode::CARD_PWD_ERROR);
        }

        $data = [
            'exchange_state' => $this->exchange_detail->status . '',
            'endtime'        => $this->exchange_detail->endtime,
        ];

        //返回批次展示图和兑换说明 2024-08-06
        if ($this->exchange_batch->instr) {
            $data['show_img'] = getImgUrl($this->exchange_batch->instr->show_img);
            $data['instr']    = $this->exchange_batch->instr->instr;
        } else {
            $data['show_img'] = '';
            $data['instr']    = '';
        }

        $goods_list = $this->getGoodsList();
        if (!empty($goods_list['goods']) && count($goods_list['goods']) == 1) {
            //单个商品
            $data            = array_merge($data, $goods_list['goods'][0]);
            $data['rdr_url'] = $goods_list['rdr_url'];
            if (array_key_exists('exchange_times', $goods_list)) {
                $data['exchange_times'] = $goods_list['exchange_times'];
            }
            if (array_key_exists('remain_times', $goods_list)) {
                $data['remain_times'] = $goods_list['remain_times'];
            }
        } else {
            $data = array_merge($data, $goods_list);
        }

        if ($this->exchange_detail->expired) {
            throw new MyException($this->getMsgByCode(SysCode::CARD_PWD_EXPIRED), SysCode::CARD_PWD_EXPIRED, null, $data);
        }

        //如果只有查询权限，判断手机号和激活码是否匹配。
        if (!empty($extra['query_only']) && !empty($extra['mobile'])) {
            if ($this->exchange_detail->bind_by != $extra['mobile']) {
                throw new MyException('兑换码或手机号错误！', SysCode::PARAMS_ERROR);
            }
        }

        //如果已兑换，则判断登录手机号与兑换码绑定的手机号是否一致
        if (!empty($extra['mobile']) && !empty($this->exchange_detail->bind_by) && $this->exchange_detail->bind_by != $extra['mobile']) {
            throw new MyException($this->getMsgByCode(SysCode::EXCHANGE_WRONG_MOBILE), SysCode::EXCHANGE_WRONG_MOBILE);
        }

        if ($isVerifyCanExchange) {
            try {
                $this->verifyCanExchange();
            } catch (MyException $e) {
                if ($this->exchange_detail) {
                    $e->addData($data);
                }
                throw $e;
            }
        }

        $extra['exchange_detail_id'] = $this->exchange_detail->id;//把兑换码id写入扩展字段。
        $extra['exchange_code']      = $this->exchange_detail->code;//把兑换码写入扩展字段。
        $extra['exchange_endtime']   = $this->exchange_detail->endtime;//把兑换码过期时间写入扩展字段。

        //如果参数为0，则把兑换码表当成用户表。
        $this->doLogin($activity_user_id > 0 ? $activity_user_id : $this->exchange_detail->id, $this->activity_id, ['1_1' => [1, 1]], $extra);

        throw new MyException($this->getMsgByCode(SysCode::SUCCESS), SysCode::SUCCESS, null, $data);
    }

    //===================================================================
    // 以下接口登录成功后才能使用。

    public function verifyBeforeCreateOrder($goods_info, $group_info = null)
    {
        $activity_user_id = session('user_info.id'); //该活动的用户id，即兑换码的id

        //活动信息及活动时间判断，在AuthController里不判断结束时间，改为这里判断。
        $this->verifyActifity();

        if ($this->isQueryOnly()) {
            throw new MyException($this->getMsgByCode(SysCode::EXCHANGE_RIGHT_ERROR), SysCode::EXCHANGE_RIGHT_ERROR);
        }

        //判断商品是否启用，并判断当前用户等级是否与商品等级相符(X，不再判断等级是否相符，均为1级)。
        $this->verifyGoods($goods_info);

        //判断是否可兑换。
        $this->verifyCanExchange($goods_info, $group_info);

        //返回 activity_user_id 在插入订单表的时候用到
        return ['activity_user_id' => $activity_user_id, 'prize_level' => 1, 'prize_level2' => 1];
    }

    //处理自定义下订单
    // 入各个活动的兑换表
    public function handleAfterCreateOrder($activity_user_id, $order_data, $goods_info, $group_info = null)
    {
        if (!$this->exchange_detail) {
            $this->exchange_detail = ExchangeDetail::find(session('extra.exchange_detail_id'));
        }

        if (!$this->exchange_batch) {
            $this->exchange_batch = $this->exchange_detail->exchange_batch;
        }

        $mobile = session('user.mobile', session('extra.mobile', ''));

        ExchangeBatch::factory($this->exchange_batch->type)->exchange($this->exchange_batch, $this->exchange_detail, $group_info, $goods_info->id, $activity_user_id, $mobile, $order_data['id']);
    }

    /**
     * 取消订单后执行的操作。主要是对各活动下单频次限制条件的更新
     * @param Order $order 订单信息
     * @date: 2024/11/8
     */
    public function handlerAfterCancelOrder($order)
    {
        $this->exchange_detail = ExchangeDetail::find($order->exchange_detail_id);
        if (!$this->exchange_detail) {
            throw new MyException('订单异常：订单对应的兑换码不存在，请联系客服处理！', SysCode::SYSTEM_ERROR);
        }

        $this->exchange_batch = $this->exchange_detail->exchange_batch;

        $this->order_cancel_result = ExchangeBatch::factory($this->exchange_batch->type)->order_cancel($order, $this->exchange_detail);
    }

    //如果传递了$goods_id参数，则判断商品是否满足兑换条件，否则只判断活动及用户状态
    public function isPermit($goods_id = null, $group_id = null)
    {
        //判断商品是否启用，并判断当前用户等级是否与商品等级相符。
        if ($goods_id) {
            $goods_info = $this->verifyGoods($goods_id);
        }

        if ($group_id) {
            $group_info = ExchangeGroup::find($group_id);
            if (empty($group_info) || $group_info->status != SysCode::COMMON_STATUS_1) {
                throw new MyException(SysCode::EXCHANGE_GOODS_NOT_PERMIT); //分组信息不存在或禁用
            }
        }

        //判断用户是否已领取。
        $this->verifyCanExchange($goods_info ?? null, $group_info ?? null);

        throw new MyException(SysCode::$resp_msg[SysCode::SUCCESS], SysCode::SUCCESS);
    }

    public function handlerBeforeGetGoodsList()
    {
        if (!$this->exchange_detail) {
            $this->exchange_detail = ExchangeDetail::find(session('extra.exchange_detail_id'));
        }

        if (!$this->exchange_batch) {
            $this->exchange_batch = $this->exchange_detail->exchange_batch;
        }

        $goods_list = $this->getGoodsList();
        unset($goods_list['rdr_url']);
        //$ret       = $goods_list;
        if (array_key_exists('exchange_times', $goods_list)) {
            $ret['exchange_times'] = $goods_list['exchange_times'];
        }
        if (array_key_exists('remain_times', $goods_list)) {
            $ret['remain_times'] = $goods_list['remain_times'];
        }
        $ret[1][1] = $goods_list['goods'] ?? $goods_list['groups'];

        return $ret;
    }

    /**
     * 验证商品有效性（目前只判断商品状态）
     * @param Goods|int $goods_id
     * @return Goods
     * @throws MyException
     * @date: 2024/11/7
     */
    protected function verifyGoods($goods_id)
    {
        if (is_object($goods_id)) {
            $goods_info = $goods_id;
            $goods_id   = $goods_id->id;
        } else {
            $goods_info = Goods::leftJoin('activity_prizes', function (\Illuminate\Database\Query\JoinClause $join) {
                $join->on('goods.id', '=', 'activity_prizes.goods_id')
                    ->where('activity_prizes.activity_id', $this->activity_id);
            })
                ->where('goods.id', $goods_id)
                ->where('activity_prizes.status', SysCode::COMMON_STATUS_1)
                ->select(['goods.*', 'activity_prizes.prize_level', 'activity_prizes.prize_level2'])
                ->first();
        }

        if (!$goods_info || $goods_info->status == SysCode::COMMON_STATUS_0) {
            throw new MyException(SysCode::$resp_msg[SysCode::GOOD_LISTS_EMPTY], SysCode::GOOD_LISTS_EMPTY);
        }

        //商品是否在活动商品列表，在下单接口OrderController里判断，此处只判断跟兑换码相关的逻辑

        return $goods_info;
    }

    /**
     * 根据兑换卡状态、类型、兑换周期等配置，判断用户是否已领取或是否达到领取条件。
     * @throws MyException
     */
    protected function verifyCanExchange($goods_info = null, $group_info = null)
    {
        if (!$this->exchange_detail) {
            $this->exchange_detail = ExchangeDetail::find(session('extra.exchange_detail_id'));
        }

        if (!$this->exchange_batch) {
            $this->exchange_batch = $this->exchange_detail->exchange_batch;
        }

        if ($goods_info) {
            ExchangeBatch::factory($this->exchange_batch->type)->can_exchange($this->exchange_batch, $this->exchange_detail, $group_info, $goods_info->id);
        }
    }

    /**
     * 获取商品列表，有分组(groups)和没分组(goods)，返回的键不一样
     * @return array ['rdr_url'=>'', 'goods'=>[]] or ['rdr_url'=>'', 'groups'=>[]]
     * @throws MyException
     * @date: 2024/11/6
     */
    protected function getGoodsList()
    {
        /** @var array $goods_list */
        $goods_list = DB::table('exchange_goods')
            ->leftJoin('goods', 'exchange_goods.goods_id', '=', 'goods.id')
            ->leftJoin('goods_details', 'exchange_goods.goods_id', '=', 'goods_details.goods_id')
            ->leftJoin('activity_prizes', function (\Illuminate\Database\Query\JoinClause $join) {
                $join->on('exchange_goods.goods_id', '=', 'activity_prizes.goods_id')
                    ->where('activity_prizes.activity_id', $this->activity_id);
            })
            ->where([
                'exchange_goods.exchange_batch_id' => $this->exchange_detail->exchange_batch_id,
                'exchange_goods.status'            => 1,
                'goods.status'                     => 1,
                'activity_prizes.status'           => 1,
            ])
            ->select(['goods.id', 'exchange_goods.goods_id', 'exchange_goods.exchange_group_id', 'goods.goods_name', 'goods.goods_type', 'goods.goods_attr', 'goods.goods_show_img', 'goods.pre_verify_type', 'goods_details.goods_imgs', 'goods_details.goods_desc', 'goods.service_time', 'goods.advance_days', 'activity_prizes.prize_level', 'activity_prizes.prize_level2'])
            ->orderBy('exchange_goods.sort')
            ->get()
            ->map(function ($item) {
                return (array)$item;
            })
            ->toArray();

        $cardInst = ExchangeBatch::factory($this->exchange_batch->type); //卡类型实现类

        $groups = [];
        if (ExchangeBatch::hasGroups($this->exchange_batch->type)) {
            foreach ($this->exchange_batch->exchange_groups as $group) {
                $groups[$group->id] = $group;
            }
        }
        $goods_list = $cardInst->with_available($this->exchange_batch, $this->exchange_detail, $goods_list, $groups);

        foreach ($goods_list as $idx => $goods) {
            $goods_list[$idx]['goods_show_img'] = $goods['goods_show_img'] = getImgUrl($goods['goods_show_img']);

            $goods_imgs = json_decode($goods['goods_imgs'], true);
            if (!empty($goods_imgs)) {
                $goods_list[$idx]['goods_img'] = $goods['goods_img'] = getImgUrl($goods_imgs[0]);
            } else {
                $goods_list[$idx]['goods_img'] = $goods['goods_img'] = $goods['goods_show_img'];
            }
            $goods_list[$idx]['goods_desc'] = $goods['goods_desc'] = $goods['goods_desc'] ?? '';
            unset($goods_list[$idx]['goods_imgs'], $goods['goods_imgs']);

            if ($groups) {
                $goods_list[$idx]['id'] = $goods_list[$idx]['goods_id'] = $goods['id'] = $goods['goods_id'] = $goods['exchange_group_id'] . '-' . $goods['goods_id'];
                if (empty($groups[$goods['exchange_group_id']]->goods)) $groups[$goods['exchange_group_id']]->goods = new Collection();
                $groups[$goods['exchange_group_id']]->goods->add($goods);
            }
        }

        $data = [];

        if (ExchangeBatch::hasGroups($this->exchange_batch->type)) {
            $data['rdr_url'] = str_replace('{s}', $this->exchange_detail->code, $this->goods_list_rdr_url);
            foreach ($groups as $group) {
                if ($group->status == 1) {
                    $data_group       = [
                        'group_name'     => $group->group_name,
                        'exchange_times' => $group->exchange_times,
                        'remain_times'   => $cardInst->get_remain_times($this->exchange_batch, $this->exchange_detail, $group),
                        'goods'          => $group->goods->toArray(),
                    ];
                    $data['groups'][] = $data_group;
                }
            }
        } else {
            if (count($goods_list) == 1) {
                $goods = $goods_list[0];
                if ($goods['goods_type'] == SysCode::GOODS_TYPE_6) {
                    $data['rdr_url'] = str_replace('{id}', $goods['goods_id'], $this->jiazheng_rdr_url);
                } else {
                    $data['rdr_url'] = str_replace('{id}', $goods['goods_id'], $this->single_goods_rdr_url);
                }
            } else {
                $data['rdr_url'] = str_replace('{s}', $this->exchange_detail->code, $this->goods_list_rdr_url);
            }
            $data['exchange_times'] = $this->exchange_batch->max_times;
            $data['remain_times']   = $cardInst->get_remain_times($this->exchange_batch, $this->exchange_detail);
            $data['goods']          = $goods_list;
        }

        return $data;
    }
}
