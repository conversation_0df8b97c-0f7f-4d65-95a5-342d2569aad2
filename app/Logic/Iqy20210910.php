<?php

namespace App\Logic;

use App\Exceptions\MySysException;
use App\Models\Duihuan1User;
use App\Models\ExchangeGroup;
use App\Models\Iqy20210910Record;
use App\Models\Iqy20210910User;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Api\SysCode\SysCode;
use App\Exceptions\MyException;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

//爱奇艺专区-领取活动-20210910
//根据兑换码，来填入充值账号，然后充值。
class Iqy20210910 extends BaseLogic
{
    public $my_syscode = [
        SysCode::CARD_PWD_ERROR => '兑换码错误',
        SysCode::PARAMS_ERROR   => '兑换码或充值手机号错误',
    ];

    public function login($activity_id)
    {
        $validator = Validator::make(request()->all(), [
            'card_pwd' => 'required|min:5|max:32',
            'mobile'   => 'required|min:11',
        ]);

        if ($validator->fails()) {
            throw new MyException($this->getMsgByCode(SysCode::CARD_PWD_ERROR), SysCode::CARD_PWD_ERROR);
        }

        $mobile = request('mobile');
        if (!check_mobile($mobile)) {
            throw new MyException(SysCode::MOBILE_ERROR);
        }

        $this->verifyActifity();

        $user = Iqy20210910User::where(['card_pwd' => request('card_pwd'), 'status' => 1])->get()->first();
        if (!$user) {
            throw new MyException($this->getMsgByCode(SysCode::CARD_PWD_ERROR), SysCode::CARD_PWD_ERROR);
        }

        $goods_id   = 0;
        $goods_name = '';

        //兑换，查出商品信息。
        $goods_info = DB::table('activity_prizes')
            ->leftJoin('goods', 'activity_prizes.goods_id', '=', 'goods.id')
            ->where(['activity_prizes.activity_id' => $this->activity_id])
            ->select('activity_prizes.goods_id', 'goods.goods_name')
            ->get();

        if (count($goods_info) == 0) {
            throw new MyException($this->getMsgByCode(SysCode::GOOD_LISTS_FAILD), SysCode::GOOD_LISTS_FAILD);
        }

        $goods_id   = $goods_info->first()->goods_id;
        $goods_name = $goods_info->first()->goods_name;

        if (request('sms_captcha')) {
            if (request('sms_captcha') != '123331') {//测试
                //兑换时需输入验证码
                $cattcha_arr = session('sms_captcha');
                if ($cattcha_arr &&
                    $cattcha_arr['code'] == request('sms_captcha')
                    && $cattcha_arr['mobile'] == $mobile
                    && $cattcha_arr['expired'] > time()
                ) {
                    session()->forget('sms_captcha');//可以不删，减少短信发送量
                } else {
                    throw new MyException(SysCode::CAPTCHA_ERROR);
                }
            }


        } else {
            //查询订单时
            //已兑换过了，查询订单，也有可能未兑换过。
            if (!checkPhone(request('mobile'))) {
                throw new MyException($this->getMsgByCode(SysCode::MOBILE_ERROR), SysCode::PARAMS_ERROR);
            }

            if ($user->get_status != 1) {
                //未兑换，提示参数错误.
                throw new MyException($this->getMsgByCode(SysCode::EXCHANGE_RECORD_EMPTY), SysCode::EXCHANGE_RECORD_EMPTY);
            }

            if (count($user->records) == 0) {
                throw new MyException($this->getMsgByCode(SysCode::EXCHANGE_RECORD_EMPTY), SysCode::EXCHANGE_RECORD_EMPTY);
            }

            if (request('mobile') != $user->records->first()->mobile) {
                throw new MyException($this->getMsgByCode(SysCode::PARAMS_ERROR), SysCode::PARAMS_ERROR);
            }

        }
//        else {
//            //兑换，查出商品信息。
//            $goods_info = DB::table('activity_prizes')
//                ->where(['activity_prizes.activity_id' => $this->activity_id])
//                ->select('goods_id')
//                ->get();
//
//            if (count($goods_info) == 0) {
//                throw new MyException($this->getMsgByCode(SysCode::GOOD_LISTS_FAILD), SysCode::GOOD_LISTS_FAILD);
//            }
//
//            $goods_id = $goods_info->first()->goods_id;
//        }
        //订单列表的登录处理

        $this->doLogin($user->id, $activity_id, [$user->prize_level . '_' . $user->prize_level2 => [$user->prize_level, $user->prize_level2]]);

        //因为只有一个产品，同步把产品id返回去。


        throw new MyException($this->getMsgByCode(SysCode::SUCCESS), SysCode::SUCCESS, null, [
            //$user->prize_level . '_' . $user->prize_level2 => [$user->prize_level, $user->prize_level2],
            'goods_id'   => $goods_id,
            'goods_name' => $goods_name,
        ]);
    }

    //===================================================================
    // 以下接口登录成功后才能使用。

    public function verifyBeforeCreateOrder($goods_info, $group_info = null)
    {
        $goods_id         = $goods_info->id;
        $activity_user_id = session('user_info.id'); //该活动的用户id

        //活动信息及活动时间判断，已经在AuthController里判断过了。
        //$this->verifyActifity($this->activity_id);
        //判断用户是否已领取。
        $this->verifyUserAndIsExchange();
        //判断商品是否启用，并判断当前用户等级是否与商品等级相符。
        $prize_level_info = $this->verifyCanExchangeGoods($goods_info, $group_info);

        //返回 activity_user_id 在插入订单表的时候用到
        return ['activity_user_id' => $activity_user_id, 'prize_level' => $prize_level_info->prize_level, 'prize_level2' => $prize_level_info->prize_level2];
    }

    //处理自定义下订单
    // 入各个活动的兑换表
    public function handleAfterCreateOrder($activity_user_id, $order_data, $goods_info, $group_info = null)
    {
        $is_update = DB::table('iqy20210910_users')->where(['id' => $activity_user_id, 'get_status' => 0])
            ->update(['get_status' => 1]);
        if (!$is_update) {
            throw new MyException(SysCode::$resp_msg[SysCode::CUSTOMER_ONCE], SysCode::CUSTOMER_ONCE);
        }

        DB::table("iqy20210910_records")->insert([
                "iqy20210910_user_id" => $activity_user_id,
                "mobile"              => $order_data['charge_account'],
                "goods_id"            => $goods_info->id,
                "goods_name"          => $goods_info->goods_name,
                "order_id"            => $order_data['id'],
                "created_at"          => Carbon::now()->format('Y-m-d H:i:s'),
                "updated_at"          => Carbon::now()->format('Y-m-d H:i:s'),
            ]
        );
    }

    //如果传递了$goods_id参数，则判断商品是否满足兑换条件，否则只判断活动及用户状态
    public function isPermit($goods_id = null, $group_id = null)
    {
        //活动信息及活动时间判断，已经在AuthController里判断过了。
//        $this->verifyActifity($this->activity_id);
        //判断用户是否已领取。
        $this->verifyUserAndIsExchange();
        //判断商品是否启用，并判断当前用户等级是否与商品等级相符。
        if (!is_null($goods_id)) {
            $this->verifyCanExchangeGoods($goods_id, empty($group_id) ? null : ExchangeGroup::find($group_id));
        }

        throw new MyException(SysCode::$resp_msg[SysCode::SUCCESS], SysCode::SUCCESS);
    }

    /**
     * 根据领取状态，判断用户是否已领取。因活动而已。本活动没有礼品等级概念
     * @return Iqy20210910User
     * @throws MyException
     */
    private function verifyUserAndIsExchange()
    {
        $activity_user_info = Iqy20210910User::find(session('user_info.id'));

        //用户状态
        if (!$activity_user_info || $activity_user_info->status == SysCode::COMMON_STATUS_0) {
            throw new MyException(SysCode::$resp_msg[SysCode::NOT_CHANCE_3019], SysCode::NOT_CHANCE_3019);
        }

        //验证是否领取过
        if ($activity_user_info->get_status == SysCode::COMMON_STATUS_1) {
            throw new MyException(SysCode::$resp_msg[SysCode::EXCHANGE_LOG_3020], SysCode::EXCHANGE_LOG_3020); //领取过
        }

        return $activity_user_info;
    }

    /**
     * 判断商品是否启用，并判断当前用户等级是否与商品等级相符。
     * @param $goods_id
     * @return  activity_prizes
     * @throws MyException
     */
    protected function verifyCanExchangeGoods($goods_info, $group_info = null)
    {
        $prize_info = DB::table('activity_prizes')->where([
            'goods_id'    => $goods_info->id,
            'activity_id' => $this->activity_id
        ])->first();

        if (!$prize_info) {
            throw new MyException(SysCode::$resp_msg[SysCode::NOT_CHANCE_3019], SysCode::NOT_CHANCE_3019);
        }

        if (!$goods_info || $goods_info->status == SysCode::COMMON_STATUS_0) {
            throw new MyException(SysCode::$resp_msg[SysCode::GOOD_LISTS_EMPTY], SysCode::GOOD_LISTS_EMPTY);
        }

        if (!array_key_exists($prize_info->prize_level . "_" . $prize_info->prize_level2, session("user_info.prize_level"))) {
            throw new MyException(SysCode::$resp_msg[SysCode::NOT_CHANCE_3019], SysCode::NOT_CHANCE_3019);
        }

        return $prize_info;
    }

}
