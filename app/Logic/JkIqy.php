<?php

namespace App\Logic;

use App\Http\Controllers\Api\SysCode\SysCode;
use App\Exceptions\MyException;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

//京科爱奇艺兑换
class JkIqy extends ExchangeBaseV2
{
//    protected $redis;
//    protected $pre_key = 'aqy'; //每天每淘宝账号订单计数redis key前缀
//    protected $max_order_limit = 99;  //每天每淘宝账号最大订单数

    public function __construct()
    {
        parent::__construct();
//        $this->redis = app('redis.connection');

        $this->single_goods_rdr_url = '/jkiqy/index.html'; //单品跳转地址
        $this->jiazheng_rdr_url     = ''; //家政服务跳转地址
        $this->goods_list_rdr_url   = ''; //商品列表跳转地址
    }

    //默认登录方法。
    public function login($activity_id = null)
    {

        $mobile      = request('mobile');
        $sms_captcha = request('sms_captcha');

        if (!check_mobile($mobile)) {
            throw new MyException(SysCode::MOBILE_ERROR);
        }

        if (!empty($sms_captcha)) {
            $this->checkSmsCaptcha($mobile, $sms_captcha);
        }

        try {

            $user = DB::table('aqy_users')->where('mobile', $mobile)->first();

            if (!$user) {
                $activity_user_id = DB::table('aqy_users')->insertGetId(['mobile' => $mobile, 'created_at' => Carbon::now()]);
            } else {
                $activity_user_id = $user->id;
            }

            $extra = ['mobile' => $mobile];
            if (empty($sms_captcha)) {
                $extra['query_only'] = true;
            }

            $this->exchange_login(false, $activity_user_id, $extra);

        } catch (MyException $e) {

            if ($e->getCode() == SysCode::SUCCESS) {

                session(['user' => ['mobile' => $mobile]]);
                session()->save();
            }

            throw $e;
        }

    }
}
