<?php
/*
 * @Author: yangy
 * @Date: 2023-10-16 11:51:35
 * @LastEditors: yangy
 * @LastEditTime: 2023-10-16 11:52:09
 * @FilePath: /gift_backend/app/Logic/Jkrx.php
 * @Description:
 *
 * Copyright (c) 2023 by 青柠利合科技(北京)有限公司, All Rights Reserved.
 */

namespace App\Logic;

use App\Http\Controllers\Api\SysCode\SysCode;
use App\Exceptions\MyException;
use App\Models\ExchangeWhitelist;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

//京科 瑞幸
class Jkrx extends ExchangeBaseV2
{
    public function __construct()
    {
        parent::__construct();

        $this->single_goods_rdr_url = '/jkrx/goods.html';           //单品跳转地址
        $this->jiazheng_rdr_url     = '/jkrx/jzindex.html?id={id}'; //家政服务跳转地址
        $this->goods_list_rdr_url   = '/jkrx/goods.html';           //商品列表跳转地址
    }

}
