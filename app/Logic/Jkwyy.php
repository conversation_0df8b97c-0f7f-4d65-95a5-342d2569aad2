<?php

namespace App\Logic;

use App\Http\Controllers\Api\SysCode\SysCode;
use App\Exceptions\MyException;
use App\Models\ExchangeWhitelist;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

//京科网易云。2023-06-14
class Jkwyy extends ExchangeBaseV2
{
    public function __construct()
    {
        parent::__construct();

        $this->single_goods_rdr_url = '/jkwyy/goods.html';           //单品跳转地址
        $this->jiazheng_rdr_url     = '/jkwyy/jzindex.html?id={id}'; //家政服务跳转地址
        $this->goods_list_rdr_url   = '/jkwyy/goods.html';           //商品列表跳转地址
    }

    //默认登录方法。
    public function login($activity_id = null)
    {

        $mobile      = request('mobile');
        $sms_captcha = request('sms_captcha');

        if (!check_mobile($mobile)) {
            throw new MyException(SysCode::MOBILE_ERROR);
        }

//        if (check_virtual_mobile($mobile)) {
//            throw new MyException('请使用非虚拟运营商的手机号进行充值', SysCode::MOBILE_ERROR);
//        }

        $this->checkSmsCaptcha($mobile, $sms_captcha);

        try {
            $extra = ['mobile' => $mobile];

            $this->exchange_login(false, 0, $extra);

        } catch (MyException $e) {

            if ($e->getCode() == SysCode::SUCCESS) {

                session(['user' => ['mobile' => $mobile]]);
                session()->save();
            } else {
                request()->attributes->set('err_line', str_replace(base_path() . DIRECTORY_SEPARATOR, '', $e->getFile()) . ':' . $e->getLine());
            }

            throw $e;
        }

    }
}
