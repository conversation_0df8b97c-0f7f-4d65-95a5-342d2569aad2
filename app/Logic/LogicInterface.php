<?php

namespace App\Logic;

use App\Models\Order;
use Exception;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Api\SysCode\DoubleCode;
use App\Http\Controllers\Api\SysCode\SysCode;

/**
 * 活动逻辑处理接口
 */
interface LogicInterface
{
    /**
     * 根据code获取对应message。用于活动个性化返回错误信息
     * @param $code
     * @return mixed
     */
    function getMsgByCode($code);

    /**
     * 活动登录。通过抛出App\Exceptions\MyException异常来控制逻辑。
     * @return mixed 登录失败返回false，也可通过MyException异常来控制。登录成功可返回业务数据数组，响应给client。[  "code" => 200, "msg" => "成功", "data" => ""]
     */
    function login($activity_id);

    //===================================================================
    // 以下接口登录成功后才能使用。

    /**
     * 兑换前，活动的自定义验证实现。验证通过返回activity_user_id。
     * @param $goods_info
     * @param $group_info
     * @return array array  ['activity_user_id' => xx, 'prize_level' => xx,  'prize_level2' => xx]
     */
    function verifyBeforeCreateOrder($goods_info, $group_info);

    /**
     * 写入公共订单表后，活动内的自定义处理逻辑实现。
     * @param $activity_user_id int 活动内的用户id
     * @param $order_data array 订单表数据，包括id
     * @param Model $goods_info  领取的商品信息.
     * @param Model $group_info 领取的商品信息.
     * @return mixed
     */
    function handleAfterCreateOrder($activity_user_id, $order_data, $goods_info, $group_info);

    /**
     * 取消订单后执行的操作。主要是对各活动下单频次限制条件的更新
     * @param Order $order 订单信息
     * @date: 2024/11/8
     */
    function handlerAfterCancelOrder($order);


    /**
     * 活动资格验证（是否有领取礼品资格）
     * @param int $goods_id 礼品id
     * @param int $group_id 礼品分组id（礼包卡等分组类卡）
     * @return mixed
     */
    function isPermit($goods_id, $group_id);

    /**
     * 获取商品列表前的操作。若返回值不为null，则取该返回值作为商品列表响应内容。
     * @return mixed
     */
    function handlerBeforeGetGoodsList();

    /**
     * 获取商品列表
     * @param NULL
     * @return mixed
     * @throws MyException
     */
    function handlerAfterGetGoodsList($goods_list);


    /**
     * 获取商品详情
     * @param $goods_list
     * @return mixed
     */
    function handlerAfterGetGoodsDetail($goods_info);

}
