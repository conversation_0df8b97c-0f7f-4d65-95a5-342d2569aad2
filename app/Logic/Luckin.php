<?php

namespace App\Logic;

use App\Http\Controllers\Api\SysCode\SysCode;
use App\Exceptions\MyException;
use App\Models\ExchangeWhitelist;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

//瑞幸咖啡-明苑风华。2023-01-25
class Luckin extends ExchangeBaseV2
{
    public function __construct()
    {
        parent::__construct();

        $this->single_goods_rdr_url = '/rx/goods.html';           //单品跳转地址
        $this->jiazheng_rdr_url     = '/rx/jzindex.html?id={id}'; //家政服务跳转地址
        $this->goods_list_rdr_url   = '/rx/goods.html';           //商品列表跳转地址
    }

    //默认登录方法。
    public function login($activity_id = null)
    {

//        //判断图形校验码。在获取短信验证码时验证了校验码，这里不做校验。
//        $captcha = request('captcha');
//        if ($captcha == '1231' && strpos(request()->url(), 'http://101.42.111.131') === 0) {
//
//        } else {
//            $rules     = ['captcha' => 'required|captcha'];
//            $validator = validator()->make(request()->all(), $rules);
//            if ($validator->fails()) {
//                //图形校验码校验失败
//                throw new MyException($this->getMsgByCode(SysCode::CAPTCHA_IMG_ERROR), SysCode::CAPTCHA_IMG_ERROR);
//            }
//        }

        $mobile      = request('mobile');
        $sms_captcha = request('sms_captcha');

        if (!check_mobile($mobile)) {
            throw new MyException(SysCode::MOBILE_ERROR);
        }

        if (check_virtual_mobile($mobile)) {
            throw new MyException('请使用非虚拟运营商的手机号进行充值', SysCode::MOBILE_ERROR);
        }

        $this->checkSmsCaptcha($mobile, $sms_captcha);

        try {

            $user = DB::table('luckin_users')->where('mobile', $mobile)->first();

            if (!$user) {
                $activity_user_id = DB::table('luckin_users')->insertGetId(['mobile' => $mobile, 'created_at' => Carbon::now()]);
            } else {
                $activity_user_id = $user->id;
            }

            $extra = ['mobile' => $mobile];

            $this->exchange_login(false, $activity_user_id, $extra);

        } catch (MyException $e) {

            if ($e->getCode() == SysCode::SUCCESS) {

                session(['user' => ['mobile' => $mobile]]);
                session()->save();
            } else {
                request()->attributes->set('err_line', str_replace(base_path() . DIRECTORY_SEPARATOR, '', $e->getFile()) . ':' . $e->getLine());
            }

            throw $e;
        }

    }
}
