<?php

namespace App\Logic;

use App\Http\Controllers\Api\SysCode\SysCode;
use App\Exceptions\MyException;

//预约码逻辑。用于蒙商积分预约处理家政类和图书订单。2021-10-15
class MengshangJf extends Exchange
{
    public function __construct() {

        $this->single_goods_rdr_url = '/blue/detail.html?id={id}'; //单品跳转地址
        $this->jiazheng_rdr_url     = '/ms/jzindex.html?id={id}'; //家政服务跳转地址
        $this->goods_list_rdr_url   = '/blue/goods.html'; //商品列表跳转地址
    }

    public $my_syscode = [
        SysCode::CARD_PWD_ERROR   => '预约码错误',
        SysCode::CARD_PWD_EXPIRED => '预约码已过期',
        SysCode::PARAMS_ERROR     => '预约码或手机号错误',

        SysCode::EXCHANGE_FIRST     => '请先预约！',

        SysCode::EXCHANGE_NOT_ALLOW      => '还未获得该商品的预约资格',
        SysCode::EXCHANGE_RECORD_EMPTY   => '暂无预约记录',
        SysCode::NOT_CHANCE_3019         => '对不起!您暂未获得预约资格',

        SysCode::EXCHANGE_TOP_LIMIT_3104 => '预约名额已领完',

        SysCode::EXCHANGE_GOODS_REPEAT     => '不能重复预约同样的礼品，请选择其它礼品^_^',
        SysCode::EXCHANGE_GOODS_NOT_PERMIT => '您没有预约该礼品的权限，请选择其它礼品^_^',
        SysCode::EXCHANGE_CYCLE_ERROR      => '本{0}已领取，请下{0}再来^_^',
        SysCode::EXCHANGE_LOG_3020         => '您已领了全部权益，请在页面底部查询领取记录。',

    ];

    public function login($activity_id) {

        try {

            parent::login($activity_id);

        } catch (MyException $e) {

            if ($e->getCode() == SysCode::CARD_PWD_ERROR) {

                $card_pwd = request('card_pwd');

                //不区分大小写
                if (stripos($card_pwd, 'I') !== false || stripos($card_pwd, 'l') !== false) {
                    throw new MyException($this->getMsgByCode(SysCode::CARD_PWD_ERROR) . '，请注意区分字母“L”和“I”并重新尝试', SysCode::CARD_PWD_ERROR);
                }
            }

            throw $e;
        }
    }
}
