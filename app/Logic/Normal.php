<?php

namespace App\Logic;

use App\Http\Controllers\Api\SysCode\SysCode;
use App\Exceptions\MyException;
use App\Models\Order;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

// 通用单选一或多选一兑换码逻辑处理类。
// 每天充值账号充值次数限制为5。
// 2022-01-29
class Normal extends ExchangeBaseV2
{
    protected $redis;
    protected $max_limit_per_account = 5; //当前活动每天每账号最大充值次数

    public function __construct()
    {
        parent::__construct();

        $this->redis = app('redis.connection');

        $this->single_goods_rdr_url = '/n/detail.html?id={id}';   //单品跳转地址
        $this->jiazheng_rdr_url     = '/n/jzindex.html?id={id}';  //家政服务跳转地址
        $this->goods_list_rdr_url   = '/n/goods.html';            //商品列表跳转地址
    }

    //默认登录方法。
    public function login($activity_id = null)
    {
        $this->exchange_login(false);
    }

    public function verifyBeforeCreateOrder($goods_info, $group_info = null)
    {
        //判断是否超过次数。
        $charge_account = request('charge_account');
        if ($this->max_limit_per_account > 0 && $charge_account) {
            $key         = $this->getChargeAccountLimitKey($goods_info->id, $charge_account);
            $order_count = $this->redis->get($key);
            if (!empty($order_count) && $order_count >= $this->max_limit_per_account) {
                $this->throwMyException(SysCode::EXCHANGE_MAX_LIMIT);
            }
        }

        return parent::verifyBeforeCreateOrder($goods_info, $group_info);
    }

    //处理自定义下订单
    // 入各个活动的兑换表
    public function handleAfterCreateOrder($activity_user_id, $order_data, $goods_info, $group_info = null)
    {
        parent::handleAfterCreateOrder($activity_user_id, $order_data, $goods_info, $group_info);

        $charge_account = request('charge_account');
        if ($this->max_limit_per_account > 0 && $charge_account) {
            //添加下单计数
            $key           = $this->getChargeAccountLimitKey($goods_info->id, $charge_account);
            $redis_results = $this->redis->multi()
                ->incr($key)
                ->expire($key, Carbon::now()->endOfDay()->timestamp - Carbon::now()->timestamp)
                ->exec();
            $order_count   = $redis_results[0];
            if ($order_count > $this->max_limit_per_account) {
                $this->throwMyException(SysCode::EXCHANGE_MAX_LIMIT);
            }
        }
    }

    /**
     * 取消订单后执行的操作。主要是对各活动下单频次限制条件的更新
     * @param Order $order 订单信息
     * @date: 2024/11/8
     */
    public function handlerAfterCancelOrder($order)
    {
        parent::handlerAfterCancelOrder($order);

        if ($this->max_limit_per_account > 0 && $order->charge_account && $this->order_cancel_result === 1) {
            try {
                $key         = $this->getChargeAccountLimitKey($order->goods_id, $order->charge_account);
                $order_count = $this->redis->get($key);
                if (!empty($order_count) && $order_count > 0) {
                    $this->redis->decr($key);
                    Log::info(sprintf("order id: %d, %s limit_key=%s decr.", $order->id, static::class, $key));
                }
            } catch (\Exception $e) {
                Log::error("order id: {$order->id}, decr error. " . $e->getMessage(), $e->getTrace());
            }
        }
    }

    //获取充值账号限制redis键
    protected function getChargeAccountLimitKey($goods_id, $charge_account)
    {
        return sprintf('%s_%s_%s', $this->activity_id, $goods_id, md5($charge_account));
    }
}
