<?php

namespace App\Logic;

use App\Http\Controllers\Api\SysCode\SysCode;
use App\Exceptions\MyException;
use App\Libraries\Enums;
use App\Models\Goods;
use App\Models\Order;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

// 通用单选一或多选一兑换码逻辑处理类。用于兑换码单品兑换；支持京东直充及天猫购物券多账号充值
// 每天充值账号充值次数限制为9。支持充值账号格式校验
// 2024-03-06
class Normal2403 extends ExchangeBaseV2
{
    protected $redis;
    protected $max_limit_per_account = 9; //当前活动每天每账号最大充值次数

    public function __construct()
    {
        parent::__construct();

        $this->redis = app('redis.connection');

        $this->single_goods_rdr_url = '/cu/detail.html?id={id}';   //单品跳转地址
        $this->jiazheng_rdr_url     = '/cu/jzindex.html?id={id}';  //家政服务跳转地址
        $this->goods_list_rdr_url   = '/cu/goods.html';            //商品列表跳转地址
    }

    //默认登录方法。
    public function login($activity_id = null)
    {
        $this->exchange_login(false);
    }

    public function verifyBeforeCreateOrder($goods_info, $group_info = null)
    {
        $charge_account = request('charge_account');

        //判断是否超过次数。
        if ($charge_account && $this->max_limit_per_account > 0) {
            $key = $this->getChargeAccountLimitKey($goods_info->id, $charge_account);;
            $order_count = $this->redis->get($key);
            if (!empty($order_count) && $order_count >= $this->max_limit_per_account) {
                $this->throwMyException(SysCode::EXCHANGE_MAX_LIMIT);
            }
        }

        $result = parent::verifyBeforeCreateOrder($goods_info, $group_info);

        if ($goods_info->pre_verify_type == Enums::PRE_VERIFY_TYPE_PRE_VERIFY) {
            //有前置验证，比如京东直充短信验证码验证。需传递verify_code参数
            $verify_code = request('verify_code');
            if ($verify_code) {
                $verify_result = $this->preVerify($goods_info->ecp_pcode, $charge_account, $verify_code);
                if ($verify_result['code'] != 200) {
                    $this->throwMyException(SysCode::CHANNEL_DEAL_ERROR, $verify_result['return_msg']);//验证失败
                }
            }
        } elseif ($goods_info->pre_verify_type == Enums::PRE_VERIFY_TYPE_TAOBAO) {
            //淘宝手机号发奖-天猫购物券
            //判断手机号对应淘宝账号数量，为1则直接下单，为多则返回数量，并触发验证码发送
            if (check_mobile($charge_account)) {
                $user_query_result = $this->taobaoUserInfoQuery($goods_info->ecp_pcode, $charge_account);
                if ($user_query_result['user_num'] > 1) {
                    //发送验证码
                    $send_result = $this->taobaoVerifyCodeSend($goods_info->ecp_pcode, $charge_account);
                    if ($send_result['code'] != 200 && $send_result['return_code'] != 'VERIFY_CODE_SYSTEM_ERROR') {
                        $this->throwMyException(SysCode::CHANNEL_DEAL_ERROR, $send_result['return_msg']);
                    }
                    $this->throwMyException(SysCode::MULTI_USERS, '手机号码对应淘宝账号数量大于1');
                }
            }
        }

        return $result;
    }

    //处理自定义下订单
    // 入各个活动的兑换表
    public function handleAfterCreateOrder($activity_user_id, $order_data, $goods_info, $group_info = null)
    {
        $charge_account = request('charge_account');

        parent::handleAfterCreateOrder($activity_user_id, $order_data, $goods_info, $group_info);

        if ($charge_account && $this->max_limit_per_account > 0) {
            //添加下单计数
            $key           = $this->getChargeAccountLimitKey($goods_info->id, $charge_account);
            $redis_results = $this->redis->multi()
                ->incr($key)
                ->expire($key, Carbon::now()->endOfDay()->timestamp - Carbon::now()->timestamp)
                ->exec();
            $order_count   = $redis_results[0];
            if ($order_count > $this->max_limit_per_account) {
                $this->throwMyException(SysCode::EXCHANGE_MAX_LIMIT);
            }
        }
    }

    /**
     * 取消订单后执行的操作。主要是对各活动下单频次限制条件的更新
     * @param Order $order 订单信息
     * @date: 2024/11/8
     */
    public function handlerAfterCancelOrder($order)
    {
        parent::handlerAfterCancelOrder($order);

        if ($order->charge_account && $this->order_cancel_result === 1 && $this->max_limit_per_account > 0) {
            try {
                $key         = $this->getChargeAccountLimitKey($order->goods_id, $order->charge_account);
                $order_count = $this->redis->get($key);
                if (!empty($order_count) && $order_count > 0) {
                    $this->redis->decr($key);
                    Log::info(sprintf("order id: %d, %s limit_key=%s decr.", $order->id, static::class, $key));
                }
            } catch (\Exception $e) {
                Log::error("order id: {$order->id}, decr error. " . $e->getMessage(), $e->getTrace());
            }
        }
    }

    //获取充值账号限制redis键
    protected function getChargeAccountLimitKey($goods_id, $charge_account)
    {
        return sprintf('%s_%s_%s', $this->activity_id, $goods_id, md5($charge_account));
    }
}
