<?php

namespace App\Logic;

use App\Http\Controllers\Api\SysCode\SysCode;
use App\Exceptions\MyException;
use App\Libraries\OrderUtils;
use App\Models\ExchangeWhitelist;
use App\Models\Order;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

//天猫购物券-农行-明苑风华。2021-11-24
class TianMao extends ExchangeBaseV2
{
    protected $redis;
    protected $pre_key                     = 'tianmao_'; //每天每淘宝账号订单计数redis key前缀  最终key前缀： gift_db_tianmao_
    protected $max_limit_per_account       = 3;          //每天每淘宝账号最大订单数
    protected $max_limit_per_month_account = 15;         //每月每淘宝账号最大订单数
    protected $max_limit_per_mobile        = 0;          //每天每手机号的最大订单数
    protected $max_limit_per_month_mobile  = 0;          //月每手机号的最大订单数

    protected $max_limit_account_mobiles = 0;          //淘宝账号对应的充值手机号数量上限 2023-03-21
    protected $max_limit_mobile_accounts = 0;          //手机号对应的淘宝账号数量上限 2023-03-21

    protected $user_mobile_in_whitelist    = false;   //手机号是否在白名单中 2022-12-23
    protected $charge_account_in_whitelist = false;   //充值账号是否在白名单中 2022-12-23

    //手机号每月兑换次数计数： gift_db_tianmao_m_xxxxxxxxxxx (xxxxxxxxxxx为手机号)
    //手机号每天兑换次数计数： gift_db_tianmao_xxxxxxxxxxx (xxxxxxxxxxx为手机号)
    //淘宝账号每月兑换次数计数：gift_db_tianmao_m_xxx (xxx为淘宝账号的md5值)
    //淘宝账号每天兑换次数计数：gift_db_tianmao_xxx (xxx为淘宝账号的md5值)
    //淘宝账号对应的手机号计数：gift_db_tianmao_am_xxx (xxx为淘宝账号的md5值)
    //手机号对应的淘宝账号计数：gift_db_tianmao_ma_xxx (xxx为淘宝账号的md5值)

    public function __construct()
    {
        parent::__construct();

        $this->redis = app('redis.connection');

        $this->single_goods_rdr_url = '/tm/goods.html';           //单品跳转地址
        $this->jiazheng_rdr_url     = '/tm/jzindex.html?id={id}'; //家政服务跳转地址
        $this->goods_list_rdr_url   = '/tm/goods.html';           //商品列表跳转地址
    }

    //默认登录方法。
    public function login($activity_id = null)
    {
//        //判断图形校验码。在获取短信验证码时验证了校验码，这里不做校验。
//        $captcha = request('captcha');
//        if ($captcha == '1231' && strpos(request()->url(), 'http://101.42.111.131') === 0) {
//
//        } else {
//            $rules     = ['captcha' => 'required|captcha'];
//            $validator = validator()->make(request()->all(), $rules);
//            if ($validator->fails()) {
//                //图形校验码校验失败
//                throw new MyException($this->getMsgByCode(SysCode::CAPTCHA_IMG_ERROR), SysCode::CAPTCHA_IMG_ERROR);
//            }
//        }

        $mobile      = request('mobile');
        $sms_captcha = request('sms_captcha');

        if (!check_mobile($mobile)) {
            throw new MyException(SysCode::MOBILE_ERROR);
        }

        if (check_virtual_mobile($mobile)) {
            throw new MyException('请使用非虚拟运营商的手机号进行充值', SysCode::MOBILE_ERROR);
        }

        $this->checkSmsCaptcha($mobile, $sms_captcha);

        try {

            $user = DB::table('tianmao_users')->where('mobile', $mobile)->first();

            if (!$user) {
                $activity_user_id = DB::table('tianmao_users')->insertGetId(['mobile' => $mobile, 'created_at' => Carbon::now()]);
            } else {
                $activity_user_id = $user->id;
            }

            $extra = ['mobile' => $mobile];

            $this->exchange_login(false, $activity_user_id, $extra);

        } catch (MyException $e) {

            if ($e->getCode() == SysCode::SUCCESS) {

                session(['user' => ['mobile' => $mobile]]);
                session()->save();
            } else {
                request()->attributes->set('err_line', str_replace(base_path() . DIRECTORY_SEPARATOR, '', $e->getFile()) . ':' . $e->getLine());
            }

            throw $e;
        }

    }

    public function verifyBeforeCreateOrder($goods_info, $group_info = null)
    {
        $this->getLimit();

        $user_mobile = session('user.mobile');

        //判断淘宝账号和手机号关联个数是否超限
        if ($this->max_limit_account_mobiles > 0) {
            $key_am     = $this->pre_key . 'am_' . md5(request('charge_account'));
            $am_members = $this->redis->sMembers($key_am);
            if (count($am_members) >= $this->max_limit_account_mobiles && !in_array($user_mobile, $am_members)) {
                $this->throwMyException(SysCode::EXCHANGE_MAX_LIMIT);
            }
        }
        if ($this->max_limit_mobile_accounts > 0) {
            $key_ma     = $this->pre_key . 'ma_' . $user_mobile;
            $ma_members = $this->redis->sMembers($key_ma);
            if (count($ma_members) >= $this->max_limit_mobile_accounts && !in_array(request('charge_account'), $ma_members)) {
                $this->throwMyException(SysCode::EXCHANGE_MAX_LIMIT);
            }
        }

        //判断手机号每月下单数是否超限
        if ($this->max_limit_per_month_mobile > 0) {
            $key_m_m         = $this->pre_key . 'm_' . $user_mobile;
            $order_count_m_m = $this->redis->get($key_m_m);
            if (!empty($order_count_m_m) && $order_count_m_m >= $this->max_limit_per_month_mobile) {
                $this->throwMyException(SysCode::EXCHANGE_MAX_LIMIT);
            }
        }

        //判断手机号每天下单数是否超限
        if ($this->max_limit_per_mobile > 0) {
            $key_m         = $this->pre_key . $user_mobile;
            $order_count_m = $this->redis->get($key_m);
            if (!empty($order_count_m) && $order_count_m >= $this->max_limit_per_mobile) {
                $this->throwMyException(SysCode::EXCHANGE_MAX_LIMIT);
            }
        }

        //判断淘宝账号每天订单数是否超限。
        if ($this->max_limit_per_account > 0) {
            $key         = $this->pre_key . md5(request('charge_account'));
            $order_count = $this->redis->get($key);
            if (!empty($order_count) && $order_count >= $this->max_limit_per_account) {
                $this->throwMyException(SysCode::EXCHANGE_MAX_LIMIT);
            }
        }

        //判断淘宝账号每月订单数是否超限。
        if ($this->max_limit_per_month_account > 0) {
            $key_m_account = $this->pre_key . 'm_' . md5(request('charge_account'));
            $order_count   = $this->redis->get($key_m_account);
            if (!empty($order_count) && $order_count >= $this->max_limit_per_month_account) {
                $this->throwMyException(SysCode::EXCHANGE_MAX_LIMIT);
            }
        }

        $result = parent::verifyBeforeCreateOrder($goods_info, $group_info);

        //判断手机号对应淘宝账号数量，为1则直接下单，为多则返回数量，并触发验证码发送
        if (empty($goods_info->ecp_target) && check_mobile(request('charge_account'))) {
            $user_query_result = $this->taobaoUserInfoQuery($goods_info->ecp_pcode, request('charge_account'));
            if ($user_query_result['user_num'] > 1) {
                //发送验证码
                $send_result = $this->taobaoVerifyCodeSend($goods_info->ecp_pcode, request('charge_account'));
                if ($send_result['code'] != 200 && $send_result['return_code'] != 'VERIFY_CODE_SYSTEM_ERROR') {
                    $this->throwMyException(SysCode::CHANNEL_DEAL_ERROR, $send_result['return_msg']);
                }
                $this->throwMyException(SysCode::MULTI_USERS, '手机号码对应淘宝账号数量大于1');
            }
        }
        return $result;
    }

    //处理自定义下订单
    // 入各个活动的兑换表
    public function handleAfterCreateOrder($activity_user_id, $order_data, $goods_info, $group_info = null)
    {
        parent::handleAfterCreateOrder($activity_user_id, $order_data, $goods_info, $group_info);

        $user_mobile    = session('user.mobile');
        $charge_account = request('charge_account');

        //判断淘宝账号和手机号关联个数处理
        if ($this->max_limit_account_mobiles > 0) {
            $key_am = $this->pre_key . 'am_' . md5($charge_account);
            $this->redis->sAdd($key_am, $user_mobile);
        }
        if ($this->max_limit_mobile_accounts > 0) {
            $key_ma = $this->pre_key . 'ma_' . $user_mobile;
            $this->redis->sAdd($key_ma, $charge_account);
        }

        //判断手机号每月下单数是否超限
        if ($this->max_limit_per_month_mobile > 0) {
            $key_m_m         = $this->pre_key . 'm_' . $user_mobile;
            $order_count_m_m = $this->redis->incr($key_m_m);
            $this->redis->expire($key_m_m, Carbon::now()->endOfMonth()->timestamp - Carbon::now()->timestamp);
            if ($order_count_m_m > $this->max_limit_per_month_mobile) {
                $this->throwMyException(SysCode::EXCHANGE_MAX_LIMIT);
            }
        }

        //判断手机号每天下单数是否超限
        if ($this->max_limit_per_mobile > 0) {
            $key_m         = $this->pre_key . $user_mobile;
            $order_count_m = $this->redis->incr($key_m);
            $this->redis->expire($key_m, Carbon::now()->endOfDay()->timestamp - Carbon::now()->timestamp);
            if ($order_count_m > $this->max_limit_per_mobile) {
                $this->throwMyException(SysCode::EXCHANGE_MAX_LIMIT);
            }
        }

        //淘宝账号每天订单数累计
        if ($this->max_limit_per_account > 0) {
            $key         = $this->pre_key . md5(request('charge_account'));
            $order_count = $this->redis->incr($key);
            $this->redis->expire($key, Carbon::now()->endOfDay()->timestamp - Carbon::now()->timestamp);
            if ($order_count > $this->max_limit_per_account) {
                $this->throwMyException(SysCode::EXCHANGE_MAX_LIMIT);
            }
        }

        //淘宝账号每月订单数累计。
        if ($this->max_limit_per_month_account > 0) {
            $key_m_account = $this->pre_key . 'm_' . md5(request('charge_account'));
            $order_count   = $this->redis->incr($key_m_account);
            $this->redis->expire($key_m_account, Carbon::now()->endOfMonth()->timestamp - Carbon::now()->timestamp);
            if ($order_count > $this->max_limit_per_month_account) {
                $this->throwMyException(SysCode::EXCHANGE_MAX_LIMIT);
            }
        }
    }

    /**
     * 取消订单后执行的操作。主要是对各活动下单频次限制条件的更新
     * @param Order $order 订单信息
     * @date: 2024/11/8
     */
    public function handlerAfterCancelOrder($order)
    {
        parent::handlerAfterCancelOrder($order);

        $mobile         = $order->mobile;
        $charge_account = $order->charge_account;

        if ($this->order_cancel_result === 1) {
            try {
                //撤回下单限制
                $key           = $this->pre_key . md5($charge_account);
                $key_m_account = $this->pre_key . 'm_' . md5($charge_account);
                $key_m         = $this->pre_key . $mobile;
                $key_m_m       = $this->pre_key . 'm_' . $mobile;
                $key_am        = $this->pre_key . 'am_' . md5($charge_account);
                $key_ma        = $this->pre_key . 'ma_' . $mobile;

                if (date('Y-m-d', strtotime($order->order_time)) === date('Y-m-d')) {
                    $order_count = $this->redis->get($key);
                    if (!empty($order_count) && $order_count > 0) {
                        $this->redis->decr($key);
                        Log::info(sprintf("order id: %d, %s limit: %s = %s, decr.", $order->id, static::class, $key, $order_count));
                    }

                    $order_count = $this->redis->get($key_m);
                    if (!empty($order_count) && $order_count > 0) {
                        $this->redis->decr($key_m);
                        Log::info(sprintf("order id: %d, %s limit: %s = %s, decr.", $order->id, static::class, $key_m, $order_count));
                    }
                }

                if (date('Y-m', strtotime($order->order_time)) === date('Y-m')) {
                    $order_count = $this->redis->get($key_m_m);
                    if (!empty($order_count) && $order_count > 0) {
                        $this->redis->decr($key_m_m);
                        Log::info(sprintf("order id: %d, %s limit: %s = %s, decr.", $order->id, static::class, $key_m_m, $order_count));
                    }

                    $order_count = $this->redis->get($key_m_account);
                    if (!empty($order_count) && $order_count > 0) {
                        $this->redis->decr($key_m_account);
                        Log::info(sprintf("order id: %d, %s limit: %s = %s, decr.", $order->id, static::class, $key_m_account, $order_count));
                    }
                }

                $this->redis->sRem($key_am, $mobile);
                Log::info(sprintf("order id: %d, %s key %s, remove %s.", $order->id, static::class, $key_am, $mobile));
                $this->redis->sRem($key_ma, $charge_account);
                Log::info(sprintf("order id: %d, %s key %s, remove %s.", $order->id, static::class, $key_ma, $charge_account));
            } catch (\Exception $e) {
                Log::error("order id: {$order->id}, decr error. " . $e->getMessage(), $e->getTrace());
            }
        }
    }


    protected function getLimit()
    {
        $this->getWhitelist();

        if ($this->charge_account_in_whitelist) {
            //充值账号在白名单
            $this->max_limit_per_account       = config('api.tianmao.wl_max_limit_per_account');
            $this->max_limit_per_month_account = config('api.tianmao.wl_max_limit_per_month_account');
        } else {
            $this->max_limit_per_account       = config('api.tianmao.max_limit_per_account');
            $this->max_limit_per_month_account = config('api.tianmao.max_limit_per_month_account');
        }

        if ($this->user_mobile_in_whitelist) {
            //手机号在白名单
            $this->max_limit_per_mobile       = config('api.tianmao.wl_max_limit_per_mobile');
            $this->max_limit_per_month_mobile = config('api.tianmao.wl_max_limit_per_month_mobile');
        } else {
            $this->max_limit_per_mobile       = config('api.tianmao.max_limit_per_mobile');
            $this->max_limit_per_month_mobile = config('api.tianmao.max_limit_per_month_mobile');
        }

        $this->max_limit_account_mobiles = config('api.tianmao.max_limit_account_mobiles');
        $this->max_limit_mobile_accounts = config('api.tianmao.max_limit_mobile_accounts');

//        request()->attributes->set('limit', [
//                'ca_in_wl'  => $this->charge_account_in_whitelist,
//                'mo_in_wl'  => $this->user_mobile_in_whitelist,
//                'account'   => $this->max_limit_per_account,
//                'm_account' => $this->max_limit_per_month_account,
//                'mobile'    => $this->max_limit_per_mobile,
//                'm_mobile'  => $this->max_limit_per_month_mobile,
//                'max_limit_account_mobiles'  => $this->max_limit_account_mobiles,
//                'max_limit_mobile_accounts'  => $this->max_limit_mobile_accounts,
//            ]
//        );
    }

    protected function getWhitelist()
    {
        $wl_mobile = ExchangeWhitelist::where('activity_id', $this->activity_id)
            ->where('user_mobile', session('user.mobile'))
            ->first();
        if ($wl_mobile) {
            $this->user_mobile_in_whitelist = empty($wl_mobile->expired_at) ||
                Carbon::parse($wl_mobile->expired_at . ' 23:59:59')->isAfter(Carbon::now());
            if ($wl_mobile->charge_account == request('charge_account')) {
                $wl_account = $wl_mobile;
            }
        }

        if (empty($wl_account)) {
            $wl_account = ExchangeWhitelist::where('activity_id', $this->activity_id)
                ->where('charge_account', request('charge_account'))
                ->first();
        }

        if ($wl_account) {
            $this->charge_account_in_whitelist = empty($wl_account->expired_at) ||
                Carbon::parse($wl_account->expired_at . ' 23:59:59')->isAfter(Carbon::now());
        }
    }
}
