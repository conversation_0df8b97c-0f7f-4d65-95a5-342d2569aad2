<?php

namespace App\Logic;

use App\Models\ExchangeDetail;
use Carbon\Carbon;
use App\Libraries\Qcp2Api;
use App\Exceptions\MyException;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Api\SysCode\SysCode;
use GuzzleHttp\Client;


//微信立减金  2023-05-22
class WxLjj extends ExchangeBaseV2
{
    protected $wechat;

    public function __construct()
    {
        parent::__construct();
        $this->single_goods_rdr_url = '/wxljj/index.html';           //单品跳转地址
        $this->wechat               = config('app.wechat');
    }

    //默认登录方法。
    public function login($activity_id = null)
    {
        try {
            //登录活动
            $card_pwd = request('card_pwd');
            if (empty(request('card_pwd'))) {
                throw new MyException($this->getMsgByCode(SysCode::CARD_PWD_ERROR), SysCode::CARD_PWD_ERROR);
            }
            if (session('user_info') && session('extra') && session('activity.id') && session('extra.exchange_code') == $card_pwd) {
                $this->exchange_detail = ExchangeDetail::find(session('extra.exchange_detail_id'));
                if (!$this->exchange_detail) {
                    throw new MyException(SysCode::$resp_msg[SysCode::NOT_LOGIN], SysCode::NOT_LOGIN, null,
                        ['wx_rdr_url' => $this->getWxAuthUrl(),]
                    );
                }

                $data['openid']         = session('extra.openid');
                $data['exchange_state'] = $this->exchange_detail->status . '';

                throw new MyException($this->getMsgByCode(SysCode::SUCCESS), SysCode::SUCCESS, null, $data);
            }

            //未登录的走到以下部分:
            $code = request('code');
            if (empty($code)) {
                throw new MyException(SysCode::$resp_msg[SysCode::NOT_LOGIN], SysCode::NOT_LOGIN, null, ['wx_rdr_url' => $this->getWxAuthUrl()]);
            } else {
                $url  = "https://api.weixin.qq.com/sns/oauth2/access_token?appid=" . $this->wechat['appid'] . "&secret=" . $this->wechat['app_secret'] . "&code=" . $code . "&grant_type=authorization_code";
                $resp = [];
                try {
                    $client    = new Client();
                    $response  = $client->request('GET', $url, ['timeout' => 5]);
                    $resp_json = $response->getBody()->getContents();
                    $resp      = json_decode($resp_json, true);
                } catch (\Exception $e) {
                    request()->attributes->set('err_msg', str_replace(base_path() . DIRECTORY_SEPARATOR, '', $e->getFile()) . ':' . $e->getLine() . '.' . $e->getMessage());
                }

                if (!empty($resp['openid'])) {
                    $extra = ['openid' => $resp['openid']];
                    // false  不去验证
                    $this->exchange_login(false, 0, $extra);
                } else {
                    //登录失败
                    throw new MyException(SysCode::$resp_msg[SysCode::NOT_LOGIN], SysCode::NOT_LOGIN, null, ['wx_rdr_url' => $this->getWxAuthUrl()]);
                }
            }
        } catch (MyException $e) {

            if ($e->getCode() == SysCode::SUCCESS) {
                if (isset($extra['openid'])) {
                    $e->addData(['openid' => $extra['openid']]);
                }
            } else {
                request()->attributes->set('err_line', str_replace(base_path() . DIRECTORY_SEPARATOR, '', $e->getFile()) . ':' . $e->getLine());
            }

            throw $e;
        }
    }

    public function verifyBeforeCreateOrder($goods_info, $group_info = null)
    {
        if (session('extra.openid') != request('charge_account')) {
            throw new MyException(SysCode::$resp_msg[SysCode::PARAMS_ERROR], SysCode::PARAMS_ERROR); //参数错误
        }
        return parent::verifyBeforeCreateOrder($goods_info, $group_info);
    }

    //处理自定义下订单
    // 入各个活动的兑换表
    public function handleAfterCreateOrder($activity_user_id, $order_data, $goods_info, $group_info = null)
    {
        //一个openid 只能领取一次的判断 (暂时去掉限制2023-06-21)
        $openid = session('extra.openid');
        $user   = DB::table('wx_coupon_users')->where('activity_id', $this->activity_id)->where('goods_id', $goods_info->id)->where('openid', $openid)->first();
        if (!$user) {
            DB::table('wx_coupon_users')->insertGetId([
                'activity_id'  => $this->activity_id,
                'goods_id'     => $goods_info->id,
                'openid'       => $openid,
                'received_num' => 1,
                'created_at'   => Carbon::now()
            ]);
        } else {
            DB::table('wx_coupon_users')->where('activity_id', $this->activity_id)->where('goods_id', $goods_info->id)->where('openid', $openid)->increment('received_num');
//            throw new MyException(SysCode::$resp_msg[SysCode::EXCHANGE_LOG_3020], SysCode::EXCHANGE_LOG_3020); //领取过
        }

        // 处理 后续流程
        parent::handleAfterCreateOrder($activity_user_id, $order_data, $goods_info, $group_info);

        $qcp2Api = Qcp2Api::getInst();
        $method  = 'order.virtual.create';

        foreach ($order_data['order_sub'] as $order_sub) {
            $resp = $qcp2Api->commonRequest($method, $order_sub['sub_order_no'], $order_sub['ecp_pcode'], $order_sub['user_mobile'], $order_sub['charge_account']);
            // qcp2 返回order_state： 1-成功，2-失败，3-处理中
            if ($resp['code'] == '200') {
                $rsp_biz_content = $resp['rsp_biz_content'];
                if ($rsp_biz_content['order_state'] === 1) {
                    $status = SysCode::ORDER_SUB_STATUS_3;//成功
                } elseif ($rsp_biz_content['order_state'] === 2) {
                    $status = SysCode::ORDER_SUB_STATUS_4;//失败
                } else {
                    $status = SysCode::ORDER_SUB_STATUS_2;//处理中
                }
            } elseif ($resp['code'] == '28') {
                $status = SysCode::ORDER_SUB_STATUS_2;//超时
            } else {
                $status = SysCode::ORDER_SUB_STATUS_4;//失败
            }

            DB::table("order_subs")->where(['sub_order_no' => $order_sub['sub_order_no']])->update(['status' => $status]);

            if ($status == SysCode::ORDER_SUB_STATUS_4) {
                throw new MyException(SysCode::FAILED);
            }
        }
    }

    public function handlerBeforeGetGoodsList()
    {
        if (!$this->exchange_detail) {
            $this->exchange_detail = ExchangeDetail::find(session('extra.exchange_detail_id'));
            if (!$this->exchange_detail) {
                throw new MyException(SysCode::$resp_msg[SysCode::NOT_LOGIN], SysCode::NOT_LOGIN, null, ['wx_rdr_url' => $this->getWxAuthUrl()]);
            }
        }

        $goods = DB::table('exchange_goods')
            ->leftJoin('exchange_groups', 'exchange_goods.exchange_group_id', '=', 'exchange_groups.id')
            ->leftJoin('goods', 'exchange_goods.goods_id', '=', 'goods.id')
            ->leftJoin('goods_details', 'goods.id', '=', 'goods_details.goods_id')
            ->where('exchange_goods.exchange_batch_id', $this->exchange_detail->exchange_batch_id)
            ->orderBy('exchange_goods.sort')
            ->select(['goods.id', 'goods.goods_price', 'goods.goods_show_img', 'goods.goods_name', 'goods.goods_type', 'goods.goods_attr', 'goods.advance_days', 'goods.service_time', 'exchange_goods.exchange_group_id', DB::raw('exchange_groups.sort group_sort'), 'exchange_groups.group_name', 'goods_details.goods_imgs', 'goods_details.goods_desc', 'goods_details.goods_params', 'goods_details.goods_instr'])
            ->get();

        $ret = [];
        $goods->map(function ($value) use (&$ret) {
//            $value->goods_show_img = Storage::disk(config("admin.upload.disk"))->url($value->goods_show_img);
            $value->goods_show_img   = env('OSS_URL') . $value->goods_show_img;
            $value->exchange_endtime = session('extra.exchange_endtime', '');
            $ret[1][1][]             = $value;

        });
        return $ret;
    }

    protected function getWxAuthUrl()
    {
        $referer = request()->server('HTTP_REFERER');
        if (strpos($referer, '&code=') !== false) {
            $referer = substr($referer, 0, strpos($referer, '&code='));
        }
        return "https://open.weixin.qq.com/connect/oauth2/authorize?appid=" . $this->wechat['appid'] . "&redirect_uri=" . urlencode($referer) . "&response_type=code&scope=snsapi_base&state=123abc#wechat_redirect";
    }

}
