<?php

namespace App\Logic;

use App\Http\Controllers\Api\SysCode\SysCode;
use App\Exceptions\MyException;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

//逸刻省钱包
class Yksave extends Normal2403
{
    public function __construct()
    {
        parent::__construct();
        $this->single_goods_rdr_url = '/yks/detail.html?id={id}';   //单品跳转地址
        $this->goods_list_rdr_url   = '/yks/goods.html';            //商品列表跳转地址
    }
}
