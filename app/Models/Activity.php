<?php

namespace App\Models;

use Encore\Admin\Traits\DefaultDatetimeFormat;
use Illuminate\Database\Eloquent\Model;

class Activity extends Model
{
    protected $table = 'activities';

    use DefaultDatetimeFormat;

    public static $status = [
        1 => '启用',
        0 => '禁用',
    ];

    //兼容 is_local_send_sms
    public static $is_send_sms = [
        1 => '发送',
        0 => '不发',
    ];

    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function getChannelApiSettings()
    {
        $api_settings = [
            'QcpV1' => config('app.QcpV1'),
            'QcpV2' => config('app.QcpV2'),
        ];
        if ($this->channel_appid) {
            $api_settings['QcpV1']['appid']      = $this->channel_appid;
            $api_settings['QcpV1']['secret_key'] = $this->channel_secketkey;
        }

        if ($this->channel2_appid) {
            $api_settings['QcpV2']['appid']       = $this->channel2_appid;
            $api_settings['QcpV2']['secret_key']  = $this->channel2_secret_key;
            $api_settings['QcpV2']['encrypt_key'] = $this->channel2_encrypt_key;
            $api_settings['QcpV2']['encrypt_iv']  = $this->channel2_encrypt_iv;
        }

        return $api_settings;
    }
}
