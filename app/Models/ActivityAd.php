<?php

namespace App\Models;

use Encore\Admin\Traits\DefaultDatetimeFormat;
use Illuminate\Database\Eloquent\Model;

class ActivityAd extends Model
{
//    protected $table = 'activity_ids';
    use DefaultDatetimeFormat;

    public function __construct(array $attributes = []) {
        parent::__construct($attributes);
    }

    protected $fillable = ['activity_id', 'show_title', 'show_img_url', 'jump_url', 'sort', 'status', 'valid_start_at', 'valid_end_at'];

    public function activity() {
        return $this->belongsTo(Activity::class);
    }

}
