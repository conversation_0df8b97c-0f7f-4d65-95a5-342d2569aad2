<?php

namespace App\Models;

use Encore\Admin\Traits\DefaultDatetimeFormat;
use Illuminate\Database\Eloquent\Model;

class ActivityPrize extends Model
{
    protected $table = "activity_prizes";

    use DefaultDatetimeFormat;

    public static $level = [
        1 => '一重礼',
        2 => '二重礼',
        3 => '三重礼',
        4 => '四重礼',
        5 => '五重礼',
    ];

    public function goods() {
        return $this->belongsTo(Goods::class);
    }

    public function activity() {
        return $this->belongsTo(Activity::class);
    }

}
