<?php

namespace App\Models\BL;

use App\Admin\SysCode\SysCode;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Traits\DefaultDatetimeFormat;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Facades\DB;

class Product extends Model
{
    protected $table = 'bl_products';

    use DefaultDatetimeFormat;

    public function projects(): BelongsToMany
    {
        return $this->belongsToMany(Project::class, 'bl_project_products', 'product_id', 'project_id');
    }

    public static function getOptions()
    {
        return static::orderBy('id', 'desc')->pluck('product_name', 'id');
    }

    protected static function boot()
    {
        parent::boot();

        //更新产品编号时，同步更新项目产品表产品编号
        static::updated(function ($model) {
            $changes = $model->getChanges();
            if (!empty($changes) && array_key_exists('product_code', $changes)) {
                DB::table('bl_project_products')
                    ->where('product_id', $model->id)
                    ->update([
                        'product_code' => $model->product_code,
                        'updated_by' => Admin::user()->id,
                        'updated_at' => now(),
                    ]);
            }
        });
    }
}
