<?php

namespace App\Models\BL;

use App\Admin\SysCode\SysCode;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Traits\DefaultDatetimeFormat;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Facades\DB;

class Project extends Model
{
    protected $table = 'bl_projects';

    use DefaultDatetimeFormat;

    public function products(): BelongsToMany
    {
        return $this->belongsToMany(Product::class, 'bl_project_products', 'project_id', 'product_id');
    }

    public static function getOptions($status = null)
    {
        if ($status) {
            return static::where(['status' => 1])->orderBy('id', 'desc')->pluck('project_name', 'id');
        } else {
            return static::orderBy('id', 'desc')->pluck('project_name', 'id');
        }

    }

    protected static function boot()
    {
        parent::boot();

        static::updated(function ($model) {
            $changes = $model->getChanges();
            if (!empty($changes)) {
                //同步更新项目产品表状态及项目编号
                if (array_key_exists('status', $changes) || array_key_exists('project_no', $changes)) {
                    DB::table('bl_project_products')
                        ->where('project_id', $model->id)
                        ->update([
                            'status'     => $changes['status'] ?? $model->status == 1 ? SysCode::COMMON_STATUS_1 : SysCode::COMMON_STATUS_0,
                            'project_no' => $model->project_no,
                            'updated_by' => Admin::user()->id,
                            'updated_at' => now(),
                        ]);
                }
                //更新项目状态时，同步更新黑名单表状态
                if (array_key_exists('status', $changes)) {
                    DB::table('bl_greylists')
                        ->where('project_id', $model->id)
                        ->update([
                            'status'     => $changes['status'] == 1 ? SysCode::COMMON_STATUS_1 : SysCode::COMMON_STATUS_0,
                            'updated_by' => Admin::user()->id,
                            'updated_at' => now(),
                        ]);
                }
            }
        });
    }
}
