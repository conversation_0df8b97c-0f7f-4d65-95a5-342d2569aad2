<?php

namespace App\Models\BL;

use App\Admin\SysCode\SysCode;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Traits\DefaultDatetimeFormat;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

//项目产品关系
class ProjectProduct extends Model
{
    use DefaultDatetimeFormat;

    protected $table = 'bl_project_products';

    public function project()
    {
        return $this->belongsTo(Project::class, 'project_id');
    }

    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    protected static function boot()
    {
        parent::boot();

        //同步删除关联表
        static::deleted(function (ProjectProduct $model) {
            DB::table('bl_greylists')
                ->where('project_id', $model->project_id)
                ->where('product_id', $model->product_id)
                ->delete();
        });

        static::updated(function (ProjectProduct $model) {
            $changes = $model->getChanges();
            if (!empty($changes) && array_key_exists('status', $changes)) {
                $project = DB::table('bl_projects')->find($model->project_id);
                DB::table('bl_greylists')
                    ->where('project_id', $model->project_id)
                    ->where('product_id', $model->product_id)
                    ->update([
                        'status'     => ($changes['status'] == 1 && $project->status == SysCode::COMMON_STATUS_1) ? SysCode::COMMON_STATUS_1 : SysCode::COMMON_STATUS_0,
                        'updated_by' => Admin::user()->id,
                        'updated_at' => now(),
                    ]);
            }
        });
    }
}
