<?php

namespace App\Models\BL;

use Encore\Admin\Traits\DefaultDatetimeFormat;
use Illuminate\Database\Eloquent\Model;

class Record extends Model
{
    protected $table = 'bl_records';

    use DefaultDatetimeFormat;

    const SETTLEMENT_STATUS_NO  = 1;
    const SETTLEMENT_STATUS_YES = 2;

    // 结算状态。1-待结算，2-已结算
    public static $settlement_status = [
        self::SETTLEMENT_STATUS_YES => '已结算',
        self::SETTLEMENT_STATUS_NO  => '待结算',
    ];

    public function project()
    {
        return $this->belongsTo(Project::class, 'project_id');
    }

    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    public function api_setting()
    {
        return $this->belongsTo(ApiSetting::class, 'api_setting_id');
    }

}
