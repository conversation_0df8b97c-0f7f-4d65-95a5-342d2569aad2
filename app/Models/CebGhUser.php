<?php

namespace App\Models;

use Encore\Admin\Traits\DefaultDatetimeFormat;
use Illuminate\Database\Eloquent\Model;

/**
 * 光大工会线上视听会员活动
 * @author: liujq
 * @Time: 2023/7/10
 */
class CebGhUser extends Model
{
    protected $table = 'ceb_gh_users';

    use DefaultDatetimeFormat;

    const STATUS_ENABLE  = 1;
    const STATUS_DISABLE = 0;
    public static $status = [
        self::STATUS_ENABLE  => '启用',
        self::STATUS_DISABLE => '禁用',
    ];

    const GET_STATUS_YES = 1;
    const GET_STATUS_NO  = 0;
    public static $get_status = [
        self::GET_STATUS_YES => '已兑换',
        self::GET_STATUS_NO  => '未兑换',
    ];

    public function goods()
    {
        return $this->belongsTo(Goods::class);
    }

    public function order()
    {
        return $this->belongsTo(Order::class)
            ->where('status', '>=', 0);
    }

}
