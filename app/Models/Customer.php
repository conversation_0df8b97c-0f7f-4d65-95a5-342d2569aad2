<?php

namespace App\Models;

use Encore\Admin\Traits\DefaultDatetimeFormat;
use Illuminate\Database\Eloquent\Model;

class Customer extends Model
{
    protected $table = 'customers';

    use DefaultDatetimeFormat;

    const CUSTOMER_STATUS_ON  = 1;
    const CUSTOMER_STATUS_OFF = 0;

    //兼容 is_local_send_sms
    public static $customer_status = [
        self::CUSTOMER_STATUS_ON  => '启用',
        self::CUSTOMER_STATUS_OFF => '禁用',
    ];

    public function activities()
    {
        return $this->hasMany(Activity::class);
    }

}
