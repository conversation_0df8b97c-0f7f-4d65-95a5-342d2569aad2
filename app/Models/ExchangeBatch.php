<?php

namespace App\Models;


use App\Exceptions\MyException;
use App\Http\Controllers\Api\SysCode\SysCode;
use Encore\Admin\Traits\DefaultDatetimeFormat;
use Illuminate\Database\Eloquent\Model;

class ExchangeBatch extends Model
{
    use DefaultDatetimeFormat;

    // 状态
    public static $status = [
        1 => '在用',
        0 => '禁用',
    ];

    protected static $cached_data = [];//缓存查找到的一些数据

    const TYPE_LIPINKA           = 1; //礼品卡（多选一）
    const TYPE_JICIKA_REP        = 2; //计次卡（多选N，可多次选同一个商品）
    const TYPE_JICIKA            = 3; //不重复计次卡（多选N，不能重复）
    const TYPE_LIBAOKA           = 4; //礼包卡（多组选一组）
    const TYPE_JICIKA_WITH_GROUP = 5; //套餐卡 分组计次卡（多组，每组可多选）

    //兑换码类型。1-礼品卡（多选一），2-计次卡（多选N，可多次选同一个商品），3-不重复计次卡（多选N，不能重复），4-礼包卡（多组选一组）
    public static $type = [
        self::TYPE_LIPINKA           => '礼品卡',
        self::TYPE_JICIKA            => '计次卡',
        self::TYPE_JICIKA_REP        => '可重复计次卡',
        self::TYPE_LIBAOKA           => '礼包卡',
        self::TYPE_JICIKA_WITH_GROUP => '套餐卡',//分组计次卡
    ];

    const EXCHANGE_CYCLE_MONTH    = 1;
    const EXCHANGE_CYCLE_WEEK     = 2;
    const EXCHANGE_CYCLE_DAY      = 3;
    const EXCHANGE_CYCLE_NO_CYCLE = 0;

    public static $exchange_cycle = [
        self::EXCHANGE_CYCLE_MONTH    => '每月一次',
        self::EXCHANGE_CYCLE_WEEK     => '每周一次',
        self::EXCHANGE_CYCLE_DAY      => '每天一次',
        self::EXCHANGE_CYCLE_NO_CYCLE => '无限制',
    ];

    static $exchange_logic_inst = [];

    /**
     * 根据卡类型判断是否有分组信息
     * @param int $type
     * @return bool
     * @date: 2024/11/4
     */
    public static function hasGroups($type)
    {
        return in_array($type, [self::TYPE_LIBAOKA, self::TYPE_JICIKA_WITH_GROUP]);
    }

    /**
     * 生成兑换批次实现类
     * @param $type
     * @return \App\Logic\Exchange\Base
     * @throws MyException
     * @date: 2024/10/25
     */
    public static function factory($type)
    {
        if (!array_key_exists($type, self::$exchange_logic_inst)) {
            $class = '\\App\\Logic\\Exchange\\';
            switch ($type) {
                case self::TYPE_LIPINKA:
                    $class = $class . 'LiPinKa';
                    break;
                case self::TYPE_JICIKA:
                    $class = $class . 'JiCiKa';
                    break;
                case self::TYPE_JICIKA_REP:
                    $class = $class . 'JiCiKaRep';
                    break;
                case self::TYPE_LIBAOKA:
                    $class = $class . 'LiBaoKa';
                    break;
                case self::TYPE_JICIKA_WITH_GROUP:
                    $class = $class . 'JiCiKaWithGroup';
                    break;
                default:
                    throw new MyException('不支持该兑换码类型', SysCode::SYSTEM_ERROR);
            }
            self::$exchange_logic_inst[$type] = new $class();
        }

        return self::$exchange_logic_inst[$type];
    }

    public function activity()
    {
        return $this->belongsTo(Activity::class);
    }

    public function instr()
    {
        return $this->hasOne(ExchangeBatchInstr::class);
    }

    public function exchange_goods()
    {
        return $this->hasMany(ExchangeGoods::class)->orderBy('sort');
    }

    public function exchange_groups()
    {
        return $this->hasMany(ExchangeGroup::class, 'exchange_batch_id', 'id')->orderBy('sort')->orderBy('id');
    }

    //组织成以商品id为键的数组。
    public function get_goods_ids($valid_enable = false)
    {
        $cached_key = 'get_goods_ids:' . $this->id . ':' . (int)$valid_enable;

        if (array_key_exists($cached_key, self::$cached_data)) {
            return self::$cached_data[$cached_key];
        }

        $goods = $this->exchange_goods;
        $ret   = [];
        foreach ($goods as $v) {
            if ($valid_enable && $v->status == 1) {
                $ret[] = $v->goods_id;
            }
        }

        self::$cached_data[$cached_key] = $ret;

        return $ret;
    }

    //组织成以组id为key，值为该组的商品id列表
    public function get_group_goods_ids($valid_enable = false)
    {
        $cached_key = 'get_group_goods_ids:' . $this->id . ':' . (int)$valid_enable;

        if (array_key_exists($cached_key, self::$cached_data)) {
            return self::$cached_data[$cached_key];
        }

        $goods = $this->exchange_goods;
        $ret   = [];
        foreach ($goods as $v) {
            if ($valid_enable && $v->status == 1) {
                $ret[$v->exchange_group_id][] = $v->goods_id;
            }
        }

        self::$cached_data[$cached_key] = $ret;

        return $ret;
    }

    public function get_goods($valid_enable = false)
    {
        $cached_key = 'get_goods:' . $this->id . ':' . (int)$valid_enable;

        if (array_key_exists($cached_key, self::$cached_data)) {
            return self::$cached_data[$cached_key];
        }

        $goods = ExchangeGoods::leftJoin("goods", 'exchange_goods.goods_id', '=', 'goods.id')
            ->where('exchange_goods.exchange_batch_id', $this->id)
            ->select(['exchange_goods.*', 'goods.goods_name', 'goods.goods_type', 'goods.ecp_target', 'goods.ecp_pcode'])
            ->orderBy('exchange_goods.sort')
            ->orderBy('exchange_goods.id');

        if ($valid_enable) {
            $goods->where('exchange_goods.status', 1)->where('goods.status', 1);
        }

        $goods = $goods->get();

        self::$cached_data[$cached_key] = $goods;

        return $goods;
    }

    protected static function boot()
    {
        parent::boot();

        //更新后，如果endtime有更新则会同步更新 exchangeDetail表的endtime
        static::updated(function ($model) {
            $changes = $model->getChanges();
            if (!empty($changes) && !empty(array_intersect(['endtime'], array_keys($changes)))) {
                ExchangeDetail::where('exchange_batch_id', $model->id)
                    ->update([
                        'endtime' => $model->endtime,
                        'expired' => 0  // 0 未过期
                    ]);
            }
        });

        static::deleted(function (Goods $model) {
            try {
                ExchangeBatchInstr::where('exchange_batch_id', $model->id)->delete();
            } catch (\Exception $e) {
            }
            return true;
        });
    }
}
