<?php

namespace App\Models;


use Encore\Admin\Traits\DefaultDatetimeFormat;
use Illuminate\Database\Eloquent\Model;

class ExchangeDetail extends Model
{
    use DefaultDatetimeFormat;

    const STATUS_NO_EXCHANGE       = 1;//未兑换
    const STATUS_EXCHANGEING       = 2;//兑换中
    const STATUS_EXCHANGE_COMPLATE = 3;//兑换完成

    // 状态 1-未兑换，2-兑换中，3-兑换完成
    public static $status = [
        1 => '未兑换',
        2 => '兑换中',
        3 => '兑换完成',
    ];

    const ENABLE_YES     = 1;
    const ENABLE_HEXIAO  = 0;//核销
    const ENABLE_ABANDON = 2;//作废
    const ENABLE_NO      = 3;//禁用

    //可用状态。1-可用，0-禁用
    public static $enable = [
        self::ENABLE_YES     => '可用',
        self::ENABLE_HEXIAO  => '核销',
        self::ENABLE_ABANDON => '作废',//不能恢复
        self::ENABLE_NO      => '禁用',//可以恢复成可用状态
    ];

    const EXPIRED_YES = 1;
    const EXPIRED_NO  = 0;

    //过期状态。0-未过期，1-已过期
    public static $expired = [
        1 => '已过期',
        0 => '未过期',
    ];

    public static $export_column_list = [
        'id'                => 'ID',
        'activity_id'       => '活动',
        'exchange_batch_id' => '批次',
        'num'               => '序列号',
        'code'              => '兑换码',
        'endtime'           => '有效期',
        'status'            => '状态',
        'expired'           => '过期',
        'enable'            => '可用状态',
        'created_at'        => '生成时间',
    ];


    public function activity()
    {
        return $this->belongsTo(Activity::class);
    }

    public function exchange_batch()
    {
        return $this->belongsTo(ExchangeBatch::class);
    }

    public function exchange_group()
    {
        return $this->belongsTo(ExchangeGroup::class);
    }

    public function goods()
    {
        return $this->belongsTo(Goods::class);
    }

    public function exchange_records()
    {
        return $this->hasMany(ExchangeRecord::class)->where('exchange_status', ExchangeRecord::EXCHANGE_STATUS_SUCC)->orderByDesc('id');
    }
}
