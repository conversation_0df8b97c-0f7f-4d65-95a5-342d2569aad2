<?php

namespace App\Models;

use Encore\Admin\Traits\DefaultDatetimeFormat;
use Illuminate\Database\Eloquent\Model;

/**
 * 兑换码禁用记录表
 */
class ExchangeDetailForbidden extends Model
{
    use DefaultDatetimeFormat;

    const FORBIDDEN_YES = '1';
    const FORBIDDEN_NO  = '0';
    // 状态 0-未禁用，1-已禁用
    public static $forbidden_status = [
        self::FORBIDDEN_NO  => '未禁用',
        self::FORBIDDEN_YES => '已禁用',
    ];

    public function activity()
    {
        return $this->belongsTo(Activity::class);
    }

    public function exchange_batch()
    {
        return $this->belongsTo(ExchangeBatch::class);
    }

    public function exchange_detail()
    {
        return $this->belongsTo(ExchangeDetail::class);
    }

}
