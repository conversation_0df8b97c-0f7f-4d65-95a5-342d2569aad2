<?php

namespace App\Models;


use Encore\Admin\Traits\DefaultDatetimeFormat;
use Illuminate\Database\Eloquent\Model;

class ExchangeForbiddenBatch extends Model
{

    use DefaultDatetimeFormat;

    const SETTLEMENT_STATUS_NO  = 1;
    const SETTLEMENT_STATUS_YES = 2;

    // 结算状态。1-待结算，2-已结算
    public static $settlement_status = [
        self::SETTLEMENT_STATUS_YES => '已结算',
        self::SETTLEMENT_STATUS_NO  => '待结算',
    ];

    public function details()
    {
        return $this->hasMany(ExchangeDetailForbidden::class);
    }

}
