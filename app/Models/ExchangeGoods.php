<?php

namespace App\Models;


use Encore\Admin\Facades\Admin;
use Encore\Admin\Traits\DefaultDatetimeFormat;
use Illuminate\Database\Eloquent\Model;

class ExchangeGoods extends Model
{

    use DefaultDatetimeFormat;

    protected $fillable = ['exchange_batch_id', 'exchange_group_id', 'goods_id', 'sort', 'status', 'created_by', 'updated_by', 'created_at', 'updated_at'];

    // 状态
    public static $status = [
        1 => '在用',
        0 => '禁用',
    ];

    public function exchange_batch()
    {
        return $this->belongsTo(ExchangeBatch::class);
    }

    public function exchange_group()
    {
        return $this->belongsTo(ExchangeGroup::class);
    }

    public function goods()
    {
        return $this->belongsTo(Goods::class);
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->created_at = date('Y-m-d H:i:s');
            if (Admin::user()) {
                $model->created_by = Admin::user()->id ?? 0;
            }
        });
        static::updating(function ($model) {
            $model->updated_at = date('Y-m-d H:i:s');
            if (Admin::user()) {
                $model->updated_by = Admin::user()->id ?? 0;
            }
        });
    }

}
