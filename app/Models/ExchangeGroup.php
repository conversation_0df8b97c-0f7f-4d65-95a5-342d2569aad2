<?php

namespace App\Models;


use Encore\Admin\Traits\DefaultDatetimeFormat;
use Illuminate\Database\Eloquent\Model;

class ExchangeGroup extends Model
{
    use DefaultDatetimeFormat;

    protected static $cached_data = [];//缓存查找到的一些数据

    // 状态
    public static $status = [
        1 => '在用',
        0 => '禁用',
    ];

    public function exchange_batch()
    {
        return $this->belongsTo(ExchangeBatch::class);
    }

    public function exchange_goods()
    {
        return $this->hasMany(ExchangeGoods::class, 'exchange_group_id', 'id');
    }

    //组织成以商品id为键的数组。
    public function get_goods_ids($valid_enable = false)
    {
        $cached_key = 'get_goods_ids:' . $this->id . ':' . (int)$valid_enable;

        if (array_key_exists($cached_key, self::$cached_data)) {
            return self::$cached_data[$cached_key];
        }

        $goods = $this->exchange_goods;
        $ret   = [];
        foreach ($goods as $v) {
            if ($valid_enable && $v->status == 1) {
                $ret[] = $v->goods_id;
            }
        }

        self::$cached_data[$cached_key] = $ret;

        return $ret;
    }

    public function get_goods($valid_enable = false)
    {
        $cached_key = 'get_goods:' . $this->id . ':' . (int)$valid_enable;

        if (array_key_exists($cached_key, self::$cached_data)) {
            return self::$cached_data[$cached_key];
        }

        $goods = ExchangeGoods::leftJoin("goods", 'exchange_goods.goods_id', '=', 'goods.id')
            ->where('exchange_goods.exchange_group_id', $this->id)
            ->select(['exchange_goods.*', 'goods.goods_name', 'goods.goods_type', 'goods.ecp_target', 'goods.ecp_pcode'])
            ->orderBy('exchange_goods.sort')
            ->orderBy('exchange_goods.id');

        if ($valid_enable) {
            $goods->where('exchange_goods.status', 1)->where('goods.status', 1);
        }

        $goods = $goods->get();

        self::$cached_data[$cached_key] = $goods;

        return $goods;
    }

}
