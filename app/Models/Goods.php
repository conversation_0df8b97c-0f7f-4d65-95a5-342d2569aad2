<?php

namespace App\Models;

use Encore\Admin\Traits\DefaultDatetimeFormat;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class Goods extends Model
{
    protected $table = 'goods';

    use DefaultDatetimeFormat;

    public static $status = [
        1 => '启用',
        0 => '禁用',
    ];

    // 商品类型。1-实物商品，2-虚拟商品卡密类，3-虚拟商品-异步直充类，4-实物+虚拟 5.短连接 6-家政服务。
    //在Api\SysCode\SysCode里也有定义，但不建议使用
    const GOODS_TYPE_ENTITY             = 1;
    const GOODS_TYPE_CARD               = 2;
    const GOODS_TYPE_RECHARGE           = 3;
    const GOODS_TYPE_ENTITY_AND_VIRTUAL = 4;//主要是组合商品使用
    const GOODS_TYPE_URL                = 5;
    const GOODS_TYPE_SERVICE            = 6;

    public static $goods_type = [
        1 => '实物商品',
        2 => '虚拟卡密商品',
        3 => '虚拟直充商品',
        4 => '实物+虚拟',
        5 => '短连接',
        6 => '家政服务',
    ];

    const COMBIN_TYPE_SINGLE = 1;
    const COMBIN_TYPE_COMBIN = 2;

    public static $combin_type = [
        1 => '单品',
        2 => '组合商品',
    ];

    public function goods_detail()
    {
        return $this->hasOne(GoodsDetail::class);
    }

    public function setGoodsShowImgAttribute($value)
    {
        if ($value === null) {
            $this->attributes['goods_show_img'] = '';
        } else {
            $this->attributes['goods_show_img'] = $value;
        }
    }

    public function setInputPatternIdsAttribute($value)
    {
        if (is_array($value)) {
            $this->attributes['input_pattern_ids'] = json_encode($value);
        } else {
            $this->attributes['input_pattern_ids'] = $value;
        }
    }

    public function getInputPatternIdsAttribute($value)
    {
        if (!empty($value)) {
            return json_decode($value, true);
        } else {
            return [];
        }
    }

    public function children()
    {
        return $this->hasMany(GoodsCombin::class);
    }

    public static function getSelectList()
    {
        $list = static::orderBy('goods_name', 'asc')->get(['id', 'goods_name', 'goods_type', 'status', 'goods_no']);
        $ret  = [];
        foreach ($list as $item) {
            $ret[$item->id] = sprintf('%s【%s】【%s】', $item->goods_name, $item->goods_no, self::$goods_type[$item->goods_type] ?? $item->goods_type);
        }
        return $ret;
    }

    protected static function boot()
    {

        parent::boot();

        static::deleted(function (Goods $model) {

            try {

                GoodsCombin::where('goods_id', $model->id)->delete();
                GoodsDetail::where('goods_id', $model->id)->delete();

            } catch (\Exception $e) {

            }

            return true;

        });

        static::updated(function (Goods $model) {
            if ($model->combin_type != Goods::COMBIN_TYPE_COMBIN) {
                //删除组合产品表的配置
                GoodsCombin::where('goods_id', $model->id)->delete();
            }
        });

    }

}
