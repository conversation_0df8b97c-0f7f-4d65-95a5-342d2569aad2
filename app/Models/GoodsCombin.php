<?php

namespace App\Models;

use Encore\Admin\Traits\DefaultDatetimeFormat;
use Illuminate\Database\Eloquent\Model;

class GoodsCombin extends Model
{

    const UPDATED_AT = null;

    protected $table = 'goods_combins';

    protected $fillable=['goods_id','child_goods_id','created_at'];

    use DefaultDatetimeFormat;

    public function goods()
    {
        return $this->belongsTo(Goods::class,'child_goods_id','id');
    }

}
