<?php

namespace App\Models;

use App\Scopes\ApiUserStatusScope;

use Illuminate\Database\Eloquent\Model;

class GoodsDetail extends Model
{
    public function goods() {
        return $this->belongsTo(Goods::class);
    }

    public function setGoodsImgsAttribute($goodsImgs)
    {
        if (is_array($goodsImgs)) {
            $this->attributes['goods_imgs'] = json_encode($goodsImgs);
        }
    }

    public function getGoodsImgsAttribute($goodsImgs)
    {
        return json_decode($goodsImgs, true);
    }
}
