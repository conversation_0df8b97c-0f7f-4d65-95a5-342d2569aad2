<?php

namespace App\Models;


use App\Http\Controllers\Api\SysCode\SysCode;
use Carbon\Carbon;
use Encore\Admin\Auth\Database\Administrator;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Traits\DefaultDatetimeFormat;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class OrderSub extends Model
{
    protected $table = "order_subs";

    use DefaultDatetimeFormat;

    public static $status = [
        -1 => '无效订单',
        0  => '已取消',
        1  => '未处理',
        2  => '处理中',
        3  => '成功',
        4  => '失败',
        5  => '失败，待重提',
    ];

    public static $sms_status = [
        1 => '未知',
        2 => '成功',
        3 => '失败',
    ];

    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public function goods()
    {
        return $this->belongsTo(Goods::class, 'goods_id');
    }

    /**
     * 失败订单重提
     * @param $id int 子订单id。这里不传model是因为重提时需要即时的订单状态。
     * @return array
     *            Date: 12/1/21
     */
    public static function reSubmit($id)
    {
        $ret                 = [];
        $ret['desc']         = '重提失败子订单';
        $ret['user_id']      = Admin::user()->id;
        $ret['user_name']    = Administrator::where('id', Admin::user()->id)->value('username');
        $ret['order_sub_id'] = $id;

        $order_sub = OrderSub::find($id);
        if (empty($order_sub)) {
            $ret['error_msg'] = '无此订单';
            return $ret;
        }

        $ret['sub_order_no'] = $order_sub->sub_order_no;
        $ret['order_id']     = $order_sub->order_id;
        $ret['order_no']     = $order_sub->order_no;

        //必须是失败的才能重提
        if ($order_sub->status == SysCode::ORDER_SUB_STATUS_4 && in_array($order_sub->goods_type, [SysCode::GOODS_TYPE_2, SysCode::GOODS_TYPE_3])) {

            try {
                DB::transaction(function () use ($order_sub) {
                    if (DB::table('order_subs')
                        ->where(['id' => $order_sub->id, 'status' => SysCode::ORDER_SUB_STATUS_4])
                        ->update(['status' => SysCode::ORDER_SUB_STATUS_1])
                    ) {

                        DB::table('orders')
                            ->where(['id' => $order_sub->order_id])
                            ->update(['status' => SysCode::ORDER_STATUS_1]);
                    }
                });
            } catch (\Exception $e) {
                $ret['error_msg'] = $e->getMessage();
                Log::channel('admin_special_optlog')->error($e->getMessage(), $e->getTrace());
                return $ret;
            }

            $ret['error_msg'] = '重提任务提交成功';

        } else {
            $ret['error_msg'] = '该订单不为失败状态，或商品类别不为虚拟商品，无需重提。';
        }

        return $ret;

    }

}
