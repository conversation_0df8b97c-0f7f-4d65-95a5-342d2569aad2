<?php

namespace App\Models;
use Illuminate\Database\Eloquent\Model;


class SmsResult extends Model
{


    const SUCCESS               = 200;
    const GATEWAY_SEQ_EMPTY     = 1000;
    const GATEWAY_SEQ_NOT_EXIST = 1001;

    //系统级错误

    const SYSTEM_ERROR = 9999;

    public static $resp_msg = [
        self::SUCCESS => '成功',
        self::GATEWAY_SEQ_EMPTY => '短信网关序列号为空',
        self::GATEWAY_SEQ_NOT_EXIST => '短信网关序列号不存在',
        self::SYSTEM_ERROR => '系统错误'
    ];


    const SMS_STATUS_1 = 1;
    const SMS_STATUS_2 = 2;
    const SMS_STATUS_3 = 3;
    const SMS_STATUS_4 = 4;
    const SMS_STATUS_5 = 5;

    public static $sms_status = [
        self::SMS_STATUS_1 => '待发送',
        self::SMS_STATUS_2 => '发送中',
        self::SMS_STATUS_3 => '发送成功',
        self::SMS_STATUS_4 => '发送失败',
        self::SMS_STATUS_5 => '发送失败（不再重试）',
    ];
}
