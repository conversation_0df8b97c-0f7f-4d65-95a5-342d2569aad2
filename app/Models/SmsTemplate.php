<?php

/**
 *
 */

namespace App\Models;

use App\Exceptions\MyException;
use Illuminate\Database\Eloquent\Model;

class SmsTemplate extends Model
{

    public function setIdentityAttribute($value)
    {
        if (is_array($value)) {
            $this->attributes['identity'] = self::identityEncode($value);
        } else {
            $this->attributes['identity'] = $value;
        }
    }


    //**************以下根据具体工程做相应调整*****************

    public function category()
    {
        return $this->belongsTo(Activity::class, 'category_id');
    }


    public function getIdentityTable()
    {
        return [
            'order_subs' => '订单',
        ];
    }

    public function getIdentityDealStatus()
    {

        return [
            3 => '订单处理状态-已发货',
            4 => '订单处理状态-发货失败'
        ];
    }

    public function isShowCardNo()
    {
        return [
            '0' => '隐藏卡号',
            '1' => '显示卡号'
        ];
    }


    public function getIdentityShow()
    {
        $identity_arr = $this->identityDecode();
        $ret          = [];
        $ret[]        = $this->getIdentityTable()[$identity_arr['i_table']];
        $ret[]        = $this->category->activity_name;
        $ret[]        = Goods::$goods_type[$identity_arr['goods_type']];
        $ret[]        = $this->getIdentityDealStatus()[$identity_arr['deal_status']];
        $ret[]        = $this->isShowCardNo()[$identity_arr['is_show_card_no']];
        $ret[]        = $this->identityExtensions()[$identity_arr['i_ext']] ?? '';

        return rtrim(implode('，', $ret), "， ");
    }

    /**
     * @param $arr
     *
     * @return string
     * @throws MyException
     */

    public static function identityEncode($arr)
    {

        if (!isset($arr['i_table']) || !isset($arr['project_id'])
            || !isset($arr['goods_type']) || !isset($arr['deal_status']) || !isset($arr['is_show_card_no']) || !isset($arr['i_ext'])) {
            throw new MyException('参数错误。需要以下键值：i_table、project_id、goods_type、deal_status、is_show_card_no、i_ext。');
        }

        return implode(',',
            [
                $arr['i_table'], $arr['project_id'], $arr['goods_type'], $arr['deal_status'], $arr['is_show_card_no'],
                $arr['i_ext']
            ]);
    }

    /**
     * @return array
     */
    public function identityDecode()
    {
        $arr = explode(',', $this->identity);
        return [
            'i_table'         => $arr[0],
            'project_id'      => $arr[1],
            'goods_type'      => $arr[2],
            'deal_status'     => $arr[3],
            'is_show_card_no' => $arr[4],
            'i_ext'           => $arr[5],
        ];
    }

    public function identityExtensions()
    {
        return [
            'icbc_star_points.tran_type.1' => '领取-(星点值专用)',
            'icbc_star_points.tran_type.2' => '兑换-(星点值专用)',
        ];
    }

}
