<?php

namespace App\Models;

use Encore\Admin\Traits\DefaultDatetimeFormat;
use Illuminate\Database\Eloquent\Model;

class Trade extends Model
{
    protected $table = 'trades';

    use DefaultDatetimeFormat;

    public function activity() {
        return $this->belongsTo(Activity::class);
    }

    public function trade_channel() {
        return $this->belongsTo(TradeChannel::class,'pay_channel_id');
    }

    public static $pay_result = [
        '0' => '默认值',
        '1' => '支付中',
        '2' => '支付成功',
        '3' => '支付失败',
        '4' => '退款中',
        '5' => '已退款',
    ];
}
