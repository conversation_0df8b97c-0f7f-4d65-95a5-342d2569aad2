<?php

namespace App\Models;


use Encore\Admin\Traits\DefaultDatetimeFormat;
use Illuminate\Database\Eloquent\Model;

class TradeChannel extends Model
{
    protected $table = "trade_channels";

    use DefaultDatetimeFormat;

    public function setReqParametersAttribute($reqParameters)
    {
        if (is_array($reqParameters)) {
            $this->attributes['req_parameters'] = json_encode($reqParameters);
        }
    }

    public function getReqParametersAttribute($reqParameters)
    {
        return json_decode($reqParameters, true);
    }


    public function setQueryParametersAttribute($queryParameters)
    {
        if (is_array($queryParameters)) {
            $this->attributes['query_parameters'] = json_encode($queryParameters);
        }
    }

    public function getQueryParametersAttribute($queryParameters)
    {
        return json_decode($queryParameters, true);
    }


    public static $reqParams = [
        'appid'             => 'appid 商户编码',
        'desc'              => 'desc 商品描述',
        'amount'            => 'amount 交易金额',
        'refer'             => 'refer 交易参考',
        't'                 => 't 请求服务时间',
        'callback'          => 'callback 异步交易指定的回调地址',
        'redirect'          => 'redirect 交易完成后的跳转地址',
        'merchanturl'       => 'merchanturl 网关取消该字段',
        'expire'            => 'expire 交易过期时间yyyymmddHHMMSS',
        'app_orderid'       => 'app_orderid app的订单号',
        'cust_id'           => 'cust_id 用户在e生活app内的cust_id',
        'goods_id'          => 'goods_id 商品id',
        'goods_type'        => 'goods_type 商品类型，默认45',
        'product_name'      => 'product_name 商品名称',
        'product_num'       => 'product_num 商品数量',
        'customer_num'      => 'customer_num 登录参数里的customer_num',
        'out_userid'        => 'out_userid 融e购授权参数里的对外用户id',
        'storeid'           => 'storeid 店铺id',
        'storename'         => 'storename 店铺名称',
        'prodid'            => 'prodid 商品id',
        'prodname'          => 'prodname 商品名称',
        'skuid'             => 'skuid 商品skuid',
        'payback_url'       => 'payback_url 返回商城url',
        'payfail_url'       => 'payfail_url 支付失败返回商城url',
        'mins_num'          => 'mins_num 分期期数',
        'pc_order_url'      => 'pc_order_url pc第三方订单详情url',
        'mob_order_url'     => 'mob_order_url 手机第三方订单详情url',
        'order_prod_type'   => 'order_prod_type 订单类型',
        'order_create_type' => 'order_create_type 分期标识空',
        'is_support_coupon' => 'is_support_coupon 是否支持电子券',
        'all_points_flag'   => 'all_points_flag 全积分抵扣标志',
        'good_points'       => 'good_points 兑换商品所需积分',
        'installment_flag'  => 'installment_flag 分期付款标志',
        'installment_times' => 'installment_times 分期期数',
        'card_type'         => 'card_type 支付卡类型',
        'tporder_create_ip' => 'tporder_create_ip e生活扫码支付 终端 IP',
    ];

    public static $queryParams = [
        'appid'   => 'appid 商户编码',
        't'       => 't 请求服务时间',
        'orderid' => 'orderid 网关交易订单号',
        'userid'  => 'openid',
    ];
}
