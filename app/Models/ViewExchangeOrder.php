<?php

namespace App\Models;


use Carbon\Carbon;
use Encore\Admin\Traits\DefaultDatetimeFormat;
use Illuminate\Database\Eloquent\Model;

class ViewExchangeOrder extends Model
{

    use DefaultDatetimeFormat;

    const EXCHANGE_STATUS_SUCC   = 1;
    const EXCHANGE_STATUS_CANCEL = 2;

    // 兑换状态。1-兑换成功，2-取消订单
    public static $exchange_status = [
        self::EXCHANGE_STATUS_SUCC   => '兑换成功',
        self::EXCHANGE_STATUS_CANCEL => '取消订单',
    ];

    public function exchange_batch() {
        return $this->belongsTo(ExchangeBatch::class);
    }

    public function exchange_group() {
        return $this->belongsTo(ExchangeGroup::class);
    }

    public function exchange_detail() {
        return $this->belongsTo(ExchangeDetail::class);
    }

    public function goods() {
        return $this->belongsTo(Goods::class);
    }

    public function order() {
        return $this->belongsTo(Order::class);
    }

    /**
     * 查询一周内数据的作用域
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOneWeek($query)
    {
        return $query->where('created_at', '>=', Carbon::now()->subWeek()->startOfDay());
    }

}
