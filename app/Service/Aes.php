<?php

namespace App\Service;

class Aes
{

	private $hex_iv = '00000000000000000000000000000000';

	// private $key = '397e2eb61307109f6e68006ebcb62f98';

	function __construct($key)
	{
		$this->key = $key;
		$this->key = hash('sha256', $this->key, true);
	}
	/*
    * 字符串加密 不写入文件
    */
	public function encrypt($input)
	{
		$data = openssl_encrypt($input, 'AES-256-CBC', $this->key, OPENSSL_RAW_DATA, $this->hexToStr($this->hex_iv));
		$data = base64_encode($data);
		return $data;
	}

	/*
    * 字符串解密
    */
	public function decrypt($input)
	{
		$decrypted = openssl_decrypt(base64_decode($input), 'AES-256-CBC', $this->key, OPENSSL_RAW_DATA, $this->hexToStr($this->hex_iv));
		return $decrypted;
	}

	function hexToStr($hex)
	{
		$string = '';
		for ($i = 0; $i < strlen($hex) - 1; $i += 2) {
			$string .= chr(hexdec($hex[$i] . $hex[$i + 1]));
		}
		return $string;
	}
}
