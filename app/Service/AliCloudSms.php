<?php


namespace App\Service;

use AlibabaCloud\SDK\Dysmsapi\V20170525\Dysmsapi;

use Darabonba\OpenApi\Models\Config;
use AlibabaCloud\SDK\Dysmsapi\V20170525\Models\SendSmsRequest;
use Illuminate\Support\Facades\Log;

class AliCloudSms
{
    /**
     * 使用AK&SK初始化账号Client
     * @param string $accessKeyId
     * @param string $accessKeySecret
     * @return Dysmsapi Client
     */
    public static function createClient($accessKeyId, $accessKeySecret) {
        $config = new Config([
            // 您的AccessKey ID
            "accessKeyId"     => $accessKeyId,
            // 您的AccessKey Secret
            "accessKeySecret" => $accessKeySecret
        ]);
        // 访问的域名
        $config->endpoint = "dysmsapi.aliyuncs.com";
        return new Dysmsapi($config);
    }

    /**
     * @param string $content
     * @return void
     */
    public static function sendSms($phoneNumbers, $signName, $templateCode, $templateParam = [], $outId = null) {
        try {
            $client         = self::createClient(config("sms.accessKeyId"), config("sms.accessKeySecret"));
            $sendSmsRequest = new SendSmsRequest([
                "phoneNumbers"  => $phoneNumbers,
                "signName"      => $signName,
                "templateCode"  => $templateCode,
                "templateParam" => json_encode($templateParam),
//                "outId"         => "fwefewfwe"
            ]);
            if (!empty($outId)) {
                $sendSmsRequest["outId"] = $outId;
            }
            Log::channel("send_sms")->info(getStdLogMessage('AliCloudSms->sendSms->request', $sendSmsRequest));

            $response = $client->sendSms($sendSmsRequest);

            $ret = $response->toMap();

//            $ret = json_decode('{"headers":{"Date":["Tue, 14 Sep 2021 05:29:03 GMT"],"Content-Type":["application\/json;charset=utf-8"],"Content-Length":["110"],"Connection":["keep-alive"],"Access-Control-Allow-Origin":["*"],"Access-Control-Allow-Methods":["POST, GET, OPTIONS"],"Access-Control-Allow-Headers":["X-Requested-With, X-Sequence, _aop_secret, _aop_signature"],"Access-Control-Max-Age":["172800"],"x-acs-request-id":["419F2981-9251-5CF8-822E-F604E085F0ED"]},"body":{"Code":"OK","Message":"OK","BizId":"155121131597343574^0","RequestId":"419F2981-9251-5CF8-822E-F604E085F0ED"}}', true);

            Log::channel("send_sms")->info(getStdLogMessage('AliCloudSms->sendSms->response', [
                    'request'  => $sendSmsRequest,
                    'response' => $ret
                ])
            );

            if (isset($ret['body']['Code'])) {
                if ($ret['body']['Code'] === 'OK') {
                    return [
                        "code"       => 0,
                        "msg"        => $ret['body']['Message'],
                        "gateway_id" => $ret['body']['BizId'],
                        "serial_no"  => $ret['body']['RequestId'],

                    ];
                } else {
                    return [
                        "code"      => -1,
                        "msg"       => $ret['body']['Code'] == 'isv.BUSINESS_LIMIT_CONTROL' ? "请一分钟后再试" : $ret['body']['Message'],
                        "serial_no" => $ret['body']['RequestId'],

                    ];
                }
            }

            return false;

        } catch (\Exception $e) {
            Log::error($e->getMessage(), $e->getTrace());
            return false;
        }
    }
}
