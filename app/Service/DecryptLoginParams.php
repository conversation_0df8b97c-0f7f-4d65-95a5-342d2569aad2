<?php

/**
 * 获取参数并解析参数
 */

namespace App\Service;

class DecryptLoginParams
{
    //做解析用
    public static function decrypt($loginParams)
    {
        $loginParams = str_replace(" ", "+", $loginParams);
        $String_key = config('api.icbc_elife_aes_key');
        $params = base64_decode(Elife_aes::AesDecrypt(
            $loginParams,
            base64_decode($String_key)
        ));
        $json_str = str_replace("'", '"', $params);
        $params = json_decode($json_str, true);
        return $params;
    }
}
