<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON><PERSON>
 * Date: 2020/6/30
 * Time: 15:13
 */

namespace App\Service;

use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\InvalidArgumentException;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;
use SysCode;

class Ecp
{

    private $default_username;
    private $default_secretkey;

    private $categories_url;
    private $subcategories_url;
    private $submit_url;
    private $mobiletelsegment_url;
    private $orderstate_url;
    private $useraccount_url;

    public function __construct() {
        $this->default_username  = config('app.ecp.username');
        $this->default_secretkey = config('app.ecp.secretkey');

        $this->categories_url       = config('app.ecp.categories_url');
        $this->subcategories_url    = config('app.ecp.subcategories_url');
        $this->submit_url           = config('app.ecp.submit_url');
        $this->mobiletelsegment_url = config('app.ecp.mobiletelsegment_url');
        $this->orderstate_url       = config('app.ecp.orderstate_url');
        $this->useraccount_url      = config('app.ecp.useraccount_url');
    }

    /**
     * 增值商品大类
     */
    public function getCategories() {
        $url  = $this->categories_url;
        $data = array();
        $re   = requestUrlByPostBuild($this->categories_url, $data, $result);
        if ($re) {
            $ecp_result = json_decode($result, true);
            unset($result);  //解除引用绑定
            if ($ecp_result['retcode'] == '0000' && !empty($ecp_result['retinfo'])) {
                return $ecp_result['retinfo'];
            }
        }
        unset($result);  //解除引用绑定
        return array();
    }

    /**
     * 根据大类查询增值商品小类
     *
     * @param string $pcode 大类
     * @param string $username ecp 账号，为空则取默认配置
     * @param string $secret_key ecp 密钥
     * @return array
     */
    public function getSubCategories($pcode, $username = '', $secret_key = '') {
        $url  = $this->subcategories_url;
        $data = array(
            'username'   => !empty($username) ? $username : $this->default_username,
            'pcode'      => $pcode,
            'timestamp'  => time(),
            'msgencrypt' => 1,
            'secretkey'  => !empty($secret_key) ? $secret_key : $this->default_secretkey,
        );
        //把请求数组按键名排序
        ksort($data);
        $data['appsign'] = md5(implode("", $data));
        unset($data['secretkey']);

        $start_timestarmp = time();

        $re = requestUrlByPostBuild($url, $data, $result);
        if ($re) {
            $ecp_result = json_decode($result, true);
            unset($result);  //解除引用绑定
            if ($ecp_result['retcode'] == '0000' && !empty($ecp_result['retinfo'])) {
                return $ecp_result['retinfo'];
            }
            $this->_ecpLogError($url, $data, $ecp_result, $start_timestarmp);
        }
        unset($result);  //解除引用绑定
        return array();
    }

    /**
     * 增值商品订购
     *
     * @param string $pcode 格式:大类商品代号_小类商品代号(tel_yd1)
     * @param string $orderid 订单号
     * @param string $user_mobile 用户手机号。tel、flow类的充值手机号。卡券类中的同步直充也用该参数做充值账号。
     * @param string $account 直充账号。如果大类为tel、flow，且$account不为空，则覆盖$user_mobile参数；如果大类为四大卡券类，且$account验证为手机号，则会覆盖$user_mobile参数。如果大类为recharge，该参数不能为空。
     * @param string $username ecp 账号，为空则取默认配置
     * @param string $secret_key ecp 密钥
     * @return array 请求成功返回 [ "code" => "0000", "data" => [ "sequence_no" => "aaa1574", "activation_code" => "aaa1574", "endtime" => "2017-01-31 00:00:00", "requestid" => "**************", "orderid" => "18h16032115381623911" ] ]
     *               返回false，表示请求没有提交到ecp。
     *               返回[]，表示超时或网络错误。
     */
    public function submitOrder($pcode, $orderid, $user_mobile, $account = '', $username = '', $secret_key = '') {
        // tel, flow, qcoins, recharge, mtickets-电子票务, mcoupons-O2O电子消费券, onlinevip-线上会员特权, mvoucher-电商代金券
        $url          = $this->submit_url;
        $ecp_code_arr = explode('_', $pcode);
        $data         = array(
            'username'   => !empty($username) ? $username : $this->default_username,
            'pcode'      => $pcode,
            'requestid'  => $orderid,
            'timestamp'  => time(),
            'msgencrypt' => 1,
            'telphone'   => $user_mobile,
            'secretkey'  => !empty($secret_key) ? $secret_key : $this->default_secretkey,
        );

        if ($ecp_code_arr[0] == 'qcoins') {

            if (!empty($account)) {
                $data['qq'] = $account;
            } else {
                return false; //ecp要求有telphone，但qcoins大类里充值账号为qq号，业务端有可能模拟手机号***********给telphone参数，所有这里限制充值账号必须有值。否则不提交到ecp。
            }

        } elseif ($ecp_code_arr[0] == 'recharge') {

            if (!empty($account)) {
                $data['recharge_no'] = $account;
            } else {
                return false; //ecp要求有telphone，但recharge大类里充值账号不一定为手机号，业务端有可能模拟手机号***********给telphone参数，所有这里限制充值账号必须有值。否则不提交到ecp。
            }

        } elseif (in_array($ecp_code_arr[0], array('tel', 'flow'))) {

            if (!empty($account)) {
                $data['telphone'] = $account;
            }

        } else {

            if (!empty($account) && checkphone($account)) {
                //卡券类里的同步直充账号处理，比如爱奇艺直充
                $data['telphone'] = $account;
            }

        }

        if (empty($data['telphone'])) {
            return false; //未提交到ecp之前，返回false
        }

        //把请求数组按键名排序
        ksort($data);
        $data['sign'] = md5(implode("", $data));
        unset($data['secretkey']);

        $timestamp1 = time();

        $re = requestUrlByPostBuild($url, $data, $result, 30);

        if ($re) {
            $ecp_result = json_decode($result, true);
            unset($result);  //解除引用绑定
            $back['code'] = $ecp_result['retcode'];
            if ($ecp_result['retcode'] == '0000' && !empty($ecp_result['retinfo'])) {
                $this->_ecpLogSucc($url, $data, $ecp_result, $timestamp1);
                $back['data'] = $ecp_result['retinfo'];
            } else {
                $this->_ecpLogError($url, $data, $ecp_result, $timestamp1);
                $back['data'] = $ecp_result['retmsg'];
            }
            return $back;
        }

        unset($result);  //解除引用绑定
        return [];//超时或网络错误。不确定是否提交到ecp，返回空数组。
    }

    /**
     * 查询手机号归属地
     * @param string $mobile
     * @return array
     */
    public function mobileTelSegment($mobile) {
        $url  = $this->mobiletelsegment_url;
        $data = array(
            'mobile' => $mobile,
        );
        $re   = requestUrlByPostBuild($url, $data, $result);
        if ($re) {
            $ecp_result = json_decode($result, true);
            unset($result);  //解除引用绑定
            if ($ecp_result['retcode'] == '0000' && !empty($ecp_result['retinfo'])) {
                return $ecp_result['retinfo'];
            }
        }
        unset($result);  //解除引用绑定
        return array();
    }

    /**
     * 查询订单
     * @auther gaomin
     * 2017年9月18日
     *
     * @param string $pcode 格式:大类商品代号
     * @param string $orderid 订单号
     * @param string $username ecp 账号，为空则取默认配置
     * @param string $secret_key ecp 密钥
     * @return array  {"code":"0000","data":{"request_id":"T15090916320701765","order_i
d":"10g15090916320721728","show_price":"10000","sell_price":"11000","ct
     * ime":"2015 - 09 - 09 16:32:07","pay_time":"0000 - 00 - 00
     * 00:00:00","mobileno":"13436554542","operator":"1","belong_to":"北京移动
     * ","reason":"","is_pay":0,"is_success":0,"is_refund":0},"retmsg":"请求成
     * 功"}
     */
    public function queryOrderState($pcode, $orderid, $username = '', $secret_key = '') {
        $url  = $this->orderstate_url;
        $ecp_code_arr = explode('_', $pcode);
        $data = array(
            'username'   => !empty($username) ? $username : $this->default_username,
            'pcode'      => $ecp_code_arr[0],
            'request_id' => $orderid,
            'timestamp'  => time(),
            'msgencrypt' => 1,
            'secretkey'  => !empty($secret_key) ? $secret_key : $this->default_secretkey,
        );
        //把请求数组按键名排序
        ksort($data);
        $data['appsign'] = md5(implode("", $data));
        unset($data['secretkey']);

        $start_timestamp = time();

        $re = requestUrlByPostBuild($url, $data, $result);

        if ($re) {
            $ecp_result = \json_decode($result, true);
            unset($result);  //解除引用绑定
            $back['code'] = $ecp_result['retcode'];
            if ($ecp_result['retcode'] == '0000' && !empty($ecp_result['retinfo'])) {
                $this->_ecpLogSucc($url, $data, $ecp_result, $start_timestamp);
                $back['data'] = $ecp_result['retinfo'];
            } else {
                $this->_ecpLogError($url, $data, $ecp_result, $start_timestamp);
                $back['data'] = $ecp_result['retmsg'];
            }
            return $back;
        }
        unset($result);  //解除引用绑定
        return array();
    }

    /**
     * 调接口成功记入日志
     * @param String $url 地址
     * @param array $data 传入值
     * @param array $result 返回值
     *
     */
    private function _ecpLogSucc($url, $data, $result, $timestamp_start = 0, $timestamp_end = 0) {

        if ($timestamp_end === 0) {
            $timestamp_end = time();
        }
        Log::channel('http_req_log')->info(getStdLogMessage('ecp_api', '', ['url' => $url, 'request' => $data, 'response' => $result], $timestamp_start, $timestamp_end));
    }

    /**
     * 调接口失败记入日志
     * @param String $url 地址
     * @param array $data 传入值
     * @param array $result 返回值
     *
     */
    private function _ecpLogError($url, $data, $result, $timestamp_start = 0, $timestamp_end = 0) {
        if ($timestamp_end === 0) {
            $timestamp_end = time();
        }
        Log::channel('http_req_log')->error(getStdLogMessage('ecp_api', '', ['url' => $url, 'request' => $data, 'response' => $result], $timestamp_start, $timestamp_end));
    }

    private function do_http_request($url, $data, $methond = 'post', $headers = [], $timeout = 30) {

        $client      = new Client();
        $reqTime     = millisecond();
        $rsp_content = '';

        $options = [];
        if (\is_array($data)) {
            $options = [
                'form_params' => $data,
            ];
        } else if (\is_string($data)) {
            $options = [
                'body' => $data,
            ];
        } else {
            throw new InvalidArgumentException('Passing in the "body" request '
                . 'option as an array to send a request is not supported. '
                . 'Please use the "form_params" request option to send a '
                . 'application/x-www-form-urlencoded request, or the "multipart" '
                . 'request option to send a multipart/form-data request.');
        }

        $options['timeout'] = $timeout;
        if (!empty($headers)) {
            $options['headers'] = $headers;
        }

        $response    = $client->request($methond, $url, $options);
        $rsp_content = $response->getBody()->getContents();


        $rspTime = millisecond();

        $log_data = [
            'reqTime'  => $reqTime,
            'rspTime'  => $rspTime,
            'procTime' => $rspTime - $reqTime,
            'url'      => $url,
            'method'   => $methond,
            'headers'  => $headers,
            'reqData'  => $data,
            'rspData'  => $rsp_content,
        ];

        Log::debug(json_encode($log_data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));

//        try {
//        } catch (RequestException $e) {
//
//            //"cURL error 28: Operation timed out after 3114 milliseconds with 0 bytes received"
//            if (strpos($e->getMessage(), 'cURL error 28: Operation timed out') !== FALSE) {
//                Log::error($e);
//                throw $e;
//            }
//
//        } catch (InvalidArgumentException $e) {
//
//            //反序列化失败。 vendor/guzzlehttp/guzzle/src/functions.php json_decode
//            $this->logger->error($e);
//
//        } catch (\Exception $e) {
//
//            Log::error($e);
//
//        }
    }

}
