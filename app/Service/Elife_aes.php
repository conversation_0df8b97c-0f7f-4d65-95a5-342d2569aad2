<?php

namespace App\Service;

class Elife_aes
{
    /**
     * This was AES-128 / CBC / PKCS5Padding
     * return base64_encode string
     * <AUTHOR>
     * @param string $plaintext
     * @param string $key
     * @return string
     */
    public static function AesEncrypt($plaintext, $key = null)
    {
        $data = openssl_encrypt($plaintext, 'AES-128-CBC', $key, OPENSSL_RAW_DATA,random_bytes(16));
        $data = base64_encode($data);
        return $data;
    }

    /**
     * This was AES-128 / CBC / PKCS5Padding
     * <AUTHOR>
     * @param string $encrypted     base64_encode encrypted string
     * @param string $key
     * @throws CException
     * @return string
     */
    public static function AesDecrypt($encrypted, $key = null)
    {
        $decrypted = openssl_decrypt(base64_decode($encrypted), 'AES-128-CBC', $key, OPENSSL_RAW_DATA);
        return $decrypted;
    }
}
