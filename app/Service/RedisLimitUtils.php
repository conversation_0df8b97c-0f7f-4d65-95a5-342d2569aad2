<?php

namespace App\Service;

use Illuminate\Support\Carbon;

/**
 * Redis计数帮助类
 */
class RedisLimitUtils
{
    const TYPE_END_OF_MINUTE  = 'endOfMinute';
    const TYPE_END_OF_QUARTER = 'endOfQuarter';
    const TYPE_END_OF_HOUR    = 'endOfHour';
    const TYPE_END_OF_DAY     = 'endOfDay';
    const TYPE_END_OF_WEEK    = 'endOfWeek';
    const TYPE_END_OF_MONTH   = 'endOfMonth';
    const TYPE_END_OF_YEAR    = 'endOfYear';


    private static $instance;

    private $redis;

    public static function getInstance()
    {
        if (!self::$instance) {
            self::$instance = new RedisLimitUtils();
        }
        return self::$instance;
    }

    public function __construct()
    {
        $this->redis = app('redis.connection');
    }

    /**
     * 如果key不存在，则执行incr并设置过期时间；如果key存在则不再设置过期时间。
     * @param $key
     * @param $ttl int ttl
     * @return int
     */
    public function increment($key, $ttl)
    {
        $lua = "if redis.call('exists',KEYS[1])<1 then local v=redis.call('incr',KEYS[1]);redis.call('expire',KEYS[1],ARGV[1]);return v;else return redis.call('incr',KEYS[1]);end";
        return (int)$this->redis->eval($lua, 1, $key, $ttl);
    }

    /**
     * 执行incr并设置过期时间。每次执行均会设置ttl。
     * @param $key
     * @param $ttl int ttl
     * @return int
     */
    public function increment_with_ttl($key, $ttl)
    {
        $lua = "local v=redis.call('incr',KEYS[1]);redis.call('expire',KEYS[1],ARGV[1]);";
        return (int)$this->redis->eval($lua, 1, $key, $ttl);

//        $result = $this->redis->multi()
//            ->incr($key)
//            ->expire($key, $ttl)
//            ->exec();
//        return (int)$result[0];
    }

    /**
     * 检查key值是否超限
     * @param $key
     * @param $max_limit int 阈值
     * @return bool
     */
    public function check($key, $max_limit = 3)
    {
        $val = $this->redis->get($key) ?? 0;
        if ($val >= $max_limit) {
            return false;
        }
        return true;
    }

    /**
     * 获取key值
     * @param $key
     * @param $default
     * @return mixed|null
     */
    public function get($key, $default = null)
    {
        return $this->redis->get($key) ?? $default;
    }

    /**
     * 自减，值不会小于0。
     * @param $key
     * @return int
     */
    public function decrement($key)
    {
        //if v then else end 等同于 if v~=false then else end
        //如果键不存在，则lua返回false，而不是nil
        $lua = "local v = redis.call('get', KEYS[1]); if v then if tonumber(v)>0 then return redis.call('decr',KEYS[1]); else return 0; end else return 0;end";
        return (int)$this->redis->eval($lua, 1, $key);
    }

    public function getSeconds($type = 'endOfDay')
    {
        return Carbon::now()->diffInSeconds(Carbon::now()->$type());
//        return Carbon::now()->$type()->timestamp - Carbon::now()->timestamp;
//        return Carbon::now()->$type()->timestamp - time();
    }

}
