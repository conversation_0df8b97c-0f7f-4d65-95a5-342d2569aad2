<?php

namespace App\Service;


use Illuminate\Support\Facades\Log;

final class SignalDealHelper
{
    private static $signal = -1;

    // 信号回调
    public static function handler($sig)
    {
        self::$signal = $sig;
        Log::warning("[pid:" . getmypid() . "] signal: " . $sig . " triggered");
    }

    public static function getSignal(){

        return self::$signal;

    }

    // 安装信号
    public static function installSignal()
    {

        pcntl_async_signals(true);
        pcntl_signal(SIGINT,  [SignalDealHelper::class, "handler"]); //2
        pcntl_signal(SIGTERM, [SignalDealHelper::class, "handler"]); //15
        pcntl_signal(SIGHUP,  [SignalDealHelper::class, "handler"]);

    }

}
