<?php


namespace App\Service;

use AlibabaCloud\SDK\Dysmsapi\V20170525\Dysmsapi;

use Darabonba\OpenApi\Models\Config;
use AlibabaCloud\SDK\Dysmsapi\V20170525\Models\SendSmsRequest;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\InvalidArgumentException;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;

class Sms
{
    const MSGTYPE_NORMAL   = 1;
    const MSGTYPE_TEMPLATE = 2;

    /**
     * 短信发送
     * @param string $mobile 手机号
     * @param int $msgtype 短信类型。1-普通短信，2-模板短信
     * @param string|array $content 如果$msgtype=1，则是短信内容，如果$msgtype=2则是模板中变量的值，json格式。
     * @param string $temp_id 模板编号
     * @param string $signtag 签名或签名序号
     * @param null $appid 短信代理网关的appid，非必须。
     * @param null $secret_key 短信代理网关的签名秘钥，非必须。
     * @return array   {"code": 200,"msg":"成功","data": {"msgid": "816f333305664fb9bdd8c1bc96ae12b1" } }
     */
    public static function sendSms($mobile, $msgtype, $content, $temp_id = '', $signtag = '', $appid = NULL, $secret_key = NULL)
    {
        try {
            $appid      = $appid ?? config('sms.sms_appid');
            $secret_key = $secret_key ?? config('sms.sms_secret_key');

            $req_data = [
                'appid'    => $appid,
                'mobile'   => $mobile,
                'msgtype'  => $msgtype,
                'tempid'   => $temp_id,
                'content'  => $msgtype == 2 ? json_encode($content) : $content,
                'extcode'  => '',
                'signtag'  => $signtag,
                'signtext' => '',
            ];

            $req_data['sign'] = self::get_sign($req_data, $secret_key);

            $url = config('sms.sms_url');
            $ret = self::do_http_post($url, $req_data);

            try {
                Log::channel('http_req_log')->info(json_encode([
                    '$url'     => $url,
                    'request'  => $req_data,
                    'response' => $ret,
                ], JSON_UNESCAPED_UNICODE));
            } catch (\Exception $e) {
            }

            return $ret;

        } catch (\Exception $e) {
            Log::error($e->getMessage(), $e->getTrace());
            return ['code' => 500, 'msg' => $e->getMessage()];
        }
    }

    /**
     * 获取签名
     * @param $req_params
     * @param $secret_key
     * @return string
     */
    private static function get_sign($req_params, $secret_key)
    {

        ksort($req_params);
//        unset($req_params['sign']);
        $key_vals = '';
        foreach ($req_params as $key => $val) {
            if ($val !== '') {
                $key_vals .= "$key=$val&";
            }
        }
        $key_vals = rtrim($key_vals, '&');

        return md5($key_vals . $secret_key);
    }

    /**
     * 验证签名
     * @param $req_params
     * @param $secret_key
     * @return bool
     */
    private static function check_sign($req_params, $secret_key)
    {

        $org_sign = $req_params['sign'];

        unset($req_params['sign']);

        $new_sign = self::get_sign($req_params, $secret_key);

        return $new_sign === $org_sign;
    }

    private static function do_http_post($url, $data, $timeout = 30, $is_return_json = true)
    {

        $client = new Client();
        $resp   = [];

        try {
            $response = $client->request('POST', $url, [
                'form_params' => $data,
                'timeout'     => $timeout,
                'headers'     => [
                    'Content-type' => 'application/x-www-form-urlencoded;charset=utf-8'
                ]
            ]);

            $resp = $rsp_content = $response->getBody()->getContents();

            if ($is_return_json) {
                $resp = json_decode($rsp_content, true);
            }

        } catch (RequestException $e) {

            //"cURL error 28: Operation timed out after 3114 milliseconds with 0 bytes received"
            if (strpos($e->getMessage(), 'timed out') !== FALSE) {
                $resp = ['code' => 28, 'msg' => $e->getMessage()];//超时
            } else {
                $resp = ['code' => 500, 'msg' => $e->getMessage()];//超时
            }

        } catch (InvalidArgumentException $e) {

            //反序列化失败。 vendor/guzzlehttp/guzzle/src/functions.php json_decode
            $resp = ['code' => 500, 'msg' => $e->getMessage(), 'content' => $resp];

        } catch (\Exception $e) {
            $resp = ['code' => 500, 'msg' => $e->getMessage()];//超时
        }

        if ((!$is_return_json) && is_array($resp)) {
            $resp = json_encode($resp, JSON_UNESCAPED_UNICODE);
        }

        return $resp;
    }

}
