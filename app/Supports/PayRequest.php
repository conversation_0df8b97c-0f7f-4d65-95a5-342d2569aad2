<?php
/**
 * 支付提交公共类
 */

namespace App\Supports;


use Illuminate\Support\Facades\Log;

class PayRequest {

    public $reqUrl    = null;
    public $reqParams = null;
    public $secret    = null;
    public $signUrl   = null;
    public $channelParams = null;

    function __construct($reqUrl, array $reqParams, $secret, $signUrl = '', array $channelParams = []) {
        $this->reqUrl    = $reqUrl;
        $this->reqParams = $reqParams;
        $this->secret    = $secret;
        $this->signUrl   = $signUrl;
        $this->channelParams = $channelParams;
    }

    /**
     * 发起支付
     * @param $reqParameterMap 组装好的提交支付所需的参数key-value
     * @return array|mixed
     * @throws \Exception
     */
    public function doPost($reqParameterMap) {

        $logger = Log::channel('trade_log');

        if (empty($reqParameterMap)){
            throw new \Exception('提交充值的所需的参数key-value键值对的集合为空！');
        }

        if (empty($this->reqUrl)) {
            throw new \Exception('提交充值的url为空！');
        }

        if (empty($this->reqParams)) {
            throw new \Exception('提交充值的所需参数集合为空！');
        }

        try {
            $data = $this->getRequestFields($reqParameterMap);

            $sign = $this->generatorSign($data);
            $data['sign'] = $sign;

            $logger->info('req_params:'. json_encode($data));
            $result = $this->sendCurl($this->reqUrl, $data, 'post', FALSE);
            $logger->info('resp:'. $result);

            return $result;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }

    }

    /**
     * 获取支付所需的参数
     * @param $reqParameterMap
     * @return array
     * @throws \Exception
     */
    private function getRequestFields($reqParameterMap) {

        $data = [];
        foreach ($this->reqParams as $v) {
            if (!array_key_exists($v, $reqParameterMap)) {
                throw new \Exception($v . ',该健值在reqParameterMap中不存在!');
            }
            $data[$v] = $reqParameterMap[$v];
        }
        return $data;
    }

    /**
     * 生成加密sign
     * @param $data
     * @return string
     */
    public function generatorSign($data) {
        $sign = '';
        if (!empty($data) && is_array($data)) {
            $string = '';
            ksort($data);
            foreach ($data as $k => $v) {
                $string .= $k . "=" . $v . "&";
            }
            $string = $string . "secret=" . $this->secret;
            $string = md5($string);
            $sign = strtoupper($string);
        }
        return $sign;
    }

    private function sendCurl ($url, $params, $mode = 'post', $xml = false) {
        $curlHandle = curl_init();
        curl_setopt($curlHandle, CURLOPT_TIMEOUT, 30);
        curl_setopt($curlHandle, CURLOPT_RETURNTRANSFER, true);
        if ($mode == 'post') {
            curl_setopt($curlHandle, CURLOPT_HTTPHEADER, array('Expect:'));
            curl_setopt($curlHandle, CURLOPT_POST, true);
            curl_setopt($curlHandle, CURLOPT_TIMEOUT, '20');
            curl_setopt($curlHandle, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_0); //强制使用哪个版本
            if ($xml === TRUE) {
                $header[] = "Content-type: text/xml"; //定义content-type为xml
                curl_setopt($curlHandle, CURLOPT_HTTPHEADER, $header);
                curl_setopt($curlHandle, CURLOPT_POSTFIELDS, $params);
            } else {
                curl_setopt($curlHandle, CURLOPT_POSTFIELDS, http_build_query($params));
            }
        } else {
            $url .= ( strpos($url, '?') === false ? '?' : '&') . http_build_query($params);
        }
        curl_setopt($curlHandle, CURLOPT_URL, $url);
        if (substr($url, 0, 5) == 'https') {
            curl_setopt($curlHandle, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($curlHandle, CURLOPT_SSL_VERIFYHOST, false);
        }

        $result = curl_exec($curlHandle);
        $curl_errno = curl_errno($curlHandle);
        $curl_error = curl_error($curlHandle);
        if ($curl_errno > 0) {
            throw new \Exception( "cURL Error ($curl_errno): $curl_error\n");
        }
        curl_close($curlHandle);
        return $result;
    }
}
