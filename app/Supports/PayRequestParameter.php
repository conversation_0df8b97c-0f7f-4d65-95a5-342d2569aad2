<?php
/**
 * 支付提交所需参数
 */

namespace App\Supports;


class PayRequestParameter {

    private $appid         = null;  //商户编码
    private $desc          = null;  //商品描述
    private $amount        = null;  //交易金额，单位分，整数
    private $refer         = null;  //交易参考，异步交易的回调中，网关会将该参数原值传给app
    private $t             = null;  //请求服务时间
    private $callback      = null;  //异步交易指定的回调地址
    private $redirect      = null;  //交易完成后的跳转地址，与wap支付相关（不保证一定会调用，依赖用户在浏览器的操作）
    private $merchanturl   = null;  //网关取消该字段
    private $expire        = null;  //交易过期时间yyyymmddHHMMSS
    private $app_orderid   = null;  //app的订单号，若提交时没app_orderid，则该值为空
    private $cust_id       = null;  //用户在e生活app内的cust_id

    // 华夏支付
    private $goods_id      = null;  //商品id 华夏支付
    private $goods_type    = null;  //商品类型，默认45  华夏支付
    private $product_name  = null;  //商品名称 华夏支付
    private $product_num   = null;  //商品数量 华夏支付
    private $customer_num  = null;  //登录参数里的customer_num  华夏支付

    // 融易购提交支付参数
    private $out_userid    = null;  //融e购授权参数里的对外用户id
    private $storeid       = null;  //店铺id
    private $storename     = null;  //店铺名称
    private $prodid        = null;  //商品id
    private $prodname      = null;  //商品名称
    private $skuid         = null;  //商品sku id
    private $payback_url   = null;  //返回商城url
    private $payfail_url   = null;  //支付失败返回商城url
    private $mins_num      = null;  //分期期数
    private $pc_order_url  = null;  //pc第三方订单详情url
    private $mob_order_url = null;  //手机第三方订单详情url
    private $order_prod_type   = null;  //订单类型
    private $order_create_type = null;  //分期标识 空、null-不分期；03-商户分期；04-客户分期
    private $is_support_coupon = null;  //是否支持电子券.空-不支持；1-支持

    // v4 参数
    private $all_points_flag   = null;  //全积分抵扣标志，0-否，1-是； 非1按0处理
    private $good_points       = null;  //兑换商品所需积分，all_points_flag=1
    private $installment_flag  = null;  //分期付款标志 0-仅全款付款 1-仅分期付款
    private $installment_times = null;  //分期期数 installment_flag=1， installment_times>=3
    private $card_type         = null;  //支付卡类型，从左往右：第1位： 工行信用卡支持标志；第2位： 工行借记卡支持标志；第3位： 非工行银联卡支持标志。各标志位，0- 否；1-是；非1按0处理

    private $tporder_create_ip = null;  //e生活扫码支付 终端 IP

    // 支付结果查询参数
    private $orderid           = null;  //gateway_orderid  （融易联的对应platform_orderid 先不考虑）
    private $userid            = null;  //openid

    // 赋值参数后的key-value集合
    private $reqParameterMap   = null;

    public function __construct()
    {
        $this->reqParameterMap = [];
    }

    /**
     * @return array|null
     */
    public function getReqParameterMap(): ?array
    {
        return $this->reqParameterMap;
    }

    /**
     * @param array|null $reqParameterMap
     */
    public function setReqParameterMap(?array $reqParameterMap): void
    {
        $this->reqParameterMap = $reqParameterMap;
    }

    /**
     * @return null
     */
    public function getAppid()
    {
        return $this->appid;
    }

    /**
     * @param null $appid
     */
    public function setAppid($appid): void
    {
        $this->appid = $appid;
        $this->reqParameterMap['appid'] = $appid;
    }

    /**
     * @return null
     */
    public function getDesc()
    {
        return $this->desc;
    }

    /**
     * @param null $desc
     */
    public function setDesc($desc): void
    {
        $this->desc = $desc;
        $this->reqParameterMap['desc'] = $desc;
    }

    /**
     * @return null
     */
    public function getAmount()
    {
        return $this->amount;
    }

    /**
     * @param null $amount
     */
    public function setAmount($amount): void
    {
        $this->amount = $amount;
        $this->reqParameterMap['amount'] = $amount;
    }

    /**
     * @return null
     */
    public function getRefer()
    {
        return $this->refer;
    }

    /**
     * @param null $refer
     */
    public function setRefer($refer): void
    {
        $this->refer = $refer;
        $this->reqParameterMap['refer'] = $refer;
    }

    /**
     * @return null
     */
    public function getT()
    {
        return $this->t;
    }

    /**
     * @param null $t
     */
    public function setT($t): void
    {
        $this->t = $t;
        $this->reqParameterMap['t'] = $t;
    }

    /**
     * @return null
     */
    public function getCallback()
    {
        return $this->callback;
    }

    /**
     * @param null $callback
     */
    public function setCallback($callback): void
    {
        $this->callback = $callback;
        $this->reqParameterMap['callback'] = $callback;
    }

    /**
     * @return null
     */
    public function getRedirect()
    {
        return $this->redirect;
    }

    /**
     * @param null $redirect
     */
    public function setRedirect($redirect): void
    {
        $this->redirect = $redirect;
        $this->reqParameterMap['redirect'] = $redirect;
    }

    /**
     * @return null
     */
    public function getMerchanturl()
    {
        return $this->merchanturl;
    }

    /**
     * @param null $merchanturl
     */
    public function setMerchanturl($merchanturl): void
    {
        $this->merchanturl = $merchanturl;
        $this->reqParameterMap['merchanturl'] = $merchanturl;
    }

    /**
     * @return null
     */
    public function getExpire()
    {
        return $this->expire;
    }

    /**
     * @param null $expire
     */
    public function setExpire($expire): void
    {
        $this->expire = $expire;
        $this->reqParameterMap['expire'] = $expire;
    }

    /**
     * @return null
     */
    public function getAppOrderid()
    {
        return $this->app_orderid;
    }

    /**
     * @param null $app_orderid
     */
    public function setAppOrderid($app_orderid): void
    {
        $this->app_orderid = $app_orderid;
        $this->reqParameterMap['app_orderid'] = $app_orderid;
    }

    /**
     * @return null
     */
    public function getCustId()
    {
        return $this->cust_id;
    }

    /**
     * @param null $cust_id
     */
    public function setCustId($cust_id): void
    {
        $this->cust_id = $cust_id;
        $this->reqParameterMap['cust_id'] = $cust_id;
    }

    /**
     * @return null
     */
    public function getGoodsId()
    {
        return $this->goods_id;
    }

    /**
     * @param null $goods_id
     */
    public function setGoodsId($goods_id): void
    {
        $this->goods_id = $goods_id;
        $this->reqParameterMap['goods_id'] = $goods_id;
    }

    /**
     * @return null
     */
    public function getGoodsType()
    {
        return $this->goods_type;
    }

    /**
     * @param null $goods_type
     */
    public function setGoodsType($goods_type): void
    {
        $this->goods_type = $goods_type;
        $this->reqParameterMap['goods_type'] = $goods_type;
    }

    /**
     * @return null
     */
    public function getProductName()
    {
        return $this->product_name;
    }

    /**
     * @param null $product_name
     */
    public function setProductName($product_name): void
    {
        $this->product_name = $product_name;
        $this->reqParameterMap['product_name'] = $product_name;
    }

    /**
     * @return null
     */
    public function getProductNum()
    {
        return $this->product_num;
    }

    /**
     * @param null $product_num
     */
    public function setProductNum($product_num): void
    {
        $this->product_num = $product_num;
        $this->reqParameterMap['product_num'] = $product_num;
    }

    /**
     * @return null
     */
    public function getCustomerNum()
    {
        return $this->customer_num;
    }

    /**
     * @param null $customer_num
     */
    public function setCustomerNum($customer_num): void
    {
        $this->customer_num = $customer_num;
        $this->reqParameterMap['customer_num'] = $customer_num;
    }

    /**
     * @return null
     */
    public function getOutUserid()
    {
        return $this->out_userid;
    }

    /**
     * @param null $out_userid
     */
    public function setOutUserid($out_userid): void
    {
        $this->out_userid = $out_userid;
        $this->reqParameterMap['out_userid'] = $out_userid;
    }

    /**
     * @return null
     */
    public function getStoreid()
    {
        return $this->storeid;
    }

    /**
     * @param null $storeid
     */
    public function setStoreid($storeid): void
    {
        $this->storeid = $storeid;
        $this->reqParameterMap['storeid'] = $storeid;
    }

    /**
     * @return null
     */
    public function getStorename()
    {
        return $this->storename;
    }

    /**
     * @param null $storename
     */
    public function setStorename($storename): void
    {
        $this->storename = $storename;
        $this->reqParameterMap['storename'] = $storename;
    }

    /**
     * @return null
     */
    public function getProdid()
    {
        return $this->prodid;
    }

    /**
     * @param null $prodid
     */
    public function setProdid($prodid): void
    {
        $this->prodid = $prodid;
        $this->reqParameterMap['prodid'] = $prodid;
    }

    /**
     * @return null
     */
    public function getProdname()
    {
        return $this->prodname;
    }

    /**
     * @param null $prodname
     */
    public function setProdname($prodname): void
    {
        $this->prodname = $prodname;
        $this->reqParameterMap['prodname'] = $prodname;
    }

    /**
     * @return null
     */
    public function getSkuid()
    {
        return $this->skuid;
    }

    /**
     * @param null $skuid
     */
    public function setSkuid($skuid): void
    {
        $this->skuid = $skuid;
        $this->reqParameterMap['skuid'] = $skuid;
    }

    /**
     * @return null
     */
    public function getPaybackUrl()
    {
        return $this->payback_url;
    }

    /**
     * @param null $payback_url
     */
    public function setPaybackUrl($payback_url): void
    {
        $this->payback_url = $payback_url;
        $this->reqParameterMap['payback_url'] = $payback_url;
    }

    /**
     * @return null
     */
    public function getPayfailUrl()
    {
        return $this->payfail_url;
    }

    /**
     * @param null $payfail_url
     */
    public function setPayfailUrl($payfail_url): void
    {
        $this->payfail_url = $payfail_url;
        $this->reqParameterMap['payfail_url'] = $payfail_url;
    }

    /**
     * @return null
     */
    public function getMinsNum()
    {
        return $this->mins_num;
    }

    /**
     * @param null $mins_num
     */
    public function setMinsNum($mins_num): void
    {
        $this->mins_num = $mins_num;
        $this->reqParameterMap['mins_num'] = $mins_num;
    }

    /**
     * @return null
     */
    public function getPcOrderUrl()
    {
        return $this->pc_order_url;
    }

    /**
     * @param null $pc_order_url
     */
    public function setPcOrderUrl($pc_order_url): void
    {
        $this->pc_order_url = $pc_order_url;
        $this->reqParameterMap['pc_order_url'] = $pc_order_url;
    }

    /**
     * @return null
     */
    public function getMobOrderUrl()
    {
        return $this->mob_order_url;
    }

    /**
     * @param null $mob_order_url
     */
    public function setMobOrderUrl($mob_order_url): void
    {
        $this->mob_order_url = $mob_order_url;
        $this->reqParameterMap['mob_order_url'] = $mob_order_url;
    }

    /**
     * @return null
     */
    public function getOrderProdType()
    {
        return $this->order_prod_type;
    }

    /**
     * @param null $order_prod_type
     */
    public function setOrderProdType($order_prod_type): void
    {
        $this->order_prod_type = $order_prod_type;
        $this->reqParameterMap['order_prod_type'] = $order_prod_type;
    }

    /**
     * @return null
     */
    public function getOrderCreateType()
    {
        return $this->order_create_type;
    }

    /**
     * @param null $order_create_type
     */
    public function setOrderCreateType($order_create_type): void
    {
        $this->order_create_type = $order_create_type;
        $this->reqParameterMap['order_create_type'] = $order_create_type;
    }

    /**
     * @return null
     */
    public function getisSupportCoupon()
    {
        return $this->is_support_coupon;
    }

    /**
     * @param null $is_support_coupon
     */
    public function setIsSupportCoupon($is_support_coupon): void
    {
        $this->is_support_coupon = $is_support_coupon;
        $this->reqParameterMap['is_support_coupon'] = $is_support_coupon;
    }

    /**
     * @return null
     */
    public function getAllPointsFlag()
    {
        return $this->all_points_flag;
    }

    /**
     * @param null $all_points_flag
     */
    public function setAllPointsFlag($all_points_flag): void
    {
        $this->all_points_flag = $all_points_flag;
        $this->reqParameterMap['all_points_flag'] = $all_points_flag;
    }

    /**
     * @return null
     */
    public function getGoodPoints()
    {
        return $this->good_points;
    }

    /**
     * @param null $good_points
     */
    public function setGoodPoints($good_points): void
    {
        $this->good_points = $good_points;
        $this->reqParameterMap['good_points'] = $good_points;
    }

    /**
     * @return null
     */
    public function getInstallmentFlag()
    {
        return $this->installment_flag;
    }

    /**
     * @param null $installment_flag
     */
    public function setInstallmentFlag($installment_flag): void
    {
        $this->installment_flag = $installment_flag;
        $this->reqParameterMap['installment_flag'] = $installment_flag;
    }

    /**
     * @return null
     */
    public function getInstallmentTimes()
    {
        return $this->installment_times;
    }

    /**
     * @param null $installment_times
     */
    public function setInstallmentTimes($installment_times): void
    {
        $this->installment_times = $installment_times;
        $this->reqParameterMap['installment_times'] = $installment_times;
    }

    /**
     * @return null
     */
    public function getCardType()
    {
        return $this->card_type;
    }

    /**
     * @param null $card_type
     */
    public function setCardType($card_type): void
    {
        $this->card_type = $card_type;
        $this->reqParameterMap['card_type'] = $card_type;
    }

    /**
     * @return null
     */
    public function getTporderCreateIp()
    {
        return $this->tporder_create_ip;
    }

    /**
     * @param null $tporder_create_ip
     */
    public function setTporderCreateIp($tporder_create_ip): void
    {
        $this->tporder_create_ip = $tporder_create_ip;
        $this->reqParameterMap['tporder_create_ip'] = $tporder_create_ip;
    }

    /**
     * @return null
     */
    public function getOrderid()
    {
        return $this->orderid;
    }

    /**
     * @param null $orderid
     */
    public function setOrderid($orderid): void
    {
        $this->orderid = $orderid;
        $this->reqParameterMap['orderid'] = $orderid;
    }

    /**
     * @return null
     */
    public function getUserid()
    {
        return $this->userid;
    }

    /**
     * @param null $userid
     */
    public function setUserid($userid): void
    {
        $this->userid = $userid;
        $this->reqParameterMap['userid'] = $userid;
    }

}
