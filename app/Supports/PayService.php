<?php

/**
 * 支付服务类
 */

namespace App\Supports;


use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Carbon;

class PayService
{

    private $payRequest = null;

    public function __construct($reqUrl, array $reqParams, $secret, $signUrl = '', array $channelParams = [])
    {

        $this->payRequest = new PayRequest($reqUrl, $reqParams, $secret, $signUrl, $channelParams);
    }

    public function doPost(PayRequestParameter $parameter, $insertTransData = [])
    {

        $logger = Log::channel('trade_log');
        if (empty($insertTransData)) {
            throw new \Exception('插入支付明细表的数值为空！');
        }

        try {

            $reqParamsMap = $parameter->getReqParameterMap();
            $result = $this->payRequest->doPost($reqParamsMap);
            $result = json_decode($result, true);
            if (!empty($result) && ($result['code'] === '0000')) {

                $now_time = Carbon::now()->format("Y-m-d H:i:s");

                $insertTransData['gateway_orderid'] = $result['orderid'];
                $insertTransData['gateway_seq'] = $result['seq'];

                $result['js_package'] = [];
                if (isset($result['form'])) {
                    $result['js_package']['form'] = $result['form'];
                }
                if (isset($result['url'])) {
                    $result['js_package']['url'] = $result['url'];
                    $insertTransData['mweb_url'] = $result['url'];
                }
                if (isset($result['code_url'])) {
                    $result['js_package']['code_url'] = $result['code_url'];
                    $insertTransData['code_url'] = $result['code_url'];
                }
                if (isset($result['qrcode'])) {
                    $result['js_package']['qrcode'] = $result['qrcode'];
                    $insertTransData['qrcode'] = $result['qrcode'];
                }
                $insertTransData['pay_result'] = '1'; // 支付状态设置支付中

                $insertTransData['pay_amount'] = $reqParamsMap['amount'];
                $insertTransData['appid'] = $reqParamsMap['appid'];
                $insertTransData['created_at'] = $now_time;
                $insertTransData['updated_at'] = $now_time;

                $logger->info('insert_data:' . json_encode($insertTransData));
                $db_result = DB::table('trades')->insert($insertTransData);

                if (!$db_result) {
                    throw new \Exception('写交易明细表失败,data: ' . json_encode($insertTransData));
                }
            }
            return $result;
        } catch (\Exception $e) {
            $logger->error($e->getMessage());
        }
    }

    function send_curl_header_json_sign($url, $data, $header)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
        $res = curl_exec($ch);
        curl_close($ch);

        $output = mb_convert_encoding($res, 'UTF-8', 'GB2312');
        $output = str_replace('GB2312', 'utf-8', $output);

        return json_decode(json_encode(simplexml_load_string($output)), true);
    }
}
