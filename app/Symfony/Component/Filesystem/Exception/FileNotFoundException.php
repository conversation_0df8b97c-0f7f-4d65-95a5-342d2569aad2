<?php
/**
 * User: yangy
 * Date: 2021/2/23
 * Time: 11:13
 * Describe:由于采用的Jacobcyl\AliOSS的版本较老,老版本的
 * Symfony\Component\Filesystem\Exception\FileNotFoundException 这个文件不存在,
 * 所以引用 Symfony\Component\HttpFoundation\File\Exception\FileNotFoundException 的这个文件
 *
 * 用扩展的方式修改这个bug,所以命名空间依旧使用Symfony\Component\Filesystem\Exception, 为了保持与之前一致,这样改动最小.
 */

namespace Symfony\Component\Filesystem\Exception;

use Symfony\Component\HttpFoundation\File\Exception\FileNotFoundException as BaseFileNotFoundException;

class FileNotFoundException extends BaseFileNotFoundException
{

}
