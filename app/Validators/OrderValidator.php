<?php

namespace App\Validators;

/**
 *一些在表单验证中无法验证的规则,在这里验证
 *
 */

use App\Http\Controllers\Api\SysCode\SysCode;
use App\Repositories\Order\OrderRepository;

use function GuzzleHttp\json_decode;

class OrderValidator
{

    /**
     * 一些在表单验证中无法验证的规则,在这里验证
     */

    /**
     * 检查Appid
     */
    public  function _checkAppid($appid)
    {
        return  OrderRepository::getUserInfoByAppid($appid);
    }

    /**
     * 检查order_no
     */

    public function _checkOrderNo($order_no)
    {
        return  OrderRepository::getOrderInfoByOrderNo($order_no);
    }

    /**
     * 解析 biz_data
     *
     * 并验证 json里面的参数是否符合要求
     */

    public  function _parseBizData($biz_data)
    {
        $biz_data_array = json_decode($biz_data, true);

        //订单编号
        if (empty($biz_data_array['order_no'])) {
            return SysCode::ORDER_NO_EMPTY_2000;
        }

        //检查订单编号是否存在
        if (!empty($this->_checkOrderNo($biz_data_array['order_no']))) {
            return SysCode::ORDER_NO_EXISTS_2013;
        }

        //下单时间
        if (empty($biz_data_array['order_time'])) {
            return SysCode::ORDER_TIME_EMPTY_2001;
        }
        //商品类型 1:代表实物订单  2:代表虚拟订单
        if (!in_array($biz_data_array['goods_type'], [SysCode::GOODS_TYPE_1, SysCode::GOODS_TYPE_2])) {
            return SysCode::GOODS_TYPE_EMPTY_2002;
        }
        //商品编号
        if (empty($biz_data_array['goods_no'])) {
            return SysCode::GOODS_NO_EMPTY_2003;
        }
        //商品名称
        if (empty($biz_data_array['goods_name'])) {
            return SysCode::GOODS_NAME_EMPTY_2004;
        }
        //订单商品数量
        if ($biz_data_array['goods_num'] <= 0) {
            return SysCode::GOODS_NUM_EMPTY_2005;
        }
        //如果是虚拟商品 只能取1;
        // goods_type = 2 是虚拟商品
        if ($biz_data_array['goods_type'] == SysCode::GOODS_TYPE_2) {
            $biz_data_array['goods_num'] = 1;

            //验证手机号
            if (!preg_match("/^1[3456789]{1}\d{9}$/", $biz_data_array['user_mobile'])) {
                return SysCode::USER_MOBILE_EMPTY_2008;
            }
            //充值账号
            if (empty($biz_data_array['charge_account'])) {
                return SysCode::CHARGE_ACCOUNT_EMPTY_2009;
            }
            //验证ecp_target
            if (empty($biz_data_array['ecp_targe'])) {
                return SysCode::ECP_TARGET_EMPTY_2006;
            }

            //验证ecp_pcode
            if (empty($biz_data_array['ecp_pcode'])) {
                return SysCode::ECP_PCODE_EMPTY_2007;
            }

            if ($biz_data_array['is_sync'] != 0) {
                return SysCode::IS_SYNC_ERROR_2014;
            }
        } else {
            //收货人姓名
            if (empty($biz_data_array['consignee_name'])) {
                return SysCode::CONSIGNEE_NAME_EMPTY_2010;
            }
            //收货人手机号
            if (!preg_match("/^1[3456789]{1}\d{9}$/", $biz_data_array['consignee_phone'])) {
                return SysCode::CONSIGNEE_PHONE_EMPTY_2011;
            }
        }
        // project_id：运营管理系统项目编号，Number，非必须。为0则取appid配置的默认项目编号。 ??

        //是否发送短信
        if (!in_array($biz_data_array['is_send_sms'], [SysCode::IS_SEND_SMS_0, SysCode::IS_SEND_SMS_1])) {
            return SysCode::IS_SEND_SMS_ERROR_2012;
        }

        return SysCode::SUCCESS_0000;
    }

    public function _checkSign($request, $secret_key)
    {
        // $clint_sign = $request->sign;
        // $local_sign = createSign($request->appid . $request->biz_data . $request->serial_no . $secret_key);
        // return $clint_sign == $local_sign;

        return true;
    }
}
