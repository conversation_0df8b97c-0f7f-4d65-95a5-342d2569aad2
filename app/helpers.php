<?php

use App\Exceptions\MyException;
use App\Http\Controllers\Api\SysCode\SysCode;
use Encore\Admin\Facades\Admin;
use GuzzleHttp\Client;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Cell\DataType;

function responseExit($code)
{
    throw new \Exception($code);
}

/**
 * 构造签名串
 */
function createSign($string)
{
    return md5($string);
}


function requestUrlByPostBuild($url, $data, &$result, $timeout = 20)
{
    if (is_array($data)) {
        $data = http_build_query($data);
    }
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_0);
    curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
    $result = curl_exec($ch);
    if (is_string($result) && strlen($result)) {
        $return = 'info';
    } else {
        //        $curl_error = curl_error($ch);
        $return = 'error';
    }
    curl_close($ch);

    if ($return == 'error') {
        //        $_data = is_array($data) ? implode('&', $data) : $data;
        //        write_log(LOG_LEVEL_ERROR, 'curl_error', $url.'|'.$_data.'|'.$curl_error);
        return false;
    }
    return true;
}

/**
 * 获取13位时间戳
 * @return float
 */
function getMillisecond()
{
    list($t1, $t2) = explode(' ', microtime());
    return (float)sprintf('%.0f', (floatval($t1) + floatval($t2)) * 1000);
}

/**
 * 将字符串中{}包含的内容替换为数组中相同键名的值
 * @param string $text 包含{}需要替换内容的字符串
 * @param array $value 键名与字符串{}内容相同的数组
 * @return string 字符串中{}全部匹配替换成功则返回替换后的字符串，否则返回空字符串
 */
function str_replace_brackets($text, $value)
{
    $result = array();
    preg_match_all("/{(.*?)}/i", $text, $result);
    $text_arr = $result[1];
    if (!empty($text_arr)) {
        if (!empty($value)) {
            foreach ($text_arr as $v) {
                if (isset($value[$v]) && !empty($value[$v])) {
                    $text = str_replace('{' . $v . '}', $value[$v], $text);
                } else {
                    return '';
                }
            }
            return $text;
        } else {
            return '';
        }
    } else {
        return $text;
    }
}

function create_order_no()
{
    return "G" . date('ymdHis') . rand('10', '99') . rand('10', '99');
}


/**
 * 导出Excel文件
 * @param array $title 标题 格式['title1', 'title2']
 * @param array $data 导出数据 ['A1' => '***', 'B2' => '***']
 * @param string $fileName 导出文件名称
 * @param array $options 配置 [ 'fileType' => 'xls',  //导出文件后缀 默认 xls
 *                             'savePath' => '/web', //自定义保存地址
 *                             'format_str' => [1,3], //列格式是文本的列索引 ]
 * @return bool
 * @throws \PhpOffice\PhpSpreadsheet\Exception
 * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
 */
function export_excel($title, $data, $fileName, $options = [])
{

    /** 设置转义格式 */
    if (isset($options['fileType']) && !in_array($options['fileType'], ['xls', 'xlsx', 'ods', 'csv', 'html', 'tcpdf', 'dompdf', 'mpdf'])) {
        return false;
    }

    //    $client = new \Redis();
    //    $client->connect('127.0.0.1','6379');
    //    $pool = new \Cache\Adapter\Redis\RedisCachePool($client);
    //    $simpleCache = new \Cache\Bridge\SimpleCache\SimpleCacheBridge($pool);
    //
    //    \PhpOffice\PhpSpreadsheet\Settings::setCache($simpleCache);
    $spreadsheet = new Spreadsheet();

    $worksheet = $spreadsheet->getActiveSheet();
    //设置标题名称
    $worksheet->setTitle('sheet');

    foreach ($title as $key => $value) {
        $worksheet->setCellValueByColumnAndRow($key + 1, 1, $value);
    }

    $row = 2; //第二行开始
    foreach ($data as $item) {
        $column = 1;
        foreach ($item as $value) {
            if (isset($options['format_str']) && in_array($column, $options['format_str'])) {
                $worksheet->setCellValueExplicitByColumnAndRow($column, $row, $value, DataType::TYPE_STRING);
            } else {
                $worksheet->setCellValueByColumnAndRow($column, $row, $value);
            }
            $column++;
        }
        $row++;
    }

    /** 设置文件后缀名 */
    if (!isset($options['fileType'])) {
        $fileName = $fileName . '.xls';
    } else {
        $fileName = $fileName . '.' . $options['fileType'];
    }

    /** 设置文件路径 */
    if (!isset($options['savePath'])) {
        $savePath = storage_path() . '/app/export/order/' . $fileName;
    } else {
        $savePath = $options['savePath'] . $fileName;
    }

    /** @var string $writerType 转义成PhpSpreadsheet能识别的后缀 */
    $writerType = ucfirst($options['fileType']);

    $writer = IOFactory::createWriter($spreadsheet, $writerType);
    $writer->save($savePath);

    /* 释放内存 */
    $spreadsheet->disconnectWorksheets();
    unset($spreadsheet);

    return $savePath;
}


/**
 * 获取标准日志格式数组
 *
 * @param $opt
 * @param $msg
 * @param array $data
 * @param int $timestamp1 时间戳1
 * @param int $timestamp2 时间戳2
 *
 * @return string
 */
function getStdLogMessage($opt, $msg, $data = [], $timestamp1 = 0, $timestamp2 = 0)
{
    $retLog = ['opt' => $opt, 'msg' => $msg];
    if (!empty($data)) {
        $retLog['data'] = $data;
    }
    if ($timestamp1 > 0) {
        $retLog['timestamp1'] = $timestamp1;
    }
    if ($timestamp2 > 0) {
        $retLog['timestamp2'] = $timestamp2;
    }

    return json_encode($retLog, JSON_UNESCAPED_UNICODE);
}

if (!function_exists('str_hide_middle')) {
    /**
     * 用*替代字符串中间部分。
     * @param string $str
     * @param string $replacement 替换成的字符。默认为*
     * @return string
     */
    function str_hide_middle($str, $replacement = '*')
    {
        if (!empty($str)) {
            $len       = mb_strlen($str);
            $begin_len = 1;
            $end_len   = 1;
            if ($len > 15) {
                $begin_len = 4;
                $end_len   = 4;
            } elseif ($len > 10) {
                $begin_len = 3;
                $end_len   = 4;
            } elseif ($len > 8) {
                $begin_len = 3;
                $end_len   = 3;
            } elseif ($len > 5) {
                $begin_len = 2;
                $end_len   = 2;
            } elseif ($len > 2) {
                $begin_len = 1;
                $end_len   = 1;
            } elseif ($len = 2) {
                $begin_len = 1;
                $end_len   = 0;
            } else {
                return str_repeat('*', $len);
            }
            $ret = mb_substr($str, 0, $begin_len) . str_repeat($replacement, $len - $begin_len - $end_len) . mb_substr($str, $len - $end_len, $end_len);
            return $ret;

        }
        return $str;
    }
}

/**
 * 获取当前内存使用情况
 * @param int $precision
 * @return string
 */
function getMemoryUsage($precision = 2)
{
    $size = memory_get_usage(true);

    $unit = ['b', 'kb', 'mb', 'gb', 'tb', 'pb'];

    return round($size / pow(1024, ($i = floor(log($size, 1024)))), $precision) . ' ' . $unit[$i];
}

/**
 * 导出csv时，过滤内容。值前后加双引号，值内双引号替换为两个双引号。回车换行替换成字符串"\r\n"
 * @param $val
 * @return mixed
 */
function filterCsvVal($val)
{
    return '"' . str_replace(['"', "\r", "\n"], ['""', '\r', '\n'], $val) . '"';
}

if (!function_exists('check_mobile')) {
    /**
     * 手机号格式验证
     *
     * @param $mobile
     *
     * @return bool
     */
    function check_mobile($mobile)
    {
        $exp = "/^1[3456789]{1}\d{9}$/";
        if (preg_match($exp, $mobile)) {
            return true;
        } else {
            return false;
        }
    }
}

if (!function_exists('check_email')) {
    /**
     * 电子邮箱验证
     *
     * @param $v
     *
     * @return bool
     */
    function check_email($v)
    {
        $exp = "/^\w+([-.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/";
        if (preg_match($exp, $v)) {
            return true;
        } else {
            return false;
        }
    }
}

if (!function_exists('check_virtual_mobile')) {
    /**
     * 虚商手机号格式验证
     *
     * @param $mobile
     *
     * @return bool
     */
    function check_virtual_mobile($mobile)
    {
        $exp = "/^(170|171|162|167|165)\d{8}$/";
        if (preg_match($exp, $mobile)) {
            return true;
        } else {
            return false;
        }
    }
}

/**
 * 所有的验证类的函数 放到这个文件中
 * 命名方式驼峰命名 函数名称以check开头
 */

//利用正则匹配手机号
function checkPhone($mobile)
{
    return check_mobile($mobile);
}

//验证网点号

function checkStoreNo($params, $num = 4)
{
    $exp = "/^\d{" . $num . "}$/";
    if (preg_match($exp, $params)) {
        return true;
    } else {
        return false;
    }
}

//验证统一认证号
function checkManagerId($params, $num = 9)
{
    $exp = "/^\d{" . $num . "}$/";
    if (preg_match($exp, $params)) {
        return true;
    } else {
        return false;
    }
}

//验证必须是数字
function checkNumber($params)
{
    $exp = "/^[0-9]*$/";
    if (preg_match($exp, $params)) {
        return true;
    } else {
        return false;
    }
}

//验证银行卡号 普通卡号  白金卡号是15位
function checkCardNo($params, $num = 16)
{
    $exp = "/^\d{" . $num . "}$/";
    if (preg_match($exp, $params)) {
        return true;
    } else {
        return false;
    }
}

function getImgUrl($img_path)
{
    if (empty($img_path)) return $img_path;

    if (config("admin.upload.disk") == 'oss') {
        return config('app.oss_url') . $img_path;
    } else {
        return Storage::disk(config("admin.upload.disk"))->url($img_path);
    }
}


function getRandStr($length)
{
    //字符组合
    $str     = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $len     = strlen($str) - 1;
    $randstr = '';
    for ($i = 0; $i < $length; $i++) {
        $num     = mt_rand(0, $len);
        $randstr .= $str[$num];
    }
    return $randstr;
}

//不包含以下字符：i/l/o/I/O
function getCardStr($length)
{
    //字符组合
//    $str = 'abcdefghjkmnpqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ23456789';
    $str       = 'ABCDEFGHJKLMNPQRSTUVWXYZ0123456789';
    $str_first = 'ABCDEFGHJKLMNPQRSTUVWXYZ123456789'; //兑换码的第一位去除数字0

    $len           = strlen($str) - 1;
    $str_start_len = strlen($str_first) - 1;
    $first_point   = mt_rand(0, $str_start_len);//获取第一位兑换码的下标
    $second_point  = mt_rand(0, 23); //选择兑换码第二位的下标,必须是字符

    $randstr = $str_first[$first_point] . $str[$second_point];
    for ($i = 0; $i < $length - 2; $i++) {
        $num     = mt_rand(0, $len);
        $randstr .= $str[$num];
    }
    return $randstr;
}

/**
 * 按行读取文件，返回数组
 * @param string $path 文件路径
 * @param int $start_line 开始行数，从1开始计数。
 * @return array
 */
function getFileContent($path, $start_line = 1)
{
    $file = fopen($path, "r");
    $arr  = [];
    $i    = 0;
    while (!feof($file)) {
        $tmp = trim(fgets($file));   //fgets()函数从文件指针中读取一行
        $i++;
        if ($i >= $start_line) {
            $arr[] = $tmp;
        }
    }
    fclose($file);
    return $arr;
}

/**
 * 按行读取文件，返回数组
 * @param string $path 文件路径
 * @param callable|null 过滤/处理函数，函数返回false，则该输入内容会被过滤掉
 * @return array
 */
function file_get_content($path, ?callable $callback = null)
{
    $file = fopen($path, "r");
    $arr  = [];
    $i    = 0;
    while (!feof($file)) {
        $tmp = $callback ? $callback(fgets($file), $i) : trim(fgets($file));   //fgets()函数从文件指针中读取一行
        $i++;
        if ($tmp !== false) {
            $arr[] = $tmp;
        }
    }
    fclose($file);
    return $arr;
}

/**
 * 将字符串参数变为数组
 * @param $query
 * @return array
 */
function convertUrlQuery($query)
{
    $queryParts = explode('&', $query);
    $params     = array();
    foreach ($queryParts as $param) {
        $item             = explode('=', $param);
        $params[$item[0]] = $item[1];
    }
    return $params;
}

/**
 * 将参数变为字符串
 * @param $array_query
 * @return string
 */
function getUrlQuery($array_query)
{
    $tmp = array();
    foreach ($array_query as $k => $param) {
        $tmp[] = $k . '=' . $param;
    }
    $params = implode('&', $tmp);
    return $params;
}

/**
 * 是否第三方后台用户
 * @return bool
 */
function admin_is_third_user()
{
    return Admin::user()->inRoles(config('admin.third_roles'));
}

/**
 * 判断数组的值是否全部为空
 * @param $arr
 * @return boolean 全部为空则返回true。
 * @author: liujq
 * @Time: 2022/12/21 15:56
 */
function check_value_all_empty($arr)
{
    $is_empty = true;
    foreach ($arr as $k => $v) {
        if (is_array($v)) {
            $is_empty = check_value_all_empty($v);
            if (!$is_empty) {
                break;
            }
        } else {
            if (!empty($v)) {
                $is_empty = false;
                break;
            }
        }
    }
    return $is_empty;
}

if (!function_exists('millisecond')) {
    /**
     * 13位时间戳
     * @return float
     */
    function millisecond()
    {
        list($t1, $t2) = explode(' ', microtime());
        return (float)sprintf('%.0f', (floatval($t1) + floatval($t2)) * 1000);
    }
}

if (!function_exists('http_request_send')) {
    /**
     * 使用GuzzleHttp\Client发送http请求。code：28->请求超时，200->请求成功，其它请求失败。
     * @param string $url
     * @param array|string $data 如果为string，则用body发送post请求。
     * @param string $method
     * @param array $header 默认： ['Content-type' => 'application/x-www-form-urlencoded;charset=utf-8']
     * @param int $timeout 默认10
     * @param string $logger_channel http_api 为空则不写日志
     * @return array 例如: ['code'=>200, 'status_code'=>200, 'data'=[], 'error_msg'=>'', 'error_trace'=>'']
     *                     Date: 11/23/21
     * @remark  Mozilla/5.0 (Windows NT 6.1; WOW64; rv:31.0) Gecko/20100101 Chrome/23.0.1271.64 Safari/537.11
     */
    function http_request_send($url, $data, $method = 'POST', $header = null, $timeout = 10, $logger_channel = 'http_req_log')
    {
        //['Content-Type' => 'application/x-www-form-urlencoded; charset=utf-8']
        //['Content-Type' => 'application/json; charset=utf-8']
        //['Content-Type' => 'text/html; charset=utf-8']
        //['Content-Type' => 'multipart/form-data;boundary=something; charset=utf-8']
        //['Accept' => 'application/json']

        $rsp_content = '';
        $resp        = [];

        if ($logger_channel) {
            $logger = \Illuminate\Support\Facades\Log::channel($logger_channel);
        } else {
            $logger = null;
        }

        $method = strtoupper($method);

        //'verify'=>false : 避免如下错误：cURL error 60: SSL certificate problem: unable to get local issuer certificate
        $options = ['timeout' => $timeout, 'verify' => false];

        if (\is_array($data)) {
            if ($method == 'GET') {
                $options['query'] = $data;
            } else if ($method == 'POST') {
                $options['form_params'] = $data;
            }
        } else if (\is_string($data)) {
            if ($method == 'GET') {
                $options['query'] = $data;
            } else if ($method == 'POST') {
                $options['body'] = $data;
            }

        } else {
            throw new InvalidArgumentException('data format error, array or string expected.');
        }

        if (!empty($header)) {
            $options['headers'] = $header;
        } else {
            $options['headers'] = [
                'Content-type' => 'application/x-www-form-urlencoded;charset=utf-8'
            ];
        }

        $req_time = millisecond();

        try {
            $client   = new Client();
            $response = $client->request($method, $url, $options);

            $rsp_content = $response->getBody()->getContents();
            $status_code = $response->getStatusCode();

            $resp = [
                'code'        => $status_code >= 200 && $status_code <= 299 ? 200 : $status_code, // 响应状态码,
                'status_code' => $status_code,
                'data'        => $rsp_content,
                'error_msg'   => $response->getReasonPhrase(),
            ];

            if ($logger) {
                $log_data = [
                    'url'         => $url,
                    'method'      => $method,
                    'header'      => $header,
                    'timeout'     => $timeout,
                    'request'     => $data,
                    'req_time'    => $req_time,
                    'rsp_time'    => millisecond(),
                    'rsp_content' => $rsp_content,
                ];
                logger_write_quiet($logger, 'info', json_encode($log_data, JSON_UNESCAPED_UNICODE));
            }
        } catch (\Exception $e) {

            $rsp_content = null;

            //cURL error 60: SSL certificate problem: unable to get local issuer certificate
            //cURL error 28: Operation timed out after 3114 milliseconds with 0 bytes received
            if ($e instanceof \GuzzleHttp\Exception\RequestException && $e->hasResponse()) {
                //以http StatusCode的方式作为响应状态码，有响应内容
                $response    = $e->getResponse();
                $rsp_content = $response->getBody()->getContents();
                $resp        = [
                    'code'        => $response->getStatusCode(),
                    'status_code' => $response->getStatusCode(),
                    'data'        => $rsp_content,
                    'error_msg'   => $response->getReasonPhrase(),
                ];

            } elseif (strpos($e->getMessage(), 'timed out after') !== FALSE) {
                $resp = [
                    'code'      => 28,//超时
                    'error_msg' => $e->getMessage(),
                    //'error_trace' => $e->getTraceAsString(),
                ];
            } else {
                $resp = [
                    'code'        => 500,
                    'error_msg'   => $e->getMessage(),
                    'error_trace' => $e->getTraceAsString(),
                ];
            }

            if ($logger) {
                $log_data = [
                    'url'      => $url,
                    'method'   => $method,
                    'header'   => $header,
                    'timeout'  => $timeout,
                    'request'  => $data,
                    'req_time' => $req_time,
                    'rsp_time' => millisecond(),
                ];
                if ($rsp_content !== null) {
                    $log_data['rsp_content'] = $rsp_content;
                    $log_data['error_type']  = get_class($e);
                    $log_data['error_msg']   = $e->getMessage();
                    $log_data['error_trace'] = $e->getTraceAsString();
                }
                logger_write_quiet($logger, 'error', json_encode($log_data, JSON_UNESCAPED_UNICODE));
            }
        }

        return $resp;
    }
}

if (!function_exists('logger_write_quiet')) {
    /**
     * 日志写入出错时不抛出异常
     * @param $logger
     * @param $level
     * @param $content
     * @author: liujq
     * @Time: 2023/5/31
     */
    function logger_write_quiet($logger, $level, $content)
    {
        try {
            if ($logger) {
                $logger->{$level}($content);
            }
        } catch (\Exception $e) {
        }
    }
}


if (!function_exists('rmb_show_format')) {
    /**
     * 金额格式化显示，带千分位。
     * @param $number int 金额，单位：厘
     * @param $decimals
     * @return string
     */
    function rmb_show_format($number, $decimals = 2)
    {
        return number_format(floatval(bcdiv($number, 1000, 3)), $decimals, '.', ',');
    }
}

if (!function_exists('rmb_yuan_to_li')) {
    /**
     * 人民币单位转换，元转换为厘
     *
     * @param $amount
     *
     * @return int
     */
    function rmb_yuan_to_li($amount)
    {
        return intval($amount * 1000);
    }
}

if (!function_exists('rmb_li_to_yuan')) {
    /**
     * 人民币单位转换，厘转换为元
     *
     * @param $amount
     *
     * @return float
     */
    function rmb_li_to_yuan($amount)
    {
        //        return floatval($amount / 1000.0);
        return floatval(bcdiv($amount, 1000, 3));
    }
}

if (!function_exists('rmb_to_chinese')) {
    /**
     * 金额转换为中文
     * @param $num int 厘
     * @return mixed|string
     */
    function rmb_to_chinese($amount)
    {
        $amount = floatval(bcdiv($amount, 1000, 3));

        $zh_num  = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
        $zh_unit = ['分', '角', '元', '拾', '佰', '仟', '万', '拾', '佰', '仟', '亿', '拾', '佰', '仟'];
        if (!is_numeric(str_replace(',', '', $amount))) {
            return $amount;
        }
        $number = strrev(round(str_replace(',', '', $amount), 2) * 100);
        $length = strlen($number);
        $ch_str = '';
        for ($length; $length > 0; $length--) {
            $index = $length - 1;
            if ($number[$index] == '0' && !in_array($zh_unit[$index], ['万', '元', '亿'])) {
                $ch_str .= $zh_num[$number[$index]];
            } elseif ($number[$index] == '0' && in_array($zh_unit[$index], ['万', '元', '亿'])) {
                $ch_str .= $zh_unit[$index];
            } else {
                $ch_str .= $zh_num[$number[$index]] . $zh_unit[$index];
            }
        }
        $format_str = trim(preg_replace(['/零{2,}/u', '/零万/', '/零元/', '/零亿/'], ['零', '万', '元', '亿'], $ch_str), '零');
        if (preg_match('/(分|角)/', $format_str) === 0) {
            $format_str .= '整';
        }
        return $format_str;
    }
}

/**
 * 统一api的异常处理
 * @param Exception $exc
 * @param $data
 * @return array {"code":200, "msg":"", "data": []}
 * @date: 2024/10/24
 */
function standardized_api_exception(\Exception $exc)
{
    $data = null;
    if ($exc instanceof MyException) {
        if ($exc->getCode() === 0) {
            $code = $exc->getMessage();
            $msg  = SysCode::$resp_msg[$code];
        } else {
            $code = $exc->getCode();
            $msg  = $exc->getMessage();
        }

        $data = $exc->getData();

        if ($code != SysCode::SUCCESS) {
            request()->attributes->set('my_exception', str_replace(base_path() . DIRECTORY_SEPARATOR, '', $exc->getFile()) . ':' . $exc->getLine() . '. message: ' . $exc->getMessage());
        }
    } else {
        Log::error($exc);
        $code = SysCode::SYSTEM_ERROR;
        $msg  = SysCode::$resp_msg[$code];
    }

    $ret = [
        'code' => $code,
        'msg'  => $msg,
    ];

    if (!is_null($data)) {
        $ret['data'] = $data;
    }

    return $ret;
}

/**
 * 用于接口交互，从$goods_id参数中分离出分组id和商品id
 * @param int|string $goods_id 1 / 1-1(分组id-商品id)
 * @return array [group_id, goods_id] 如果没有组id，则zuid赋值为0
 * @date: 2024/11/4
 */
function exchange_split($goods_id)
{
    if (is_string($goods_id) && count($arr = explode('-', $goods_id)) > 1) {
        return $arr;
    } else {
        return [0, $goods_id];
    }
}

if (!function_exists("array_pluck")) {
    /**
     * 自定义数组pluck功能，可返回多个属性值列表
     * @param array $array
     * @param string|array|null $value 如果为字符串，则用Arr::pluck()处理并返回；如果为数组，返回以$key为键，值为包含$value指定的键的数组（若$value为空数组，则值为包含所有键值对的数组）。
     * @param null|string $key
     * @return array
     * @date: 2023/12/27
     */
    function array_pluck(array $array, $value, $key = null)
    {
        if (!is_array($value)) {
            return Arr::pluck($array, $value, $key);
        } else {
            if (empty($key)) return Arr::pluck($array, $value, $key);
            $ret = [];
            if (empty($value)) {
                foreach ($array as $item) {
                    $ret[$item[$key]] = $item;
                }
            } else {
                foreach ($array as $item) {
                    $ret[$item[$key]] = Arr::only($item, $value);
                }
            }
            return $ret;
        }
    }
}
