<?php

return [
    'app_key'        => env("APP_KEY"),

    //充值账号类型配置
    'charge_account' => [
        'mobile'  => [
            'name'        => '手机号',
            'label'       => '手机号',
            'placeholder' => '请输入手机号',
            'regexp'      => '/^1[3456789]{1}\d{9}$/',
            'error_msg'   => '请输入正确的手机号'
        ],
        'qq'      => [
            'name'        => 'QQ号码',
            'label'       => 'QQ号码',
            'placeholder' => '请填写您与QQ音乐绑定的QQ号',
            'regexp'      => '/^[1-9]\d{4,9}$/',
            'error_msg'   => '请输入正确的QQ号码'
        ],
        'jyk_zsy' => [
            'name'        => '中石油加油卡',
            'label'       => '加油卡卡号',
            'placeholder' => '请填写以90或96开头的中石油加油卡主卡卡号',
            'regexp'      => '/^9(0|6)\d{14}$/',
            'error_msg'   => '请输入正确的加油卡卡号'
        ],
        'jyk_zsh' => [
            'name'        => '中石化加油卡',
            'label'       => '加油卡卡号',
            'placeholder' => '请填写以100011开头的中石化加油卡主卡IC卡号',
            'regexp'      => '/^100011\d{13}$/',
            'error_msg'   => '请输入正确的加油卡卡号'
        ],
        'taobao'  => [
            'name'        => '淘宝账号',
            'label'       => '淘宝账号',
            'placeholder' => '请输入淘宝账号',
            'regexp'      => '/^\S{1,32}$/',
            'error_msg'   => '请输入正确的淘宝账号'
        ],
    ],

    "goods_setting"         => json_decode(env("GOODS_SETTING", "{}"), true),

    //家政类预约日期排除以下配置里的日期。
    'jiazheng_exclude_date' => [
        [
            'begin' => '2023-01-06',
            'end'   => '2023-02-05',
            'msg'   => '1月6日-2月5日适逢春节期间，家政服务暂停预约，请您选择其它时间进行预约，给您造成的不便敬请谅解。',
        ],
    ],

    //活动自有配置
    'tianmao'    => [
        'max_limit_per_account'       => intval(env('TIANMAO_MAX_LIMIT_PER_ACCOUNT', '0')),  //每天每账号下单次数。
        'max_limit_per_month_account' => intval(env('TIANMAO_MAX_LIMIT_PER_MONTH_ACCOUNT', '0')),  //每月每账号下单次数。
        'max_limit_per_mobile'        => intval(env('TIANMAO_MAX_LIMIT_PER_MOBILE', '0')),  //每天每手机号下单次数。0为不限制。
        'max_limit_per_month_mobile'  => intval(env('TIANMAO_MAX_LIMIT_PER_MONTH_MOBILE', '0')),  //每天每手机号下单次数。0为不限制。

        //以下为充值账号或手机号在白名单中的限制
        'wl_max_limit_per_account'       => intval(env('TIANMAO_WL_MAX_LIMIT_PER_ACCOUNT', '0')),  //每天每账号下单次数。
        'wl_max_limit_per_month_account' => intval(env('TIANMAO_WL_MAX_LIMIT_PER_MONTH_ACCOUNT', '0')),  //每月每账号下单次数。
        'wl_max_limit_per_mobile'        => intval(env('TIANMAO_WL_MAX_LIMIT_PER_MOBILE', '0')),  //每天每手机号下单次数。0为不限制。
        'wl_max_limit_per_month_mobile'  => intval(env('TIANMAO_WL_MAX_LIMIT_PER_MONTH_MOBILE', '0')),  //每天每手机号下单次数。0为不限制。

        //以下为淘宝账号对应的手机号最大个数
        'max_limit_account_mobiles'  => intval(env('TIANMAO_MAX_LIMIT_ACCOUNT_MOBILES', '0')),  //淘宝账号对应的充值手机号数量上限 2023-03-21。0为不限制。
        //手机号对应的淘宝账号数量上限
        'max_limit_mobile_accounts'  => intval(env('TIANMAO_MAX_LIMIT_MOBILE_ACCOUNTS', '0')),  //手机号对应的淘宝账号数量上限 2023-03-21。0为不限制。
    ],

//    奈雪的茶
    'nxtea'    => [
        'max_limit_per_account'       => intval(env('NXTEA_MAX_LIMIT_PER_ACCOUNT', '3')),  //每天每账号下单次数。
        'max_limit_per_month_account' => intval(env('NXTEA_MAX_LIMIT_PER_MONTH_ACCOUNT', '90')),  //每月每账号下单次数。
        'max_limit_per_mobile'        => intval(env('NXTEA_MAX_LIMIT_PER_MOBILE', '0')),  //每天每手机号下单次数。0为不限制。
        'max_limit_per_month_mobile'  => intval(env('NXTEA_MAX_LIMIT_PER_MONTH_MOBILE', '0')),  //每天每手机号下单次数。0为不限制。

        //以下为充值账号或手机号在白名单中的限制
        'wl_max_limit_per_account'       => intval(env('NXTEA_WL_MAX_LIMIT_PER_ACCOUNT', '3')),  //每天每账号下单次数。
        'wl_max_limit_per_month_account' => intval(env('NXTEA_WL_MAX_LIMIT_PER_MONTH_ACCOUNT', '99')),  //每月每账号下单次数。
        'wl_max_limit_per_mobile'        => intval(env('NXTEA_WL_MAX_LIMIT_PER_MOBILE', '99')),  //每天每手机号下单次数。0为不限制。
        'wl_max_limit_per_month_mobile'  => intval(env('NXTEA_WL_MAX_LIMIT_PER_MONTH_MOBILE', '99')),  //每天每手机号下单次数。0为不限制。
    ],

    'luckin'    => [
        'max_limit_per_account'       => intval(env('LUCKIN_MAX_LIMIT_PER_ACCOUNT', '3')),  //每天每账号下单次数。
        'max_limit_per_month_account' => intval(env('LUCKIN_MAX_LIMIT_PER_MONTH_ACCOUNT', '90')),  //每月每账号下单次数。
        'max_limit_per_mobile'        => intval(env('LUCKIN_MAX_LIMIT_PER_MOBILE', '0')),  //每天每手机号下单次数。0为不限制。
        'max_limit_per_month_mobile'  => intval(env('LUCKIN_MAX_LIMIT_PER_MONTH_MOBILE', '0')),  //每天每手机号下单次数。0为不限制。

        //以下为充值账号或手机号在白名单中的限制
        'wl_max_limit_per_account'       => intval(env('LUCKIN_WL_MAX_LIMIT_PER_ACCOUNT', '3')),  //每天每账号下单次数。
        'wl_max_limit_per_month_account' => intval(env('LUCKIN_WL_MAX_LIMIT_PER_MONTH_ACCOUNT', '99')),  //每月每账号下单次数。
        'wl_max_limit_per_mobile'        => intval(env('LUCKIN_WL_MAX_LIMIT_PER_MOBILE', '99')),  //每天每手机号下单次数。0为不限制。
        'wl_max_limit_per_month_mobile'  => intval(env('LUCKIN_WL_MAX_LIMIT_PER_MONTH_MOBILE', '99')),  //每天每手机号下单次数。0为不限制。
    ],

    //统计接口
    'statistics' => [
        'order_sale_app_id' => env("ORDER_SALE_APPID", ""),
        'secret_key'        => env("SECRET_KEY", '')
    ]
];
