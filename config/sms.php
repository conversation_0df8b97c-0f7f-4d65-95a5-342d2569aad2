<?php

return [
    'accessKeyId'               => env('ALI_ACCESS_KEY_ID', ''),
    'accessKeySecret'           => env('ALI_ACCESS_KEY_SECRET', ''),
    'signName'                  => env('SIGN_NAME', ''),//默认短信签名

    // 验证码配置
    'captcha_expired'           => env('CAPTCHA_EXPIRED', 600),//验证码过期前保持的时间。
    'captcha_end'               => env('CAPTCHA_END', 60),//验证码几秒后可以重发，跟前端保持一致。
    'captcha_sms_template_code' => env('CAPTCHA_SMS_TEMPLATE_CODE', ''),

    'sms_url'              => env('SMS_URL', ''),
    'sms_appid'            => env('SMS_APPID', ''),
    'sms_secret_key'       => env('SMS_SECRET_KEY', ''),

    //蒙商积分兑换短信配置【蒙商银行积分兑换】
    'activity_MengshangJf' => [
        'appid'           => '8d25fdad2de364c2326a581d5992bee6',//短信网关的appid
        'secret_key'      => '3a3e14254d1466a18300d557e699aa23',
        'captcha_temp_id' => 'JSM43159-0001',//短信验证码的模板编号
        'sign_name'       => '',//签名
    ],
    //天猫购物券短信配置【明苑风华】
    'activity_TianMao'     => [
        'appid'           => '8e92195420da23564846a4b41a7cb763',//短信网关的appid
        'secret_key'      => '374bfc0adae2ac312bf611b9d77fe71d',
        'captcha_temp_id' => 'JSM43159-0005',//短信验证码的模板编号
        'sign_name'       => '',//签名
    ],
    //爱奇艺活动-20211210【青柠】
    'activity_Aqy'         => [
        'appid'           => 'qnlhe434ab16a44d98413149004aea74',//短信网关的appid
        'secret_key'      => 'b645467181663d2e08f7c59af0d63314',
        'captcha_temp_id' => 'JSM43159-0006',//短信验证码的模板编号
        'sign_name'       => '',//签名
    ],
    //北京初创世纪文化传播有限公司光大银行项目 20220915
    'activity_AqyCC'       => [
        'appid'           => 'chuchuang43c260313dbb7483dca4741',//短信网关的appid
        'secret_key'      => 'd81bf18e8a85e7f6f0a3ca0acd5b255a',
        'captcha_temp_id' => 'JSM43159-0014',//短信验证码的模板编号
        'sign_name'       => '',//签名
    ],
    //瑞幸咖啡【明苑风华】 2023-01-28
    'activity_Luckin'      => [
        'appid'           => '8e92195420da23564846a4b41a7cb763',//短信网关的appid
        'secret_key'      => '374bfc0adae2ac312bf611b9d77fe71d',
        'captcha_temp_id' => 'JSM43159-0005',//短信验证码的模板编号
        'sign_name'       => '',//签名
    ],
    //奈雪的茶【明苑风华】 2023-01-28
    'activity_Nxtea'       => [
        'appid'           => '8e92195420da23564846a4b41a7cb763',//短信网关的appid
        'secret_key'      => '374bfc0adae2ac312bf611b9d77fe71d',
        'captcha_temp_id' => 'JSM43159-0005',//短信验证码的模板编号
        'sign_name'       => '',//签名
    ],
    //京科爱奇艺
    'activity_JkIqy'       => [
        'appid'           => 'qnlhe434ab16a44d98413149004aea74',//短信网关的appid
        'secret_key'      => 'b645467181663d2e08f7c59af0d63314',
        'captcha_temp_id' => 'JSM43159-0006',//短信验证码的模板编号
        'sign_name'       => '',//签名
    ],
    'activity_Hxtm'        => [
        //'appid'           => '8e92195420da23564846a4b41a7cb763',//短信网关的appid
        //'secret_key'      => '374bfc0adae2ac312bf611b9d77fe71d',
        //'captcha_temp_id' => 'JSM43159-0005',//短信验证码的模板编号
        'appid'           => 'qnlhe434ab16a44d98413149004aea74',//短信网关的appid
        'secret_key'      => 'b645467181663d2e08f7c59af0d63314',
        'captcha_temp_id' => 'JSM43159-0006',//短信验证码的模板编号
        'sign_name'       => '',//签名
    ],

    // 美团
    'activity_Mt'          => [
        'appid'           => '8e92195420da23564846a4b41a7cb763',//短信网关的appid
        'secret_key'      => '374bfc0adae2ac312bf611b9d77fe71d',
        'captcha_temp_id' => 'JSM43159-0005',//短信验证码的模板编号
        'sign_name'       => '',//签名
    ],
    'activity_Iqy'         => [
        'appid'           => '8e92195420da23564846a4b41a7cb763',//短信网关的appid
        'secret_key'      => '374bfc0adae2ac312bf611b9d77fe71d',
        'captcha_temp_id' => 'JSM43159-0005',//短信验证码的模板编号
        'sign_name'       => '',//签名
    ],

    // 京科网易云
    'activity_Jkwyy'       => [
        'appid'           => 'huashuo2543078a8b4829c5df8d8b5bb',//短信网关的appid
        'secret_key'      => '94c51497d0d70a426fb9b92b5d85fda3',
        'captcha_temp_id' => 'JSM43159-0022',//短信验证码的模板编号
        'sign_name'       => '',//签名
    ],

    //小满宜城-光大工会线上视听会员活动 20230713
    'activity_CebGh'       => [
        'appid'           => 'xiaoman491a2226baff59092aaca1392',//短信网关的appid
        'secret_key'      => 'xb2ec4ee118bdc349c578766432425e0',
        'captcha_temp_id' => 'JSM43159-0023',//短信验证码的模板编号
        'sign_name'       => '',//签名
    ],

    // 京科网易云
    'activity_Jkrx'        => [
        'appid'           => 'huashuo2543078a8b4829c5df8d8b5bb',//短信网关的appid
        'secret_key'      => '94c51497d0d70a426fb9b92b5d85fda3',
        'captcha_temp_id' => 'JSM43159-0022',//短信验证码的模板编号
        'sign_name'       => '',//签名
    ],
];
