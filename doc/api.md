####1、登录接口：

/api/login
```
请求参数：
    card_no：
    card_pwd
    act： 固定值

响应：
{
	"code":"200",
	"msg":"",
	"data":{
       "1_1": [1, 1],
       "1_2": [1, 2]
    }

}

说明：
data的值为礼品级别列表{"1_1": [1,1], "1_2": [1, 2]}，表示当前用户有（level1=1，level2=1）、（level1=1，level2=2）两组等级的礼品可以领取。
系统默认礼品等级有两级，如果该活动没有礼品等级的概念，那么默认level1=1，level2=1。

```

####2、判断是否登录接口
/api/is-login
```
请求参数：
    无

响应：
{
	"code":"200",
	"msg":""
}

说明：
201-未登录。
200-已登录。

```

####3、资格验证
/api/is-permit
```
请求参数：
    goods_id
    
    响应：
    {
    	"code":"200", //200表示验证通过
    	"msg":"",
    	"data":{}
    }
```


####4、可兑换商品列表
/api/goods-list
```
请求参数：
   无

响应1（非兑换卡类活动，或兑换卡类且无分组，比如礼品卡、计次卡）：
{
    "code": 200,
    "msg": "成功",
    "data": {
        "exchange_times": 2,//兑换卡有效
        "remain_times": 1,//兑换卡有效
        "1": {
            "1": [
                {
                    "id": 35,//同goods_id，为了兼容
                    "goods_id": 35,
                    "exchange_group_id": 0,
                    "goods_name": "天猫购物券一元（测试）",
                    "goods_type": 3,
                    "goods_attr": "",
                    "goods_show_img": "https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/天猫.png",
                    "pre_verify_type": 2,
                    "goods_desc": "<p></p><p><img src=\"https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/1709864845ksKieo.jpg\" style=\"max-width:100%;\"><br></p>",
                    "service_time": "08:00,13:00",
                    "advance_days": 5,
                    "prize_level": 1,
                    "prize_level2": 1,
                    "available": true,
                    "goods_img": "https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/b94b506f2c9c434695b62fec5c5a5963.png"
                },
                {
                    "id": 37,//同goods_id，为了兼容
                    "goods_id": 37,
                    "exchange_group_id": 0,
                    "goods_name": "QQ音乐豪华绿钻会员",
                    "goods_type": 3,
                    "goods_attr": "",
                    "goods_show_img": "https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/1421f37031e4fb7e7948976598983895.png",
                    "pre_verify_type": 0,
                    "goods_desc": "<p></p><p><img src=\"https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/1722936310C7MvvE.png\" style=\"max-width:100%;\"><br></p>",
                    "service_time": "08:00,13:00",
                    "advance_days": 5,
                    "prize_level": 1,
                    "prize_level2": 1,
                    "available": false,
                    "goods_img": "https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/0ae141a3a8dc910a6a5faf7df58a28f8.png"
                }
            ]
        }
    }
}

响应2（兑换卡且有分组，比如礼包卡、套餐卡）

{
    "code": 200,
    "msg": "成功",
    "data": {
        "1": {
            "1": [
                {
                    "group_name": "分组1",
                    "exchange_times": 2,
                    "remain_times": 1,
                    "goods": [
                        {
                            "id": "1-35",//同goods_id，为了兼容
                            "goods_id": "1-35",
                            "exchange_group_id": 1,
                            "goods_name": "天猫购物券一元（测试）",
                            "goods_type": 3,
                            "goods_attr": "",
                            "goods_show_img": "https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/天猫.png",
                            "pre_verify_type": 2,
                            "goods_desc": "<p></p><p><img src=\"https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/1709864845ksKieo.jpg\" style=\"max-width:100%;\"><br></p>",
                            "service_time": "08:00,13:00",
                            "advance_days": 5,
                            "prize_level": 1,
                            "prize_level2": 1,
                            "available": true,
                            "goods_img": "https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/b94b506f2c9c434695b62fec5c5a5963.png"
                        },
                        {
                            "id": "1-38",//同goods_id，为了兼容
                            "goods_id": "1-38",
                            "exchange_group_id": 1,
                            "goods_name": "喜马拉雅VIP年卡",
                            "goods_type": 3,
                            "goods_attr": "",
                            "goods_show_img": "https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/fcd89f05f33ca6e7f436d7c130c19fbf.png",
                            "pre_verify_type": 1,
                            "goods_desc": "<p></p><p><img src=\"https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/1722936387dyTKzc.png\" style=\"max-width:100%;\"><br></p>",
                            "service_time": "08:00,13:00",
                            "advance_days": 5,
                            "prize_level": 1,
                            "prize_level2": 1,
                            "available": false,
                            "goods_img": "https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/710dbf6da9182b899786456850de6216.png"
                        }
                    ]
                },
                {
                    "group_name": "分组2",
                    "exchange_times": 1,
                    "remain_times": 0,
                    "goods": [
                        {
                            "id": "3-38",//同goods_id，为了兼容
                            "goods_id": "3-38",
                            "exchange_group_id": 3,
                            "goods_name": "喜马拉雅VIP年卡",
                            "goods_type": 3,
                            "goods_attr": "",
                            "goods_show_img": "https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/fcd89f05f33ca6e7f436d7c130c19fbf.png",
                            "pre_verify_type": 1,
                            "goods_desc": "<p></p><p><img src=\"https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/1722936387dyTKzc.png\" style=\"max-width:100%;\"><br></p>",
                            "service_time": "08:00,13:00",
                            "advance_days": 5,
                            "prize_level": 1,
                            "prize_level2": 1,
                            "available": false,
                            "goods_img": "https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/710dbf6da9182b899786456850de6216.png"
                        },
                        {
                            "id": "3-37",//同goods_id，为了兼容
                            "goods_id": "3-37",
                            "exchange_group_id": 3,
                            "goods_name": "QQ音乐豪华绿钻会员",
                            "goods_type": 3,
                            "goods_attr": "",
                            "goods_show_img": "https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/1421f37031e4fb7e7948976598983895.png",
                            "pre_verify_type": 0,
                            "goods_desc": "<p></p><p><img src=\"https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/1722936310C7MvvE.png\" style=\"max-width:100%;\"><br></p>",
                            "service_time": "08:00,13:00",
                            "advance_days": 5,
                            "prize_level": 1,
                            "prize_level2": 1,
                            "available": false,
                            "goods_img": "https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/0ae141a3a8dc910a6a5faf7df58a28f8.png"
                        }
                    ]
                }
            ]
        }
    }
}

说明：
data["1"]["1"]下的列表，表示等级（level1=1，level2=1）下的礼品列表。
goods_type：商品类型。1-实物商品，2-虚拟商品卡密类，3-虚拟商品-异步直充类，4-实物+虚拟，5-短链接，6-家政服务类
```

####5、商品详情
/api/goods-detail
```
请求参数：
	goods_id

响应：
{
    "code": "200",
    "msg": "成功",
    "data": {
        "id": "1-35",
        "goods_type": 3,
        "goods_name": "天猫购物券一元（测试）",
        "goods_attr": "",
        "pre_verify_type": 2,
        "is_show_card_no": 0,
        "advance_days": 5,
        "service_time": "08:00,13:00",
        "goods_imgs": [
            "https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/b94b506f2c9c434695b62fec5c5a5963.png"
        ],
        "goods_desc": "<p></p><p><img src=\"https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/1709864845ksKieo.jpg\" style=\"max-width:100%;\"><br></p>",
        "goods_params": "",
        "goods_instr": ""
    }
}

说明：
pre_verify_type：充值验证类型。0-无，1-短信验证码(京东），2-淘宝手机号充值
```


####6、兑换
/api/order
```
请求参数：
	goods_id  商品id
	charge_account  充值账号（虚拟产品用）
	verify_code: 有前置验证的商品，需要填写验证码（比如京东直充），不填写则不做验证。
	user_mobile 用户手机号（如果有值，则订单中的用户手机号取该值）
	consignee_name  收货人/家政服务联系人
	consignee_phone  收货人电话/家政服务联系电话
	consignee_address  收货地址//家政服务地址
	service_date 预约日期（家政类）
	service_time 预约时间（家政类。例如：10:00）
	order_remark 订单备注
	
响应：
{
	"code": "200",
	"msg": "成功",
	"data": [
		"rediret_url":"",
		"goods_name":""
	]
}

说明：
code：
  4001 - 手机号码对应账号数量大于1(淘宝天猫购物券充值)
  4002 - 前置验证失败，msg为失败原因
rediret_url：可选参数，不为空则下单成功后跳转到该url。

```


####7、兑换记录
/api/order-record
* 商品类型。1-实物商品，2-虚拟商品卡密类，3-虚拟商品-异步直充类，4-虚拟_实物，5-短连接
```
响应：
[
  {
	"order_no":"19377497621936498264", 
	"order_time":"2020/3/4",
	"goods_type":"1",
	"goods_show_img":"http://xxxxxxxx/xxjpg",
	"goods_name":"SKV水壶",
	"goods_attr":"红色",
	"prize_level":"礼品等级1",
	"prize_level2":"礼品等级2",
	"charge_account":"***********",
	"consignee_name":"张三",
	"consignee_phone":"***********",
	"consignee_address":"北京市东城区"
	"activation_code":"",
	"sequence_no":"",
	"endtime":"",
	"logistics_sn":"",
	"logistics_company":"",
	"service_date":"2021-10-23",
    "service_time":"08:00"
  }
]
```

#### 8、短信验证码

/api/captcha
```
请求参数：
   mobile: 手机号,
   captcha: 图形验证码（除蒙商积分兑换活动外，其它均为必须输入）。2021-11-30新增
   act: 活动编号

响应：
{
    "code": 200,
    "msg": "成功",
}

说明：
200  - 成功
9999 - 失败

根据每个活动发不同的短信。

```

#### 9、图形验证码

/api/captcha-img
```
说明：返回图形验证码图片流。
前端调用例子：<img src="/api/captcha-img?_r=0.21423"/>
```

#### 10、活动广告列表。目前用于商品列表下方展示。
/api/activity-ad
```
请求参数：
   无

响应：
{
    "code": 200,
    "msg": "成功",
    "data": [
      {
        "show_title": "下月书单",
        "show_img_url": "https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/9f64693ff8db15a4860fbf2dce3acc43.jpg",
        "jump_url": "http://127.0.0.1:8082/blue/goods.html"
      }
    ]
}

说明：
200  - 成功

```

#### 11、淘宝充值-手机号查询账号信息
/api/tb-user-query
```
请求参数：
   goods_id: 商品id
   mobile: 淘宝账号绑定的手机号
   verify_code: （可选）短信验证码。若为空，则返回手机号对应淘宝账号数量

响应：
{
    "code": 200,
    "msg": "成功",
    "data": {
        "user_num": 2,
        "user_infos": [
            {
                "active": true,
                "buyer_display_nick_mask" => "苏*3",
                "buyer_nick_mask" => "苏*3",
                "obs_buyer_id" => "RAzN8HWUvKgcM2fUjMowQvgLHiLH5kv6F7wDwh8uLPE4MDaRXqd"
            },
            {
                "active": false,
                "buyer_display_nick_mask" => "t*0",
                "buyer_nick_mask" => "t*0",
                "obs_buyer_id" => "RAzN8HWNgjUGdtnZwasrGmemEdSW6NtC2NAkTXoTCwo2Yyr4VrA"
            }
        ]
    }
}

说明：
200  - 成功
其它为失败
user_infos->active: 是否是活跃帐号（true-标识活跃帐号；false-非活跃帐号）
user_infos->buyer_nick_mask: 淘宝帐号脱敏nick
user_infos->buyer_display_nick_mask: 淘宝昵称脱敏nick
user_infos->obs_buyer_id: 加密的淘宝ID

```

#### 12、淘宝充值-账号查询验证码发送
/api/tb-verify-code-send
```
请求参数：
   goods_id: 商品id
   mobile: 淘宝账号绑定的手机号

响应：
{
    "code": 200,
    "msg": "成功"
}

说明：
200  - 成功

```

#### 13、前置验证：验证码发送、验证
/api/pre-verify
```
请求参数：
   goods_id: 商品id
   charge_account: 充值账号
   verify_code: 验证码。为空时发送验证码；不为空时验证

响应：
{
    "code": 200,
    "msg": "成功"
}

说明：
200  - 成功

```

#### 14、公共页面说明

- /detail.html?id=xx  商品详情

- /myorder.html  我的订单

- /submit.html   提交订单

- /success.html  提交成功

