### 爱奇艺专区活动相关接口-20210910

#### 1、登录接口：

/api/login

```
请求参数：
    mobile:       登录手机号
    sms_captcha:  短信验证码
    act：         固定值
响应：
{
    "code":"200",
    "msg":"",
    "data":{}

}

说明：这里不返回礼品等级，返回唯一的商品id
```

#### 2、判断是否登录接口

/api/is-login

```
请求参数：
    无

响应：
{
	"code":"200",
	"msg":""
}

说明：
201-未登录。
200-已登录。

```

#### 3、短信验证码

/api/captcha

```
请求参数：
   mobile: 手机号

响应：
{
    "code": 200,
    "msg": "成功",
}

说明：
200  - 成功
9999 - 失败

```

#### 4、兑换

/api/order

```
请求参数：
	goods_id  商品id
	charge_account  充值账号（虚拟产品用）
	
响应：
{
	"code": "200",
	"msg": "成功",
	"data": [
		"rediret_url":"",
		"goods_name":"爱奇艺黄金VIP季卡"
	]
}
```

#### 5、兑换记录

/api/order-record

* 商品类型。1-实物商品，2-虚拟商品卡密类，3-虚拟商品-异步直充类，4-虚拟_实物，5-短连接

```
响应：
[
  {
	"order_no":"19377497621936498264", 
	"order_time":"2020/3/4",
	"goods_type":"1",
	"goods_show_img":"http://xxxxxxxx/xxjpg",
	"goods_name":"SKV水壶",
	"goods_attr":"红色",
	"prize_level":"礼品等级1",
	"prize_level2":"礼品等级2",
	"charge_account":"***********",
	"consignee_name":"张三",
	"consignee_phone":"***********",
	"consignee_address":"北京市东城区"
	"activation_code":"",
	"sequence_no":"",
	"endtime":"",
	"logistics_sn":"",
	"logistics_company":""
  }
]
```

#### 6、页面说明

- /gdyh/index.html 首页
- /gdyh/goods.html 商品列表

