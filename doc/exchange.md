### 兑换码登录接口逻辑。(蒙商积分商城兑换除外，该项目验证码参数名为captcha)

#### 1、登录接口：

/api/login

```
请求参数：
    card_pwd:     兑换码
    mobile:       登录账号（手机号，非必须）
    sms_captcha:  短信验证码（兑换登录时需要，非必须）
    captcha:      图形校验码（图形验证码，非必须。获取短信验证码时为必须，这里不需要再次验证。）
    act：         固定值（活动加密标识）

响应：
单个商品：
{
    "code": 200,
    "msg": "成功",
    "data": {
        "exchange_state": "3",
        "endtime": "2024-12-13",
        "show_img": "https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/bilibili logo.png",
        "instr": "<p></p><p>兑换说明1</p><p><span style=\"color: rgb(194, 79, 74);\">兑换说明2</span><br></p><p>兑换说明3<br></p>",
        "id": 36,//同goods_id，为了兼容
        "goods_id": 36,
        "exchange_group_id": 0,
        "goods_name": "京东E卡1元",
        "goods_type": 3,
        "goods_attr": "1元",
        "goods_show_img": "https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/1667293609002.jpg",
        "pre_verify_type": 1,
        "goods_desc": "<p></p><div style=\"text-align: center;\"><span style=\"background-color: rgb(194, 79, 74); color: rgb(238, 236, 224); font-weight: bold; font-size: large;\">微信立减金领取说明</span></div><p><br><p><span style=\"background-color: rgb(194, 79, 74); color: rgb(238, 236, 224);\">权益使用说明</span></p></p><p><span style=\"font-size: small;\"><br>1）微信立减金兑换方法：用户需复制微信立减金的券码链接，在微信APP内打开链接（可通过在微信中搜索该链接进入领取页面），点击【立即领取】，领取后可在「微信-我-卡包-券和礼品卡」查看；<br><br>2）在使用微信支付时，绑定北京银行借记卡扣款且大于指定金额以上的订单进行付款时可用；<br><br>3）微信立减金仅限本人领取，一旦领取不可撤销、不可转让。注意不要将兑换券泄露给他人；<br><br>4）领取至微信卡包后，自领取之日起30天有效，有效期以微信卡包展示为准，到期后自动失效，逾期未使用不再补发；<br><br>5）转账、理财等部分商户不可使用立减金抵扣；<br><br>6）如权益兑换及使用过程中有任何问题，请咨询客服：400-613-5568。</span></p><p><br></p>",
        "service_time": "08:00,13:00",
        "advance_days": 5,
        "prize_level": 1,
        "prize_level2": 1,
        "available": false,
        "goods_img": "https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/c9286f957fef054fedbf3d3216501a42.jpg",
        "rdr_url": "/bl/detail.html?id=36",
        "exchange_times": 1,
        "remain_times": 0
    }
}

多个商品：
{
    "code": 200,
    "msg": "成功",
    "data": {
        "exchange_state": "3",
        "endtime": "2025-08-06",
        "show_img": "https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/558a3f3f3e52935ef1a24f7457e06048.png",
        "instr": "<p></p><img src=\"https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/1722936596tEWVZN.png\" style=\"max-width: 100%;\">",
        "rdr_url": "/blzh/goods.html",
        "exchange_times": 2,
        "remain_times": 1,
        "goods": [
            {
                "id": 35,//同goods_id，为了兼容
                "goods_id": 35,
                "exchange_group_id": 0,
                "goods_name": "天猫购物券一元（测试）",
                "goods_type": 3,
                "goods_attr": "",
                "goods_show_img": "https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/天猫.png",
                "pre_verify_type": 2,
                "goods_desc": "<p></p><p><img src=\"https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/1709864845ksKieo.jpg\" style=\"max-width:100%;\"><br></p>",
                "service_time": "08:00,13:00",
                "advance_days": 5,
                "prize_level": 1,
                "prize_level2": 1,
                "available": true,
                "goods_img": "https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/b94b506f2c9c434695b62fec5c5a5963.png"
            },
            {
                "id": 37,//同goods_id，为了兼容
                "goods_id": 37,
                "exchange_group_id": 0,
                "goods_name": "QQ音乐豪华绿钻会员",
                "goods_type": 3,
                "goods_attr": "",
                "goods_show_img": "https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/1421f37031e4fb7e7948976598983895.png",
                "pre_verify_type": 0,
                "goods_desc": "<p></p><p><img src=\"https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/1722936310C7MvvE.png\" style=\"max-width:100%;\"><br></p>",
                "service_time": "08:00,13:00",
                "advance_days": 5,
                "prize_level": 1,
                "prize_level2": 1,
                "available": false,
                "goods_img": "https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/0ae141a3a8dc910a6a5faf7df58a28f8.png"
            },
            {
                "id": 38,//同goods_id，为了兼容
                "goods_id": 38,
                "exchange_group_id": 0,
                "goods_name": "喜马拉雅VIP年卡",
                "goods_type": 3,
                "goods_attr": "",
                "goods_show_img": "https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/fcd89f05f33ca6e7f436d7c130c19fbf.png",
                "pre_verify_type": 1,
                "goods_desc": "<p></p><p><img src=\"https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/1722936387dyTKzc.png\" style=\"max-width:100%;\"><br></p>",
                "service_time": "08:00,13:00",
                "advance_days": 5,
                "prize_level": 1,
                "prize_level2": 1,
                "available": true,
                "goods_img": "https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/710dbf6da9182b899786456850de6216.png"
            }
        ]
    }
}

有分组的多商品返回格式：
{
    "code": 200,
    "msg": "成功",
    "data": {
        "exchange_state": "2",
        "endtime": "2025-12-31",
        "show_img": "",
        "instr": "",
        "rdr_url": "/blzh/goods.html",
        "groups": [
            {
                "group_name": "分组1",
                "exchange_times": 2,
                "remain_times": 1,
                "goods": [
                    {
                        "id": "1-35",//同goods_id，为了兼容
                        "goods_id": "1-35",
                        "exchange_group_id": 1,
                        "goods_name": "天猫购物券一元（测试）",
                        "goods_type": 3,
                        "goods_attr": "",
                        "goods_show_img": "https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/天猫.png",
                        "pre_verify_type": 2,
                        "goods_desc": "<p></p><p><img src=\"https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/1709864845ksKieo.jpg\" style=\"max-width:100%;\"><br></p>",
                        "service_time": "08:00,13:00",
                        "advance_days": 5,
                        "prize_level": 1,
                        "prize_level2": 1,
                        "available": true,
                        "goods_img": "https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/b94b506f2c9c434695b62fec5c5a5963.png"
                    },
                    {
                        "id": "1-38",//同goods_id，为了兼容
                        "goods_id": "1-38",
                        "exchange_group_id": 1,
                        "goods_name": "喜马拉雅VIP年卡",
                        "goods_type": 3,
                        "goods_attr": "",
                        "goods_show_img": "https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/fcd89f05f33ca6e7f436d7c130c19fbf.png",
                        "pre_verify_type": 1,
                        "goods_desc": "<p></p><p><img src=\"https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/1722936387dyTKzc.png\" style=\"max-width:100%;\"><br></p>",
                        "service_time": "08:00,13:00",
                        "advance_days": 5,
                        "prize_level": 1,
                        "prize_level2": 1,
                        "available": false,
                        "goods_img": "https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/710dbf6da9182b899786456850de6216.png"
                    },
                    {
                        "id": "1-37",//同goods_id，为了兼容
                        "goods_id": "1-37",
                        "exchange_group_id": 1,
                        "goods_name": "QQ音乐豪华绿钻会员",
                        "goods_type": 3,
                        "goods_attr": "",
                        "goods_show_img": "https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/1421f37031e4fb7e7948976598983895.png",
                        "pre_verify_type": 0,
                        "goods_desc": "<p></p><p><img src=\"https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/1722936310C7MvvE.png\" style=\"max-width:100%;\"><br></p>",
                        "service_time": "08:00,13:00",
                        "advance_days": 5,
                        "prize_level": 1,
                        "prize_level2": 1,
                        "available": true,
                        "goods_img": "https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/0ae141a3a8dc910a6a5faf7df58a28f8.png"
                    }
                ]
            },
            {
                "group_name": "分组2",
                "exchange_times": 1,
                "remain_times": 0,
                "goods": [
                    {
                        "id": "3-38",//同goods_id，为了兼容
                        "goods_id": "3-38",
                        "exchange_group_id": 3,
                        "goods_name": "喜马拉雅VIP年卡",
                        "goods_type": 3,
                        "goods_attr": "",
                        "goods_show_img": "https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/fcd89f05f33ca6e7f436d7c130c19fbf.png",
                        "pre_verify_type": 1,
                        "goods_desc": "<p></p><p><img src=\"https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/1722936387dyTKzc.png\" style=\"max-width:100%;\"><br></p>",
                        "service_time": "08:00,13:00",
                        "advance_days": 5,
                        "prize_level": 1,
                        "prize_level2": 1,
                        "available": false,
                        "goods_img": "https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/710dbf6da9182b899786456850de6216.png"
                    },
                    {
                        "id": "3-37",//同goods_id，为了兼容
                        "goods_id": "3-37",
                        "exchange_group_id": 3,
                        "goods_name": "QQ音乐豪华绿钻会员",
                        "goods_type": 3,
                        "goods_attr": "",
                        "goods_show_img": "https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/1421f37031e4fb7e7948976598983895.png",
                        "pre_verify_type": 0,
                        "goods_desc": "<p></p><p><img src=\"https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/1722936310C7MvvE.png\" style=\"max-width:100%;\"><br></p>",
                        "service_time": "08:00,13:00",
                        "advance_days": 5,
                        "prize_level": 1,
                        "prize_level2": 1,
                        "available": false,
                        "goods_img": "https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/0ae141a3a8dc910a6a5faf7df58a28f8.png"
                    }
                ]
            }
        ]
    }
}

说明：
exchange_state: 1-未兑换，2-兑换中（还有礼品未兑换完），3-兑换完毕。
goods: 兑换码对应的商品列表(2024-08-06)
goods_id/goods_name/goods_type/pre_verify_type/goods_img/goods_desc: 兑换码对应单个商品时才有这三个字段。
goods_type：商品类型。1-实物商品，2-虚拟商品卡密类，3-虚拟商品-异步直充类，4-实物+虚拟，5-短链接，6-家政服务类
pre_verify_type：充值验证类型。0-无，1-短信验证码(京东），2-淘宝手机号充值
rdr_url: 跳转地址。分为单品详情页，家政服务页面，多商品列表页三类。单品详情页和家政服务类的url后带商品id参数：id=xx。
goods_img: 商品图片的第一张
goods_show_img: 商品展示图
show_img: 批次展示图(2024-08-06)
instr: 批次兑换说明(2024-08-06)
available: 可兑换状态(true-可兑换，false-不可兑换)。过期未兑换的返回false。

code:
  200  - 成功 
  3025 - 兑换码已过期，会同时返回单个商品的信息

```

#### 2、订单重置接口

/api/order-reset

```
请求参数：
   order_no: 订单号

响应：
单个商品：
{
    "code":"200",
    "msg":"",
    "data":{
        "rdr_url":"/xxx/xxxxx.html?id=xx",
        "exchange_state":"3",
        "goods_id": "1",
        "goods_name":"爱奇艺黄金VIP季卡",
        "goods_type": "1",
        "pre_verify_type": "1",
        "goods_img": "https://star6.oss-cn-beijing.aliyuncs.com/gift/imgs/editor/腾讯视频.png"
        "goods_desc": "<p>商品详情</p>"
    }
}

多个商品：
{
    "code":200,
    "msg":"成功",
    "data":{
        "rdr_url":"/norm/goods.html",
        "exchange_state":"1"
    }
}

说明：见"登录接口"

```

#### 3、其他接口见api.md文件。

我的订单->订单列表中的图片用的goods_show_img（展示图） 单品首页顶部banner图用的商品图的第一张
