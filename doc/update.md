###更新记录
<br/>

#### 兑换卡类型：

```
计次卡类型：
const TYPE_LIPINKA = 1; //礼品卡（多选一）
const TYPE_JICIKA_REP   = 2; //计次卡（多选N，可多次选同一个商品）暂不用
const TYPE_JICIKA       = 3; //不重复计次卡（多选N，不能重复）
const TYPE_LIBAOKA      = 4; //礼包卡（多组选一组）
const TYPE_JICI_LIBAOKA = 5; //分组计次卡（多组，每组可多选）

//兑换码类型。1-礼品卡（多选一），2-计次卡（多选N，可多次选同一个商品），3-不重复计次卡（多选N，不能重复），4-礼包卡（多组选一组）
public static $type = [
    self::TYPE_LIPINKA      => '礼品卡',
    self::TYPE_JICIKA       => '计次卡',
    //self::TYPE_JICIKA_REP => '计次卡(同商品可重复选择)', //暂不用
    //self::TYPE_LIBAOKA    => '礼包卡', //暂不用
    self::TYPE_JICI_LIBAOKA => '分组计次卡',
];
```

#### 2024-10-17 增加分组计次卡
- 弃用计次卡（可重复），礼包卡未实现，增加分组计次卡
- 数据库：
    * exchange_details表：
        弃用exchanged_group_id字段；exchanged_goods列形如：1,2,3（礼品卡、计次卡，已兑换商品id列表），或者1-1,2-2,3-3（礼包卡、分组计次卡，分组id-已兑换商品id）
        exchanged_goods列长255，预估能容纳25次兑换(1111-1111,...)，预设兑换次数最大值为25，再多的话，需要调整该字段长度。
    * 
- 接口功能
    * 商品列表，需要返回group_id（商品列表接口、登录接口）
    * 下单/兑换接口/商品详情接口（？）/
    * 
- 后台功能
    * 批次商品配置（参考stargate项目中商品分期配置）
    * 兑换失败订单重置
    * 兑换码列表中的兑换产品显示
    * 批次对应商品管理菜单去掉增删改功能，或删掉该菜单

- 测试相关
  * 31-百联单品: 9j7n1bF08Jra9JOjKGAg3A==
  * 32-佰联组合: nS6ysqLUs8ai2488uCcIoQ==
  * 31 百联单品 礼品卡 38 YZHFDF0Q 2H3298MX PRM8W25Q ZNW6VB2U QB7YDL1F
  * 32 佰联组合 计次卡 39 8KDZ69YJ MGYNSNEN QPB0V8JS NUFGDJ29 GF1S5FML
  * 32 佰联组合 套餐卡 40 QL5U88NJ 3APBEC1Y AHWYZWU5 8T2NQVGZ 7WGZJ6PP

  * 需测试的活动：蒙商、光大工会活动？、天猫、泰隆、

```
empty($group_id) ? null : ExchangeGroup::find($group_id)

 $goods_info = Goods::find($goods_id);
        if ($group_id) {
            $this->exchange_group = ExchangeGroup::find($group_id);
        }
        
ExchangeBatch::factory($this->exchange_batch->type)->can_exchange($this->exchange_batch, $this->exchange_detail, $this->exchange_group, $goods_info->id);
```
