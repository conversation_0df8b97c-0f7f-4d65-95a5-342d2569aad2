<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <!--北京初创世纪文化传播有限公司光大银行项目-->
    <title>爱奇艺专区</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/swiper.min.css">
    <link rel="stylesheet" href="../css/icbc.css">
    <link rel="stylesheet" href="../css/common.css">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" type="text/css" href="../css/aqy/aqyintroduce.css">

    <script src="../js/jquery-3.1.0.min.js"></script>
    <script src="../js/bootstrap.min.js"></script>
    <script src="../js/swiper.min.js"></script>
    <script src="../js/popper.min.js"></script>
    <script src="../js/common.js"></script>
    <script src="../js/vue.min.js"></script>
    <script src="../js/jquery.bpopup.js"></script>
    <script src="../js/flex.js"></script>
</head>
<body>
<div class="container">
    <div class="headCon">
        <ul class="headConUl">
            <li>
                <p class="inputTitle">兑换码</p>
                <input type="text" class="covertCode" placeholder="请输入兑换码">

            </li>
            <li>
                <p class="inputTitle">手机号</p>
                <input type="text" class="phone" placeholder="请输入您在爱奇艺绑定的手机号">

            </li>
            <li>
                <p class="inputTitle">验证码</p>
                <input type="text" class="phoneCode" placeholder="请输入验证码" maxlength="6">
                <div class="codeBtn">获取验证码</div>

            </li>


        </ul>
        <div class="headConBtn">兑换</div>

    </div>
    <div class="detailCon">
        <h2>兑换说明</h2>
        <p>1.登入兑换界面，输入兑换码、手机号及短信验证码。</p>
        <p>2.确认充值账号，提交充值，等待充值到账。</p>
        <p>3.本商品只能在手机、电脑、IPAD上使用。</p>
        <h2 class="mt50">温馨提示</h2>
        <p>1、爱奇艺账号同一时间登录不能超过两台设备，切勿分享他人以免造成封号/盗号</p>
        <p>2、同一个爱奇艺账户同时激活多个激活码，有效期可叠加（例：同时激活两个爱奇艺会员月卡，会员有效期为从激活当天起两个月）。  </p>
        <p>3、如在使用过程中有疑问，可联系爱奇艺客服：************（服务时间09:00 - 24:00）。</p>
        <p>在线客服（推荐）：爱奇艺官网首页底端【帮助中心】/爱奇艺APP-【我的】-【帮助反馈】-在线客服。
        </p>


    </div>
    <div class="footBtn">订单查询</div>


</div>
<div class="popup displayno">

    <div class="popupCon1 popupConStyle displayno">
        <img src="../images/cole.png" alt="" class="closeBtn" style="z-index: 9998;">
        <p class="popupTitle">您兑换的是：</p>
        <p class="popupText goodsName"></p>
        <p class="popupTitle ">请确认您的充值账号：</p>
        <p class="popupText duiPhone"></p>
        <div class="line"></div>
        <p class="tipTitle">温馨提示：</p>
        <p class="tipText">请仔细核对您的充值账号，充值过程不可逆，一旦充值成功不可退换。</p>
        <div class="confirmBtn orderBtn">确认</div>

    </div>
    <div class="popupCon2 popupConStyle displayno">
        <img src="../images/cole.png" alt="" class="closeBtn" style="z-index: 9998;">
        <ul class="headConUl">
            <li>
                <p class="inputTitle">兑换码</p>
                <input type="text" class="covertCodePopup" placeholder="请输入兑换码">

            </li>
            <li>
                <p class="inputTitle">手机号</p>
                <input type="text" class="phonePopup" placeholder="请输入您在爱奇艺绑定的手机号">

            </li>
        </ul>
        <div class="confirmBtn orderListBtn">确认</div>

    </div>
    <div class="popupCon3 popupConStyle displayno">
        <!-- <img src="../images/cole.png" alt="" class="closeBtn" style="z-index: 9998;"> -->
        <img class="popupImg" src="../images/errorIcon.png" alt="">
        <p class="errorMsg"></p>
        <div class="confirmBtn close">确认</div>

    </div>


</div>
<!-- toast -->
<div id="m-toast-pop" style="z-index:9999;" class="m-toast-pop">
    <div class="m-toast-inner">
        <div class="m-toast-inner-text" id="m-toast-inner-text"></div>
    </div>
</div>

<script>
    (function () {
        var aqyintroduce = {
            goodsId: '',
            goodsName: '',
            init: function () {
                this.bind();
                this.getCard();
            },
            bind: function () {
                //点击获取验证码校验
                $('.codeBtn').on('click', function () {
                    var phoneNum = $('.phone').val();
                    //首先校验手机号
                    if (isPhoneNumber(phoneNum)) {
                        $.ajax({//发送验证码
                            type: "POST",
                            url: "/api/captcha",
                            data: {
                                'mobile': phoneNum,
                                "act": aqyintroduce.getAct(),

                            },
                            async: true,
                            dataType: "json",
                            xhrFields: {
                                withCredentials: true
                            },
                            success: function (res, status, xhr) {
                                if (res.code == '200') {
                                    aqyintroduce.countDown(60);
                                } else {
                                    toastinfo(res.msg);
                                }
                            },
                            error: function (error, xhr, abort) {
                                toastinfo("网络错误，请稍后再试！");
                            }
                        })

                    } else {
                        toastinfo('请输入正确的手机号！');
                    }
                });
                //点击弹窗的关闭按钮
                $('.closeBtn').on('click', function () {
                    $('.popup').addClass('displayno');
                    $('.popupCon1').addClass('displayno');
                    $('.popupCon3').addClass('displayno');
                    $('.popupCon2').addClass('displayno');
                });
                //点击二次确认得兑换
                $('.orderBtn').on('click', function () {
                    var phoneNum = $('.phone').val();
                    $.ajax({
                        type: "POST",
                        url: "/api/order",
                        data: {
                            "goods_id": aqyintroduce.goodsId,
                            "charge_account": phoneNum,
                        },
                        async: true,
                        dataType: "json",
                        xhrFields: {
                            withCredentials: true
                        },
                        success: function (res, status, xhr) {
                            if (res.code == '200') {
                                window.location.href = './aqyresult.html';
                            } else {
                                $('.errorMsg').text(res.msg);
                                $('.popup').removeClass('displayno');
                                $('.popupCon1').addClass('displayno');
                                $('.popupCon3').removeClass('displayno');
                            }
                        },
                        error: function (error, xhr, abort) {
                            toastinfo("网络错误，请稍后再试！");
                        }
                    })
                });
                //点击错误提示得确定按钮
                $('.close').on('click', function () {
                    $('.popup').addClass('displayno');
                    $('.popupCon1').addClass('displayno');
                    $('.popupCon3').addClass('displayno')
                });

                //点击兑换按钮
                $('.headConBtn').on('click', function () {
                    var phoneNum = $('.phone').val();
                    var code = $('.covertCode').val();
                    var phoneCode = $('.phoneCode').val();

                    if (code == '') {
                        return toastinfo('请输入兑换码！');
                    }
                    if (!isPhoneNumber(phoneNum)) {
                        return toastinfo('请输入正确的手机号！');
                    }
                    if (phoneCode == '') {
                        return toastinfo('请输入验证码！');
                    }
                    if ($('.headConBtn').hasClass('noClick')) {
                        return;
                    }
                    $('.headConBtn').addClass('noClick');
                    $.ajax({
                        type: "POST",
                        url: "/api/login",
                        data: {
                            "card_pwd": code,
                            "sms_captcha": phoneCode,
                            "mobile": phoneNum,
                            "act": aqyintroduce.getAct(),
                        },
                        async: true,
                        dataType: "json",
                        xhrFields: {
                            withCredentials: true
                        },
                        success: function (res, status, xhr) {
                            $('.headConBtn').removeClass('noClick');
                            if (res.code == '200') {
                                aqyintroduce.goodsId = res.data.goods_id;
                                aqyintroduce.goodsName = res.data.goods_name;
                                $('.goodsName').text(res.data.goods_name);
                                $('.duiPhone').text(phoneNum);
                                $('.popup').removeClass('displayno');
                                $('.popupCon1').removeClass('displayno')

                            } else {
                                toastinfo(res.msg);
                            }
                        },
                        error: function (error, xhr, abort) {
                            $('.headConBtn').removeClass('noClick');
                            toastinfo("网络错误，请稍后再试！");
                        }
                    })


                });
                //点击查询列表按钮
                $('.orderListBtn').on('click', function () {
                    var covertCodePopup = $('.covertCodePopup').val();
                    var phonePopup = $('.phonePopup').val();
                    if (covertCodePopup == '') {
                        return toastinfo("请输入正确的兑换码！");
                    }
                    if (!isPhoneNumber(phonePopup)) {
                        return toastinfo('请输入正确的手机号！');
                    }
                    $.ajax({
                        type: "POST",
                        url: "/api/login",
                        data: {
                            "card_pwd": covertCodePopup,
                            "sms_captcha": '',
                            "mobile": phonePopup,
                            "act": aqyintroduce.getAct(),

                        },
                        async: true,
                        dataType: "json",
                        xhrFields: {
                            withCredentials: true
                        },
                        success: function (res, status, xhr) {
                            if (res.code == '200') {
                                $('.popup').addClass('displayno');
                                $('.popupCon2').addClass('displayno');
                                window.location.href = '../myorder.html?_r=1128';
                            } else {
                                toastinfo(res.msg);
                            }
                        },
                        error: function (error, xhr, abort) {
                            $('.headConBtn').removeClass('noClick');
                            toastinfo("网络错误，请稍后再试！");
                        }
                    })


                });
                //点击底部订单查询
                $('.footBtn').on('click', function () {
                    $.ajax({
                        type: "POST",
                        url: "/api/is-login",
                        async: true,
                        xhrFields: {
                            withCredentials: true
                        },
                        success: function (res, status, xhr) {
                            if (res.code == '200') {
                                window.location.href = "../myorder.html?_r=1128";
                            } else {
                                $('.popup').removeClass('displayno');
                                $('.popupCon2').removeClass('displayno')
                            }
                        },
                        error: function (error, xhr, abort) {
                            $('.popup').removeClass('displayno');
                            $('.popupCon2').removeClass('displayno')
                        }
                    })
                })

            },
            countDown: function (timeNum) {
                if ($(".codeBtn").hasClass("noClick")) {
                    return;
                }
                $('.codeBtn').addClass('noClick');
                var time;
                var num = timeNum;
                time = setInterval(function () {
                    num = num - 1;
                    if (num <= 0) {
                        num = timeNum;
                        $(".codeBtn").html('获取验证码');
                        $('.codeBtn').removeClass('noClick');
                        clearInterval(time);
                        return false;
                    } else {
                        $('.codeBtn').html(num);
                    }
                }, 1000)
            },
            getAct: function () {
                return 'oa+3udepLQSjK+0Tlf7nyA==';
            },
            getCard: function () {
                var card = GetQueryString('s');
                if (card) {
                    card = card.split(/[.,，。]/)[0];
                    if (card) {
                        $('.covertCode').val(card);
                    }
                }
            }

        };
        aqyintroduce.init();


    })()

</script>
</body>
</html>
