<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>权益兑换</title>
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/bootstrap.min.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/icbc.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/common.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/style.css">
    <link rel="stylesheet" type="text/css" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/tm/index.css">

    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/jquery-3.1.0.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/bootstrap.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/popper.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/common.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/vue.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/jquery.bpopup.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/flex.js"></script>

    <style>
        .tm_header {
            padding-top: 0.3rem;
            padding-left: 0.3rem;
            font-size: .3rem;
        }

        .noClick {
            background: #999;
            pointer-events: none;
        }

        .account_list {
            display: flex;
            font-size: .2rem;
            background: #fff;
            margin: .3rem auto;
            width: 90%;
            border-radius: 1rem;
            border: 2px solid #fff;
        }

        .selectedClass {
            border: 2px solid #ff0000;
            display: flex;
            font-size: .2rem;
            background: #fff;
            margin: .3rem auto;
            width: 90%;
            border-radius: 1rem;
        }

        .account_info {
            font-size: .28rem;
            margin: .3rem 0 0 .2rem;
        }

        .account_info .zhm {
            color: #B3B3B3;
            margin-top: .13rem;
        }

        img {
            padding: .15rem;
            width: 1.5rem;
            height: 1.5rem;
        }

    </style>
</head>

<body>
<div class="container" id="app">
    <p class="tm_header">根据手机号查询到以下账号,请选择:</p>
    <div v-for="(item, index) in accountList" :key="index" :class="num===index?'selectedClass':'account_list'"
         @click="selectAccount(index)">
        <div class="account_header"><img src="../images/tm/header.png" alt=""></div>
        <div class="account_info">
            <p style="font-weight:bold">{{item.buyer_display_nick_mask}} <span v-if="item.active">(最活跃账号)</span>
            </p>
            <p class="zhm">账户名:{{item.buyer_nick_mask}}</p>
        </div>
    </div>

    <div class="confirmBtn">确认充值</div>

    <!-- toast -->
    <div id="m-toast-pop" style="z-index:9999;" class="m-toast-pop">
        <div class="m-toast-inner">
            <div class="m-toast-inner-text" id="m-toast-inner-text"></div>
        </div>
    </div>
</div>
<script>

    let accountList = sessionStorage.getItem('accountList');
    // 第一次进入页面或者刷新页面的时候 重置账号和account_name
    sessionStorage.charge_account_jm = '';
    sessionStorage.account_name = ''
    let vueData = {
        accountList: JSON.parse(accountList),
        num: -1 // 默认不选择,需要用户手动选择
    }

    let vm = new Vue({
        el: '#app',
        data: vueData,
        mounted: function () {

        },
        methods: {
            selectAccount: function (id) {
                // 存提交的时候所需要的参数
                sessionStorage.setItem('charge_account_jm', this.accountList[id].obs_buyer_id);
                sessionStorage.setItem('account_name', this.accountList[id].buyer_nick_mask + '-' + this.accountList[id].buyer_display_nick_mask);
                this.num = id;

            }
        }
    });

    $('.confirmBtn').on('click', function () {
        let goods_id = sessionStorage.getItem('goods_id');
        let charge_account = sessionStorage.getItem('charge_account_jm'); // 充值加密账号
        let account_name = sessionStorage.getItem('account_name');
        let user_mobile = sessionStorage.getItem('charge_account'); // 充值手机号

        if (charge_account == '' || account_name == '') {
            toastinfo("请选择要充值的账号！");
            return;
        }
        $.ajax({//确定绑定
            type: "POST",
            url: "/api/order",
            data: {
                "goods_id": goods_id,
                "charge_account": charge_account,
                "user_mobile": user_mobile,
                'order_remark': account_name,
            },
            async: true,
            dataType: "json",
            xhrFields: {
                withCredentials: true
            },
            success: function (res, status, xhr) {
                if (res.code == '200') {
                    // 这个手机号只有一个账户的情况下直接成功
                    window.location.href = './success.html'
                } else if (res.code == '3030') {
                    //超限
                    $('.popupConfirm').removeClass('noClick');
                    $('.popup').addClass('displayNo');
                    window.location.href = './error.html?_v=1'
                } else {
                    toastinfo(res.msg);
                }
            },
            error: function (error, xhr, abort) {
                $('.popupConfirm').removeClass('noClick');
                window.location.href = './error.html';

                toastinfo("网络错误，请稍后再试！");
            }
        })
    });

</script>
</body>

</html>
