<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>商品列表</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/swiper.min.css">
    <link rel="stylesheet" href="../css/icbc.css">
    <link rel="stylesheet" href="../css/common.css">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" type="text/css" href="../css/goods.css">

    <script src="../js/jquery-3.1.0.min.js"></script>
    <script src="../js/bootstrap.min.js"></script>
    <script src="../js/swiper.min.js"></script>
    <script src="../js/popper.min.js"></script>
    <script src="../js/common.js"></script>
    <script src="../js/vue.min.js"></script>
    <script src="../js/jquery.bpopup.js"></script>
    <style>
        .goodsName {
            font-size: .28rem;
            height: 0.8rem;
            line-height: 0.4rem;
            text-overflow: -o-ellipsis-lastline;
            overflow: hidden;
            text-align: center;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }

        .adsStyle {
            width: 7rem;
            float: left;
            margin-top: 0.3rem;
            margin-left: 0.25rem;

        }

        .container {
            padding-bottom: 1rem;
            float: left;
        }

        .adsTitle {
            float: left;
            width: 7rem;
            margin-left: 0.25rem;
            margin-top: 0.5rem;
            font-size: 0.38rem;
        }

    </style>
</head>
<body>
<div class="container">
    <div class="contbox" id="app">
        <div class="cm-padded-15 cm-font-size-16"><span class="cm-font-size-18">本月书单</span>&nbsp;&nbsp;&nbsp;&nbsp;<span
            class="cm-font-size-15">您可以选择以下任意一款兑换</span></div>
        <div class="cm-f cm-f-w listbox">
            <div class="list" v-for="(item, key) in goodsList" :key="key" @click="openDetail(item.id)">
                <div class="listinner">
                    <div class="imgbox cm-f-c-c">
                        <img :src="item.goods_show_img" :style="{height: wd + 'px'}" class="goodsimg" alt="">
                    </div>
                    <div class="cm-padded-10">
                        <div class="cm-font-size-16 goodsName" v-text="item.goods_name"
                             style="min-height: .96rem;"></div>
                        <!-- <div v-if="item.goods_type==1" class="cm-f-b-c cm-margin-t-10">
                            <div class="cm-font-size-14 cm-color-grey">市场参考价</div>
                            <div style="color:#1398E9;" class="cm-font-size-20 cm-color-lightred">￥109</div>
                        </div> -->
                    </div>
                </div>
            </div>
        </div>
<!--        <div class="nomore cm-color-grey cm-font-size-14 cm-margin-t-15 cm-tx-c">&nbsp;</div>-->

        <div v-for="(item, key) in vueData.ads">
            <p v-show="item.show_title!='' && item.show_title!=null " class="adsTitle">
                {{item.show_title}}</p>
            <img v-show="item.show_img_url!='' && item.show_img_url!=null " class="adsStyle"
                 @click="adsClick(item)" :src="item.show_img_url" alt="">
        </div>

        <div class="botbtnbox cm-f-c-c cm-color-white cm-line-h-1" style="margin-top:0.3rem;float: left;"
             onClick="openWin()">
            <div style="background:#1398E9;" class="botbtn cm-f-c-c cm-font-size-16">查询预约记录</div>
        </div>
    </div>
</div>

<!-- toast -->
<div id="m-toast-pop" class="m-toast-pop">
    <div class="m-toast-inner">
        <div class="m-toast-inner-text" id="m-toast-inner-text"></div>
    </div>
</div>

<script>
    var vueData = {
        goodsList: [
            {
                "id": 20,
                "goods_show_img": "https://star0.oss-cn-shanghai.aliyuncs.com/prize/imgs/goods/主图1.png",
                "goods_name": "苏泊尔智能电饭煲",
                "goods_type": '1',
                "is_has_stock": 1 //暂不用这个，根据活动要求，有的活动没有。
            }, {
                "id": 20,
                "goods_show_img": "https://star0.oss-cn-shanghai.aliyuncs.com/prize/imgs/goods/主图1.png",
                "goods_name": "苏泊尔智能电饭煲",
                "goods_type": '1',
                "is_has_stock": 1 //暂不用这个，根据活动要求，有的活动没有。
            }
        ],
        wd: 0,
        ads: [] //ad. [{"show_title": "","show_img_url": "", "jump_url": ""}]
    };

    function openWin() {
        // console.log(1111)
        jump('../myorder.html?_r=1128&proj=' + GetQueryString('proj'));
    }

    var vm = new Vue({
        el: '#app',
        data: vueData,
        mounted: function () {
            this.$nextTick(function () {
                var wd = $('.imgbox').width()
                vueData.wd = wd
            })
        },
        methods: {
            adsClick(data) {
                if (data.jump_url != '' && data.jump_url != null && data.jump_url != undefined) {
                    window.location.href = data.jump_url;
                }
            },
            openDetail: function (id) {
                $.ajax({
                    type: "POST",
                    url: "/api/is-permit",
                    data: {
                        'goods_id': id

                    },
                    async: true,
                    dataType: "json",
                    xhrFields: {
                        withCredentials: true
                    },
                    success: function (res, status, xhr) {
                        if (res.code == '200') {
                            jump('detail.html?id=' + id + '&proj=' + GetQueryString('proj'));

                        } else {
                            toastinfo(res.msg);
                        }
                    },
                    error: function (error, xhr, abort) {
                        toastinfo("网络错误，请稍后再试！");
                    }
                })
            },

        }
    });
    var fn = function () {
        $.ajax({
            type: "POST",
            url: "/api/goods-list",
            async: true,
            dataType: "json",
            xhrFields: {
                withCredentials: true
            },
            success: function (res, status, xhr) {
                if (res.code == '200') {
                    // console.log(res.msg);
                    vueData.goodsList = res.data['1']['1']
                    console.log(vueData.goodsList)
                } else {
                    toastinfo(data.msg);
                }
            },
            error: function (error, xhr, abort) {
                toastinfo("网络错误，请稍后再试！");
            }
        })
    }();

    var ad = function () {
        $.ajax({
            type: "POST",
            url: "/api/activity-ad",
            async: true,
            dataType: "json",
            xhrFields: {
                withCredentials: true
            },
            success: function (res, status, xhr) {
                if (res.code == '200') {
                    // console.log(res.msg);
                    vueData.ads = res.data;
                    console.log(vueData.ads)
                } else {
                    toastinfo(data.msg);
                }
            },
            error: function (error, xhr, abort) {
                toastinfo("网络错误，请稍后再试！");
            }
        })
    }();
</script>
</body>
</html>
