<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<title>权益兑换中心</title>
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
	<link rel="stylesheet" href="../css/bootstrap.min.css">
	<link rel="stylesheet" href="../css/swiper.min.css">
	<link rel="stylesheet" href="../css/icbc.css">
	<link rel="stylesheet" href="../css/common.css">
	<link rel="stylesheet" href="../css/style.css">
	<link rel="stylesheet" type="text/css" href="../css/jz/aqyintroduce.css">

	<script src="../js/jquery-3.1.0.min.js"></script>
	<script src="../js/bootstrap.min.js"></script>
	<script src="../js/swiper.min.js"></script>
	<script src="../js/popper.min.js"></script>
	<script src="../js/common.js"></script>
	<script src="../js/vue.min.js"></script>
	<script src="../js/jquery.bpopup.js"></script>
	<script src="../js/flex.js"></script>
</head>
<body>
<div class="container">
	    <p class="headTitle">权益兑换中心</p>

	<div class="headCon">
		<ul class="headConUl">
			<li>
				<img class="liICon" src="../images/duiCode.png" alt="">
				<!-- <p class="inputTitle">兑换码</p> -->
				<input type="text" class="covertCode" placeholder="请填写兑换码">

			</li>
			<li>
				<!-- <p class="inputTitle">手机号</p> -->
				<img class="liICon" style="left:0.25rem;" src="../images/phone.png" alt="">
				<input type="text" class="phone" placeholder="请填写手机号">

			</li>
			<li>
				<img class="liICon" src="../images/code.png" alt="">
				<input type="text" class="phoneCode" placeholder="请填写验证码">
				<div class="codeBtn">获取验证码</div>

			</li>


		</ul>
		<div class="headConBtn">确定</div>

	</div>
	<div class="footBtn">订单查询</div>


</div>
<div class="popup displayno">


	<div class="popupCon2 popupConStyle displayno" style="background:#2575b7;">
	   <img src="../images/cole.png" alt="" class="closeBtn">
		<ul class="headConUl">
			<li>
				<img class="liICon" src="../images/duiCode.png" alt="">
				<input type="text" class="covertCodePopup" placeholder="请输入兑换码">

			</li>
			<li>
				<img class="liICon" style="left:0.25rem;" src="../images/phone.png" alt="">
				<input type="text" class="phonePopup" placeholder="请填写手机号">

			</li>
		</ul>
		<div class="confirmBtn orderListBtn">确认</div>

	</div>
	<div class="popupCon3 popupConStyle displayno" >
		<!-- <img src=".../images/cole.png" alt="" class="closeBtn"> -->
		<img class="popupImg" src=".../images/errorIcon.png" alt="">
		<p class="errorMsg"></p>
		<div class="confirmBtn close" style="background:#2575B7;color:#fff;">确认</div>

	</div>
</div>
<!-- toast -->
<div id="m-toast-pop" style="z-index:9999;" class="m-toast-pop">
	<div class="m-toast-inner">
		<div class="m-toast-inner-text" id="m-toast-inner-text"></div>
	</div>
</div>

<script>
	(function(){
		var aqyintroduce ={
			goodsId:'',
			goodsName:'',
			init:function(){
				this.bind();
			},
			bind:function(){
				//点击获取验证码校验
				$('.codeBtn').on('click',function(){
					var  phoneNum  = $('.phone').val();
					//首先校验手机号
					if(isPhoneNumber(phoneNum)){
						$.ajax({//发送验证码
							type: "POST",
							url:"/api/captcha",
							data: {
								'mobile': phoneNum

							},
							async: true,
							dataType: "json",
							xhrFields: {
								withCredentials: true
							},
							success : function (res, status, xhr) {
								if(res.code == '200'){
									aqyintroduce.countDown(60)
								}else{
									toastinfo(res.msg);
								}
							},
							error: function (error, xhr, abort) {
								toastinfo("网络错误，请稍后再试！");
							}
						})

					}else{
						toastinfo('请输入正确的手机号！')
					}
				})
				//点击弹窗的关闭按钮
				$('.closeBtn').on('click',function(){
					$('.popup').addClass('displayno');
					$('.popupCon1').addClass('displayno')
					$('.popupCon3').addClass('displayno')
					$('.popupCon2').addClass('displayno')
				})
				//点击二次确认得兑换
				$('.orderBtn').on('click',function(){
					var  phoneNum  = $('.phone').val();
					$.ajax({
						type: "POST",
						url:"/api/order",
						data: {
							"goods_id":aqyintroduce.goodsId,
							"charge_account":phoneNum,
						},
						async: true,
						dataType: "json",
						xhrFields: {
							withCredentials: true
						},
						success : function (res, status, xhr) {
							if(res.code == '200'){
								window.location.href='../aqyresult.html';
							}else{
								$('.errorMsg').text(res.msg)
								$('.popup').removeClass('displayno');
								$('.popupCon1').addClass('displayno')
								$('.popupCon3').removeClass('displayno')

							}
						},
						error: function (error, xhr, abort) {
							toastinfo("网络错误，请稍后再试！");
						}
					})
				})
				//点击错误提示得确定按钮
				$('.close').on('click',function(){
					$('.popup').addClass('displayno');
					$('.popupCon1').addClass('displayno')
					$('.popupCon3').addClass('displayno')
				})

				//点击兑换按钮
				$('.headConBtn').on('click',function(){
					var  phoneNum  = $('.phone').val();
					var  code = $('.covertCode').val();
					var  phoneCode = $('.phoneCode').val();

					if(code==''){
						return toastinfo('请输入兑换码！');
					}
					if(!isPhoneNumber(phoneNum)){
						return toastinfo('请输入正确的手机号！');
					}
					if(phoneCode==''){
						return toastinfo('请输入验证码！');
					}
					if($('.headConBtn').hasClass('noClick')){
						return ;
					}
					$('.headConBtn').addClass('noClick');
					$.ajax({
						type: "POST",
						url:"/api/login",
						data: {
							"card_pwd":code,
							"sms_captcha":phoneCode,
							"mobile": phoneNum,
							"act":'',

						},
						async: true,
						dataType: "json",
						xhrFields: {
							withCredentials: true
						},
						success : function (res, status, xhr) {
							$('.headConBtn').removeClass('noClick');
							if(res.code == '200'){
								window.location.href=res.data.rdr_url
								// aqyintroduce.goodsId=res.data.goods_id;
								// aqyintroduce.goodsName=res.data.goods_name;
								// $('.goodsName').text(res.data.goods_name);
								// $('.duiPhone').text(phoneNum);
								// $('.popup').removeClass('displayno');
								// $('.popupCon1').removeClass('displayno')

							}else{
								toastinfo(res.msg);
							}
						},
						error: function (error, xhr, abort) {
							window.location.href='../jz/jzindex.html'
							$('.headConBtn').removeClass('noClick');
							toastinfo("网络错误，请稍后再试！");
						}
					})


				})
				//点击查询列表按钮
				$('.orderListBtn').on('click',function(){
					var  covertCodePopup = $('.covertCodePopup').val();
					var  phonePopup = $('.phonePopup').val();
					if(covertCodePopup==''){
						return toastinfo("请输入正确的兑换码！");
					}
					if(!isPhoneNumber(phonePopup)){
						return toastinfo('请输入正确的手机号！');
					}
					$.ajax({
						type: "POST",
						url:"/api/login",
						data: {
							"card_pwd":covertCodePopup,
							"sms_captcha":'',
							"mobile": phonePopup,
							"act":'',

						},
						async: true,
						dataType: "json",
						xhrFields: {
							withCredentials: true
						},
						success : function (res, status, xhr) {
							if(res.code == '200'){
								$('.popup').addClass('displayno');
								$('.popupCon2').addClass('displayno')
								window.location.href= res.data.rdr_url
							}else{
								toastinfo(res.msg);
							}
						},
						error: function (error, xhr, abort) {
							$('.headConBtn').removeClass('noClick');
							toastinfo("网络错误，请稍后再试！");
						}
					})


				})
				//点击底部订单查询
				$('.footBtn').on('click',function(){
					$.ajax({
						type: "POST",
						url:"/api/is-login",
						async: true,
						xhrFields: {
							withCredentials: true
						},
						success : function (res, status, xhr) {
							if(res.code == '200'){
								window.location.href="myorder.html?_r=1128";
							}else{
								$('.popup').removeClass('displayno');
								$('.popupCon2').removeClass('displayno')
							}
						},
						error: function (error, xhr, abort) {
							$('.popup').removeClass('displayno');
							$('.popupCon2').removeClass('displayno')
						}
					})
				})

			},
			countDown:function(timeNum){
				if( $(".codeBtn").hasClass("noClick")){
					return ;
				}
				$('.codeBtn').addClass('noClick');
				var time ;
				var num=timeNum;
				time= setInterval(function(){
					num =num-1;
					if(num <=0){
						num =timeNum;
						$(".codeBtn").html('获取验证码');
						$('.codeBtn').removeClass('noClick');
						clearInterval(time);
						return false;
					}else{
						$('.codeBtn').html(num);
					}
				},1000)
			}

		}
		aqyintroduce.init();


	})()




</script>
</body>
</html>
