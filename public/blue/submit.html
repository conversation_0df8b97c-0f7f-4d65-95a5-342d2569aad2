<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>提交订单</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/swiper.min.css">
    <link rel="stylesheet" href="../css/icbc.css">
    <link rel="stylesheet" href="../css/common.css">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/success.css">

    <link rel="stylesheet" type="text/css" href="../css/submit.css">

    <script src="../js/jquery-3.1.0.min.js"></script>
    <script src="../js/bootstrap.min.js"></script>
    <script src="../js/swiper.min.js"></script>
    <script src="../js/popper.min.js"></script>
    <script src="../js/common.js"></script>
    <script src="../js/vue.min.js"></script>
    <script src="../js/jquery.bpopup.js"></script>
    <link href="apple-touch-icon.png" rel="apple-touch-icon">
    <link href="../city/css/font-awesome.min.css" rel="stylesheet">
    <link href="../city/css/framework7.ios.min.css" rel="stylesheet">
    <link href="../city/css/framework7.ios.colors.min.css" rel="stylesheet">
    <link href="../city/css/style.css" rel="stylesheet">

</head>
<body>
<div class="container">
    <div class="titlebox">
        <p class="title"></p>
        <p class="subtitle"></p>
    </div>

    <!-- 邮寄类产品 -->
    <div class="buybox hide">
        <input type="text" name="" placeholder="请输入收货人姓名" class="name">
        <input type="text" name="" placeholder="请输入收货人手机号" class="name phone">
        <div class="sepline buyline"></div>
        <input type="text" placeholder="请选择城市" readonly id="location" name="location">
        <input type="text" name="" placeholder="请输入详细的收货地址" class="address">
    </div>

    <!-- 充值类产品 -->
    <div class="rechargebox hide">
        <p class="title2">充值号码</p>
        <div class="sepline"></div>
        <input type="text" name="" class="recharge" placeholder="请输入充值号码">
    </div>

    <div style="background:#1398E9;" class="rechargebtn">
        <p>立即预约</p>
    </div>
</div>

<!-- toast -->
<div id="m-toast-pop" class="m-toast-pop">
    <div class="m-toast-inner">
        <div class="m-toast-inner-text" id="m-toast-inner-text"></div>
    </div>
</div>
<div class="popupError displayno">
    <div class="failbox">
        <div class="failinner">
            <img src="../images/failicon.png" class="iconfail">
            <div class="cm-color-red cm-tx-c info cm-margin-t-10">预约失败</div>
            <div class="cm-font-size-14 cm-tx-c cm-margin-t-15 exc_err_msg">您已经预约过其他商品啦</div>
            <div class="botbtnbox cm-f-c-c cm-color-white cm-line-h-1">
                <div class="botbtn cm-f-c-c cm-font-size-16" onclick="toorders()">查询预约记录</div>
            </div>
        </div>
    </div>
</div>
<script src="../city/js/framework7.min.js"></script>
<script src="../city/js/regionsObject2.js"></script>
<script src="../city/js/cityPicker.js"></script>
<script>
    //1-实物商品，2-虚拟商品卡密类，3-虚拟商品-异步直充类，4-实物+虚拟。
    var type = GetQueryString("type");
    if (type == 1) {
        $('.buybox')[0].classList.remove('hide');
    } else {
        $('.rechargebox')[0].classList.remove('hide');
    }
    $('.title')[0].innerText = GetQueryString("name");
    $('.subtitle')[0].innerText = GetQueryString("attr");


    $('.rechargebtn').on('click', function () {
        if (type == 1) {

            if ($('.name')[0].value == "") {
                toastinfo('请填写姓名');
                return;
            }
            if (!isPhoneNumber($('.phone')[0].value)) {
                toastinfo('请填写正确的手机号');
                return;
            }
            if ($('#location').val() == "") {
                toastinfo('请选择省市区');
                return;
            }
            if ($('.address')[0].value.length < 5) {
                toastinfo('请填写完整的省市区及详细收货地址');
                return;
            }
        } else {
            if ($('.recharge')[0].value == "") {
                toastinfo('请填写充值账号');
                return;
            }
        }
        $.ajax({
            type: "POST",
            url: "/api/order",
            data: {
                'goods_id': GetQueryString('id'),
                'consignee_name': $('.name')[0].value,
                'consignee_phone': $('.phone')[0].value,
                'consignee_address': $('#location').val() + ' ' + $('.address')[0].value,
                'charge_account': $('.recharge')[0].value
            },
            async: true,
            dataType: "json",
            xhrFields: {
                withCredentials: true
            },
            success: function (data, status, xhr) {
                if (data.code == '200') {
                    console.log(data.msg);
                    if (GetQueryString('proj') == 'slt') {
                        jump(GetQueryString('proj') + '/success.html');
                    } else {
                        jump('success.html?proj=' + GetQueryString('proj'));
                    }
                } else {
                    $('.popupError').removeClass('displayno')
                    $('.exc_err_msg').html(data.msg);
                    // toastinfo(data.msg);
                }
            },
            error: function (error, xhr, abort) {
                toastinfo("网络错误，请稍后再试！");
            }
        })
    })

    function toorders() {
        $('.popupError').removeClass('displayno');
        jump('../myorder.html?_r=1128');
    }
</script>
</body>
</html>
