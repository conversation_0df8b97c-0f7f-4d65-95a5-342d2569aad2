<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>权益兑换</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/bootstrap.min.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/swiper.min.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/icbc.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/common.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/style.css">
    <link rel="stylesheet" type="text/css" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/tm/index.css">

    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/jquery-3.1.0.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/bootstrap.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/swiper.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/popper.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/common.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/vue.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/jquery.bpopup.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/flex.js"></script>
</head>

<body>
<div class="container">
    <img class="headImg" src="https://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/luckin/top.png" alt="">
    <div class="inputBox mt30">
        <p class="inputText">充值账号</p>
        <input class="longInput taobaoId" type="text" placeholder="请输入您的手机号">
    </div>
    <div class="confirmBtn">确定</div>
    <div class="conBox">
        <div class="boxHeader">
            <img class="leftImg" src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/tm/left.png" alt="">
            <p class="conTitle">兑换规则</p>
            <img class="rightImg" src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/tm/right.png" alt="">
        </div>
        <p class="colorNum">1.</p>
        <p class="colorOne">兑换方式：本产品为自动充值，请确认后输入需要充值的手机号。请务必核对好号码，由于号码填写错误导致充值错误的，将无法退换、退款。</p>
        <p class="colorNum">2.</p>
        <p class="colorOne">权益查看：购买成功后，饮品券将会在1个工作日内充值到
            luckin coffee “咖啡钱包〞账户内。</p>
        <p class="colorNum">3.</p>
        <p class="colorOne">注意事项：本券仅适用于luckin coffee APP及小程序内下单购买饮品，一次可使用多张。本券有效期三年，不做退換，不予提现，不设找零，抵用金额不可开具发票。</p>
        <p class="colorNum">4.</p>
        <p class="colorOne">客服咨询电话：4006526678。</p>
    </div>

</div>

</div>
<img class="topImg displayNo" src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/tm/top.png" alt="">
<div class="popup displayNo">
    <div class="popupCon">
        <img class="closeImg" src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/cole.png" alt="">
        <p class="text1">请确认您的充值账号</p>
        <p class="text1">充值账号：<span class="tmNumber"></span></p>
        <p class="text2">请仔细核对您填写的充值账号是否正确，避免错充。</p>
        <div class="reset">修改</div>
        <div class="popupConfirm">确认</div>
    </div>
</div>
<!-- toast -->
<div id="m-toast-pop" style="z-index:9999;" class="m-toast-pop">
    <div class="m-toast-inner">
        <div class="m-toast-inner-text" id="m-toast-inner-text"></div>
    </div>
</div>

<script>
    (function () {
        var tmIndex = {
            init: function () {
                this.bind();
            },
            bind: function () {
                //监听页面滚动
                $(document).scroll(function () {
                    var scroH = $(document).scrollTop();
                    var viewH = $(window).height();
                    var contentH = $(document).height();
                    if (scroH > 300) {
                        $('.topImg').removeClass('displayNo')

                    }
                    if (scroH < 300) {
                        $('.topImg').addClass('displayNo')

                    }
                });
                //点击淘宝文字
                $('.seeDetail').on('click', function () {
                    $("html,body").animate({
                        scrollTop: 400
                    }, 500);
                });
                //点击回到顶部按钮
                $('.topImg').on('click', function () {
                    $("html,body").animate({
                        scrollTop: 0
                    }, 500);
                });
                //点击弹窗的关闭按钮
                $('.closeImg').on('click', function () {
                    $('.popup').addClass('displayNo')
                });
                //点击修改按钮弹窗关闭
                $('.reset').on('click', function () {
                    $('.popup').addClass('displayNo')

                });
                //点击确定按钮
                $('.confirmBtn').on('click', function () {
                    var taobaoId = $('.taobaoId').val();
                    if (taobaoId == '') {
                        toastinfo('请输入充值账号！');
                        return false;
                    }
                    $('.tmNumber').text(taobaoId);
                    $('.popup').removeClass('displayNo')
                });
                //点击弹窗的确定按钮
                $('.popupConfirm').on('click', function () {
                    if ($('.popupConfirm').hasClass('noClick')) {
                        return;
                    }
                    var goods_id = GetQueryString('goods_id');
                    var taobaoId = $('.taobaoId').val();
                    $('.popupConfirm').addClass('noClick');
                    $.ajax({//确定绑定
                        type: "POST",
                        url: "/api/order",
                        data: {
                            "goods_id": goods_id,
                            "charge_account": taobaoId,
                        },
                        async: true,
                        dataType: "json",
                        xhrFields: {
                            withCredentials: true
                        },
                        success: function (res, status, xhr) {
                            if (res.code == '200') {
                                $('.popup').addClass('displayNo');
                                $('.popupConfirm').removeClass('noClick');
                                window.location.href = './success.html'
                            } else if (res.code == '3030') {
                                //超限
                                $('.popupConfirm').removeClass('noClick');
                                $('.popup').addClass('displayNo');
                                window.location.href = './error.html?_v=1'//超限跳转
                            } else {
                                toastinfo(res.msg);
                            }
                        },
                        error: function (error, xhr, abort) {
                            $('.popupConfirm').removeClass('noClick');
                            window.location.href = './error.html';

                            toastinfo("网络错误，请稍后再试！");
                        }
                    })

                })
            }

        };
        tmIndex.init();

    })()

</script>
</body>

</html>
