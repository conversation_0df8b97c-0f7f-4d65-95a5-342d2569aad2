<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8" />
        <title>权益兑换</title>
        <meta
            name="viewport"
            content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"
        />
        <link
            rel="stylesheet"
            href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/bootstrap.min.css"
        />
        <link rel="stylesheet" href="" />
        <link
            rel="stylesheet"
            href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/icbc.css"
        />
        <link
            rel="stylesheet"
            href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/common.css"
        />
        <link
            rel="stylesheet"
            href="https://star6.oss-cn-beijing.aliyuncs.com/public/css/cu_common.css"
        />
        <link
            rel="stylesheet"
            href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/style.css"
        />
        <link
            rel="stylesheet"
            type="text/css"
            href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/tm/index.css"
        />

        <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/jquery-3.1.0.min.js"></script>
        <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/bootstrap.min.js"></script>
        <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/swiper.min.js"></script>
        <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/popper.min.js"></script>
        <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/common.js"></script>
        <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/vue.min.js"></script>
        <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/jquery.bpopup.js"></script>
        <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/flex.js"></script>

        <style>
            .container_box {
                display: flex;
                flex-direction: column;
                width: 7.5rem;
                margin: 0 auto;
                overflow-y: scroll;
                background: #f2f2f2;
                padding-bottom: 1rem;
            }

            .noClick {
                background: #999;
                pointer-events: none;
            }

            .conBox2 {
                width: 7rem;
                float: left;
                margin-left: 0.25rem;
                border-radius: 0.15rem;
                box-shadow: 0 2px 4px #999;
                background: #fff;
                margin-top: 0.25rem;
                /*padding-bottom: 0.25rem;*/
                margin-bottom: 0.25rem;
            }

            .imgBox {
                /*width: 7rem;*/
                width: 100%;
            }

            .headImg {
                margin: 0;
                width: 100%;
                height: 100%;
                padding: 0.15rem;
            }

            .innerContent {
                /*margin: .25rem;*/
            }

            .innerContent p br {
                display: none;
                visibility: hidden;
            }

            .goods_flex {
                margin: 0.1rem 0.15rem;
                display: flex;
                flex-wrap: wrap;
                justify-content: space-between;
                /*margin: .2rem .1rem;*/
            }

            .goods_card {
                padding: 0.1rem;
                width: 49%;
                height: 4rem;
                background: #fff;
                border-radius: 0.2rem;
                margin-bottom: 0.1rem;
            }

            .search_box {
                display: flex;
                justify-content: center;
            }

            .search_button {
                font-size: 0.4rem;
                color: #e60033;
            }

            .goods_name {
                font-size: 0.3rem;
                text-align: center;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 1; /* 设置显示的行数 */
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .gray-image {
                filter: grayscale(100%); /* 将图片置为灰色 */
                /*pointer-events: none; !* 禁用点击事件 *!*/
            }

            [v-cloak] {
                display: none;
            }

            /* 添加弹窗样式 */
            .custom-popup {
                display: none;
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                padding: 0.3rem;
                border-radius: 0.15rem;
                box-shadow: 0 0.02rem 0.1rem rgba(0, 0, 0, 0.2);
                z-index: 1000;
                width: 6rem;
            }

            .popup-buttons {
                display: flex;
                justify-content: space-between;
                margin-top: 0.3rem;
            }

            .popup-button {
                padding: 0.2rem 0.3rem;
                border-radius: 0.08rem;
                border: none;
                cursor: pointer;
                width: 2.5rem;
                font-size: 0.28rem;
            }

            .popup-content p {
                text-align: center;
                font-size: 0.3rem;
                margin-bottom: 0.3rem;
                color: #333;
            }

            .view-order-btn {
                background: rgba(230, 0, 51, 1);
                color: white;
            }

            .close-btn {
                background: #999;
                color: white;
            }

            .popup-mask {
                display: none;
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.6);
                z-index: 999;
            }
        </style>
    </head>

    <body>
        <div class="container_box" id="app_index" v-cloak>
            <div class="imgBox">
                <div
                    onclick="viewOrder()"
                    style="
                       width: 1.5rem;
                        height: 0.8rem;
                        position: fixed;
                        text-align: center;
                        line-height: 0.8rem;
                        right: 0px;
                        bottom: 5rem;
                        z-index: 9999;
                        background-color: rgba(230, 0, 51, 1);
                        color: white;
                        border-top-left-radius: 0.5rem;
                        border-bottom-left-radius: 0.5rem;
                        font-size: 0.25rem;
                        box-shadow: rgba(0, 0, 0, 0.2) -0.02rem 0.02rem 0.1rem;
                    "
                >
                    查看订单
                </div>
                <img class="headImg" src="" alt="" />
            </div>
            <div class="goods_flex">
                <div
                    class="goods_card"
                    v-for="(item,index) in items"
                    :key="index"
                >
                    <div v-if="ret_code==3025">
                        <img
                            :src="item.goods_show_img"
                            style="width: 100%"
                            class="gray-image"
                            @click="goodsOutChange()"
                        />
                        <div class="goods_name">{{ item.goods_name }}</div>
                    </div>
                    <div v-else>
                        <div v-if="!item.available">
                            <img
                                :src="item.goods_show_img"
                                style="width: 100%"
                                class="gray-image"
                                @click="goodsChanged(item.goods_id, true)"
                            />
                            <div class="goods_name">{{ item.goods_name }}</div>
                        </div>
                        <div v-else>
                            <img
                                :src="item.goods_show_img"
                                style="width: 100%"
                                @click="goodsDetails(item.goods_id)"
                            />
                            <div class="goods_name">{{item.goods_name}}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="innerContent"></div>
            <!--    <div class="conBox2">-->
            <!--        <div class="innerContent"></div>-->
            <!--    </div>-->
<!--            <div class="search_box">-->
<!--                <div class="search_button" onclick="searchOrders()">-->
<!--                    查询订单-->
<!--                </div>-->
<!--            </div>-->
        </div>

        <!-- 添加弹窗HTML结构 -->
        <div class="popup-mask" id="popupMask"></div>
        <div class="custom-popup" id="exchangedPopup">
            <div class="popup-content">
                <p style="text-align: center; margin-bottom: 20px">
                    该商品已兑换
                </p>
                <div class="popup-buttons">
                    <button
                        class="popup-button close-btn"
                        onclick="closePopup()"
                    >
                        关闭
                    </button>
                    <button
                        class="popup-button view-order-btn"
                        onclick="viewOrder()"
                    >
                        查看订单
                    </button>
                </div>
            </div>
        </div>

        <img
            class="topImg displayNo"
            src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/tm/top.png"
            alt=""
        />
        <!--&lt;!&ndash; toast &ndash;&gt;-->
        <div id="m-toast-pop" style="z-index: 9999" class="m-toast-pop">
            <div class="m-toast-inner">
                <div class="m-toast-inner-text" id="m-toast-inner-text"></div>
            </div>
        </div>

        <script>
            (function () {
                var tmIndex = {
                    init: function () {
                        this.getCard();
                        this.startInit();
                        this.bind();
                    },
                    startInit: function () {
                        let gift_card_pwd =
                            sessionStorage.getItem("gift_card_pwd");
                        // 拿到用户提交的兑换码后进行登录
                        // 返回该兑换码下的相关信息 1.首页图 2.商品信息 3.goods_id 4.type (用于页面展示)
                        $.ajax({
                            type: "POST",
                            url: "/api/login",
                            data: {
                                card_pwd: gift_card_pwd,
                                act: "v0rjXyz8/LjTx6NZ1Df8Tg==", //百联权益商城预付卡组合商品
                            },
                            async: true,
                            dataType: "json",
                            xhrFields: {
                                withCredentials: true,
                            },
                            success: function (res, textStatus, xhr) {
                                if (res.code == "200") {
                                    // exchange_state: 1-未兑换，2-兑换中（还有礼品未兑换完），3-兑换完毕。
                                    // 已兑换的情况 直接跳到订单列表
                                    if (res.data.exchange_state == "3") {
                                        // toastinfo('该兑换码已兑换');
                                        // setTimeout(() => {
                                        //     jump('../myorder.html?_r=1128')
                                        // }, 2000);
                                        document.getElementById(
                                            "popupMask"
                                        ).style.display = "block";
                                        document.getElementById(
                                            "exchangedPopup"
                                        ).style.display = "block";
                                    }
                                } else if (res.code == "3025") {
                                    //  3025 - 兑换码已过期，会同时返回单个商品的信息
                                    toastinfo(res.msg);
                                } else if (res.code == "9995") {
                                    toastinfo(res.msg);
                                } else {
                                    // 最后跳到 兑换链接不可用界面
                                    jump("./invalid.html?_r=1128");
                                    // $('.codeImg').click();
                                    // toastinfo(res.msg);
                                }

                                // 首页图
                                $(".headImg").attr("src", res.data.show_img);
                                // 兑换规则图
                                $(".innerContent").html(res.data.instr);
                                var ret_code;
                                var localdata;
                                if (res.data?.goods.length > 0) {
                                    localdata = res.data?.goods;
                                } else {
                                    localdata = [...res.data];
                                }
                                ret_code = res.code;
                                // 展示该兑换码下面的商品
                                var vm = new Vue({
                                    el: "#app_index",
                                    data: {
                                        items: localdata,
                                        ret_code: ret_code,
                                    },
                                    methods: {
                                        goodsDetails: function (goods_id) {
                                            jump(
                                                "./charge.html?id=" + goods_id
                                            );
                                        },
                                        goodsChanged: function (
                                            goods_id,
                                            exchanged
                                        ) {
                                            // toastinfo("您已经兑换该商品!");
                                            jump(
                                                "./charge.html?id=" +
                                                    goods_id +
                                                    "&exchanged=" +
                                                    exchanged
                                            );
                                        },
                                        goodsOutChange: function () {
                                            toastinfo("您的兑换码已过期!");
                                        },
                                    },
                                });
                            },
                            //有以下三个参数：XMLHttpRequest 对象、错误信息、（可选）捕获的异常对象。
                            //如果发生了错误，错误信息（第二个参数）除了得到 null 之外，还可能是 "timeout", "error", "notmodified" 和 "parsererror"。
                            error: function (xhr, textStatus, errorThrown) {
                                if (xhr.status == 429) {
                                    $(".codeImg").click();
                                    toastinfo("操作太频繁了，请稍后再试！");
                                } else {
                                    toastinfo("网络错误，请稍后再试！");
                                }
                            },
                        });
                    },
                    bind: function () {
                        //监听页面滚动
                        $(document).scroll(function () {
                            let scroH = $(document).scrollTop();
                            // var viewH = $(window).height();
                            // var contentH = $(document).height();
                            // 控制返回首页图片 大于300的时候显示图片,小于300的时候隐藏图片
                            if (scroH > 300) {
                                $(".topImg").removeClass("displayNo");
                            }
                            if (scroH < 300) {
                                $(".topImg").addClass("displayNo");
                            }
                        });
                        //点击回到顶部按钮 加了个动画
                        $(".topImg").on("click", function () {
                            $("html,body").animate(
                                {
                                    scrollTop: 0,
                                },
                                500
                            );
                        });
                        //点击弹窗的关闭按钮
                        $(".closeImg").on("click", function () {
                            $(".popup").addClass("displayNo");
                        });
                        //点击修改按钮弹窗关闭
                        $(".reset").on("click", function () {
                            $(".popup").addClass("displayNo");
                        });
                    },
                    // 从url中获取兑换码
                    getCard: function () {
                        let card = GetQueryString("s");
                        if (card) {
                            card = card.split(/[.,，。]/)[0];
                            if (card) {
                                // $('.card_pwd').val(card);
                                // 存到session
                                sessionStorage.setItem("gift_card_pwd", card);
                            }
                        }
                    },
                };
                tmIndex.init();
            })();

            function searchOrders() {
                jump("../myorder.html?_r=1128");
            }

            // 添加弹窗相关函数
            function viewOrder() {
                jump("../myorder.html?_r=1128");
            }

            function closePopup() {
                document.getElementById("popupMask").style.display = "none";
                document.getElementById("exchangedPopup").style.display =
                    "none";
            }
        </script>
    </body>
</html>
