<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>兑换成功</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/bootstrap.min.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/icbc.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/common.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/style.css">
    <link rel="stylesheet" type="text/css" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/tm/index.css">


    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/jquery-3.1.0.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/bootstrap.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/common.js"></script>
</head>
<body>
<div class="container">
    <div class="sucbox">
        <img class="star" src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/sucicon.png">
        <p class="s-txt">充值成功</p>
        <p class="s-wait">
            充值一般30分钟内到账，请注意查看
        </p>

    </div>
    <div class="backBtn">返回</div>
    <div class="seeOrderBtn">查询订单</div>


</div>
<script>
    //点击返回按钮
    $('.backBtn').on('click', function () {
        window.history.back()
    })
    //点击查询订单按钮
    $('.seeOrderBtn').on('click', function () {

        $('.popup').addClass('displayno');
        jump('../myorder.html?_r=1128');
    })


</script>
</body>
</html>
