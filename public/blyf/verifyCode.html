<!DOCTYPE html>
<html>
<head>
    <title>权益兑换</title>
    <meta charset="utf-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">

    <!--    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/common.css">-->


    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/flex.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/jquery-3.1.0.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/common.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/popper.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/jquery.bpopup.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/common.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/vue.min.js"></script>
    <style>
        #verificationCodeContainer {
            display: flex;
            justify-content: center;
        }

        .verificationCodeSpan {
            width: .8rem;
            height: .8rem;
            text-align: center;
            font-size: .3rem;
            border: 1px solid #ccc;
            margin-right: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .send_code {
            margin-top: 1rem;
            text-align: center;
            font-size: .3rem;
            margin-bottom: 1rem;
            color: #B3B3B3;
        }

        .retry {
            color: #0000FF;
        }

        .m-toast-pop {
            display: none;
            position: fixed;
            width: 100%;
            top: 0;
            bottom: 0;
            right: 0;
            overflow: auto;
            text-align: center;
        }

        .m-toast-inner {
            position: absolute;
            left: 50%;
            top: 70%;
            width: 100%;
            transform: translate(-50%, -50%);
            -webkit-transform: translate(-50%, -50%);
            text-align: center;
        }

        .m-toast-inner-text {
            display: inline-block;
            margin: 0 22px;
            padding: 19px 21px;
            font-size: 16px;
            color: #FFFFFF;
            letter-spacing: 0;
            line-height: 20px;
            background: rgba(0, 0, 0, 0.72);
            border-radius: 10px;
        }

        .wss {
            /*border: 1px solid red;*/
            box-shadow: 1px 1px 15px red;
        }

        .noClick {
            background: #999;
            pointer-events: none;
        }
    </style>
</head>
<body>
<div class="send_code">验证码将发送到 <span id="send_tel"></span></div>

<div id="verificationCodeContainer">
    <div class="verificationCodeSpan"></div>
    <div class="verificationCodeSpan"></div>
    <div class="verificationCodeSpan"></div>
    <div class="verificationCodeSpan"></div>
</div>
<input
    type="number"
    id="verificationCode"
    name="code"
    value=""
    maxlength="4"
    style="opacity: 0; pointer-events: none"
/>

<div class="send_code" style="margin-top: 0;">验证码已发送 <span class="codeBtn_1"></span><br/>可能会有延后请耐心等待…
</div>

<!-- toast -->
<div id="m-toast-pop" style="z-index:9999;" class="m-toast-pop">
    <div class="m-toast-inner">
        <div class="m-toast-inner-text" id="m-toast-inner-text"></div>
    </div>
</div>

<script>
    function isOneNumber(num) {
        var reg = /^[0-9]$/;
        return reg.test(num);
    }

    (function () {
        let taobaoId = sessionStorage.getItem('charge_account')
        let goods_id = sessionStorage.getItem('goods_id')
        let send_phone = taobaoId.substring(0, 3) + '******' + taobaoId.substring(taobaoId.length - 4);
        $('#send_tel').html(send_phone)

        let codeInput = $("#verificationCode");
        let codeSpans = $(".verificationCodeSpan");
        codeInput.focus(); // 试试自动填充
        codeInput.on("input", function () {
            let code = codeInput.val();
            for (let i = 0; i < codeSpans.length; i++) {
                if (i < code.length) {
                    $(codeSpans[i]).text(code[i]);
                    $(codeSpans[i]).siblings().removeClass('wss');
                    $(codeSpans[i]).addClass('wss');
                } else {
                    $(codeSpans[i]).text("");
                }
            }
            let verify_code = $('input[name="code"]').val()
            if (verify_code.length === 4) {
                gdyhintroduce.getAccountList(verify_code)
            }
        });

        codeSpans.on("click", function () {
            codeInput.focus();
        });

        codeInput.on("keydown", function (e) {
            if (e.keyCode === 8 || e.keyCode === 46) {
                let index = codeInput.val().length - 1;
                if (index >= 0) {
                    $(codeSpans[index]).text("");
                }
            } else if (
                (e.keyCode >= 48 && e.keyCode <= 57) ||
                (e.keyCode >= 96 && e.keyCode <= 105)
            ) {
                var index = codeInput.val().length;
                if (index < codeSpans.length) {
                    $(codeSpans[index]).text(e.key);
                }
            }
        });
        // 重新发送
        $('.codeBtn_1').on('click', function () {
            if ($(".codeBtn_1").hasClass("noClick")) {
                return;
            }
            gdyhintroduce.getVerifyCode();
        });

        var gdyhintroduce = {
            init: function () {
                this.bind();
            },
            bind: function () {
                gdyhintroduce.countDown(180)
            },
            countDown: function (timeNum) {
                if ($(".codeBtn_1").hasClass('retry')) {
                    $(".codeBtn_1").removeClass('retry')
                }
                if ($(".codeBtn_1").hasClass("noClick")) {
                    return;
                }

                $('.codeBtn_1').addClass('noClick');
                var time;
                var num = timeNum;
                time = setInterval(function () {
                    num = num - 1;
                    if (num <= 0) {
                        num = timeNum;
                        $(".codeBtn_1").html('(重新发送)');
                        $(".codeBtn_1").addClass('retry');
                        $('.codeBtn_1').removeClass('noClick');
                        clearInterval(time);
                        return false;
                    } else {
                        $('.codeBtn_1').html('(' + num + ')');
                    }
                }, 1000)
            },
            // 获取淘宝账号列表
            getAccountList: function (verify_code) {
                $.ajax({
                    type: "POST",
                    url: "/api/tb-user-query",
                    data: {
                        "goods_id": goods_id,
                        "mobile": taobaoId,
                        "verify_code": verify_code,
                    },
                    async: true,
                    dataType: "json",
                    xhrFields: {
                        withCredentials: true
                    },
                    success: function (res, status, xhr) {
                        if (res.code == '200') {
                            // 存储账号列表 并跳转到 account页面
                            sessionStorage.setItem('accountList', JSON.stringify(res.data.user_infos));
                            window.location.href = './accounts.html?_v=1'
                        } else {
                            // 验证失败
                            // 清空所有输入框的值，并将焦点切回第一个输入框
                            // $(codeSpans).text("");
                            toastinfo("验证失败");

                        }
                    },
                    error: function (error, xhr, abort) {
                        $('.headConBtn').removeClass('noClick');
                        toastinfo("网络错误，请稍后再试！");
                    }
                })
            },

            // 重新发送验证码
            getVerifyCode: function () {
                $.ajax({
                    type: "POST",
                    url: "/api/tb-verify-code-send",
                    data: {
                        "goods_id": goods_id,
                        "mobile": taobaoId,
                    },
                    async: true,
                    dataType: "json",
                    xhrFields: {
                        withCredentials: true
                    },
                    success: function (res, status, xhr) {
                        if (res.code == '200') {
                            // 存储账号列表 并跳转到 account页面
                            gdyhintroduce.countDown(180)
                        } else {
                            // 重发失败
                            // 清空所有输入框的值，并将焦点切回第一个输入框
                            //$(codeSpans).text("");
                            toastinfo(res.msg);
                        }
                    },
                    error: function (error, xhr, abort) {
                        $('.headConBtn').removeClass('noClick');
                        toastinfo("网络错误，请稍后再试！");
                    }
                })
            },


        }
        gdyhintroduce.init();
    })()
</script>
</body>
</html>
