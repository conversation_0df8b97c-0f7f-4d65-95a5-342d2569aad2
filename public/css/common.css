
.clearfix {
    *zoom: 1;
}
.over-flow{
    overflow: hidden;
}
.clearfix:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: ".";
    clear: both;
    height: 0;
}

.center-block {
    display: block;
    margin: 0 auto;
}

.pull-right {
    float: right !important;
}

.pull-left {
    float: left !important;
}

.hide {
    display: none !important;
}
.hides{
    display: none;
}

.show {
    display: block !important;
}

.invisible {
    visibility: hidden;
}

.text-hide {
    font: 0/0 a;
    color: transparent;
    text-shadow: none;
    background-color: transparent;
    border: 0;
}

.hidden {
    display: none !important;
}

.affix {
    position: fixed;
}

h1,
h2,
h3,
h4,
h5,
h6,
p,
menu,
dl,
dd {
    margin: 0;
}

a {
    color: inherit;
    text-decoration: none;
    -webkit-tap-highlight-color: transparent;
}

input,
textarea,
select,
button {
    outline: none;
}

body {
    font-size: .24rem;
}

body,
ul,
ol {
    padding: 0;
    margin: 0;
    list-style: none;
}

img {
    vertical-align: middle;
    border: 0 none;
    display: block;
}


.container {
    width: 7.5rem;
    margin: 0 auto;
}
.fitwidth{
    width: 7.5rem;
}
header {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 100000!important;
    right: 0;
    margin: 0 auto;
    /*background-color: #2b2b2a;*/
    color: #fff;
    z-index: 1;
    width: 7.5rem;
    height: 0.8rem;

}

header a {
    display: block;
    position: absolute
}

header a i {
    display: block;
    width: 7.5rem;
    height: 0.88rem
}

header .title {
    font-size: 0.36rem;
    font-weight: 400;
    text-align: center;
    height: 0.88rem;
    line-height: 0.88rem;
    position: absolute;
    margin: 0 auto;
    width: 7.5rem;
    bottom: 0
}

header .back {
    left: 0;
    bottom: 0
}

header .back i {
    /*float: left;*/
    width: 1rem;

    background: url(../img/arrow-left.png) 0.4rem no-repeat;
    background-size: .2rem .35rem;
    /*background: red;*/
}

header .home {
    height:0.88rem;
    line-height:0.88rem;
    padding: 0 .2rem;
    font-size: .3rem;
    left: .9rem;
    bottom: 0
}

header .menu {

    right: 0;
    bottom: 0;
}

header  .e {
    display: none;
    float: right;
    width: 1rem;
    background: url(../img/icon-order.png) 0.4rem 0.22rem no-repeat;
    background-size: .3rem .4rem;
    /*width:2rem;*/
    /*background:red;*/
}
input{
    font-family: PingFangSC-Regular, sans-serif;
    line-height: normal;
}
.mt10{
    margin-top: 0.1rem;
}

.tel{
    float: right;
    width: 0.4rem;
    margin: 0.18rem 0.1rem 0 0;
}

[v-cloak]{
    display: none;
}
/*toast*/
.m-toast-pop {display: none; position: fixed; width: 100%;top: 0;bottom: 0;right: 0;overflow: auto;text-align: center;}
.m-toast-inner {position: absolute;left:50%;top:70%;width: 100%; transform:translate(-50%,-50%);-webkit-transform:translate(-50%,-50%);text-align: center;}
.m-toast-inner-text{display: inline-block;margin: 0 22px; padding: 19px 21px;font-size: 16px;color: #FFFFFF;letter-spacing: 0;line-height: 20px;background: rgba(0,0,0,0.72);border-radius: 10px;}
html{
    font-size:13.33333333vw;
}
@media (min-width: 576px) {
  html{
    /*max-width: 540px;*/
    font-size:72px;
  }
}

@media (min-width: 768px) {
  html{
    /*max-width: 720px;*/
    font-size:96px;
  }
}

@media (min-width: 992px) {
  html{
    /*max-width: 960px;*/
    font-size:128px;
  }
}

@media (min-width: 1200px) {
  html{
    /*max-width: 1140px;*/
    font-size:152px;
  }
}

body{font-size: 0.14rem;}
.container{
    font-size: 0;
    padding: 0;

}
input{
    background: transparent;
    border: 0 solid;
}
.modal-dialog{
  margin-left: 0;
}
.modal-dialog .modal-content{
  background:none;
}
.modal-dialog .modal-content .modal-body{
  padding: 0;
}

.managerbox{
    width: 5.84rem;
    height: .61rem;
    background: url(../images/managerbg.png) top center no-repeat;
    background-size: 5.84rem .61rem;
    margin-left: .83rem;
}
.managerbox p{
    font-size: .24rem;
    color: #552f2a;
    display: inline;
    width: 5rem;
    margin-left: .2rem;
    line-height: .61rem;
}
.managerbox .btnexit{
    width: .74rem;
    display: inline-block;
    float:right;
    margin-top: .1rem;
    margin-right: .2rem;
}
