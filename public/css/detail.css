
.nav{
	width: 100%;
	/*position: fixed;*/
    /*top: 0;*/
    z-index: 2;
    height: 0.9rem;
	padding: 0 .6rem;
	border-bottom: .01rem solid #D8D8D8;
}
.container {
	/*height: 100vh;*/
	margin-top: 0!important;
	/*padding-top: .8rem;*/
}
.nav li{
	width: 100%;
	line-height: 0.9rem;
	text-align: center;
	background: #fff;
}
.nav li p{
	/* width: 23.333333%; */
	/* float: left; */
	/* margin-left: 7.5%; */
	height: 0.86rem;
	font-size: 0.3rem;
}
.on{
	border-bottom: .04rem solid #E0262B;
	color: #E0262B;
}

.darkline{
	width: 7.5rem;
	height: .2rem;
	background-color: #eeeeee;
}
.tab{
	width: 7.5rem;
}
.tab1, .tab2{
	width: 7.1rem;
	padding: .3rem .2rem .2rem .2rem;
    font-size: .26rem;
}
.tab0 .title{
	margin-left: .28rem;
	font-size: .3rem;
	margin-top: .4rem;
}
.tab0 .subtitle{
	margin-left: .28rem;
	font-size: .26rem;
	margin-top: .1rem;
	padding-bottom: .2rem;
	color: darkgray;
}
.tab0 .content{
	width: 7.3rem;
    margin-left: .1rem;
    /*border-radius: .3rem;*/
    margin-bottom: 1.4rem;
    padding: .3rem .2rem .2rem .2rem;
    font-size: .26rem;
    border-top: 1px solid #E0E0E0;
}
.tab0 .goodlabel{
	color: black;
    margin-top: .3rem;
    margin-left: .28rem;
    font-size: .32rem;
    display: inline-block;
    margin-bottom: .2rem;
}

.recharge{
	position: fixed;
	/* left: 0; */
	bottom: 0rem;
	width: 7.5rem;
	height: 1rem;
	background-color: #E03147;
}


/*轮播图*/
.swiper-container{
	width: 7.5rem;
	/*height: 4.5rem; */
	/* margin-top: .2rem; */
}
.swiper-slide{
	width: 7.5rem;
	/*height: 4.5rem;*/
}
.swiper-slide img{
	/*width: 6.33rem;*/
	/*height: 4.5rem;*/
	/*margin-left: .585rem;*/
	/* width: 7rem; */
	/* margin-left: .25rem; */
	/*object-fit: contain;*/
	width: 100%;
}
.swiper-pagination-bullet {
    background: #979797;
}
.swiper-pagination-bullet-active {
    background-color: #E0262B;
}

