
.nav {
    width: 100%;
    /*position: fixed;*/
    /*top: 0;*/
    z-index: 2;
    height: 0.9rem;
    padding: 0 .6rem;
    border-bottom: .01rem solid #D8D8D8;
}

.container {
    /*height: 100vh;*/
    margin-top: 0 !important;
    /*padding-top: .8rem;*/
}

.nav li {
    width: 100%;
    line-height: 0.9rem;
    text-align: center;
    background: #fff;
}

.nav li p {
    /* width: 23.333333%; */
    /* float: left; */
    /* margin-left: 7.5%; */
    height: 0.86rem;
    font-size: 0.3rem;
}

.on {
    border-bottom: .04rem solid #E0262B;
    color: #E0262B;
}

.darkline {
    width: 7.5rem;
    height: .2rem;
    background-color: #eeeeee;
}

.tab {
    width: 7.5rem;
}

.tab1, .tab2 {
    width: 7.1rem;
    padding: .3rem .2rem .2rem .2rem;
    font-size: .26rem;
}

.tab0 .title {
    margin-left: .28rem;
    font-size: .3rem;
    margin-top: .4rem;
}

.tab0 .subtitle {
    margin-left: .28rem;
    font-size: .26rem;
    margin-top: .1rem;
    padding-bottom: .2rem;
    color: darkgray;
}

.tab0 .content {
    width: 7.3rem;
    margin-left: .1rem;
    /*border-radius: .3rem;*/
    margin-bottom: 1.4rem;
    padding: .3rem .2rem .2rem .2rem;
    font-size: .26rem;
    border-top: 1px solid #E0E0E0;
}

.tab0 .goodlabel {
    color: black;
    margin-top: .3rem;
    margin-left: .28rem;
    font-size: .32rem;
    display: inline-block;
    margin-bottom: .2rem;
}

.recharge {
    position: fixed;
    /* left: 0; */
    bottom: 0rem;
    width: 7.5rem;
    height: 1rem;
    background-color: #E03147;
}


/*轮播图*/
.swiper-container {
    width: 7.5rem;
    /*height: 4.5rem; */
    /* margin-top: .2rem; */
}

.swiper-slide {
    width: 7.5rem;
    /*height: 4.5rem;*/
}

.swiper-slide img {
    /*width: 6.33rem;*/
    /*height: 4.5rem;*/
    /*margin-left: .585rem;*/
    /* width: 7rem; */
    /* margin-left: .25rem; */
    /*object-fit: contain;*/
    width: 100%;
}

.swiper-pagination-bullet {
    background: #979797;
}

.swiper-pagination-bullet-active {
    background-color: #E0262B;
}

/*弹窗部分*/


.noClick {
    background: #999;
}

.popup {
    width: 100%;
    height: 100%;
    position: fixed;
    left: 0;
    top: 0;
    background: rgba(0, 0, 0, 0.6);
    z-index: 99;

}

.displayno {
    display: none;
}

.displayno1 {
    display: none;
}


.popupCon-xy {
    width: 7rem;
    height: 6.26rem;
    padding: 0.3rem;
    border-radius: 0.2rem;
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}
.popupCon-xy1 {
    width: 7rem;
    height: 5rem;
    padding: 0.3rem;
    border-radius: 0.2rem;
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}

.nr-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    /*z-index: 1;*/
}

.headConUl {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    /*z-index: 2;*/
    padding-top: 0.8rem;
}

.covertCode, covertCode1 {
    width: 5.6rem;
    line-height: 1rem;
    font-size: 0.28rem;
    border-bottom: none;
    color: #BCBCBC;
    background: #fff;
    margin: 0 auto;
    border-radius: 0.1rem;
    padding: 0 0.2rem;
}

/*.covertCode1 {*/
/*    width: 5.6rem;*/
/*    line-height: 1rem;*/
/*    font-size: 0.28rem;*/
/*    border-bottom: none;*/
/*    color: #BCBCBC;*/
/*    background: #fff;*/
/*    margin: 0 auto;*/
/*    border-radius: 0.1rem;*/
/*    padding: 0 0.2rem;*/
/*}*/

input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
    font-size: 0.28rem;
    color: #BCBCBC;
}

input:-moz-placeholder,
textarea:-moz-placeholder {
    font-size: 0.28rem;
    color: #BCBCBC;

}

input::-moz-placeholder,
textarea::-moz-placeholder {
    font-size: 0.28rem;
    color: #BCBCBC;

}

input:-ms-input-placeholder,
textarea:-ms-input-placeholder {
    font-size: 0.28rem;
    color: #BCBCBC;

}

.div_input1 {
    position: relative;
    left: 1.3rem;
    top: 1rem;
    z-index: 2;
}

.div_input2 {
    position: relative;
    left: 1.3rem;
    top: 1.28rem;
    z-index: 2;
}

/*确认充值号正确与否*/
.confirmInfo {
    position: relative;
    height: 1.5rem;
    /*border:1px solid red;*/
    top: 1.5rem;
}

/*确认QQ号正确与否*/
.confirmInfo1 {
    position: relative;
    height: 1.5rem;
    /*border:1px solid red;*/
    top: 1.5rem;
}

/* 确认兑换框*/
.confirmSubmit {
    position: relative;
    height: 1.3rem;
    /*border: 1px solid red;*/
    top: 2.4rem;
}

/* 确认兑换框1*/
.confirmSubmit1 {
    position: relative;
    height: 1.3rem;
    /*border: 1px solid red;*/
    top: 2.4rem;
}

.mtk_goods_name {
    position: relative;
    top: 1.06rem;
    font-size: 0.28rem;
    font-weight: bold;
    color: #F4B865;
    left: 1.4rem;
}

.mtk_charge_number {
    position: relative;
    top: 1.25rem;
    font-size: 0.28rem;
    color: #F4B865;
    left: 1.4rem;
}

.mtk_goods_name1 {
    position: relative;
    top: 1.06rem;
    font-size: 0.28rem;
    color: #F4B865;
    left: 1.4rem;
}

.mtk_charge_number1 {
    position: relative;
    top: 1.25rem;
    font-size: 0.28rem;
    color: #F4B865;
    left: 1.4rem;
}

.dhsb img {
    width: 7rem;
    height: 6.08rem;
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}

.dhsb .searchLog {
    position: relative;
    height: 1rem;
    /*border:1px solid red;*/
    top: 9.3rem;
}

.close_btn {
    position: relative;
    height: 1rem;
    top: -2.2rem;
    width: 1rem;
    right: -5.8rem;
    /*border:1px solid red;*/
}

.close_btn_qr {
    position: relative;
    height: 1rem;
    top: -2.5rem;
    width: 0.8rem;
    right: -6rem;
    /*border:1px solid red;*/
}


