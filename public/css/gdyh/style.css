body{
    box-sizing: border-box;
}
input::-moz-placeholder {
    font-size: 0.3rem;
    color: #BCBCBC;
    /*opacity: 0.7;*/
}

input:-ms-input-placeholder {
    font-size: 0.3rem;
    color: #BCBCBC;
    /*opacity: 0.7;*/
}

input::-webkit-input-placeholder {
    font-size: 0.3rem;
    color: #BCBCBC;
    /*opacity: 0.7;*/
}

input[type="text"],
input[type="password"],
input[type="search"],
input[type="email"],
input[type="tel"],
input[type="url"],
input[type="date"],
input[type="datetime-local"],
input[type="time"],
input[type="number"],
select,
textarea {
    border: none;
    background-color: transparent;
    border-radius: 0;
    box-shadow: none;
    display: block;
    padding: 0;
    margin: 0;
    width: 100%;
    height: .88rem;
    line-height: normal;
    color: #424242;
    font-family: inherit;
    box-sizing: border-box;
    -webkit-user-select: text;
    user-select: text;
    -webkit-appearance: none;
    appearance: none;
}
input[type="search"]::-webkit-search-cancel-button {
    display: none;
}
