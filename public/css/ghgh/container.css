/*body{*/
/*	background: #DDC097;*/
/*	width:7.5rem;*/
/*	!*height:15.16rem;*!*/
/*	!*margin:0 auto;*!*/
/*}*/
* {
    margin: 0;
    padding: 0;
}

body {
    box-sizing: border-box;
}

.container {
    width: 7.5rem;
    height: 15.16rem;
    /*height: 100vh;*/
    background-size: 7.5rem 15.16rem;
    position: relative;
    margin: 0 auto;
    /*border: 1px solid red;*/
    /*left:50%;*/
    /*transform: translateX(-50%);*/
}

.wrapper {
    width: 7.5rem;
    height: 10.3rem;
    /*border: 1px solid red;*/
    background-size: 7.5rem 10.3rem;
    /*overflow: hidden;*/
    margin: 0 auto;
    position: relative;
    top: 4.24rem;
    /*position: absolute;*/
    /*top: 4.24rem;*/
}

.wrapper img {
    display: block;
    margin: 0 auto;
    width: 94%;
}

.wrapper_input {
    width: 4rem;
    position: relative;
    left: 1.8rem;
    top: -3.64rem;
    margin-bottom: 0.29rem;
}

.wrapper_input_content {
    display: block;
    height: .6rem;
    outline: none;
    border: none;
    /*color: #BCBCBC;*/
    font-size: .3rem;
}

.codeBtn {
    position: relative;
    top: -.72rem;
    right: -2.8rem;
    width: 2rem;
    height: .5rem;
    background: #F4B865;
    font-size: .3rem;
    border-radius: .5rem;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
}

.noClick {
    background: #999;
}


.wrapper .login {
    position: relative;
    height: .8rem;
    /*width: 70%;*/
    margin: 0 auto;
    /*left: .22rem;*/
    top: -4rem;
    opacity: 0;
}

.wrapper .dhsm {
    position: relative;
    top: -3.4rem;
    /*position: absolute;*/
    /*top: 5.6rem;*/
    /*width: 94%;*/
    /*left: .22rem;*/
}

.swiper-container {
    width: 7.5rem;
    position:absolute;
    top:0;
}

.swiper-slide {
    width: 7.5rem;
}

.swiper-slide img {
    width: 100%;
}

/*.swiper-pagination-bullet {*/
/*    background: #979797;*/
/*}*/
/*.swiper-pagination-bullet-active {*/
/*    background-color: #E0262B;*/
/*}*/
