body {
    background-color: #f5f5f5;
    height: auto;
}
.contbox {
    padding-bottom: 1rem;
}
.listbox {
    padding-left: .2rem;
    padding-right: .2rem;
    margin-bottom: .3rem;
}
.container{
    margin-top:0!important;
}
.list {
    width: 50%;
    padding: .08rem;
}
.listinner {
    background-color: #fff;
    overflow: hidden;
    border-radius: .3rem;
}
.imgbox {
    width: 100%;
    overflow: hidden;
}
.goodsimg {
    width: 100%;
}
.botbtnbox {
    /*position: fixed;*/
    bottom: .8rem;
    width: 7.5rem;
    margin-top: .5rem;
}
.botbtn {
    background: -webkit-linear-gradient(top, #E74957 , #DC2833); /* Safari 5.1 - 6.0 */
    background: -o-linear-gradient(bottom, #E74957, #DC2833); /* Opera 11.1 - 12.0 */
    background: -moz-linear-gradient(bottom, #E74957, #DC2833); /* Firefox 3.6 - 15 */
    background: linear-gradient(to bottom, #E74957 , #DC2833); /* 标准的语法 */
    width: 72%;
    height: .9rem;
    border-radius: .45rem;
}
