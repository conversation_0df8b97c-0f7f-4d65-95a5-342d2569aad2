body{
    box-sizing: border-box;
}

.click{
    z-index: 9999;
}

.root {
    width: 7.5rem;
    height: 100%;
    position: relative;
    margin: 0 auto;
    background-size: 100% 100%;
    min-height: 19.58rem;
}

/* 顶部 */
header {
    width: 100%;
    height: 0.8rem;
    background: #fff;
    position: fixed;
    top: 0;
    left: 0;
}

.back {
    width: 0.2rem;
    float: left;
    margin: 0.2rem 0 0 0.2rem;
}

header p {
    left: 2rem;
    font-size: 0.35rem;
    margin-top: 0.15rem;
    right: 2rem;
    width: 3.5rem;
    color: black;
    text-align: center;
    position: absolute;
    color: black;
}

.share {
    float: right;
    width: 0.4rem;
    margin: 0.18rem 0.1rem 0 0;
}

input::-moz-placeholder {
    font-size: 0.24rem;
    color: #999999;
    opacity: 0.7;
}

input:-ms-input-placeholder {
    font-size: 0.24rem;
    color: #999999;
    opacity: 0.7;
}

input::-webkit-input-placeholder {
    font-size: 0.24rem;
    color: #999999;
    opacity: 0.7;
}

footer{
    position: fixed;
    bottom: 0;left: 0;
    background: #000;
    width: 7.5rem;
    height: 1.5rem;
}
footer img{
    float: left;
    width: 1.15rem;
    margin: 0.2rem 0 0 0.6rem;
}
footer p{
    float: left;
    color: #fff;
    font-size: 0.30rem;
    margin-left: 0.25rem;
    margin-top: 0.25rem;
}
footer button{
    float: left;
    width: 2rem;
    height: 1rem;
    background: #f00;
    color: #fff;
    font-size: 0.32rem;
    border-radius: 0.05rem;
    margin: 0.3rem 0 0 0.5rem;
    border: none;
}