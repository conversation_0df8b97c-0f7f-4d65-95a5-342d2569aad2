

.noClick {
	background: #999;
}

.popup {
	width: 100%;
	height: 100%;
	position: fixed;
	left: 0;
	top: 0;
	background: rgba(0, 0, 0, 0.6);
	z-index: 99;

}

.displayno {
	display: none;
}

.line {
	width: 100%;
	height: 0.1rem;
	border-bottom: 1px solid #999;
	margin-top: 0.3rem;
	float: left;
}


.popupCon-xy {
	width: 7rem;
	height: 6.6rem;
	padding: 0.3rem;
	border-radius: 0.2rem;
	position: fixed;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
}

.nr-bg {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 1;
}

.headConUl {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 2;
	padding-top: 0.8rem;
}

.headConUl .covertCode {
	width: 5.6rem;
	line-height: 1rem;
	font-size: 0.28rem;
	border-bottom: none;
	color: #333;
	background: #fff;
	margin: 0 auto;
	border-radius: 0.1rem;
	padding: 0 0.2rem;
}

input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
	font-size: 0.28rem;
	color: #F2D5D5;
}

input:-moz-placeholder,
textarea:-moz-placeholder {
	font-size: 0.28rem;
	color: #F2D5D5;

}

input::-moz-placeholder,
textarea::-moz-placeholder {
	font-size: 0.28rem;
	color: #F2D5D5;

}

input:-ms-input-placeholder,
textarea:-ms-input-placeholder {
	font-size: 0.28rem;
	color: #F2D5D5;

}

.headConBtn {
	width: 5.6rem;
	height: 0.9rem;
	line-height: 0.9rem;
	text-align: center;
	color: #C70224;
	border-radius: 0.5rem;
	margin: 0.4rem auto;
	font-size: 0.40rem;
	font-weight: bold;
	background: linear-gradient(to right, #FEDEE1 0%, #FEFDFD 50%, #FEDEE1 100%);

}

.hr {
	width: 5.6rem;
	height: 1px;
	border-bottom: 1px dashed #F3C68A;
	position: absolute;
	top: 52%;
	left: 50%;
	margin-left: -2.8rem;
}

.popup .title-wrap {
	margin-top: 1rem;
	line-height: 0.36rem;
	text-align: center;
}

.popup .title-wrap .line-hr {
	display: inline-block;
	width: 1.4rem;
	height: 1px;
	background: linear-gradient(to right, #FEDEE1 0%, #FEFDFD 100%);
	margin-bottom: 0.1rem;
}

.title-wrap .txt {
	font-size: 0.36rem;
	color: #fff;
	margin: 0 0.3rem;
}

.sp-cont {
	padding: 0.2rem 0.4rem;
	font-size: 0.28rem;
	color: #fff;
}

.closeBtn-xy {
	width: 0.5rem;
	height: 0.5rem;
	position: absolute;
	right: 0.6rem;
	top: -0.2rem;
	z-index: 9;
}

.popup-qr .headConUl {
	padding: 0.4rem 0.5rem 0;
}

.c-title {
	font-size: 0.34rem;
	color: #fff;
	margin-bottom: 0.2rem;
}

.c-goods {
	font-size: 0.28rem;
	color: #fff;
	margin-bottom: 0.1rem;
}

.popup-qr .headConBtn {
	margin: 0.2rem auto 0.2rem;
}

.popup-qr .title-wrap {
	margin-top: 0.8rem;
}

.popupCon-sp {
	width: 7rem;
	height: 8rem;
	padding: 0.5rem 0;
	border-radius: 0.2rem;
	position: fixed;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
}
.popupCon-sp .headConUl{
	position: absolute;
	top: 0.5rem;
	left: 0.2rem;
	z-index: 2;
	width: 6.6rem;
	height: 7rem;
	padding-top: 0;
	overflow-y: auto;
}
.popupCon-sp .title-wrap{
	margin-top: 0.2rem;
}

.popupCon-sp .sp-cont{
	padding: 0.1rem 0.4rem;
}
.popupCon-sp .sp-cont li{
	font-size: 0.26rem;
	color: #fff;
}

.popupCon-sp  .title-wrap .line-hr {
	display: inline-block;
	width: 1rem;
	height: 1px;
	background: linear-gradient(to right, #FEDEE1 0%, #FEFDFD 100%);
	margin-bottom: 0.1rem;
}

.popupCon-sp .title-wrap .txt {
	font-size: 0.3rem;
	color: #fff;
	margin: 0 0.3rem;
}






