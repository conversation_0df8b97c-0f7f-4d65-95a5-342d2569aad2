.popup{
	width:100%;
	height:100%;
	position:fixed;
	left:0;
	top:0;
	background:rgba(0,0,0,0.6);
	z-index: 99;

}
.displayno{
	display: none;
}
.popupConStyle{
	width:7rem;
	padding:0.3rem ;
	background: #fff;
	border-radius: 0.2rem;
	position: fixed;
	left: 50%;
	top:50%;
	transform:translate(-50%,-50%);

}
.popupTitle{
	width: 100%;
	float: left;
	font-size: 0.3rem;
		text-align: left;
		font-weight: 600;
		margin-top: 0.2rem;

}
.popupText{
	width: 100%;
	float: left;
	font-size: 0.26rem;
	color:#999999;
		text-align: left;
		margin-top: 0rem;
}
.line{
	width: 100%;
	height:0.1rem;
	border-bottom:1px solid #999;
	margin-top: 0.3rem;
	float:left;
}
.tipTitle{
	width: 100%;
	float: left;
	font-size: 0.26rem;
	color:#b62b23;
		text-align: left;
		margin-top: 0.2rem;
}
.tipText{
	width: 100%;
	float: left;
	font-size: 0.26rem;
	color:#b62b23;
		text-align: left;
		margin-top: 0.05rem;
}
.confirmBtn{
		width: 100%;
	height: 0.9rem;
	line-height: 0.9rem;
     text-align: center;
     color: #2575B7;
     background: #fff;
     float: left;
     border-radius: 0.5rem;
     margin-top: 0.4rem;
     font-size: 0.32rem;
     margin-bottom:0.5rem;
     opacity:1!important;

}
.headCon{
	width:6.6rem;
	padding:0.2rem 0.3rem 0.8rem 0.3rem;
	background: #2575b7;
	border-radius: 0.2rem;
	float: left;
	margin-left: 0.45rem;
	margin-top:1rem;
}
.liICon{
	position: absolute;
	left:0.2rem;
	top:0.35rem;
	/* width: 0.4rem; */
}
.headConUl li{
	width:100%;
	height: 1.1rem;
	list-style: none;
	float: left;
	position: relative;
	border-bottom: 1px solid #979797;
}
.popupImg{
	width:2rem;
	height: 2rem;
	float: left;
	margin-left:2.2rem;
	margin-top:0.3rem;
}
.errorMsg,.successMsg{
	width:100%;
	text-align:center;
	font-size:0.28rem;
	font-weight: 600;
	float:left;
	margin-top:0.2rem;

}
.headImg{
	width:2.5rem;
	height: 2.5rem;
	float: left;
	margin-left:2.5rem;
	margin-top:0.8rem;
}
.seeLIst{
		width:3.5rem;
	height:0.8rem;
	line-height: 0.8rem;
	text-align: center;
	font-size: 0.3rem;
	color: #fff;
	margin-left: 2rem;
	margin-top: 0.3rem;
	background: #50d676;
	float: left;
	border-radius: 0.4rem;
}
.headConUl li .covertCode,.headConUl li .phone,.headConUl li .covertCodePopup,.headConUl li .phonePopup{
	width:4.8rem;
line-height: 1rem;
margin-left: 0.9rem;
margin-top: 0.15rem;
float: left;
font-size: 0.28rem;
border-bottom: none;
color: #fff;
}
.headConUl li .phoneCode{
	width:2.2rem;
line-height: 1rem;
margin-left: 0.9rem;
margin-top: 0.15rem;
float: left;
font-size: 0.28rem;
color: #fff;
}
input::-webkit-input-placeholder, textarea::-webkit-input-placeholder {
  font-size: 0.28rem;
  color:#71A5D0;
}

input:-moz-placeholder, textarea:-moz-placeholder {
   font-size: 0.28rem;
  color:#71A5D0;

}

input::-moz-placeholder, textarea::-moz-placeholder {
   font-size: 0.28rem;
  color:#71A5D0;

}

input:-ms-input-placeholder, textarea:-ms-input-placeholder {
   font-size: 0.28rem;
  color:#71A5D0;

} 
.closeBtn{
	width:0.5rem;
	height:0.5rem;
	position: absolute;
	right:0.2rem;
	top:0.2rem;
}