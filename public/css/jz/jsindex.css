body{
    background-color: #eee;
    padding-bottom:0.5rem;
}
.container{
    width: 7.5rem;
    overflow-x: hidden;
}
.headCon{
    width:7rem;
    height: 2rem;
    float: left;
    margin-left: 0.25rem;
    border-radius: 0.4rem;
    background-color: #F3F3F3;
    margin-top: 0.2rem;
    position: relative;
}
.classHeadTitle{
    position: absolute;
    left:0.4rem;
    top:0.45rem;
    font-size: 0.32rem;
    color:#222;

}
.classHeadDes{
    position: absolute;
    left:0.4rem;
    top:1.1rem;
    font-size: 0.26rem;
    color:#959595;
}
.headIcon{
    width: 1.5rem;
    height: 1.5rem;
    position: absolute;
    right: 0.5rem;
    top: 0.25rem;
}
.conList{
    width: 100%;
    padding:0.25rem;
    float: left;

}
.mt40{
    margin-top: 0.4rem;
}
.title{
    font-size: 0.26rem;
    float: left;
    width: 100%;

}

.mr20{
    margin-right: 0.2rem;
}
.list{
   border:1px solid  #B0B0B0;
   color: #222;
   padding:0.15rem 0.2rem;
   float: left;
   font-size: 0.26rem;
   margin-top:0.2rem;
}
.listActive{
    border:1px solid #2793D3!important;
    color:#2793D3!important;
}
.dataCheck{
    float: left;
    margin-top: 0.2rem!important;
    width: 100%;
    height: 0.8rem;
    padding-left: 0.2rem!important;
    border: 1px  solid #C1C1C1!important;
}
.clip{
    font-size: 0.24rem;
    color:red;
    margin-top: 0.1rem;
    float: left;
}
.listTime{
    border:1px solid  #B0B0B0;
    color: #222;
    padding:0.15rem 0.2rem;
    float: left;
    font-size: 0.26rem;
    width:3.2rem;
    text-align: center;
    margin-top:0.2rem;
}
.ml40{
    margin-left:0.6rem;
}

.oneCon{
    width: 100%;
    float: left;
    padding-bottom: 0.2rem;
    background-color: #fff;
    border-bottom-left-radius:0.2rem;
    border-bottom-right-radius:0.2rem;

}
.twoCon{
    width: 100%;
    float: left;
    background-color: #fff;
    border-radius:0.2rem;
    margin-top: 0.05rem;
}
.userDetail{
    width: 100%;
    float: left;
}
.userDetail li{
    width: 100%;
    margin-top: 0.2rem;
    list-style: none;
    float: left;
    height: 0.7rem;
    border:1px  solid #C1C1C1!important;

}
.liTitle{
    width: 1.8rem;
    float: left;
    height: 0.4rem;
    line-height: 0.4rem;
    text-align: center;
    font-size: 0.26rem;
    color: #333333;
    margin-top: 0.15rem;
    border-right: 1px solid #333;
}
.userDes{
    width:4.8rem!important;
    float: left;
    height: 0.7rem!important;
    line-height: 0.6rem!important;
    margin-left: 0.2rem!important;
}
.submitBtn{
    width: 6rem;
    height: 0.9rem;
    line-height: 0.9rem;
    color: #fff;
    background-color: #1398E9;
    float: left;
    border-radius: 0.5rem;
    font-size: 0.26rem;
    text-align: center;
    margin-top: 0.3rem;
    margin-left: 0.5rem;
}
.clipTitle{
    width: 100%;
    float: left;
    margin-top: 0.2rem;
    height: 0.8rem;
    color: #333;
    border-bottom: 1px solid #DDDDDD;
    font-size: 0.32rem;
    text-align: center;
}
.clipDes{
    width: 100%;
    text-align: left;
    float: left;
    margin-top: 0.2rem;
    font-size: 0.26rem;
}
.popup,.timePopup{
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.6);
    position: fixed;
    left:0;
    top:0;
    z-index: 900;
}
.timeCon{
    width: 7rem;
    height: 7rem;
    background: #fff;
    border-radius: 0.2rem;
    position: fixed;
    left:50%;
    top:50%;
    padding: 0.2rem;
    transform: translate(-50%,-50%);
}
.displayno{
    display: none!important;
}
.timeTitle{
    width: 100%;
    line-height: 1rem;
    text-align: center;
    margin-top: 0.1rem;
    color:#FE4747;
    font-size: 0.26rem;
}
.timeClip{
    width: 4.5rem;
    height: 3rem;
    margin-left: 1rem;
    float: left;
    line-height: 0.5rem;
    text-align: left;
    margin-top: 1rem;
    color:#FE4747;
    font-size: 0.26rem;
}
.confirmBtn{
    width: 6rem;
    height: 1rem;
    float: left;
    line-height: 1rem;
    margin-left: 0.25rem;
    margin-top:0.2rem;
    text-align: center;
    border-radius: 0.6rem;
    color: #fff;
    cursor: pointer;
    font-size: 0.26rem;
    background: #F94343;

}

.timeImg{
    width: 6.5rem;
    position: absolute;
    top:1.5rem;
    left:0.25rem;
}
.popupCon{
    width:7rem;
    height: auto;
    background-color: #fff;
    border-radius: 0.2rem;
    position: fixed;
    left:50%;
    top:50%;
    padding: 0.2rem;
    transform: translate(-50%,-50%);
}
.popupTitle{
    width: 100%;
    line-height: 1rem;
    text-align: center;
    font-size: 0.32rem;
}
.popupUl{
    width: 100%;
    float: left;
}
.popupUl li{
    list-style: none;
    float: left;
    width: 100%;
}
.popupLiOne{
    width: 1.5rem;
    float: left;
    font-size: 0.26rem;
    text-align: right;
    line-height: 0.4rem;
    margin-top: 0.1rem;

}
.popupLiTwo{
    width: 4rem;
    float: left;
    line-height: 0.4rem;
    font-size: 0.26rem;
    text-align: left;
    margin-top: 0.1rem;
    margin-left: 0.1rem;
}
.lineBr{
    width: 100%;
    float: left;
    height: 0.5rem;
    position: relative;
    margin-top: 0.3rem;
}
.leftCircle{
    position: absolute;
    left:-0.2rem;
    top:0;
    width:0.1rem;
    height:0.2rem;
    background-color:#5F5F5F;
    border-radius:0 0.1rem 0.1rem 0; /* 左上、右上、右下、左下 */
}
.rightCircle{
    width:0.1rem;
    height:0.2rem;
    position: absolute;
    right:-0.2rem;
    background-color:#5F5F5F;
    border-radius:0.1rem 0 0 0.1rem; /* 左上、右上、右下、左下 */
}
.line{
    width: 100%;
    height: 0.02rem;
    border-bottom: 1px solid #DDDDDD;
    position: absolute;
    bottom: 0.3rem;
}
.popupClip{
    color: red;
    width: 100%;
    float: left;
    font-size: 0.26rem;
    line-height: 0.6rem;
}
.popupconfirmBtn{
    width: 5rem;
    height: 0.8rem;
    background-color: #1398E8;
    color: #fff;
    line-height: 0.8rem;
    font-size: 0.26rem;
    float: left;
    margin-left:0.7rem;
    text-align: center;
    border-radius: 0.5rem;
    margin-top: 0.5rem;
    margin-bottom: 0.3rem;
}
.closePopup{
    position: absolute;
    right: 0.5rem;
    top:0.2rem;
    width:0.5rem;
}
.selectName{
    width:2rem;
    height: 0.7rem;
    float: left;
    margin-left: 0.2rem;
    font-size: 0.26rem;
    /* border: 1px solid #333; */
}
.headImg{
    width: 5rem;
    float: left;
    margin-left:1.25rem;
}
.resultClip{
    color:red;
    width: 6.5rem;
    float: left;
    margin-left: 0.5rem;
    line-height: 0.6rem;
    font-size: 0.26rem;


}
.resultBtn{
    width:2rem;
	height:0.9rem;
	line-height: 0.9rem;
	text-align: center;
	font-size: 0.26rem;
	color: #333;
	margin-left: 2.7rem;
	margin-top: 2rem;
	/* background: #50d676; */
	float: left;
	border-bottom: 1px solid #333;
	/* border-radius: 0.4rem; */
}
