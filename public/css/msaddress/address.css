* {
    padding: 0;
    margin: 0;
}

.main_div {
    /*padding: .3rem;*/
    /*background: #eee;*/
    overflow: scroll;

}

.address_icon {
    margin-right: .2rem;
    display: block;
}

.car_icon {
    width: .3rem;
    display: block;
    margin: auto;
    /*border: 1px solid ;*/
}


.car_name {
    font-size: .35rem;
    padding-top: .4rem;
    padding-bottom: .5rem;
    font-weight: bold;
}

.detail_address {
    color: #999;
    font-size: .3rem;
    flex: 1;
}

.wly {
    width: 1.5rem;
    font-weight: bold;
    font-size: .3rem;

}

.address_list {
    display: flex;
    padding-bottom: .3rem;
    text-align: left;
    line-height: .4rem;
}

.car_address_list {
    border-bottom: 1px solid #999;
    width: 90%;
    margin: 0 auto;
    /*padding: .3rem;*/
    /*background: #fff;*/
}

.car_address_list:last-child {
    border-bottom: none;
}

.footer {
    height: 2rem;
    /*background: #eee;*/
}

.form-group {
    width: 100%;
    height: 1rem;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    position: fixed;
    background: #fff;
}

.gap {
    width: 100%;
    height: .1rem;
    background: #eee;
}

.form-control {
    border: none !important;
    color: #2359FF;
    font-size: .3rem;
    background: #fff;
    text-align: center;
}

select {
    text-align: center;
    /*display: block;*/
    width: 100%;
}

.gpline {
    border-left: 1px solid #ccc;
    height: .5rem;
    margin-left: .11rem;
    /*margin-right: .01rem;*/

}

.header {
    text-align: center;
    width: 33%;
}

.ellipsis {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

/*.search{*/
/*    position: fixed;*/
/*}*/

.top_header {
    padding-top: 1rem;
}

.no_site {
    text-align: center;
    margin-top: 2rem;
    color: #999;
    font-size: .3rem;
}
