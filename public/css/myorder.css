body{
	background-color: #dcdcdc;
}
.container li{
	/*height: 3rem;*/
	background-color: white;
	padding-bottom: .2rem;
	margin-top: .2rem;
}
.order{
	margin-left: .2rem;
    margin-top: .2rem;
    font-size: .28rem;
    display: inline-block;
}
.sepline{
	width: 7.5rem;
	background-color : darkgray;
	height: 1px;
	margin-top: .1rem;
	margin-bottom: .1rem;
}
.lightline{
	width: 7.1rem;
	background-color: lightgray;
	height: 1px;
	margin-top: .2rem;
	margin-left: .2rem;
}
.title{
	font-size: .32rem;
    margin-left: .2rem;
    width: 5rem;
    display: inline-block;
}
.info{
	display: inline-block;
	width: 7.1rem;
	color: darkgray;
	margin-top: .2rem;
	margin-left: .2rem;
	font-size: .26rem;
}
.right{
	float: right;
}

.goodimg{
	width: 1.6rem;
	margin-left: .2rem;
	display: inline-block;
	height: 1.6rem;
    object-fit: contain;
}

.alert{
    display: none;
    width: 5.26rem;
    height: 3.8rem;
    background:  url(../images/alert.png) top center no-repeat;
    background-size: 5.26rem 3.8rem;
}
.alert .title{
    width: 100%;
    text-align: center;
    font-size: .32rem;
    margin-top: 1.3rem;
}
.alert .closebtn{
    display: inline-block;
    margin-top: 1rem;
    margin-left: 1rem;
    width: 3.26rem;
    height: 1rem;
}
.noData{
	width: 100%;
	text-align: center;
	color: #222;
	line-height: 2rem;
    font-size: 0.28rem;
}
.displayno{
	display:none;
}
