.cm-color-red {
    color: #BC020D;
}
.cm-color-lightred {
    color: #FF3B30;
}
.cm-color-white {
    color: #fff;
}
.cm-color-gold {
    color: #FFE1C1;
}
.cm-color-grey {
    color: #999;
}
.cm-tx-c {
	text-align: center;
}
.cm-tx-l {
	text-align: left;
}
.cm-tx-r {
	text-align: right;
}
.cm-ftw-b {
	font-weight: bold;
}
.cm-pos-r {
    position: relative;
}
@media screen and (-webkit-min-device-pixel-ratio:1.5) {
	.cm-border-l {
		border: none;
        background-image: -webkit-linear-gradient(0deg, #eee, #eee 50%, transparent 50%);
        background-image: linear-gradient(270deg, #eee, #eee 50%, transparent 50%);
        background-size: 1px 100%;
        background-repeat: no-repeat;
        background-position: left;
	}
	.cm-border-r {
		border: none;
        background-image: -webkit-linear-gradient(0deg, #eee, #eee 50%, transparent 50%);
        background-image: linear-gradient(270deg, #eee, #eee 50%, transparent 50%);
        background-size: 1px 100%;
        background-repeat: no-repeat;
        background-position: right;
	}
	.cm-border-t{
		border: none;
        background-size: 100% 1px;
		background-repeat: no-repeat;
		background-position: top;
        background-image: linear-gradient(0,#eee,#eee 50%,transparent 50%);
        background-image: -webkit-linear-gradient(90deg,#eee,#eee 50%,transparent 50%);
	}
	.cm-border-b {
		border: none;
        background-size: 100% 1px;
		background-repeat: no-repeat;
		background-position: bottom;
        background-image: linear-gradient(0,#eee,#eee 50%,transparent 50%);
        background-image: -webkit-linear-gradient(90deg,#eee,#eee 50%,transparent 50%);
	}
	.cm-border-all{
        border: none;
        background-image: -webkit-linear-gradient(270deg, #dddddd, #dddddd 50%, transparent 50%), -webkit-linear-gradient(180deg, #dddddd, #dddddd 50%, transparent 50%), -webkit-linear-gradient(90deg, #dddddd, #dddddd 50%, transparent 50%), -webkit-linear-gradient(0, #dddddd, #dddddd 50%, transparent 50%);
        background-image: linear-gradient(180deg, #dddddd, #dddddd 50%, transparent 50%), linear-gradient(270deg, #dddddd, #dddddd 50%, transparent 50%), linear-gradient(0deg, #dddddd, #dddddd 50%, transparent 50%), linear-gradient(90deg, #dddddd, #dddddd 50%, transparent 50%);
        background-size: 100% 1px, 1px 100%, 100% 1px, 1px 100%;
        background-repeat: no-repeat;
        background-position: top, right top, bottom, left top;
    }
}
/*内外边距类*/
.cm-padded-0 {
	padding: 0 !important;
}
.cm-padded-5 {
	padding: .1rem !important;
}
.cm-padded-10 {
	padding: .2rem !important;
}
.cm-padded-15 {
	padding: .3rem !important;
}
.cm-padded-20 {
	padding: .4rem !important;
}
.cm-padded-t-0 {
	padding-top: 0 !important;
}
.cm-padded-t-5 {
	padding-top: .1rem !important;
}
.cm-padded-t-10 {
	padding-top: .2rem !important;
}
.cm-padded-t-15 {
	padding-top: .3rem !important;
}
.cm-padded-t-20 {
	padding-top: .4rem !important;
}
.cm-padded-b-0 {
	padding-bottom: 0 !important;
}
.cm-padded-b-5 {
	padding-bottom: .1rem !important;
}
.cm-padded-b-10 {
	padding-bottom: .2rem !important;
}
.cm-padded-b-15 {
	padding-bottom: .3rem !important;
}
.cm-padded-b-20 {
	padding-bottom: .4rem !important;
}
.cm-padded-l-0 {
	padding-left: 0 !important;
}
.cm-padded-l-5 {
	padding-left: .1rem !important;
}
.cm-padded-l-10 {
	padding-left: .2rem !important;
}
.cm-padded-l-15 {
	padding-left: .3rem !important;
}
.cm-padded-l-20 {
	padding-left: .4rem !important;
}
.cm-padded-r-0 {
	padding-right: 0 !important;
}
.cm-padded-r-5 {
	padding-right: .1rem !important;
}
.cm-padded-r-10 {
	padding-right: .2rem !important;
}
.cm-padded-r-15 {
	padding-right: .3rem !important;
}
.cm-padded-r-20 {
	padding-right: .4rem !important;
}
.cm-margin-0 {
	margin: 0 !important;
}
.cm-margin-5 {
	margin: .1rem !important;
}
.cm-margin-10 {
	margin: .2rem !important;
}
.cm-margin-15 {
	margin: .3rem !important;
}
.cm-margin-t-0 {
	margin-top: 0 !important;
}
.cm-margin-t-5 {
	margin-top: .1rem !important;
}
.cm-margin-t-10 {
	margin-top: .2rem;
}
.cm-margin-t-15 {
	margin-top: .3rem !important;
}
.cm-margin-t-20 {
	margin-top: .4rem !important;
}
.cm-margin-b-0 {
	margin-bottom: 0 !important;
}
.cm-margin-b-5 {
	margin-bottom: .1rem !important;
}
.cm-margin-b-10 {
	margin-bottom: .2rem !important;
}
.cm-margin-b-15 {
	margin-bottom: .3rem !important;
}
.cm-margin-b-20 {
	margin-bottom: .4rem !important;
}
.cm-margin-l-0 {
	margin-left: 0 !important;
}
.cm-margin-l-5 {
	margin-left: .1rem !important;
}
.cm-margin-l-10 {
	margin-left: .2rem !important;
}
.cm-margin-l-15 {
	margin-left: .3rem !important;
}
.cm-margin-r-0 {
	margin-right: 0 !important;
}
.cm-margin-r-5 {
	margin-right: .1rem !important;
}
.cm-margin-r-10 {
	margin-right: .2rem !important;
}
.cm-margin-r-15 {
	margin-right: .3rem !important;
}
.cm-margin-r-20 {
	margin-right: .4rem !important;
}
.cm-font-size-12 {
	font-size: .24rem;
}
.cm-font-size-13 {
	font-size: .26rem;
}
.cm-font-size-14 {
	font-size: .28rem;
}
.cm-font-size-15 {
	font-size: .3rem;
}
.cm-font-size-16 {
	font-size: .32rem;
}
.cm-font-size-17 {
	font-size: .34rem;
}
.cm-font-size-18 {
	font-size: .36rem;
}
.cm-font-size-19 {
	font-size: .38rem;
}
.cm-font-size-20 {
	font-size: .4rem;
}
.cm-f {
	display: flex;
}
.cm-f-w {
	flex-wrap: wrap;
}
.cm-d-c {
	flex-direction: column;
}
.cm-f-c-c {
	display: flex;
	justify-content: center;
	align-items: center;
}
.cm-f-c-e {
	display: flex;
	justify-content: center;
	align-items: flex-end;;
}
.cm-f-s-c {
	display: flex;
	justify-content: flex-start;
	align-items: center;
}
.cm-f-s-e {
	display: flex;
	justify-content: flex-start;
	align-items: center;
}
.cm-f-b {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
}
.cm-f-b-c {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.cm-f-a-c {
	display: flex;
	justify-content: space-around;
	align-items: center;
}
.cm-f-e-c {
	display: flex;
	justify-content: flex-end;
	align-items: center;
}
.cm-f-1 {
	flex: 1;
}
.cm-line-h-1 {
    line-height: 1;
}
input[type="text"],
input[type="password"],
input[type="search"],
input[type="email"],
input[type="tel"],
input[type="url"],
input[type="date"],
input[type="datetime-local"],
input[type="time"],
input[type="number"],
select,
textarea {
    border: none;
    background-color: transparent;
    border-radius: 0;
    box-shadow: none;
    display: block;
    padding: 0;
    margin: 0;
    width: 100%;
    height: .88rem;
    line-height: normal;
    color: #424242;
    font-size: 0.36rem;
    font-family: inherit;
    box-sizing: border-box;
    -webkit-user-select: text;
            user-select: text;
    -webkit-appearance: none;
            appearance: none;
}
input[type="search"]::-webkit-search-cancel-button {
	display: none;
}
.m-toast-pop {
    z-index: 999;
}
