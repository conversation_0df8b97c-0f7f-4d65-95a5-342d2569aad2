body{
	background-color: #eeeeee;
}
.container{
	background-color: #eeeeee;
	padding-top:0!important;
	margin-top:0!important;

}

.success{
	width: 7.08rem;
	height: 3.51rem;
	background:  #de2127 url(../images/success.png) no-repeat center top;
	background-size: 7.08rem 3.51rem;
	margin: 1rem .21rem;
}



.titlebox{
	width: 7.5rem;
	min-height: 1.8rem;
	background-color: white;
}
.titlebox .title{
	font-size: .35rem;
    display: inline-block;
    margin-top: .5rem;
    margin-left: .28rem;
}
.titlebox .subtitle{
	margin-left: .28rem;
    font-size: .24rem;
    margin-top: .1rem;
    color: darkgray;
}

.rechargebox, .buybox{
	width: 7.5rem;
	padding-bottom: .2rem;
	margin-top: .2rem;
	background-color: white;
}
.rechargebox .title2{
	margin-left: .28rem;
    font-size: .3rem;
    padding-top: .2rem;
}
.rechargebox input{
	margin-left: .28rem;
    margin-top: .2rem;
    background: transparent;
    width: 6.8rem;
    border-width: 0;
    font-size: .28rem;
} 
.buybox input{
	margin-left: .28rem;
    margin-top: .2rem;
    background: transparent;
    width: 6.8rem;
    border-width: 0;
}
.buybox .name{
	font-size: .3rem;
	margin-top: .2rem;
}
.buybox .address{
	font-size: .26rem;
}
.sepline{
	background-color: lightgray;
	width: 7.1rem;
	height: 1px;
	margin-left: .2rem;
	margin-top: .1rem;
}

.name::-webkit-input-placeholder {
    font-size: .26rem;
}

.rechargebtn{
	width: 7.5rem;
	height: 1rem;
	background-color: #e13146;
	position: fixed;
	bottom:0;
}
.rechargebtn p{
	width: 100%;
	text-align: center;
	color: white;
	margin-top: .3rem;
	font-size: .3rem;
}
.popupError{
	width: 100%;
	height: 100%;
	position: fixed;
	left: 0;
	top:0;
	z-index: 999999;
	background: rgba(0,0,0,0.6)
}
.displayno{
	display:none;
}
.failinner{
	position: absolute;
	left: 50%;
	top:50%;
	transform: translate(-50%,-50%);
	-moz-transform: translate(-50%,-50%);
	-ms-transform: translate(-50%,-50%);
	
	-o-transform: translate(-50%,-50%);
	-webkit-transform: translate(-50%,-50%);



}
.botbtn{
	margin-left: 0.6rem;
	line-height:0.9rem;
	color: #fff;
	font-size: 0.30rem;


}