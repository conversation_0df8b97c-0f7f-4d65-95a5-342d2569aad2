
.iconsuccess{
	width: 2.2rem;
	margin: auto;
	margin-top: 1.8rem;
}
.info {
	font-size: .56rem;
}
.btnorders{
	width: 4.47rem;
	margin: 1rem auto;
}
.botbtnbox {
    margin-top: 1.5rem;
    width: 100%;
}
.botbtn {
    background: -webkit-linear-gradient(top, #E74957 , #DC2833); /* Safari 5.1 - 6.0 */
    background: -o-linear-gradient(bottom, #E74957, #DC2833); /* Opera 11.1 - 12.0 */
    background: -moz-linear-gradient(bottom, #E74957, #DC2833); /* Firefox 3.6 - 15 */
    background: linear-gradient(to bottom, #E74957 , #DC2833); /* 标准的语法 */
    width: 80%;
    height: .9rem;
    border-radius: .45rem;
    
}
.container {
	height: 100vh;
	margin-top: 0!important;
	padding-top: .8rem;
}
.failbox {
	height: 100%;
	background-color: #979797;
	padding-top: 1rem;
}
.failinner {
	width: 90%;
	margin: auto;
	background-color: #fff;
	border-radius: .2rem;
	padding: .5rem 0;
	text-align: center;
}
.iconfail {
	width: 1.5rem;
	margin: auto;
}