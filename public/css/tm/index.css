body{
	background: #F2F2F2;
}
.container{
	width: 7.5rem;
	height: 100%;
	/*float: left;*/
	margin:0 auto;
	overflow-y: scroll;
	background: #F2F2F2;
	padding-bottom: 1rem;
}
.headImg{
	width:7rem;
	height: 3.5rem;
	margin-left: 0.25rem;
	float: left;
	margin-top: 0.25rem;
}
.inputBox{
	width: 7rem;
	height: 0.85rem;
	float: left;
	margin-left: 0.25rem;
	background: #fff;
	border-radius: 0.1rem;
	margin-top:0.2rem;
}
.inputBoxShort{
	width: 4.6rem;
	height: 0.85rem;
	float: left;
	margin-left: 0.25rem;
	background: #fff;
	border-radius: 0.1rem;
	margin-top:0.2rem;
}
.inputBoxRight{
	width: 2.1rem;
	height: 0.85rem;
	border-radius: 0.1rem;
	float: right;
	margin-right: 0.25rem;
	margin-top:0.2rem;
}
.codeImg{
	width: 100%;
	height: 100%;
	border-radius: 0.1rem;
}
.mt30{
	margin-top: 0.4rem;
}
.inputText{
	color: rgba(230,0,51,1);
	font-size: 0.32rem;
	float: left;
	margin-left: 0.2rem;
	line-height: 0.85rem;
}
.inputBoxShort .longInput{
	height: 0.85rem;
	width:3rem;
	float: left;
	margin-left: 0.2rem;
	line-height: 0.82rem;
	font-size: 0.26rem;
	border:none;
}
.inputBox .longInput{
	height: 0.85rem;
	width:5rem;
	float: left;
	margin-left: 0.2rem;
	line-height: 0.82rem;
	font-size: 0.26rem;
	border:none;
}
.getCode{
	width:100%;
	height: 100%;
	border-radius: 0.1rem;
	line-height: 0.85rem;
	text-align: center;
	font-size: 0.26rem;
	color: #fff;
	background: rgba(230,0,51,1);
}
.noClick{
	background: #989898!important;
}
.confirmBtn{
	width:7rem;
	height:0.85rem;
	line-height: 0.85rem;
	text-align: center;
	color: #fff;
	background: rgba(230,0,51,1);
	font-size: 0.28rem;
	float: left;
	margin-left: 0.25rem;
	border-radius: 0.1rem;
	margin-top:0.3rem;
}
.conBox{
	width: 7rem;
	float: left;
	margin-left: 0.25rem;
	border-radius: 0.15rem;
	box-shadow: 0 2px 4px #999;
    background: #fff;
    margin-top: 0.25rem;
    padding-bottom: 0.25rem;
}
.boxHeader{
	width:100%;
	height: 0.5rem;
	float: left;

}
.conTitle{
	font-size: 0.28rem;
	color: #E60033;
	float: left;
	margin-left:0.1rem;

}
.leftImg{
	width: 2rem;
	float: left;
	margin-top: 0.15rem;
	margin-left: 1rem;

}
.boxHeader{
	margin-top: 0.1rem;
}
.rightImg{
		width: 2rem;
	float: left;
	margin-top: 0.15rem;
	margin-left: 0.1rem;
}
.colorOne{
	width: 6.3rem;
	color: #000;
	float: left;
	font-size: 0.24rem;
	margin-top: 0.2rem;
}
.colorNum{
	width:0.3rem;
	color: #000;
	float: left;
	font-size: 0.24rem;
	margin-left: 0.2rem;
	margin-top: 0.2rem;

}
.mt10{
	margin-top: 0.05rem!important;
}
.conText{
	margin-left: 0.25rem;
}
.mt20{
	margin-top: 0.2rem!important;
}
.conImg{
	width: 6.5rem;
	float: left;
}
.popup{
	width: 100%;
	height: 100%;
	position: fixed;
	left: 0;
	top:0;
	z-index: 999;
	background: rgba(0,0,0,0.6);

}
.popupCon{
	width: 7rem;
	padding:0.3rem 0;
	background: #fff;
	border-radius: 0.15rem;
	position: absolute;
	left: 50%;
	top:50%;
	transform: translate(-50%,-50%);
}
.closeImg{
	width:0.4rem;
	height: 0.4rem;
	position: absolute;
	right:0.2rem;
	top:0.2rem;
}
.popupTextOne{
	text-align: center;
	width:6.5rem;
	margin-left:0.25rem;
	margin-top: 0.8rem;
	font-size: 0.26rem;

}
.popupTextTwo{
	text-align: center;
	width:6.5rem;
	margin-left:0.25rem;
	margin-top: 0.05rem;
	font-size: 0.26rem;
}
.seeOrderlist{
	width: 6.5rem;
	height: 0.85rem;
	line-height: 0.85rem;
	text-align: center;
	color: #fff;
	background: #E60033;
	float: left;
	margin-left: 0.25rem;
	margin-top: 0.6rem;
	border-radius: 0.15rem;
    font-size: 0.26rem;
}
.displayNo{
	display: none;
}
.seeDetail{
	color: #E60033;
	font-size: 0.26rem;
	float: right;
	margin-top: 0.2rem;

}
.topImg{
	width: 0.8rem;
	height: 0.8rem;
	position: fixed;
	right:0;
	bottom:1rem;
	opacity: 0.6;
}
.text1{
	font-size: 0.26rem;
	width: 100%;
	float: left;
	margin-left: 0.25rem;
	margin-top: 0.25rem;
}
.text2{
	font-size: 0.26rem;
	width: 100%;
	float: left;
	margin-left: 0.25rem;
	margin-top: 0.25rem;
	color: #E60033;
}
.reset{
	width: 3.2rem;
	height: 0.85rem;
	float: left;
	margin-left: 0.2rem;
	border-radius: 0.1rem;
	border:1px solid #E60033;
	line-height: 0.85rem;
	text-align: center;
	color:  #E60033;
	margin-top: 0.4rem;
	margin-bottom: 0.2rem;
    font-size: 0.26rem;
}
.popupConfirm{
	width: 3.2rem;
	height: 0.85rem;
	float: right;
	margin-right: 0.2rem;
	border-radius: 0.1rem;
	background:#E60033;
	line-height: 0.85rem;
	text-align: center;
	color: #fff;
	margin-top: 0.4rem;
	margin-bottom: 0.2rem;
    font-size: 0.26rem;
}
.sucbox{
	width: 100%;
	height: 4.8rem;
	background: #fff;
	box-shadow: 0 2px 4px #999;
}
.star{
	width: 3.5rem;
	float: left;
	margin-left: 2rem;
	margin-top:0.8rem;
}
.star2{
	width: 2rem;
	float: left;
	margin-left: 2.75rem;
	margin-top:0.8rem;
}
.s-txt{
	width: 100%;
	text-align: center;
	font-size: 0.26rem;
	color: #000;
	float: left;
	margin-top: 0.5rem;

}
.s-wait{
	width: 100%;
	text-align: center;
	font-size: 0.26rem;
	color: #B3B3B3;
	float: left;
	margin-top: 0.2rem;
}
.backBtn{
	width: 7rem;
	height: 0.85rem;
	line-height: 0.85rem;
	float: left;
	margin-left: 0.25rem;
	text-align: center;
	font-size: 0.26rem;
	background: #E60033;
	color: #fff;
	border-radius: 0.15rem;
	margin-top: 0.5rem;
}
.seeOrderBtn{
	width: 7rem;
	height: 0.85rem;
	line-height: 0.85rem;
	float: left;
	margin-left: 0.25rem;
	text-align: center;
	font-size: 0.26rem;
	/*background: #E60033;*/
	color: #E60033;
	border-radius: 0.15rem;
	margin-top: 0.5rem;
	border:1px solid #E60033;
}
