.container {
    height: 100vh;
    margin-top: 0 !important;
    /*padding-top: .8rem;*/
}

.contbox {
    background-color: #960A1E;
    overflow: hidden;
    min-height: 100%;
    background-image: url(../../images/exbg3.png);
    background-repeat: no-repeat;
    background-size: 100% auto;
    overflow-y: scroll;
    padding-bottom: 1rem;
}

.bgimg {
    width: 100%;
}

.cont {
    margin-top: 1rem;
}

.continfo {
    font-size: .5rem;
    font-family: "楷体", "楷体_GB2312";
}

.cont2 {
    font-size: .3rem;
    margin-top: .8rem;
}

.formbox {
    width: 93%;
    margin: 100% auto 0;
}

.formboxinner {
    padding: .6rem .4rem;
    border-radius: .15rem;
    /* overflow: hidden; */
    background-color: #fff;
    z-index: 3;
}

.shadowimg {
    position: absolute;
    left: 0rem;
    width: 100%;
}

.shadowtop {
    top: -.01rem;
}

.shadowbot {
    bottom: -.01rem;
    transform: rotate(180deg);
}

.lineimg {
    height: 0.02rem;
    width: 1.5rem;
}

.lineright {
    transform: rotate(180deg);
}

input[type="text"]:-moz-placeholder {
    font-size: .36rem;
}

input[type="text"]:-ms-input-placeholder {
    font-size: .36rem;
}

input[type="text"]::-webkit-input-placeholder {
    font-size: .36rem;
}

.formitem input[type="text"] {
    font-size: .36rem;
    height: .9rem;
    padding-left: .2rem;
}

.formbtn {
    background: -webkit-linear-gradient(top, #E74957, #DC2833); /* Safari 5.1 - 6.0 */
    background: -o-linear-gradient(bottom, #E74957, #DC2833); /* Opera 11.1 - 12.0 */
    background: -moz-linear-gradient(bottom, #E74957, #DC2833); /* Firefox 3.6 - 15 */
    background: linear-gradient(to bottom, #E74957, #DC2833); /* 标准的语法 */
    width: 95%;
    height: .9rem;
    margin: .6rem auto 0;
    border-radius: .45rem;
}

.botline {
    width: 100%;
    height: .24rem;
    border-radius: .12rem;
    background-color: #A5211A;
    position: absolute;
    bottom: -.12rem;
    z-index: 1;
}

.gifimg {
    position: absolute;
    bottom: -1.5rem;
    right: -.8rem;
    z-index: 9;
    width: 2rem;
    height: auto;
}
