.container {
    height: 100vh;
    margin-top: 0 !important;
    background-color: #F8220F;
}

.contbox {
    background-color: #F8220F;
    /*background-color: linear-gradient(to bottom, #e6a075, #E7DFED);*/
    overflow: hidden;
    min-height: 100vh;
    /*background-image: url(../../images/zxlz/home.jpg);*/
    background-image: url(https://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/zxlz/home1.png);

    background-repeat: no-repeat;
    background-size: 100% auto;
    overflow-y: scroll;
    /*padding-bottom: 1rem;*/
}

.shadowimg {
    position: absolute;
    left: 0rem;
    width: 100%;
}

.shadowtop {
    top: -.01rem;
}

.shadowbot {
    bottom: -.01rem;
    transform: rotate(180deg);
}

.lineimg {
    height: 0.02rem;
    width: 1.5rem;
}

.lineright {
    transform: rotate(180deg);
}

input[type="text"]:-moz-placeholder {
    font-size: .3rem;
    text-align: center;
}

input[type="text"]:-ms-input-placeholder {
    font-size: .3rem;
}

input[type="text"]::-webkit-input-placeholder {
    font-size: .3rem;
    text-align: center;
}

.formitem input[type="text"] {
    height: 11.25rem;
    padding-left: 0;
    text-align: center;
    width: 100%;
}

.formbtn {
    width: 75%;
    height: .9rem;
    margin: .6rem auto 0;
    border-radius: .45rem;
    /*border: 1px solid green;*/
    position: absolute;
    top:5.5rem;
    left: 1rem;
}

.botline {
    width: 100%;
    height: .24rem;
    border-radius: .12rem;
    background-color: #A5211A;
    position: absolute;
    bottom: -.12rem;
    z-index: 1;
}

.gifimg {
    position: absolute;
    bottom: -1.5rem;
    right: -.8rem;
    z-index: 9;
    width: 2rem;
    height: auto;
}
