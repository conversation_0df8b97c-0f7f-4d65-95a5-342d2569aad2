<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>权益兑换</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/bootstrap.min.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/swiper.min.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/icbc.css">
    <!--    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/common.css">-->
    <link rel="stylesheet" href="https://star6.oss-cn-beijing.aliyuncs.com/public/css/cu_common.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/style.css">
    <link rel="stylesheet" type="text/css" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/tm/index.css">

    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/jquery-3.1.0.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/bootstrap.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/swiper.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/popper.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/common.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/vue.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/jquery.bpopup.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/flex.js"></script>

    <style>
        .container_box {
            width: 7.5rem;
            margin: 0 auto;
            overflow-y: scroll;
            background: #F2F2F2;
            padding-bottom: 1rem;
        }

        .noClick {
            background: #999;
            pointer-events: none;
        }

        .conBox{
            width: 7rem;
            float: left;
            margin-left: 0.25rem;
            border-radius: 0.15rem;
            box-shadow: 0 2px 4px #999;
            background: #fff;
            margin-top: 0.25rem;
            padding-bottom: 0.25rem;
        }
        .innerContent{
            margin: .25rem;
        }
    </style>
</head>

<body>
<div class="container_box">
    <!--    <img class="headImg" src="https://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/luckin/top.png" alt="">-->
    <img class="headImg" src="" alt="">
    <div class="inputBox mt30">
        <p class="inputText">充值账号</p>
        <input class="longInput charge_account" type="text" placeholder="请输入您的手机号">
    </div>
    <div id="yzm" class="displayNo">
        <div class="inputBoxShort">
            <p class="inputText">验证码</p>
            <input class="longInput sms_captcha" type="text" placeholder="点击输入短信验证码" maxlength="6">
        </div>
        <div class="inputBoxRight">
            <div class="getCode">获取验证码</div>
        </div>
    </div>
    <div class="confirmBtn">确定</div>
    <div class="conBox">
        <div class="innerContent"></div>
    </div>

</div>

</div>
<img class="topImg displayNo" src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/tm/top.png" alt="">
<div class="popup displayNo">
    <div class="popupCon">
        <img class="closeImg" src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/cole.png" alt="">
        <p class="text1">请确认您的充值账号</p>
        <p class="text1">充值账号：<span class="tmNumber"></span></p>
        <p class="text2">请仔细核对您填写的充值账号是否正确，避免错充。</p>
        <div class="reset">修改</div>
        <div class="popupConfirm">确认</div>
    </div>
</div>
<!-- toast -->
<div id="m-toast-pop" style="z-index:9999;" class="m-toast-pop">
    <div class="m-toast-inner">
        <div class="m-toast-inner-text" id="m-toast-inner-text"></div>
    </div>
</div>

<script>
    (function () {
        var tmIndex = {
            init: function () {
                this.getCard();
                this.startInit();
                this.bind();
            },
            startInit: function () {
                let gift_card_pwd = sessionStorage.getItem('gift_card_pwd');
                // 拿到用户提交的兑换码后进行登录
                // 返回该兑换码下的相关信息 1.首页图 2.商品信息 3.goods_id 4.type (用于页面展示)
                $.ajax({
                    type: "POST",
                    url: "/api/login",
                    data: {
                        "card_pwd": gift_card_pwd,
                        "act": 'dqeI1sxLCaZidetTsBQPnA==', //通用那个
                    },
                    async: true,
                    dataType: "json",
                    xhrFields: {
                        withCredentials: true
                    },
                    success: function (res, textStatus, xhr) {
                        $('.indexConfirm').removeClass('noClick');
                        console.log(res)
                        if (res.code == '200') {
                            // exchange_state: 1-未兑换，2-兑换中（还有礼品未兑换完），3-兑换完毕。
                            // 已兑换的情况 直接跳到订单列表
                            if (res.data.exchange_state == '3') {
                                toastinfo('该兑换码已兑换');
                                setTimeout(() => {
                                    jump('../myorder.html?_r=1128')
                                }, 2000);
                            }
                            //根据返回的 产品类型觉得是否显示验证码
                            // 将里面的goodsId值存到session中  发送验证码的时候请求不同的接口
                            sessionStorage.setItem('goods_id', res.data.goods_id); //存商品ID
                            //充值验证类型。0-无，1-短信验证码(京东），2-淘宝手机号充值
                            // 只有京东卡 显示短信验证码
                            if (res.data.pre_verify_type == 1) {
                                $("#yzm").removeClass('displayNo');
                            }
                            // 首页图
                            $(".headImg").attr("src", res.data.goods_img);
                            // 兑换规则图
                            $(".innerContent").html(res.data.goods_desc);
                        } else if (res.code == '3025') {
                            if (res.data.pre_verify_type == 1) {
                                $("#yzm").removeClass('displayNo');
                                $(".getCode").addClass('noClick')
                            }
                            $(".confirmBtn").addClass('noClick')
                            // 首页图
                            $(".headImg").attr("src", res.data.goods_img);
                            // 兑换规则图
                            $(".innerContent").html(res.data.goods_desc);
                            toastinfo(res.msg);
                        } else {
                            jump('./invalid.html?_r=1128')
                            // $('.codeImg').click();
                            // toastinfo(res.msg);
                        }
                    },
                    //有以下三个参数：XMLHttpRequest 对象、错误信息、（可选）捕获的异常对象。
                    //如果发生了错误，错误信息（第二个参数）除了得到 null 之外，还可能是 "timeout", "error", "notmodified" 和 "parsererror"。
                    error: function (xhr, textStatus, errorThrown) {
                        $('.indexConfirm').removeClass('noClick');
                        if (xhr.status == 429) {
                            $('.codeImg').click();
                            toastinfo("操作太频繁了，请稍后再试！");
                        } else {
                            toastinfo("网络错误，请稍后再试！");
                        }
                    }
                })
            },
            bind: function () {
                //监听页面滚动
                $(document).scroll(function () {
                    let scroH = $(document).scrollTop();
                    // var viewH = $(window).height();
                    // var contentH = $(document).height();
                    // 控制返回首页图片 大于300的时候显示图片,小于300的时候隐藏图片
                    if (scroH > 300) {
                        $('.topImg').removeClass('displayNo')
                    }
                    if (scroH < 300) {
                        $('.topImg').addClass('displayNo')
                    }
                });
                //点击回到顶部按钮 加了个动画
                $('.topImg').on('click', function () {
                    $("html,body").animate({
                        scrollTop: 0
                    }, 500);
                });
                //点击弹窗的关闭按钮
                $('.closeImg').on('click', function () {
                    $('.popup').addClass('displayNo')
                });
                //点击修改按钮弹窗关闭
                $('.reset').on('click', function () {
                    $('.popup').addClass('displayNo')

                });
                //点击确定按钮
                $('.confirmBtn').on('click', function () {
                    // 获取充值账号
                    let charge_account = $('.charge_account').val();
                    if (charge_account == '') {
                        toastinfo('请输入充值账号！');
                        return false;
                    }
                    // 在确认弹框中显示充值账号,并显示弹窗.
                    $('.tmNumber').text(charge_account);
                    $('.popup').removeClass('displayNo')
                });
                //点击弹窗的确定按钮
                $('.popupConfirm').on('click', function () {
                    if ($('.popupConfirm').hasClass('noClick')) {
                        return;
                    }
                    let charge_account = $('.charge_account').val();
                    if (charge_account == '') {
                        toastinfo('请输入充值账号！');
                        return false;
                    }
                    $('.popupConfirm').addClass('noClick');
                    // 确认之后将账号存在sessionStorage中
                    sessionStorage.setItem('charge_account', charge_account); //存充值手机号
                    // 如果需要 验证码则一并把验证码存入
                    // sessionStorage.setItem('verify_code', $('.sms_captcha').val()); //前置验证码
                    let goods_id = sessionStorage.getItem('goods_id'); //取商品ID
                    // let verify_code = sessionStorage.getItem('verify_code'); //取商品ID

                    if (!$("#yzm").hasClass('displayNo')) {
                        // 说明是京东商品 则需要验证 验证码
                        var verify_code = $('.sms_captcha').val();
                        if (verify_code == '') {
                            toastinfo('请输入验证码！');
                            $('.popupConfirm').removeClass('noClick');
                            return false;
                        }
                    }
                    $.ajax({
                        // 确定兑换
                        // 普通商品
                        // 京东商品
                        // 天猫手机号下只有一个账号的情况
                        type: "POST",
                        url: "/api/order",
                        data: {
                            "goods_id": goods_id,
                            "charge_account": charge_account,
                            "verify_code": verify_code, // 例如 京东商品
                            // "user_mobile": charge_account, // ??? todo 一定是手机号?
                            // 'order_remark': '',
                        },
                        async: true,
                        dataType: "json",
                        xhrFields: {
                            withCredentials: true
                        },
                        success: function (res, status, xhr) {
                            if (res.code == '200') {
                                $('.popup').addClass('displayNo');
                                // 销毁 goods_id, verify_code
                                sessionStorage.setItem('charge_account', ''); //销毁充值手机号
                                sessionStorage.setItem('verify_code', ''); //销毁前置验证码
                                window.location.href = './success.html'
                            } else if (res.code == '3020') {
                                // 兑换码已使用
                                toastinfo(res.msg);
                                setTimeout(() => {
                                    jump('../myorder.html?_r=1128')
                                }, 2000);
                            } else if (res.code == '3030') {
                                //超限
                                $('.popup').addClass('displayNo');
                                window.location.href = './error.html?_v=1'//超限跳转
                            } else if (res.code == '4001') {
                                // 有多个账号的情况
                                // 直接跳到输入验证码界面
                                window.location.href = './verifyCode.html?_v=1'//
                            } else {
                                toastinfo(res.msg);
                            }
                            $('.popupConfirm').removeClass('noClick');
                        },
                        error: function (error, xhr, abort) {
                            $('.popupConfirm').removeClass('noClick');
                            window.location.href = './error.html';
                            toastinfo("网络错误，请稍后再试！");
                        }
                    })

                });
                // 获取验证码  一个是京东,一个是天猫
                $('.getCode').on('click', function () {
                    var phoneNum = $('.charge_account').val();
                    let goods_id = sessionStorage.getItem('goods_id'); //取商品ID
                    //首先校验手机号
                    if (isPhoneNumber(phoneNum)) {
                        $.ajax({//发送验证码
                            type: "POST",
                            url: "/api/pre-verify",
                            data: {
                                'charge_account': phoneNum,
                                'goods_id': goods_id,
                                'verify_code': '', //
                            },
                            async: true,
                            dataType: "json",
                            xhrFields: {
                                withCredentials: true
                            },
                            success: function (res, status, xhr) {
                                if (res.code == '200') {
                                    tmIndex.countDown(60);
                                } else {
                                    $('.codeImg').click();
                                    toastinfo(res.msg);
                                }
                            },
                            error: function (xhr, textStatus, errorThrown) {
                                if (xhr.status == 429) {
                                    $('.codeImg').click();
                                    toastinfo("操作太频繁了，请稍后再试！");
                                } else {
                                    toastinfo("网络错误，请稍后再试！");
                                }
                            }
                        })

                    } else {
                        toastinfo('请输入正确的手机号！')
                    }
                });
            },
            countDown: function (timeNum) {
                if ($(".getCode").hasClass("noClick")) {
                    return;
                }
                $('.getCode').addClass('noClick');
                var time;
                var num = timeNum;
                time = setInterval(function () {
                    num = num - 1;
                    if (num <= 0) {
                        num = timeNum;
                        $(".getCode").html('获取验证码');
                        $('.getCode').removeClass('noClick');
                        clearInterval(time);
                        return false;
                    } else {
                        $('.getCode').html(num);
                    }
                }, 1000)
            },
            // 从url中获取兑换码
            getCard: function () {
                let card = GetQueryString('s');
                if (card) {
                    card = card.split(/[.,，。]/)[0];
                    if (card) {
                        // $('.card_pwd').val(card);
                        // 存到session
                        sessionStorage.setItem('gift_card_pwd', card);
                    }
                }
            }
        };
        tmIndex.init();

    })()

</script>
</body>

</html>
