  <!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<title>兑换中心</title>
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
	<link rel="stylesheet" href="css/bootstrap.min.css">
	<link rel="stylesheet" href="css/swiper.min.css">
	<link rel="stylesheet" href="css/icbc.css">
	<link rel="stylesheet" href="css/common.css">
	<link rel="stylesheet" href="css/style.css">
	<link rel="stylesheet" type="text/css" href="css/exlogin.css">

 	<script src="js/jquery-3.1.0.min.js"></script>
  	<script src="js/bootstrap.min.js"></script>
  	<script src="js/swiper.min.js"></script>
    <script src="js/popper.min.js"></script>
	<script src="js/common.js"></script>
	<script src="js/vue.min.js"></script>
	<script src="js/jquery.bpopup.js"></script>
</head>
<body>
	<div class="container">
		<div class="contbox cm-pos-r">
			<div class="cont">
				<div class="continfo cm-tx-c cm-color-white">兑换中心</div>
				<div class="formbox cm-pos-r">
					<div class="formboxinner cm-pos-r">
						<img src="images/shadow.png" class="shadowimg shadowtop" alt="">
						<div class="forminfo cm-f-c-c cm-margin-b-10">
							<img src="images/line.png" class="lineimg" alt="">
							<div class="cm-color-grey cm-font-size-14 cm-margin-l-10 cm-margin-r-10">请输入您的卡号和密码进行兑换</div>
							<img src="images/line.png" class="lineimg lineright" alt="">
						</div>
						<div class="formcont">
							<div class="formitem cm-border-b">
								<input type="text" name="" placeholder="请输入卡号" class="cardno">
							</div>
							<div class="formitem cm-border-b">
								<input type="text" name="" placeholder="请输入密码" class="password">
							</div>
						</div>
						<div class="formbtn cm-color-white cm-f-c-c cm-line-h-1 cm-font-size-15">立即兑换</div>
						<img src="images/shadow.png" class="shadowimg shadowbot" alt="">
					</div>
					<img src="images/gift_03.png" class="gifimg" alt="">
					<div class="botline"></div>
				</div>
			</div>
		</div>
	</div>

	<!-- toast -->
	<div id="m-toast-pop" class="m-toast-pop">
        <div class="m-toast-inner">
            <div class="m-toast-inner-text" id="m-toast-inner-text"></div>
        </div>
    </div>

	<script>
		$('.formbtn').on('click', function(){
			if($('.cardno')[0].value == ""){
				toastinfo('请填写卡号');
				return;
			}
			if($('.password')[0].value == ""){
				toastinfo('请填写密码');
				return;
			}
			// if(GetQueryString('proj') == 'slt'){
			// 	jump(GetQueryString('proj') + '/goods.html');
			// }else{
			// 	jump('goods.html?proj=' + GetQueryString('proj') + '&level=' + level);
			// }
			// return
			$.ajax({
	            type: "POST",
	            url: "/api/login",
	            data: {
	            	'act': 'hOquiSbCeb023r/+w7f3vw==',
	            	'card_no' : $('.cardno')[0].value,
	            	'card_pwd' : $('.password')[0].value
	            },
	            async: true,
	            dataType: "json",
	            xhrFields: {
	                withCredentials: true
	            },
	            success : function (res, status, xhr) {
	            	if(res.code == '200'){
	            		// console.log(data.msg);
						var level =res.data['1_1']
	            		if(GetQueryString('proj') == 'slt'){
	            			jump(GetQueryString('proj') + '/goods.html');
	            		}else{
	            			jump('goods.html?proj=' + GetQueryString('proj') + '&level1=' + level[0]+'&level2=' + level[1]);
	            		}
					}else{
	                    toastinfo(data.msg);
	                }
	            },
	            error: function (error, xhr, abort) {
	                toastinfo("网络错误，请稍后再试！");
	            }
	        })
		})
	</script>
</body>
</html>
