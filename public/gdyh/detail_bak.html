<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>商品详情</title>
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/bootstrap.min.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/swiper.min.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/icbc.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/common.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/style.css">
    <link rel="stylesheet" type="text/css" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/detail.css">
        <link rel="stylesheet" type="text/css"
              href="https://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/gdyh/detail.css?_r=12312">
<!--    <link rel="stylesheet" type="text/css" href="../css/gdyh/detail.css">-->

    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/jquery-3.1.0.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/bootstrap.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/swiper.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/popper.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/common.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/vue.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/jquery.bpopup.js"></script>
    <!--访问统计-->
    <script>
        var _hmt = _hmt || [];
        (function () {
            var hm = document.createElement("script");
            hm.src = "https://hm.baidu.com/hm.js?f43c7827f4ea614a1ef838902bd1feff";
            var s = document.getElementsByTagName("script")[0];
            s.parentNode.insertBefore(hm, s);
        })();
    </script>
</head>
<body>
<div class="container">
    <ul class="nav cm-border-b">
        <li class="cm-f-b-c">
            <p class="on">商品详情</p>
            <p>商品参数</p>
            <p>购物须知</p>
        </li>
    </ul>
    <div class="tab tab0">
        <!-- 轮播 -->
        <div class="swiper-container" id="swiper">
            <div class="swiper-wrapper">
                <div class="swiper-slide" v-for="item in items">
                    <img :src='item'>
                </div>
            </div>
            <div class="swiper-pagination"></div>
        </div>
        <!-- <img src="images/01.png" class="banner" style="width: 6.5rem; margin-left: .5rem; margin-top: 1rem;"> -->
        <p class="title"></p>
        <p class="subtitle"></p>
        <div class="darkline"></div>
        <label class="goodlabel" style="padding-bottom:0.05rem;border-bottom: solid .05rem #E03147;">商品详情</label>
        <div class="content">

        </div>
    </div>
    <div class="tab tab1 hide">

    </div>
    <div class="tab tab2 hide">

    </div>

    <div class="recharge cm-font-size-16 cm-color-white cm-f-c-c" onclick="rechargefun()" style="background:#F4B865">
        立即兑换
    </div>
</div>

<!-- toast -->
<div id="m-toast-pop" class="m-toast-pop">
    <div class="m-toast-inner">
        <div class="m-toast-inner-text" id="m-toast-inner-text"></div>
    </div>
</div>

<!-- 模态框部分--开始 -->

<!-- 填写手机号模态框 -->
<div class="popup popup-fp displayno">
    <div class="popupCon-xy popupConStyle">
        <img class="nr-bg" src="https://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/gdyh/mtk1.png" alt="">
        <div class="div_input">
            <input type="text" maxlength="25" class="covertCode" style="color:#424242;font-size: 0.28rem;"
                   placeholder="请填写您的充值账号">
        </div>
        <div class="close_btn"></div>
        <div class="confirmInfo"></div>
    </div>
</div>
<!-- 确认信息模态框 -->
<div class="popup popup-qr displayno">
    <div class="popupCon-xy popupConStyle">
        <img class="nr-bg" src="https://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/gdyh/qrmtk1.png" alt="">
        <p class="mtk_goods_name"></p>
        <p class="mtk_charge_number"></p>
    </div>
    <div class="close_btn_qr"></div>
    <div class="confirmSubmit"></div>
</div>

<!--填写QQ号模态框-->
<div class="popup popup-fp1 displayno1">
    <div class="popupCon-xy popupConStyle">
        <img class="nr-bg" src="https://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/gdyh/mtkqq.png" alt="">
        <div class="div_input">
            <input type="text" maxlength="25" class="covertCodeQq" style="color:#424242;font-size: 0.28rem;"
                   placeholder="请填写您的充值账号">
        </div>
        <div class="close_btn"></div>
        <div class="confirmInfo1"></div>
    </div>
</div>
<!-- QQ部分 确认信息模态框-->
<div class="popup popup-qr1 displayno1">
    <div class="popupCon-xy popupConStyle">
        <img class="nr-bg" src="https://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/gdyh/qrmtkqq.png" alt="">
        <p class="mtk_goods_name1"></p>
        <p class="mtk_charge_number1"></p>
    </div>
    <div class="close_btn_qr"></div>
    <div class="confirmSubmit1"></div>
</div>

<!--模态框部分--结束-->

<!--兑换失败-->
<div class="popup dhsb displayno">
    <img class="nr-bg" src="https://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/gdyh/dhsb.png" alt="">
    <div class="searchLog"></div>
</div>

<script>
    var parameter;
    var goods_id;
    var goods_name;

    function isQQ(value) {
        return /^[1-9][0-9]{4,9}$/.test(value);
    }

    //立即兑换
    function rechargefun() {

        // 先判断一下是否有资格兑换
        $.ajax({
            type: "POST",
            url: "/api/is-permit",
            data: {
                'goods_id': goods_id

            },
            async: true,
            dataType: "json",
            xhrFields: {
                withCredentials: true
            },
            success: function (res, status, xhr) {
                if (res.code == '200') {
                    // jump('/detail.html?id=' + id + '&proj=' + GetQueryString('proj'));
                    if (goods_name.indexOf('QQ音乐') >= 0) {
                        $('.popup-fp1').removeClass('displayno1');
                        $('.popup-qr1').addClass('displayno1');
                    } else {
                        $('.popup-fp').removeClass('displayno');
                        $('.popup-qr').addClass('displayno');
                    }
                } else if (res.code == '3020') {
                    toastinfo('您已经领取过奖品了，请查看兑换记录。');
                } else {
                    toastinfo(res.msg);
                }
            },
            error: function (error, xhr, abort) {
                toastinfo("网络错误，请稍后再试！");
            }
        })
    }

    //手机号
    $('.confirmInfo').on('touchstart', function () {
        var phoneNum = $('.covertCode').val();
        if (!isPhoneNumber(phoneNum)) {
            return toastinfo('请输入正确的充值手机号！');
        }
        if ($('.confirmInfo').hasClass('noClick')) {
            return;
        }
        $('.popup-fp').addClass('displayno');
        $('.popup-qr').removeClass('displayno');
        $('.mtk_goods_name').html(goods_name)
        $('.mtk_charge_number').html(phoneNum)
    })
    // QQ号 确认
    $('.confirmInfo1').on('touchstart', function () {
        var qqNum = $('.covertCodeQq').val();
        if (!isQQ(qqNum)) {
            return toastinfo('请输入正确的充值QQ号！');
        }
        if ($('.confirmInfo1').hasClass('noClick')) {
            return;
        }
        $('.popup-fp1').addClass('displayno1');
        $('.popup-qr1').removeClass('displayno1');
        $('.mtk_goods_name1').html(goods_name)
        $('.mtk_charge_number1').html(qqNum)
    })

    //点击弹窗中的确认
    // 手机号充值
    $('.confirmSubmit').on('touchstart', function () {
        var phoneNum = $('.covertCode').val();
        if (!isPhoneNumber(phoneNum)) {
            return toastinfo('请输入正确的充值手机号！');
        }
        $('.popup-qr').addClass('displayno');
        startSubmit(phoneNum);
    })
    // QQ号充值
    $('.confirmSubmit1').on('touchstart', function () {
        var qqNum = $('.covertCodeQq').val();
        if (!isQQ(qqNum)) {
            return toastinfo('请输入正确的QQ号！');
        }
        $('.popup-qr1').addClass('displayno1');
        startSubmit(qqNum);
    })

    // 提交订单
    function startSubmit(charge_account) {
        $.ajax({
            type: "POST",
            url: "/api/order",
            data: {
                "goods_id": goods_id,
                "charge_account": charge_account,
            },
            async: true,
            dataType: "json",
            xhrFields: {
                withCredentials: true
            },
            success: function (res, status, xhr) {
                // $('.headConBtn').removeClass('noClick');
                if (res.code == '200') {
                    window.location.href = './success.html'
                    $('.popup-fp').addClass('displayno');
                    $('.popup-qr').addClass('displayno');
                } else {
                    console.log(res)
                    if (res.code == '3020') {
                        //隐藏兑换失败弹窗
                        $('.dhsb').removeClass('displayno');
                    } else {
                        toastinfo(res.msg);
                    }
                }
            },
            error: function (error, xhr, abort) {
                $('.headConBtn').removeClass('noClick');
                toastinfo("网络错误，请稍后再试！");
            }
        })
    }


    //获取详情
    function getdetail() {
        var id = GetQueryString('id');
        if (id == "") {
            toastinfo('参数不完整');
            return;
        }
        $.ajax({
            type: "POST",
            url: "/api/goods-detail",
            data: {
                'goods_id': id,
            },
            async: true,
            dataType: "json",
            xhrFields: {
                withCredentials: true
            },
            success: function (data, status, xhr) {
                if (data.code == '200') {
                    $('.title')[0].innerText = data.data.goods_name;
                    $('.subtitle')[0].innerText = data.data.goods_attr;
                    //商品详情
                    $('.content')[0].innerHTML = data.data.goods_desc;
                    //商品参数、购物须知
                    $('.tab1')[0].innerHTML = data.data.goods_params;
                    $('.tab2')[0].innerHTML = data.data.goods_instr;

                    //轮播图 goods_imgs
                    var vm = new Vue({
                        el: '#swiper',
                        data: {
                            items: data.data.goods_imgs
                        }
                    });
                    goods_id = GetQueryString('id');
                    goods_name = data.data.goods_name;
                    parameter = '?id=' + GetQueryString('id') + '&type=' + data.data.goods_type + '&name=' + data.data.goods_name + '&attr=' + data.data.goods_attr + '&proj=' + GetQueryString('proj');
                } else {
                    toastinfo(data.msg);
                }
            },
            error: function (error, xhr, abort) {
                toastinfo("网络错误，请稍后再试！");
            }
        })
    }

    getdetail();

    // 查看记录

    $('.searchLog').on('touchstart click', function () {
        $('.dhsb').addClass('displayno')
        jump('./myorder.html?_r=1128&proj=' + GetQueryString('proj'));
    })

    // 关闭模态框
    $('.close_btn').on('touchstart click', function () {
        $('.popup-fp').addClass('displayno');
        $('.popup-fp1').addClass('displayno1');
        // $('dhsb').addClass('displayno')
    })
    $('.close_btn_qr').on('touchstart click', function () {
        $('.popup-qr').addClass('displayno');
        $('.popup-qr1').addClass('displayno1');
    })

    // 轮播图
    var mySwiper = new Swiper('.swiper-container', {
        speed: 2000,
        pagination: {
            el: '.swiper-pagination',
        },
        loop: true,
        on: {
            slideChangeTransitionEnd: function () {
                console.log(this.activeIndex); //切换结束时，告诉我现在是第几个slide

                selindex = this.activeIndex;
                if (this.activeIndex == 5) {
                    selindex = 1;
                } else if (this.activeIndex == 0) {
                    selindex = 4;
                }

            },
            onTouchMove: function () {
                console.log('onTouchMove');
            }
        },
    })

    //tab切换
    var index;
    $(document).on('touchstart click', '.nav li p', function (index) {
        $(this).addClass('on').siblings().removeClass('on');
        index = $(this).index();
        for (var i = 0; i < 3; i++) {
            $('.tab')[i].classList.add('hide');
        }
        var curtab = '.tab' + index;
        $(curtab)[0].classList.remove('hide');
        // $(".main .details").eq(index).show().siblings().hide();
        $('body').scrollTop(0);
    });
</script>
</body>
</html>
