<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>我的订单</title>
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/bootstrap.min.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/icbc.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/common.css">
    <link rel="stylesheet" type="text/css" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/myorder.css">

    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/jquery-3.1.0.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/bootstrap.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/popper.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/common.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/vue.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/jquery.bpopup.js"></script>
    <!--访问统计-->
    <script>
        var _hmt = _hmt || [];
        (function () {
            var hm = document.createElement("script");
            hm.src = "https://hm.baidu.com/hm.js?f43c7827f4ea614a1ef838902bd1feff";
            var s = document.getElementsByTagName("script")[0];
            s.parentNode.insertBefore(hm, s);
        })();
    </script>
</head>
<body>
<div class="container">
    <ul id="list" v-cloak>
        <li v-for="item in items">
            <p class="order">订单号：<span class="orderno">{{item.order_no}}</span></p>
            <div class="sepline"></div>
            <img :src='item.goods_imgs' class="goodimg">
            <p class="title"><span class="goodname">{{item.goods_name}}</span></p>
            <div class="lightline"></div>
            <p class="info">订单时间：<span class="right">{{item.order_time}}</span></p>
            <div class="lightline"></div>
            <p class="info">状态：<span class="right">{{item.status}}</span></p>
            <p class="info" v-if="item.exchange_code">兑换码：<span class="right">{{item.exchange_code}}</span></p>
            <p class="info" v-for="(o_val, o_i) in item.show">{{o_val.title}}：<span class="right">{{o_val.text}}</span>
            </p>
            <div v-for="(s_val, s_i) in item.order_subs">
                <div class="lightline" v-if="s_val.show && s_val.show.length>0"></div>
                <p class="info" v-for="(ss_val, ss_i) in s_val.show">{{ss_val.title}}：<span class="right">{{ss_val.text}}</span></p>
            </div>
        </li>
    </ul>
</div>
<div class="noData displayno">暂无兑换记录</div>


<!-- toast -->
<div id="m-toast-pop" class="m-toast-pop">
    <div class="m-toast-inner">
        <div class="m-toast-inner-text" id="m-toast-inner-text"></div>
    </div>
</div>

<!-- 提示弹框 -->
<div class="alert">
    <p class="title"></p>
    <div class="closebtn b-close"></div>
</div>
<script>

    function getorder() {
        $.ajax({
            type: "POST",
            url: "/api/order-record",
            data: {},
            async: true,
            dataType: "json",
            xhrFields: {
                withCredentials: true
            },
            success: function (data, status, xhr) {
                if (data.code == '200') {
                    var localdata = data.data;
                    for (var i = 0; i < localdata.length; i++) {
                        if (localdata[i].goods_type == 1) {
                            localdata[i].show = [
                                {title: "收货人", text: data.data[i].consignee_name},
                                {title: "收货人手机号", text: data.data[i].consignee_phone},
                                {title: "收货人地址", text: data.data[i].consignee_address},
                            ];

                        } else if (localdata[i].goods_type == 2) {
                            localdata[i].show = [];
                        } else if (localdata[i].goods_type == 3) {
                            localdata[i].show = [
                                {title: "充值号码", text: data.data[i].charge_account}
                            ];

                        } else if (localdata[i].goods_type == 6) {
                            localdata[i].show = [
                                {title: "服务内容", text: data.data[i].goods_name},
                                {title: "预约日期", text: data.data[i].service_date + ' ' + data.data[i].service_time},
                                {
                                    title: "联系方式",
                                    text: data.data[i].consignee_name + ' - ' + data.data[i].consignee_phone
                                },
                                {title: "联系地址", text: data.data[i].consignee_address},
                            ];
                        }

                        for (var j = 0; j < localdata[i].order_subs.length; j++) {

                            var sub_info = localdata[i].order_subs[j];
                            var show = [];
                            if (sub_info.goods_type == 1) {
                                localdata[i].order_subs[j].show = show.concat([
                                    {title: "物流信息", text: sub_info.logistics_sn + ' ' + sub_info.logistics_company},
                                ]);

                            } else if (sub_info.goods_type == 2) {
                                if (localdata[i].order_subs.length > 1 && localdata[i].goods_no != sub_info.goods_no) {
                                    show = [{title: "商品", text: sub_info.goods_name}];
                                }
                                localdata[i].order_subs[j].show = show.concat([
                                    {title: "卡号", text: sub_info.sequence_no},
                                    {title: "密码", text: sub_info.activation_code},
                                    {title: "有效期", text: sub_info.endtime},
                                ]);

                            } else if (sub_info.goods_type == 3) {
                                // localdata[i].order_subs[j].show = show.concat([
                                //     {title: "充值号码", text: sub_info.charge_account}
                                // ]);

                            } else if (sub_info.goods_type == 6) {
                                // localdata[i].order_subs[j].show = show.concat([
                                //     {title: "服务内容", text: sub_info.goods_name},
                                //     {title: "预约日期", text: sub_info.service_date + ' ' + sub_info.service_time},
                                //     {
                                //         title: "联系方式",
                                //         text: sub_info.consignee_name + ' - ' + sub_info.consignee_phone
                                //     },
                                //     {title: "联系地址", text: sub_info.consignee_address},
                                // ]);
                            }
                        }
                    }

                    var vm = new Vue({
                        el: '#list',
                        data: {
                            items: localdata
                        }
                    });
                } else if (data.code == '201' || data.code == '3018') {
                    toastinfo("未登录！");
                    setTimeout(function () {
                        window.location.href = 'index.html';
                    }, 1000);
                } else {
                    $('.noData').removeClass('displayno');
                    toastinfo("暂无记录");
                    return;
                }
            },
            error: function (error, xhr, abort) {
                toastinfo("网络错误，请稍后再试！");
            }
        })
    }

    getorder();
</script>
</body>
</html>
