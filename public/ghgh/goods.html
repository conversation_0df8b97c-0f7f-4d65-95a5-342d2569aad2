<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>商品列表</title>
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <!--    <link rel="stylesheet" href="../css/bootstrap.min.css">-->
    <!--    <link rel="stylesheet" href="../css/swiper.min.css">-->
    <!--    <link rel="stylesheet" href="../css/icbc.css">-->
    <!--    <link rel="stylesheet" href="../css/common.css">-->
    <!--    <link rel="stylesheet" href="../css/style.css">-->
    <!--    <script src="../js/jquery-3.1.0.min.js"></script>-->
    <!--    <script src="../js/bootstrap.min.js"></script>-->
    <!--    <script src="../js/swiper.min.js"></script>-->
    <!--    <script src="../js/popper.min.js"></script>-->
    <!--    <script src="../js/common.js"></script>-->
    <!--    <script src="../js/vue.min.js"></script>-->
    <!--    <script src="../js/jquery.bpopup.js"></script>-->
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/bootstrap.min.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/swiper.min.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/icbc.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/common.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/style.css">
        <link rel="stylesheet" type="text/css"
              href="https://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/gdyh/goods.css">

    <link rel="stylesheet" type="text/css" href="../css/gdyh/goods.css?r=123">
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/jquery-3.1.0.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/bootstrap.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/swiper.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/popper.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/common.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/vue.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/jquery.bpopup.js"></script>
    <style>
        .container {
            padding-bottom: 1rem;
            /*float: left;*/
        }
    </style>
    <!--访问统计-->
    <script>
        var _hmt = _hmt || [];
        (function () {
            var hm = document.createElement("script");
            hm.src = "https://hm.baidu.com/hm.js?f43c7827f4ea614a1ef838902bd1feff";
            var s = document.getElementsByTagName("script")[0];
            s.parentNode.insertBefore(hm, s);
        })();
    </script>
</head>

<body>
<div class="container">
    <div class="contbox">
        <div class="cm-padded-10 cm-font-size-16">
            <img class="choice_one" src="https://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/gdyh/choice.png" alt=""></img>
        </div>
        <div class="list-box" id="app">
            <div class="list-li" v-for="(item, key) in goodsList" :key="key" @click="openDetail(item.id)">
                <div class="img-box cm-f-c-c">
                    <img :src="item.goods_show_img" class="goods-img" alt="">
                </div>
            </div>
        </div>
    </div>
</div>
<div class="dhjl" onClick="openWin()">兑换记录</div>
<div class="dhjl" style="bottom: 3rem;" id="tel">客服咨询</div>

<!-- toast -->
<div id="m-toast-pop" class="m-toast-pop">
    <div class="m-toast-inner">
        <div class="m-toast-inner-text" id="m-toast-inner-text"></div>
    </div>
</div>
<script>
    var vueData = {
        goodsList: [],
        goodsRow: {
            goods_name: '',
            id: ''
        }
    }

    function openWin() {
        jump('./myorder.html?_r=1128&proj=' + GetQueryString('proj'));
    }

    $("#tel").click(function () {
        window.location.href = "tel:4006506888";
    });

    var vm = new Vue({
        el: '#app',
        data: vueData,
        mounted: function () {

        },
        methods: {
            openDetail: function (id) {
                jump('detail.html?id=' + id + '&proj=' + GetQueryString('proj'));
            },
        }
    });
    (function () {
        $.ajax({
            type: "POST",
            url: "/api/goods-list",
            async: true,
            dataType: "json",
            xhrFields: {
                withCredentials: true
            },
            success: function (data, status, xhr) {
                if (data.code == '200') {
                    vueData.goodsList = data.data['1']['1']
                    console.log(vueData.goodsList)
                } else if (data.code == '201' || data.code == '3018') {
                    toastinfo("未登录！");
                    setTimeout(function () {
                        window.location.href = 'index.html';
                    }, 1000);
                } else {
                    toastinfo(data.msg);
                }
            },
            error: function (error, xhr, abort) {
                toastinfo("网络错误，请稍后再试！");
            }
        })
    })();
</script>
</body>

</html>
