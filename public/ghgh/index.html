<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>员工权益兑换</title>
  <meta name="viewport"
        content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
  <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/swiper.min.css">
  <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/common.css">
  <link rel="stylesheet" type="text/css" href="../css/ghgh/style.css">
  <link rel="stylesheet" type="text/css" href="../css/ghgh/container.css">
  <script src="../js/flex.js"></script>
  <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/jquery-3.1.0.min.js"></script>
  <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/bootstrap.min.js"></script>
  <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/swiper.min.js"></script>
  <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/popper.min.js"></script>
  <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/common.js"></script>
  <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/vue.min.js"></script>
  <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/jquery.bpopup.js"></script>
  <style>
    /*toast*/
    .m-toast-pop {
      display: none;
      position: fixed;
      width: 100%;
      top: 0;
      bottom: 0;
      right: 0;
      overflow: auto;
      text-align: center;
    }

    .m-toast-inner {
      position: absolute;
      left: 50%;
      top: 45%;
      width: 100%;
      transform: translate(-50%, -50%);
      -webkit-transform: translate(-50%, -50%);
      text-align: center;
    }

    .m-toast-inner-text {
      display: inline-block;
      margin: 0 22px;
      padding: 19px 21px;
      font-size: 16px;
      color: #FFFFFF;
      letter-spacing: 0;
      line-height: 20px;
      background: rgba(0, 0, 0, 0.72);
      border-radius: 10px;
    }
  </style>
</head>
<body>
<div class="container">
  <div class="swiper-container">
    <div class="swiper-wrapper">
      <div class="swiper-slide"><img src="../images/ghgh/bg0.jpg" alt=""></div>
<!--      <div class="swiper-slide"><img src="../images/ghgh/bg5.png" alt=""></div>-->
<!--      <div class="swiper-slide"><img src="../images/ghgh/bg6.png" alt=""></div>-->
<!--      <div class="swiper-slide"><img src="../images/ghgh/bg7.png" alt=""></div>-->
<!--      <div class="swiper-slide"><img src="../images/ghgh/bg8.png" alt=""></div>-->
    </div>
  </div>
  <div class="wrapper" style="z-index: 999;">
    <img src="../images/ghgh/input2.png" alt="">
    <div class="wrapper_input">
      <input type="text" maxlength="11" class="wrapper_input_content phone" placeholder="请填写您的手机号">
    </div>
    <div class="wrapper_input">
      <input type="text" maxlength="6" class="wrapper_input_content phoneCode" placeholder="请填写短信验证码">
      <div class="codeBtn">获取验证码</div>
    </div>
    <div class="login headConBtn"></div>

    <img src="../images/ghgh/dhsm1.png" alt="" class="dhsm">
  </div>
</div>
<div id="m-toast-pop" style="z-index:999;" class="m-toast-pop">
  <div class="m-toast-inner">
    <div class="m-toast-inner-text" id="m-toast-inner-text"></div>
  </div>
</div>
<script>
  (function () {
    var ghintroduce = {
      goodsId: '',
      goodsName: '',
      init: function () {
        this.bind();
      },
      bind: function () {
        $.ajax({
          type: "POST",
          url: "/api/is-login",
          async: true,
          xhrFields: {
            withCredentials: true
          },
          success: function (res, status, xhr) {
            if (res.code == '200') {
              window.location.href = './goods.html';
            }
          },
          error: function (error, xhr, abort) {
          }
        })

        //点击获取验证码校验
        $('.codeBtn').on('click', function () {
          var phoneNum = $('.phone').val();
          //首先校验手机号
          if (isPhoneNumber(phoneNum)) {
            $.ajax({//发送验证码
              type: "POST",
              url: "/api/captcha",
              data: {
                'mobile': phoneNum,
                "act": 'WdabCkh6d77ydLrESy3Qgw==',
              },
              async: true,
              dataType: "json",
              xhrFields: {
                withCredentials: true
              },
              success: function (res, status, xhr) {
                console.log(res)
                if (res.code == '200') {
                  // $('.codeBtn').addClass('noClick');
                  // $(".codeBtn").html(60);
                  ghintroduce.countDown(60)
                } else {
                  toastinfo(res.msg);
                }
              },
              error: function (error, xhr, abort) {
                toastinfo("网络错误，请稍后再试！");
              }
            })

          } else {
            toastinfo('请填写正确的手机号！')
          }
        })
        //点击登录按钮
        $('.headConBtn').on('click', function () {
          var phoneNum = $('.phone').val();
          var phoneCode = $('.phoneCode').val();

          if (!isPhoneNumber(phoneNum)) {
            return toastinfo('请填写正确的手机号！');
          }
          if (phoneCode == '') {
            return toastinfo('请输入短信验证码！');
          }

          if ($('.headConBtn').hasClass('noClick')) {
            return;
          }
          $('.headConBtn').addClass('noClick');
          $.ajax({
            type: "POST",
            url: "/api/login",
            data: {
              "sms_captcha": phoneCode,
              "mobile": phoneNum,
              "act": 'WdabCkh6d77ydLrESy3Qgw==',

            },
            async: true,
            dataType: "json",
            xhrFields: {
              withCredentials: true
            },
            success: function (res, status, xhr) {
              $('.headConBtn').removeClass('noClick');
              console.log(res)
              if (res.code == '200') {
                //登录成功后跳转到商品页面
                window.location.href = './goods.html';
              } else {
                toastinfo(res.msg);
              }
            },
            error: function (error, xhr, abort) {
              $('.headConBtn').removeClass('noClick');
              toastinfo("网络错误，请稍后再试！");
            }
          })


        });

      },
      countDown: function (timeNum) {
        if ($(".codeBtn").hasClass("noClick")) {
          return;
        }
        $('.codeBtn').addClass('noClick');
        var time;
        var num = timeNum;
        time = setInterval(function () {
          num = num - 1;
          if (num <= 0) {
            num = timeNum;
            $(".codeBtn").html('获取验证码');
            $('.codeBtn').removeClass('noClick');
            clearInterval(time);
            return false;
          } else {
            $('.codeBtn').html(num);
          }
        }, 1000)
      }

    }
    ghintroduce.init();
  })()

  // var selindex = 0;
  // var mySwiper = new Swiper('.swiper-container', {
  //   // speed: 20,
  //   loop: true,
  //   autoplay: {
  //     disableOnInteraction: false,
  //     delay: 5000
  //   }
  // })

</script>
</body>
</html>
