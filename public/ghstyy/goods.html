<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>商品列表</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/swiper.min.css">
    <link rel="stylesheet" href="../css/icbc.css">
    <link rel="stylesheet" href="../css/common.css">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" type="text/css" href="../css/ghstyy/goods.css">

    <script src="../js/jquery-3.1.0.min.js"></script>
    <script src="../js/bootstrap.min.js"></script>
    <script src="../js/swiper.min.js"></script>
    <script src="../js/popper.min.js"></script>
    <script src="../js/common.js"></script>
    <script src="../js/vue.min.js"></script>
    <script src="../js/jquery.bpopup.js"></script>
    <style>
        .container {
            padding-bottom: 1rem;
            float: left;
        }
    </style>
</head>

<body>
<div class="container">
    <div class="contbox">
        <div class="cm-padded-10 cm-font-size-16">
            <p class="cm-font-size-15">以下会员您可任意1个兑换</p>
        </div>
        <div class="list-box" id="app">
            <div class="list-li" v-for="(item, key) in goodsList" :key="key" @click="openDetail(item)">
                <div class="img-box cm-f-c-c">
                    <img :src="item.goods_show_img" class="goods-img" alt="">
                </div>
                <!--<div class="goods-name">{{item.goods_name}}</div>-->
                <!--<div class="goods-desc"></div>-->

            </div>
        </div>
        <div class="title-wrap">
            <img class="img2" src="../images/ghstyy/hyqyjs.png" alt="">
        </div>

    </div>
</div>

<!-- toast -->
<div id="m-toast-pop" class="m-toast-pop">
    <div class="m-toast-inner">
        <div class="m-toast-inner-text" id="m-toast-inner-text"></div>
    </div>
</div>

<!-- 填写手机号模态框 -->
<div class="popup popup-fp displayno">
    <div class="popupCon-xy popupConStyle">
        <img class="nr-bg" src="../images/ghstyy/nr_bg.png" alt="">
        <img src="../images/ghstyy/close.png" alt="" class="closeBtn-xy">
        <div class="headConUl">
            <input type="text" class="covertCode" placeholder="请填写您的充值手机号">
            <div class="headConBtn confirmPhone">确定</div>
            <div class="hr"></div>
            <div class="title-wrap">
                <span class="line-hr"></span>
                <span class="txt">温馨提示</span>
                <span class="line-hr"></span>
            </div>
            <p class="sp-cont">
                本产品为直充产品,兑换成功后将自动充值至您所填写的账号中,请仔细核对您所填写的充值账号是否正确,避免错冲。</p>
        </div>
    </div>
</div>
<!--填写QQ号模态框-->
<div class="popup popup-fp1 displayno1">
    <div class="popupCon-xy popupConStyle">
        <img class="nr-bg" src="../images/ghstyy/nr_bg.png" alt="">
        <img src="../images/ghstyy/close.png" alt="" class="closeBtn-xy1">
        <div class="headConUl">
            <input type="text" class="covertCode1" placeholder="请填写您的充值QQ号">
            <div class="headConBtn confirmPhone1">确定</div>
            <div class="hr"></div>
            <div class="title-wrap">
                <span class="line-hr"></span>
                <span class="txt">温馨提示</span>
                <span class="line-hr"></span>
            </div>
            <p class="sp-cont">
                本产品为直充产品,兑换成功后将自动充值至您所填写的账号中,请仔细核对您所填写的充值账号是否正确,避免错冲。</p>
        </div>
    </div>
</div>

<!-- 确认信息模态框 -->
<div class="popup popup-qr displayno">
    <div class="popupCon-xy popupConStyle">
        <img class="nr-bg" src="../images/ghstyy/qr_bg.png" alt="">
        <img src="../images/ghstyy/close.png" alt="" class="closeBtn-xy">
        <div class="headConUl">
            <p class="c-title">请确认您的充值信息</p>
            <p class="c-goods">
                <span>兑换产品：</span>
                <span class="g-name"></span>
            </p>
            <p class="c-goods">
                <span>充值账号：</span>
                <span class="g-phone"></span>
            </p>
            <div class="hr2"></div>
            <div class="title-wrap title-wrap-sp">
                <span class="line-hr"></span>
                <span class="txt">温馨提示</span>
                <span class="line-hr"></span>
            </div>
            <p class="sp-cont">
                本产品为直充产品,兑换成功后将自动充值至您所填写的账号中,请仔细核对您所填写的充值账号是否正确,避免错冲。</p>
            <div class="headConBtn confirmSubmit">确定</div>
        </div>
    </div>
</div>

<!-- QQ部分-->
<div class="popup popup-qr1 displayno1">
    <div class="popupCon-xy popupConStyle">
        <img class="nr-bg" src="../images/ghstyy/qr_bg.png" alt="">
        <img src="../images/ghstyy/close.png" alt="" class="closeBtn-xy1">
        <div class="headConUl">
            <p class="c-title">请确认您的充值信息</p>
            <p class="c-goods">
                <span>兑换产品：</span>
                <span class="g-name"></span>
            </p>
            <p class="c-goods">
                <span>充值账号：</span>
                <span class="g-phone"></span>
            </p>
            <div class="hr2"></div>
            <div class="title-wrap title-wrap-sp">
                <span class="line-hr"></span>
                <span class="txt">温馨提示</span>
                <span class="line-hr"></span>
            </div>
            <p class="sp-cont">
                本产品为直充产品,兑换成功后将自动充值至您所填写的账号中,请仔细核对您所填写的充值账号是否正确,避免错冲。</p>
            <div class="headConBtn confirmSubmit1">确定</div>
        </div>
    </div>
</div>


<script>
    var vueData = {
        goodsList: [],
        goodsRow: {
            goods_name: '',
            id: ''
        }
    }

    var vm = new Vue({
        el: '#app',
        data: vueData,
        mounted: function () {

        },
        methods: {
            openDetail: function (row) {
                vueData.goodsRow = row;
                // 商品id 214 表示QQ产品  QQ产品充值需要用QQ号
                if (row.id == 214) {
                    $('.popup-fp1').removeClass('displayno1');
                } else {
                    $('.popup-fp').removeClass('displayno');
                }
            },
        }
    });
    var fn = function () {
        $.ajax({
            type: "POST",
            url: "/api/goods-list",
            async: true,
            dataType: "json",
            xhrFields: {
                withCredentials: true
            },
            success: function (res, status, xhr) {
                if (res.code == '200') {
                    vueData.goodsList = res.data['1']['1']
                    // vueData.goodsList = goodsList
                    console.log(vueData.goodsList)
                } else {
                    toastinfo(data.msg);
                }
            },
            error: function (error, xhr, abort) {
                toastinfo("网络错误，请稍后再试！");
            }
        })

        //点击确定手机号
        $('.confirmPhone').on('click', function () {
            var phonePopup = $('.covertCode').val();
            if (!isPhoneNumber(phonePopup)) {
                return toastinfo('请输入正确的手机号！');
            }
            $('.popup-fp').addClass('displayno');
            $('.popup-qr').removeClass('displayno');
            $('.g-name').html(vueData.goodsRow.goods_name);
            $('.g-phone').html(phonePopup);
        })

        //点击确定QQ号
        $('.confirmPhone1').on('click', function () {
            var phonePopup = $('.covertCode1').val();
            if (!isQQ(phonePopup)) {
                return toastinfo('请输入正确的QQ号！');
            }
            $('.popup-fp1').addClass('displayno1');
            $('.popup-qr1').removeClass('displayno1');
            $('.g-name').html(vueData.goodsRow.goods_name);
            $('.g-phone').html(phonePopup);
        })

        //点击提交
        $('.confirmSubmit').on('click', function () {
            var phoneNum = $('.covertCode').val();
            if (!isPhoneNumber(phoneNum)) {
                return toastinfo('请输入正确的充值手机号！');
            }
            if ($('.headConBtn').hasClass('noClick')) {
                return;
            }
            $('.headConBtn').addClass('noClick');
            $.ajax({
                type: "POST",
                url: "/api/order",
                data: {
                    "goods_id": vueData.goodsRow.id,
                    "charge_account": phoneNum,
                },
                async: true,
                dataType: "json",
                xhrFields: {
                    withCredentials: true
                },
                success: function (res, status, xhr) {
                    $('.headConBtn').removeClass('noClick');
                    if (res.code == '200') {
                        window.location.href = './success.html'
                        $('.popup-fp').addClass('displayno');
                        $('.popup-qr').addClass('displayno');
                    } else {
                        toastinfo(res.msg);
                    }
                },
                error: function (error, xhr, abort) {
                    $('.headConBtn').removeClass('noClick');
                    toastinfo("网络错误，请稍后再试！");
                }
            })
        })

        // QQ点击提交
        $('.confirmSubmit1').on('click', function () {
            var phoneNum = $('.covertCode1').val();
            if (!isQQ(phoneNum)) {
                return toastinfo('请输入正确的充值QQ号！');
            }
            if ($('.headConBtn').hasClass('noClick')) {
                return;
            }
            $('.headConBtn').addClass('noClick');
            $.ajax({
                type: "POST",
                url: "/api/order",
                data: {
                    "goods_id": vueData.goodsRow.id,
                    "charge_account": phoneNum,
                },
                async: true,
                dataType: "json",
                xhrFields: {
                    withCredentials: true
                },
                success: function (res, status, xhr) {
                    $('.headConBtn').removeClass('noClick');
                    if (res.code == '200') {
                        window.location.href = './success.html'
                        $('.popup-fp1').addClass('displayno1');
                        $('.popup-qr1').addClass('displayno1');
                    } else {
                        toastinfo(res.msg);
                    }
                },
                error: function (error, xhr, abort) {
                    $('.headConBtn').removeClass('noClick');
                    toastinfo("网络错误，请稍后再试！");
                }
            })
        })

        //点击关闭
        $('.closeBtn-xy').on('click', function () {
            $('.popup-fp').addClass('displayno');
            $('.popup-qr').addClass('displayno');
        })
        // QQ点击关闭
        $('.closeBtn-xy1').on('click', function () {
            $('.popup-fp1').addClass('displayno1');
            $('.popup-qr1').addClass('displayno1');
        })

        //会员权益说明
        $('.txt-und').on('click', function () {
            $('.popup-sp').removeClass('displayno');

        })

        //点击关闭
        $('.closeBtn-sp').on('click', function () {
            $('.popup-sp').addClass('displayno');
        });

    }();
</script>
</body>

</html>
