<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>天猫购物券兑换</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/bootstrap.min.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/swiper.min.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/icbc.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/common.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/style.css">
    <link rel="stylesheet" type="text/css" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/tm/index.css">

    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/jquery-3.1.0.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/bootstrap.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/swiper.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/popper.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/common.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/vue.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/jquery.bpopup.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/flex.js"></script>
</head>

<body>
<div class="container">
    <img class="headImg" src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/tm/tm.png" alt="">
    <div class="inputBox mt30">
        <p class="inputText">手机号</p>
        <input class="longInput taobaoId" type="text" placeholder="请输入与淘宝账号绑定的手机号码">
    </div>
    <div class="confirmBtn">确定</div>
    <div class="conBox">
        <div class="boxHeader">
            <img class="leftImg" src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/tm/left.png" alt="">
            <p class="conTitle">兑换说明</p>
            <img class="rightImg" src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/tm/right.png" alt="">
        </div>
        <p class="colorNum">1.</p>
        <p class="colorOne">持短信收到的天猫购物券兑换码，登陆短信中的天猫购物券兑换页面链接，填写与淘宝绑定的手机号，购物券直接充值至淘宝账号中。</p>
        <p class="colorNum">2.</p>
        <p class="colorOne">充值成功后，全部购物券30分钟内到账，请注意查看。</p>
        <p class="colorNum" style="color:#E60033;">3.</p>
        <p class="colorOne" style="color:#E60033;">每天登陆天猫购物券兑换页面最多可充值3次，次日可继续充值。</p>
    </div>
    <div class="conBox">
        <div class="boxHeader">
            <img class="leftImg" style="margin-left:1rem;" src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/tm/left.png" alt="">
            <p class="conTitle">有效期</p>
            <img class="rightImg" src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/tm/right.png" alt="">
        </div>
        <div class="conText">
<!--            <p class="colorOne ">天猫购物券自充值到账之日起29天内有效，超出有效期不予以退还，请在有效期内使用。</p>-->
            <p class="colorOne ">天猫购物券收到兑换短信后，请您在2个月内充值哦（具体有效期以实际收到的短信内容为准）。天猫购物券充值到账之日起29天内有效。超出有效期不予以退还，请在有效期内兑换和使用。</p>
        </div>
    </div>
    <div class="conBox">
        <div class="boxHeader">
            <img class="leftImg" style="margin-left:0.85rem;" src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/tm/left.png" alt="">
            <p class="conTitle">使用方法</p>
            <img class="rightImg" src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/tm/right.png" alt="">
        </div>
        <div class="conText">
            <p class="colorOne ">购物下单时，如账号有可用购物券，购物金额达到券码面值，则结算时自动勾选购物券抵扣现金。如本次购物不需要抵扣，请手动取消勾选。<br>
                购物券可用于单件商品的付款，也可用于多件商品的合并付款，同时支持在跨店铺订单中使用；同一个订单只能使用一张天猫购物券。
            </p>
        </div>
    </div>
    <div class="conBox">
        <div class="boxHeader">
            <img class="leftImg" style="margin-left:0.85rem;" src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/tm/left.png" alt="">
            <p class="conTitle">使用范围</p>
            <img class="rightImg" src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/tm/right.png" alt="">
        </div>
        <div class="conText">
            <p class="colorOne ">天猫购物券可用于在天猫商家店铺内购买商品（不支持购买黄金、虚拟产品、特殊类目/商品、天猫国际免税店商品等，具体使用范围以天猫公示为准）。
            </p>
        </div>
    </div>
    <div class="conBox">
        <div class="boxHeader">
            <img class="leftImg" style="margin-left:0.85rem;" src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/tm/left.png" alt="">
            <p class="conTitle">补充说明</p>
            <img class="rightImg" src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/tm/right.png" alt="">
        </div>
        <div class="conText">
            <p class="colorOne " style="color:#E60033;">
                问：使用购物券退换货会退券吗？
            </p>
            <p class="colorOne " style="margin-top:0rem;">
                答：使用天猫购物券购买天猫宝贝后，如在确认收货前发起退款，退款完成时购物券在有效期内，退款或交易关闭后将退还天猫购物券（部分退款，购物券会等比例退回，使用门槛和有效期不变），退款完成时购物券在有效期外，该部分购物券会为用户重新增加30天延展期，保证用户的使用感受（部分退款，购物券会等比例退回，使用门槛不变，延展30天）；如在确认收货后发起退款，购物券概不退回。
            </p>
            <p class="colorOne " style="color:#E60033;margin-top:0.3rem;">
                问：天猫购物券有使用门槛吗？
            </p>
            <p class="colorOne " style="margin-top:0rem;">
                答：在天猫平台消费满10.01元/20.01元/50.01元/100.01元/200.01元时可使用10元/20元/50元/100元/200元天猫购物券。
            </p>
            <p class="colorOne " style="color:#E60033;margin-top:0.3rem;">
                问：购物券过期了，还能使用吗？
            </p>
            <p class="colorOne " style="margin-top:0rem;">
                答：超出有效期不能使用，请在有效期内使用。

            </p>
            <p class="colorOne " style="color:#E60033;margin-top:0.3rem;">
                问：使用购物券支付可以开票吗？
            </p>
            <p class="colorOne " style="margin-top:0rem;">
                答：购物券支付的部分是不可以开票的，非购物券支付的部分可以开票。

            </p>
            <p class="colorOne " style="color:#E60033;margin-top:0.3rem;">
                问：天猫购物券可以转赠他人吗？
            </p>
            <p class="colorOne " style="margin-top:0rem;">
                答：天猫购物券并非现金形式，不可提现、不可转赠、不可抵扣虚拟产品或为他人付款。

            </p>
            <p class="colorOne " style="color:#E60033;margin-top:0.3rem;">
                问：一个订单可以使用多张购物券吗?
            </p>
            <p class="colorOne " style="margin-top:0rem;">
                答：同一个订单只能使用一张天猫购物券。

            </p>
            <p class="colorOne " style="color:#E60033;margin-top:0.3rem;">
                问：享受店铺优惠时还能使用天猫购物券吗？
            </p>
            <p class="colorOne " style="margin-top:0rem;">
                答：可以与商家单品优惠、店铺优惠、店铺/单品优惠券及红包叠加使用，付款时默认优先使用商家店铺/单品优惠券，如使用商家优惠后金额仍满足天猫购物券使用条件则可继续叠加使用天猫购物券。
            </p>
            <p class="colorOne " style="color:#E60033;margin-top:0.3rem;">
                问：天猫购物券在手机端和PC端有使用区别吗？
            </p>
            <p class="colorOne " style="margin-top:0rem;">
                答：天猫购物券在无线端（包括H5页面、PAD、手机、支付宝钱包）暂不支持先试后买及分期付款商品，PC端上购物券可在先试后买及分期付款商品订单中进行正常抵扣。如您需要购买先试后买及分期付款商品，请移驾PC完成下单支付，无线可支持时间详见规则更新。
            </p>
        </div>
    </div>
</div>

</div>
<img class="topImg displayNo" src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/tm/top.png" alt="">
<div class="popup displayNo">
    <div class="popupCon">
        <img class="closeImg" src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/cole.png" alt="">
        <p class="text1">请确认您的充值账号</p>
        <p class="text1">充值账号：<span class="tmNumber"></span></p>
        <p class="text2">请仔细核对您填写的充值账号是否正确，避免错充。</p>
        <div class="reset">修改</div>
        <div class="popupConfirm">确认</div>
    </div>
</div>
<!-- toast -->
<div id="m-toast-pop" style="z-index:9999;" class="m-toast-pop">
    <div class="m-toast-inner">
        <div class="m-toast-inner-text" id="m-toast-inner-text"></div>
    </div>
</div>

<script>
    (function () {
        var tmIndex = {
            init: function () {
                this.bind();
            },
            bind: function () {
                //监听页面滚动
                $(document).scroll(function () {
                    var scroH = $(document).scrollTop();
                    var viewH = $(window).height();
                    var contentH = $(document).height();
                    if (scroH > 300) {
                        $('.topImg').removeClass('displayNo')

                    }
                    if (scroH < 300) {
                        $('.topImg').addClass('displayNo')

                    }
                });
                //点击淘宝文字
                // $('.seeDetail').on('click', function () {
                //     $("html,body").animate({
                //         scrollTop: 400
                //     }, 500);
                // });
                //点击回到顶部按钮
                $('.topImg').on('click', function () {
                    $("html,body").animate({
                        scrollTop: 0
                    }, 500);
                });
                //点击弹窗的关闭按钮
                $('.closeImg').on('click', function () {
                    $('.popup').addClass('displayNo')
                });
                //点击修改按钮弹窗关闭
                $('.reset').on('click', function () {
                    $('.popup').addClass('displayNo')

                });
                //点击确定按钮
                $('.confirmBtn').on('click', function () {
                    var taobaoId = $('.taobaoId').val();
                    if (taobaoId == '' || !isPhoneNumber(taobaoId)) {
                        toastinfo('请输入与淘宝账号绑定的手机号码！');
                        return false;
                    }
                    $('.tmNumber').text(taobaoId);
                    $('.popup').removeClass('displayNo')
                });
                //点击弹窗的确定按钮
                $('.popupConfirm').on('click', function () {
                    if ($('.popupConfirm').hasClass('noClick')) {
                        return;
                    }
                    var goods_id = GetQueryString('goods_id');
                    var taobaoId = $('.taobaoId').val();
                    $('.popupConfirm').addClass('noClick');
                    $.ajax({//确定绑定
                        type: "POST",
                        url: "/api/order",
                        data: {
                            "goods_id": goods_id,
                            "charge_account": taobaoId,
                        },
                        async: true,
                        dataType: "json",
                        xhrFields: {
                            withCredentials: true
                        },
                        success: function (res, status, xhr) {
                            if (res.code == '200') {
                                $('.popup').addClass('displayNo');
                                $('.popupConfirm').removeClass('noClick');
                                window.location.href = './success.html'
                            } else if (res.code == '3030') {
                                //超限
                                $('.popupConfirm').removeClass('noClick');
                                $('.popup').addClass('displayNo');
                                window.location.href = './error.html?_v=1'//超限跳转
                            } else {
                                toastinfo(res.msg);
                            }
                        },
                        error: function (error, xhr, abort) {
                            $('.popupConfirm').removeClass('noClick');
                            window.location.href = './error.html';

                            toastinfo("网络错误，请稍后再试！");
                        }
                    })

                })
            }

        };
        tmIndex.init();

    })()

</script>
</body>

</html>
