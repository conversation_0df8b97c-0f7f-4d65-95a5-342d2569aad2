<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>权益兑换</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/bootstrap.min.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/swiper.min.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/icbc.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/common.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/style.css">
    <link rel="stylesheet" type="text/css" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/iqy/index.css">
    <link rel="stylesheet" type="text/css" href="../css/iqy/index.css">

    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/jquery-3.1.0.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/bootstrap.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/swiper.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/popper.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/common.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/vue.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/jquery.bpopup.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/flex.js"></script>
</head>

<body>
<div class="container">
    <img class="headImg" src="https://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/iqy/top.png" alt="">
    <div class="inputBox mt30">
        <p class="inputText">充值账号</p>
        <input class="longInput taobaoId" type="text" placeholder="请输入您的手机号">
    </div>
    <div class="confirmBtn">确定</div>
    <div class="conBox">
        <div class="boxHeader">
            <img class="leftImg" src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/iqy/left.png" alt="">
            <p class="conTitle">发货说明</p>
            <img class="rightImg" src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/iqy/right.png"
                 alt="">
        </div>
        <p class="colorNum">1、</p>
        <p class="colorOne">
            本商品发货方式为<span style="color:#03B84D">直充</span>，订单支付成功后将自动充值至您的账号中。</p>
        <p class="colorNum">2、</p>
        <p class="colorOne">
            下单时填写的收货手机号码作为爱奇艺个人账号，会员权益充值至下单时填写的收货手机号码账号中。</p>
        <p class="colorNum">3、</p>
        <p class="colorOne">
            如下单时填写的收货手机号码未注册爱奇艺账号，系统自动将该手机号创建为登陆账号，会员权益充值至该账户内。客户请使用下单时填写的手机号进行登陆，并选择短信登陆方式后设置密码。</p>
        <p class="colorNum">4、</p>
        <p class="colorOne">
            如果下单时填写的收货手机号码已经是爱奇艺账号，会员权益将充值至该账号中，会员期限在原账号基础上<span
            style="color:#03B84D">自动叠加延期30天。</span></p>
        <p class="colorNum">5、</p>
        <p class="colorOne">同一个爱奇艺账号同时购买多个产品，<span style="color:#03B84D">有效期可叠加</span>（例如同时激活两个爱奇艺会员月卡，会员有效期为从激活当天起两个月）。
        </p>
    </div>

    <div class="conBox">
        <div class="boxHeader">
            <img class="leftImg" src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/iqy/left.png" alt="">
            <p class="conTitle">温馨提示</p>
            <img class="rightImg" src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/iqy/right.png"
                 alt="">
        </div>
        <p class="colorNum">1、</p>
        <p class="colorOne"><span style="color:#03B84D">本商品不支持电视端</span>，只能在手机APP、电脑、Pad端使用，适用于爱奇艺中国大陆地区用户。
        </p>
        <p class="colorNum">2、</p>
        <p class="colorOne">1个VIP会员账号<span style="color:#03B84D">最多可以在5个设备上登录</span>，但<span
            style="color:#03B84D">同一时间段只支持在2个不同设备上使用</span>。</p>
        <p class="colorNum">3、</p>
        <p class="colorOne">
            <span style="color:#03B84D">本产品不支持个人购买后转售</span>，官方一旦判定用户使用违规，将收回其会员权益，被收回的会员权益无法找回。
        </p>
        <p class="colorNum">4、</p>
        <p class="colorOne">本商品属于虚拟商品，自动充值，一经充值成功无法退款。</p>
    </div>

    <div class="conBox">
        <div class="boxHeader">
            <img class="leftImg" src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/iqy/left.png" alt="">
            <p class="conTitle">会员权益</p>
            <img class="rightImg" src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/iqy/right.png"
                 alt="">
        </div>
        <div>
            <img class="listImg" src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/iqy/list.png" alt="">
        </div>
        <p class="pTitle">内容特权</p>
        <p class="pContent">1、院线新片：院线电影第一时间看。</p>
        <p class="pContent">2、海量高分大片：打造您的VP私家影院。</p>
        <p class="pContent">3、热剧抢先看；热门电视剧提前看。</p>
        <p class="pContent">4、新剧集提前看：做任务先看更多剧集。</p>
        <p class="pTitle">观影特权</p>
        <p class="pContent">1、专享广告特权：VIP手动跳过精品影视、福利权益等会员专属推荐内容。</p>
        <p class="pContent">2、蓝光1080P：VIP高清视觉体验。</p>
        <p class="pContent">3、杜比全景声：立体环绕声超级音质。</p>
        <p class="pContent">4、会员赠片：赠好友影片免费观看。</p>
        <p class="pTitle">身份特权</p>
        <p class="pContent">1、尊贵标识：VIP皇冠戴起来。</p>
        <p class="pContent">2、尊享皮肤：尊享移动客户端皮肤。</p>
        <p class="pContent">3、明星见面会：明星爱豆零距离接触。</p>
        <p class="pContent">4、参与综艺录制：参与录制综艺节目。</p>
        <p class="pTitle">生活特权</p>
        <p class="pContent">1、每日福利：VIP福利天天见。</p>
        <p class="pContent">2、生日礼包：VIP最贴心的生日礼物。</p>
        <p class="pContent">3、等级礼包：等级专属福利。</p>
    </div>

</div>

</div>
<img class="topImg displayNo" src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/tm/top.png" alt="">
<div class="popup displayNo">
    <div class="popupCon">
        <img class="closeImg" src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/cole.png" alt="">
        <p class="text1">请确认您的充值账号</p>
        <p class="text1">充值账号：<span class="tmNumber"></span></p>
        <p class="text2">请仔细核对您填写的充值账号是否正确，避免错充。</p>
        <div class="reset">修改</div>
        <div class="popupConfirm">确认</div>
    </div>
</div>
<!-- toast -->
<div id="m-toast-pop" style="z-index:9999;" class="m-toast-pop">
    <div class="m-toast-inner">
        <div class="m-toast-inner-text" id="m-toast-inner-text"></div>
    </div>
</div>

<script>
    (function () {
        var tmIndex = {
            init: function () {
                this.bind();
            },
            bind: function () {
                //监听页面滚动
                $(document).scroll(function () {
                    var scroH = $(document).scrollTop();
                    var viewH = $(window).height();
                    var contentH = $(document).height();
                    if (scroH > 300) {
                        $('.topImg').removeClass('displayNo')

                    }
                    if (scroH < 300) {
                        $('.topImg').addClass('displayNo')

                    }
                });
                //点击淘宝文字
                $('.seeDetail').on('click', function () {
                    $("html,body").animate({
                        scrollTop: 400
                    }, 500);
                });
                //点击回到顶部按钮
                $('.topImg').on('click', function () {
                    $("html,body").animate({
                        scrollTop: 0
                    }, 500);
                });
                //点击弹窗的关闭按钮
                $('.closeImg').on('click', function () {
                    $('.popup').addClass('displayNo')
                });
                //点击修改按钮弹窗关闭
                $('.reset').on('click', function () {
                    $('.popup').addClass('displayNo')

                });
                //点击确定按钮
                $('.confirmBtn').on('click', function () {
                    var taobaoId = $('.taobaoId').val();
                    if (taobaoId == '') {
                        toastinfo('请输入充值账号！');
                        return false;
                    }
                    $('.tmNumber').text(taobaoId);
                    $('.popup').removeClass('displayNo')
                });
                //点击弹窗的确定按钮
                $('.popupConfirm').on('click', function () {
                    if ($('.popupConfirm').hasClass('noClick')) {
                        return;
                    }
                    var goods_id = GetQueryString('goods_id');
                    var taobaoId = $('.taobaoId').val();
                    $('.popupConfirm').addClass('noClick');
                    $.ajax({//确定绑定
                        type: "POST",
                        url: "/api/order",
                        data: {
                            "goods_id": goods_id,
                            "charge_account": taobaoId,
                        },
                        async: true,
                        dataType: "json",
                        xhrFields: {
                            withCredentials: true
                        },
                        success: function (res, status, xhr) {
                            if (res.code == '200') {
                                $('.popup').addClass('displayNo');
                                $('.popupConfirm').removeClass('noClick');
                                window.location.href = './success.html'
                            } else if (res.code == '3030') {
                                //超限
                                $('.popupConfirm').removeClass('noClick');
                                $('.popup').addClass('displayNo');
                                window.location.href = './error.html?_v=1'//超限跳转
                            } else {
                                toastinfo(res.msg);
                            }
                        },
                        error: function (error, xhr, abort) {
                            $('.popupConfirm').removeClass('noClick');
                            window.location.href = './error.html';

                            toastinfo("网络错误，请稍后再试！");
                        }
                    })

                })
            }

        };
        tmIndex.init();

    })()

</script>
</body>

</html>
