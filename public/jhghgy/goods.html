<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>商品列表</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/swiper.min.css">
    <link rel="stylesheet" href="../css/icbc.css">
    <link rel="stylesheet" href="../css/common.css">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" type="text/css" href="../css/jhghgy/goods.css">

    <script src="../js/jquery-3.1.0.min.js"></script>
    <script src="../js/bootstrap.min.js"></script>
    <script src="../js/swiper.min.js"></script>
    <script src="../js/popper.min.js"></script>
    <script src="../js/common.js"></script>
    <script src="../js/vue.min.js"></script>
    <script src="../js/jquery.bpopup.js"></script>
    <style>
        .container {
            padding-bottom: 1rem;
            float: left;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="contbox">
            <div class="cm-padded-10 cm-font-size-16">
                <span class="cm-font-size-15">以下会员您可任意1个兑换</span>
            </div>
            <div class="list-box" id="app">
                <div class="list-li" v-for="(item, key) in goodsList" :key="key" @click="openDetail(item)">
                    <div class="img-box cm-f-c-c">
                        <img :src="item.goods_show_img" class="goods-img" alt="">
                    </div>
                    <div class="goods-name">{{item.goods_name}}</div>
                    <div class="goods-desc"></div>

                </div>
            </div>
            <div class="title-wrap">
                <span class="line-hr"></span>
                <span class="txt">会员权益介绍</span>
                <span class="line-hr"></span>
            </div>
            <ul class="sp-cont">
                <li>1. 以上会员适用于电视/电脑/手机/iPad等全部终端。</li>
                <li>2. 兑换时填写的充值手机号为您在该视频网站的注册账号。若该手机号未注册,则将自动以您填写的手机号为您注册会员。</li>
                <li>3. 如账号中已有会员权益且未到期,兑换成功后将在原有会员时长基础上自动叠加延期。</li>
                <li>4. 兑换成功后会员权益有效期为12个月。</li>
                <li>5. 更多详细会员权益介绍请以官方说明为准。</li>
            </ul>
            <div class="title-wrap1">
                <span class="line-sir">▷</span>
                <span class="txt-und">会员权益充值说明</span>
                <span class="line-sir">◁</span>
            </div>
        </div>
    </div>

    <!-- toast -->
    <div id="m-toast-pop" class="m-toast-pop">
        <div class="m-toast-inner">
            <div class="m-toast-inner-text" id="m-toast-inner-text"></div>
        </div>
    </div>

    <!-- 填写手机号模态框 -->
    <div class="popup popup-fp displayno">
        <div class="popupCon-xy popupConStyle">
            <img class="nr-bg" src="../images/nr_bg.png" alt="">
            <img src="../images/close.png" alt="" class="closeBtn-xy">
            <div class="headConUl">
                <input type="text" class="covertCode" placeholder="请填写您的充值手机号">
                <div class="headConBtn confirmPhone">确定</div>
                <div class="hr"></div>
                <div class="title-wrap">
                    <span class="line-hr"></span>
                    <span class="txt">温馨提示</span>
                    <span class="line-hr"></span>
                </div>
                <p class="sp-cont">本产品为直充产品,兑换成功后将自动充值至您所填写的账号中,请仔细核对您所填写的充值账号是否正确,避免错冲。</p>
            </div>
        </div>
    </div>

    <!-- 确认信息模态框 -->
    <div class="popup popup-qr displayno">
        <div class="popupCon-xy popupConStyle">
            <img class="nr-bg" src="../images/nr_bg.png" alt="">
            <img src="../images/close.png" alt="" class="closeBtn-xy">
            <div class="headConUl">
                <p class="c-title">请确认您的充值信息</p>
                <p class="c-goods">
                    <span>兑换产品：</span>
                    <span class="g-name"></span>
                </p>
                <p class="c-goods">
                    <span>充值账号：</span>
                    <span class="g-phone"></span>
                </p>
                <div class="headConBtn confirmSubmit">确定</div>
                <div class="hr"></div>
                <div class="title-wrap">
                    <span class="line-hr"></span>
                    <span class="txt">温馨提示</span>
                    <span class="line-hr"></span>
                </div>
                <p class="sp-cont">本产品为直充产品,兑换成功后将自动充值至您所填写的账号中,请仔细核对您所填写的充值账号是否正确,避免错冲。</p>
            </div>
        </div>
    </div>

    <!-- 会员权益说明模态框 -->
    <div class="popup popup-sp displayno">
        <div class="popupCon-sp popupConStyle">
            <img class="nr-bg" src="../images/nr_bg.png" alt="">
            <img src="../images/close.png" alt="" class="closeBtn-xy closeBtn-sp">
            <div class="headConUl">
                <div>
                    <div class="title-wrap" style="margin-top: 0;">
                        <span class="line-hr"></span>
                        <span class="txt">爰奇艺星钻VIP会员</span>
                        <span class="line-hr"></span>
                    </div>
                    <ul class="sp-cont">
                        <li>1. 作为爱奇艺个人条号，会员权益将充值至填写的收货手机号码账号中。</li>
                        <li>2. 如果下单时填写的手机号码已经是爱奇艺张号，会员权益将充值至该账号中，会员期限在原账号基础上自动 叠加延期。</li>
                        <li>3. 如条号中有黄金会员末到期且激活星钻会员，账号优先显示星钻会员权益，星钻会员到期后会继续显示账号中剩余的黄金会员。</li>
                        <li>4. 星钻会员条号同一时间不得超过2台设备，切勿分享他人以免造成封号/盗号</li>
                    </ul>
                </div>
                <div>
                    <div class="title-wrap">
                        <span class="line-hr"></span>
                        <span class="txt">腾讯视频超级影视VIP会员</span>
                        <span class="line-hr"></span>
                    </div>
                    <ul class="sp-cont">
                        <li>1. 充值账号仅支持手机号码。请填写腾讯视频超级影视账户绑定的手机号码。QQ号微信号不支持。</li>
                        <li>2.
                            若用手机号码成功购买开通腾讯视频超级影视会员后，该手机号码未绑定到腾讯视频超级影视会员，需要绑定后才能享受到腾讯视频超级影视会员特权。手机号码绑定腾讯视频方法：登录腾讯视频APP点击"个人中心"后下滑到底部，点击“个人信息"，绑定开通的手机号码。每个手机号码只能绑定一个腾讯视频账号。
                        </li>
                        <li>3. 如账号中有腌讯视频VIP会员未到期且充值超级影VIP会员，账号优先显示超级影视会员权益，超级影视会员到期后会继续显示账号中剩余的腾讯视频VIP会员。</li>
                    </ul>
                </div>
                <div>
                    <div class="title-wrap">
                        <span class="line-hr"></span>
                        <span class="txt">芒果TV全屏影视会员</span>
                        <span class="line-hr"></span>
                    </div>
                    <ul class="sp-cont">
                        <li>1. 充值条号为手机号，第三方条号（如QQ、微信、微博 等）不支持直充，第三方账号可登陆芒果APP个人中心 绑定手机号后再充值。</li>
                        <li>2. 充值前请确认您的电视机是否支持使用芒果TV全屏影视会员；如乐视智能电视等不支持，购买后若不支持，将无法退款退货。</li>
                    </ul>
                </div>
                <div>
                    <div class="title-wrap">
                        <span class="line-hr"></span>
                        <span class="txt">优酷酷喵VIP会员</span>
                        <span class="line-hr"></span>
                    </div>
                    <ul class="sp-cont">
                        <li>1. 只支持优酷注册账号充值，请填写手机号充值，昵称及其他合作账号不支持充值。</li>
                        <li>2. 如填写的手机号还末注册为优酷个人账号，系统将自动默认您下单时填写的手机号为优酷个人账号，请使用手机号+短信验证码登录/注册个人账号，即可查看会员直充到账情况。</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        var vueData = {
            goodsList: [
            // {
            //     "id": 20,
            //     "goods_show_img": "https://img.alicdn.com/tfs/TB12XVQr8Bh1e4jSZFhXXcC9VXa-640-212.png",
            //     "goods_name": "苏泊尔智能电饭煲",
            //     "goods_type": '1',
            //     "is_has_stock": 1 //暂不用这个，根据活动要求，有的活动没有。
            // }, {
            //     "id": 21,
            //     "goods_show_img": "https://img1.baidu.com/it/u=1927736179,2830069487&fm=26&fmt=auto",
            //     "goods_name": "苏泊尔智能电饭煲",
            //     "goods_type": '1',
            //     "is_has_stock": 1 //暂不用这个，根据活动要求，有的活动没有。
            // }, {
            //     "id": 22,
            //     "goods_show_img": "https://img.alicdn.com/tfs/TB12XVQr8Bh1e4jSZFhXXcC9VXa-640-212.png",
            //     "goods_name": "苏泊尔智能电饭煲",
            //     "goods_type": '1',
            //     "is_has_stock": 1 //暂不用这个，根据活动要求，有的活动没有。
            // }, {
            //     "id": 23,
            //     "goods_show_img": "https://img.alicdn.com/tfs/TB12XVQr8Bh1e4jSZFhXXcC9VXa-640-212.png",
            //     "goods_name": "苏泊尔智能电饭煲",
            //     "goods_type": '1',
            //     "is_has_stock": 1 //暂不用这个，根据活动要求，有的活动没有。
            // }
            ],
            goodsRow: {
                goods_name: '',
                id: ''
            }
        }

        var vm = new Vue({
            el: '#app',
            data: vueData,
            mounted: function () {

            },
            methods: {
                openDetail: function (row) {
                    vueData.goodsRow = row;
                    $('.popup-fp').removeClass('displayno');
                },
            }
        });
        var fn = function () {
            $.ajax({
                type: "POST",
                url: "/api/goods-list",
                async: true,
                dataType: "json",
                xhrFields: {
                    withCredentials: true
                },
                success: function (res, status, xhr) {
                    if (res.code == '200') {
                        vueData.goodsList = res.data['1']['1']
                        console.log(vueData.goodsList)
                    } else {
                        toastinfo(data.msg);
                    }
                },
                error: function (error, xhr, abort) {
                    toastinfo("网络错误，请稍后再试！");
                }
            })

            //点击确定手机号
            $('.confirmPhone').on('click', function () {
                var phonePopup = $('.covertCode').val();
                if (!isPhoneNumber(phonePopup)) {
                    return toastinfo('请输入正确的手机号！');
                }
                $('.popup-fp').addClass('displayno');
                $('.popup-qr').removeClass('displayno');
                $('.g-name').html(vueData.goodsRow.goods_name);
                $('.g-phone').html(phonePopup);
            })

            //点击提交
            $('.confirmSubmit').on('click', function () {
                var phoneNum = $('.covertCode').val();
                if (!isPhoneNumber(phoneNum)) {
                    return toastinfo('请输入正确的充值手机号！');
                }
                if ($('.headConBtn').hasClass('noClick')) {
                    return;
                }
                $('.headConBtn').addClass('noClick');
                $.ajax({
                    type: "POST",
                    url: "/api/order",
                    data: {
                        "goods_id": vueData.goodsRow.id,
                        "charge_account": phoneNum,
                    },
                    async: true,
                    dataType: "json",
                    xhrFields: {
                        withCredentials: true
                    },
                    success: function (res, status, xhr) {
                        $('.headConBtn').removeClass('noClick');
                        if (res.code == '200') {
                            window.location.href = './success.html'
                            $('.popup-fp').addClass('displayno');
                            $('.popup-qr').addClass('displayno');
                        } else {
                            toastinfo(res.msg);
                        }
                    },
                    error: function (error, xhr, abort) {
                        $('.headConBtn').removeClass('noClick');
                        toastinfo("网络错误，请稍后再试！");
                    }
                })
            })

            //点击关闭
            $('.closeBtn-xy').on('click', function () {
                $('.popup-fp').addClass('displayno');
                $('.popup-qr').addClass('displayno');
            })

            //会员权益说明
            $('.txt-und').on('click', function () {
                $('.popup-sp').removeClass('displayno');

            })

            //点击关闭
            $('.closeBtn-sp').on('click', function () {
                $('.popup-sp').addClass('displayno');
            })


        }();
    </script>
</body>

</html>
