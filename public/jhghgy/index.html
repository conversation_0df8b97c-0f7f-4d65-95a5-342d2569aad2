<!DOCTYPE html>
<html>

<head>
	<meta charset="utf-8">
	<title>观影活动权益兑换</title>
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
	<link rel="stylesheet" href="../css/bootstrap.min.css">
	<link rel="stylesheet" href="../css/swiper.min.css">
	<link rel="stylesheet" href="../css/icbc.css">
	<link rel="stylesheet" href="../css/common.css">
	<link rel="stylesheet" href="../css/style.css">
	<link rel="stylesheet" type="text/css" href="../css/jhghgy/aqyintroduce.css">

	<script src="../js/jquery-3.1.0.min.js"></script>
	<script src="../js/bootstrap.min.js"></script>
	<script src="../js/swiper.min.js"></script>
	<script src="../js/popper.min.js"></script>
	<script src="../js/common.js"></script>
	<script src="../js/vue.min.js"></script>
	<script src="../js/jquery.bpopup.js"></script>
	<script src="../js/flex.js"></script>
</head>

<body>
	<div class="container">
		<div class="headTitle1">员工</div>
		<p class="headTitle2">观影活动权益兑换</p>
		<p class="headTitle3">全屏视频会员VIP</p>
		<div class="tb-wrap">
			<img src="../images/tb_jh.png" alt="">
		</div>

		<div class="headCon">
			<img class="nr-bg" src="../images/nr_bg.png" alt="">
			<div class="headConUl">
				<p class="title">请填写您的兑换码</p>
				<input type="text" class="covertCode" placeholder="请填写兑换码">
				<div class="headConBtn">确定</div>
				<div class="hr"></div>
				<div class="title-wrap">
					<span class="line-hr"></span>
					<span class="txt">领取说明</span>
					<span class="line-hr"></span>
				</div>
				<ul class="sp-cont">
					<li>1. 请输入兑换码。</li>
					<li>2. 选择视频会员权益,填写充值账号。</li>
					<li>3. 兑换成功,会员权益自动充值至充值账号。</li>
					<li>4. 兑换码有效期为3年,兑换成功后会员权益有效期为12个月。</li>
				</ul>
			</div>

		</div>
		<p class="zx-t">任何使用问题请咨询客服电话</p>
		<p class="zx-t">400-8797-816</p>


	</div>

	<!-- toast -->
	<div id="m-toast-pop" style="z-index:9999;" class="m-toast-pop">
		<div class="m-toast-inner">
			<div class="m-toast-inner-text" id="m-toast-inner-text"></div>
		</div>
	</div>

	<script>
		(function () {
			var aqyintroduce = {
				goodsId: '',
				goodsName: '',
				init: function () {
					this.bind();
				},
				bind: function () {
					//点击兑换按钮
					$('.headConBtn').on('click', function () {
						var code = $('.covertCode').val();
						if (code == '') {
							return toastinfo('请输入兑换码！');
						}

						if ($('.headConBtn').hasClass('noClick')) {
							return;
						}
						$('.headConBtn').addClass('noClick');
						$.ajax({
							type: "POST",
							url: "/api/login",
							data: {
								"card_pwd": code,
								"act": 'oHZS/fsj1XNCu5pHNAz6iw==',

							},
							async: true,
							dataType: "json",
							xhrFields: {
								withCredentials: true
							},
							success: function (res, status, xhr) {
								$('.headConBtn').removeClass('noClick');
								if (res.code == '200') {
									window.location.href = res.data.rdr_url
								} else {
									toastinfo(res.msg);
								}
							},
							error: function (error, xhr, abort) {
								$('.headConBtn').removeClass('noClick');
								toastinfo("网络错误，请稍后再试！");
							}
						})


					})

				}
			}
			aqyintroduce.init();


		})()
	</script>
</body>

</html>
