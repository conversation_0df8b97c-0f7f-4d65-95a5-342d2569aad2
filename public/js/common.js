function GetQueryString(name) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
    var r = window.location.search.substr(1).match(reg);
    if (r != null) return decodeURIComponent(r[2]);
    return null;
}

//toast
function toastinfo(message) {
    $('#m-toast-inner-text').text(message);
    $('#m-toast-pop').fadeIn();
    setTimeout(function () {
        $('#m-toast-pop').fadeOut();
    }, 2000);
}

//toasts 换行提示文字
function toastinfos(message) {
    $('#m-toast-inner-text').html(message);
    $('#m-toast-pop').fadeIn();
    setTimeout(function () {
        $('#m-toast-pop').fadeOut();
    }, 2000);
}

function isTest() {
    if (window.location.href.indexOf('127.0.0.1') >= 0) {//增加测试环境商品id，只有11一个商品
        return true;
    } else {
        return false;
    }
}

function jump(str) {
    window.location.href = str;
}

//解决ios手机输入框不能垂直居中问题
function initpos() {
    for (var i = 0; i < $('input').length; i++) {
        var hbox = $('input')[i].parentNode.offsetHeight;
        var hitem = $('input')[i].offsetHeight;
        var topx = (hbox - hitem) / 2;
        var u = navigator.userAgent;
        if (!!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)) {//ios设备，输入框显示便下，
            topx += 1;
        }
        $('input')[i].style['margin-top'] = topx + 'px';
    }
}

var axiosUrl = '';


/**判断是否是手机号**/
function isPhoneNumber(tel) {
    var reg = /^0?1[3|4|5|6|7|8|9][0-9]\d{8}$/;
    return reg.test(tel);
}

/**
 *
 */

function isQQ(value) {
    return /^[1-9][0-9]{4,9}$/.test(value);
}
