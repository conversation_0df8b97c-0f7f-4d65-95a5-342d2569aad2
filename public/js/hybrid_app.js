
var hybrid_app ={};
var ua = navigator.userAgent;
//判断是否是融e联 ios
hybrid_app.isRELIphone = function () {
    if (ua.indexOf('ICBCiPhoneBS')>-1) {
        return true;
    }
    return false;
};
//  判断是否是融e联 android
hybrid_app.isRELAndroid = function () {
    if (ua.indexOf('ICBCAndroidBS')>-1 ) {
        return true;
    }
    return false;
};
/**
 * 检测当前浏览器是否为Android(Chrome)
 */
hybrid_app.isAndroid = function() {
    if (ua.indexOf('Android')>-1) {
        return true;
    }
    return false;
};
/**
 * 检测当前浏览器是否为iPhone(Safari)
 */
hybrid_app.isIPhone = function() {
    if (ua.indexOf('iPhone')>-1) {
        return true;
    }
    return false;
};
function connectWebViewJavascriptBridge(callback) {
    if(window.WebViewJavascriptBridge){
        return callback(WebViewJavascriptBridge);
    }else{
        document.addEventListener('WebViewJavascriptBridgeReady', function() {
            callback(WebViewJavascriptBridge);
        }, false);
    }
    if(window.WVJBCallbacks) { return window.WVJBCallbacks.push(callback); }
    window.WVJBCallbacks = [callback];
    var WVJBIframe=document.createElement('iFrame');
    WVJBIframe.style.display='none';
    WVJBIframe.src='wvjbscheme://__BRIDGE_LOADED__';
    document.documentElement.appendChild(WVJBIframe);
    setTimeout(function(){document.documentElement.removeChild(WVJBIframe)});

}
connectWebViewJavascriptBridge(function(bridge) {
    bridge.init();
    bridge.registerHandler("callback",callback)
});

var ICBCBridge = {
    callHandler: function(name, params, callback) {
        connectWebViewJavascriptBridge(function(bridge) {
            bridge.callHandler(name, params, callback);
        });
    },
    send: function(params, callback) {
        connectWebViewJavascriptBridge(function(bridge) {
            bridge.send(params, callback);
        });
    }
};
window.ICBCBridge = ICBCBridge;

hybrid_app.base64Encode = function (str) {
    _utf8_encode = function (string) {
        string = string.replace(/\r\n/g, "\n");
        var utftext = "";
        for (var n = 0; n < string.length; n++) {
            var c = string.charCodeAt(n);
            if (c < 128) {
                utftext += String.fromCharCode(c);
            } else if ((c > 127) && (c < 2048)) {
                utftext += String.fromCharCode((c >> 6) | 192);
                utftext += String.fromCharCode((c & 63) | 128);
            } else {
                utftext += String.fromCharCode((c >> 12) | 224);
                utftext += String.fromCharCode(((c >> 6) & 63) | 128);
                utftext += String.fromCharCode((c & 63) | 128);
            }
        }
        return utftext;
    };

    _keyStr = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
    var output = "";
    var chr1, chr2, chr3, enc1, enc2, enc3, enc4;
    var i = 0;
    str = _utf8_encode(str);
    while (i < str.length) {
        chr1 = str.charCodeAt(i++);
        chr2 = str.charCodeAt(i++);
        chr3 = str.charCodeAt(i++);
        enc1 = chr1 >> 2;
        enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);
        enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);
        enc4 = chr3 & 63;
        if (isNaN(chr2)) {
            enc3 = enc4 = 64;
        } else if (isNaN(chr3)) {
            enc4 = 64;
        }
        output = output +
            _keyStr.charAt(enc1) + _keyStr.charAt(enc2) +
            _keyStr.charAt(enc3) + _keyStr.charAt(enc4);
    }
    return output;
};

hybrid_app.share = function (shareInfo) {
    if (hybrid_app.isAndroid()) {
        Myutils.share(shareInfo);
    } else {
        //（ios需要将参数base64加密）
        window.ICBCBridge.callHandler("Myutils.share", hybrid_app.base64Encode(shareInfo));
    }
}

//第三方显示或隐藏tabbar isShow:true显示tabbar 反之隐藏
hybrid_app.showToolBar = function(isShow){
    //如果是安卓
    if(hybrid_app.isAndroid()){
        Myutils.showToolBar(isShow);
    }else{  //ios
        window.ICBCBridge.callHandler("Myutils.showToolBar",isShow)
    }
}

//第三方返回上一级页面
hybrid_app.back = function(){
    //如果是安卓
    if(hybrid_app.isAndroid()){
        Myutils.back();
    }else{  //ios
        window.ICBCBridge.callHandler("Myutils.back")
    }
}
//第三方开启定位
hybrid_app.openGPS = function(){
    //如果是安卓
    if(hybrid_app.isAndroid()){
        Myutils.openGPS();
    }else{  //ios
        window.ICBCBridge.callHandler("Myutils.openGPS")
    }
}
//第三方关闭定位
hybrid_app.closeGPS = function(){
    //如果是安卓
    if(hybrid_app.isAndroid()){
        Myutils.closeGPS();
    }else{  //ios
        window.ICBCBridge.callHandler("Myutils.closeGPS")
    }
}

//第三方获取定位  参数是回调的函数名的字符串
hybrid_app.getMyLocation = function(getGps){
    //如果是安卓
    if(hybrid_app.isAndroid()){
        Myutils.getMyLocation(getGps);
    }else{  //ios
        window.ICBCBridge.callHandler("Myutils.getMyLocation",getGps)
    }
}


//第三方打电话功能  参数是电话号
hybrid_app.callPhoneNumber = function(tel){
    //如果是安卓
    if(hybrid_app.isAndroid()){
        Myutils.callPhoneNumber(tel);
    }else{  //ios
        window.ICBCBridge.callHandler("Myutils.callPhoneNumber",tel)
    }
}

var loginCallback = $.Callbacks();

var act;
//中途登录
hybrid_app.merLogin = function (prams) {
    if(prams != null){
        act = prams;
    }
    var data = null;
    var reg = new RegExp("(^|&)loginParams=([^&]*)(&|$)");
    if(window.location.search != null && window.location.search.length > 12){
        var r = window.location.search.substr(1).match(reg);
        if (r != null){
            data = decodeURIComponent(r[2]);
        }
    }
    //url里带有loginParams参数
    if(data != null && data != ''){
        callLogin2(data);
    }
    //如果是安卓
    else if (hybrid_app.isAndroid()) {
        Myutils.open("callLogin2");
    } else if (hybrid_app.isIPhone()) { //ios

        window.ICBCBridge.callHandler("Myutils.open", "callLogin2")
    }
    //else {
    //    var data = "pSs6sElROJtRtloIy8UMGDzS2AmQ8ag8qRqoCs7wK8UvAb26eR4Nm5tdKJGgkZiDUND4VadZ9yFZlogZGo2u+jIR/dwOkV/cNg1PFgqbqkXTQUl4G3huFQvqDLLBb2DQRgzwOUMEgMbWntHQ+DAdVIvTt72AtoNXI0tjuelsbmy2lHMF5dt5XMD1v+eNxeIP01Jr71tH2uBlWePD9oYt7CrpLRDIFj9Rf/dj258d8Ejo18obBDighlLNOaIbuce7yH+dLq5YJLSbmHKWUZJ/txiuovIbm70zROSbueWeevM=";
    //    callLogin(data);
    //}

}
// //中途登录的回调
// function callLogin(param) {
//
//     //解密参数
//     $.ajax({
//         type: "post",
//         url:  "/api/login-params",
//         data: {"loginParams": param},
//         dataType: "json",
//         success: login_success
//     });
// }
//中途登录的回调2
function callLogin2(param) {

    //解密参数
    $.ajax({
        type: "post",
        url:  "/api/common-login-params",
        data: {"loginParams": param,
            'act':act
        },
        dataType: "json",
        success: login_success
    });
}

function login_success(data) {
    if (data.code == 200) {
        // 保存token
        // sessionStorage.setItem("token", data.data.token);
        try {
            if (typeof (doLoginSuccess) == "function") {
                doLoginSuccess(data);
            }
        } catch (e) {
            console.warn('function doLoginSuccess is not defined');
        }

    } else {
        try {
            if (typeof (doLoginFail) == "function") {
                doLoginFail(data);
            } else {
                if (data.msg != undefined) {
                    // alert(data.msg);
                } else {
                    // alert("登录失败，请重新登录");
                }
            }
        } catch (e) {
            // console.warn('function doLoginFail is not defined');
            console.warn(e);
        }

    }
}
function decryptLoginCallback(data) {
    var _res = data["Res"][0].Res;
    if (_res == 1) {
        var userId = data["CustInfo"][0].ID;
        var mobile = data["CustInfo"][0].Mobile;
        var custId = data["CustInfo"][0].CustNo;
        $.cookie(WXCOOKIEPRE + "txtMobile", mobile);
        $.cookie(WXCOOKIEPRE + "userId", userId);
        $.cookie(WXCOOKIEPRE + "icbceCustId", custId);
        $.cookie(WXCOOKIEPRE + "storeId", 311497);
        $.cookie(WXCOOKIEPRE + "mobile", mobile);
        $.cookie(WXCOOKIEPRE + "GLOBALUSERID", userId, { path: "/" });
        $.cookie(WXCOOKIEPRE + "GLOBALMOBILE", mobile, { path: "/" });
        $.cookie(WXCOOKIEPRE + "GLOBALSTOREID", 311497, { path: "/" });

        loginCallback.fire();
    } else {
        alert("中间登录失败");
    }
}
//获取定位信息的回调
function getGps(param) {

}
function goBack1() {
    if (hybrid_app.isRELIphone() || hybrid_app.isRELAndroid()) {
        hybrid_app.back();
    } else {
        window.history.go(-1);
    }
}

function initPage() {
    var ua = navigator.userAgent;
    if (ua.indexOf('elife_moblie') < 0) {
        $('.footer_absolute_box').show();

        //隐藏分享按钮
        $('#btnShare').hide();
    }
}

//跳转下载APP
function openApp() {
    var ua = navigator.userAgent;
    var timer;
    var downUrl = "http://a.app.qq.com/o/simple.jsp?pkgname=com.icbc.elife";

    //判断是否是融e联
    if (ua.indexOf('ICBCAndroidBS') > -1) {
        window.location.href = 'com.icbc.elife://elife'; //跳到 安卓 url scheme
        timer = window.setTimeout(function () {
            window.location.href = downUrl;
        }, 1000)
    } else if (ua.indexOf('ICBCiPhoneBS') > -1) {
        window.location.href = 'com.icbc.elife://';//跳到ios  url scheme地址
        timer = window.setTimeout(function () {
            window.location.href = downUrl;
        }, 1000)
    } else {
        window.location.href = downUrl;//应用宝地址
    }
}
