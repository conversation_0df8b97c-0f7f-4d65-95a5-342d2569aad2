$(function () {
    $("#aigo_header").click(function(){
        if (document.referrer === '') {
            hybrid_app.back();
            console.log("返回referrer")
        } else {
            window.history.go(-1);
            console.log("返回history")
        }
    });

    $("#aigo_share").click(function () {
        share();
    });

    $('.container').css('margin-top','.8rem');

    var ua = navigator.userAgent;
    if (ua.indexOf('elife_moblie') < 0) {
        // $('.click')[0].classList.add('hide');
        // $('.container').css('margin-top',0);
    }
});


function share() {
    var title = $('title')[0].innerText;
    //pngUrl：分享后链接显示的图片地址
    var pngUrl = "//xkdxtuesday.oss-cn-shanghai.aliyuncs.com/public/aigo/img/elogo2.jpg";
    //shareUrl：分享后点击链接需要跳转的地址

    var shareUrl = 'https://gift.guangguang.net.cn:11443/ecl/index.html';
    //title：分享后链接显示的标题
    var title = title;
    //content：分享后链接显示的内容
    var content = title;
    //封装需要分享的对象
    var shareInfo = {
        PNGUrl: pngUrl,
        ShareUrl: shareUrl,
        Title: title,
        Content: content
    };
    // 将对象转为字符串
    shareInfo = JSON.stringify(shareInfo);
    hybrid_app.share(shareInfo);
}
var axiosUrl ="";//测试环境接口请求地址
