!function(e,t){"use strict";"object"==typeof module&&"object"==typeof module.exports?module.exports=e.document?t(e,!0):function(e){if(!e.document)throw new Error("jQuery requires a window with a document");return t(e)}:t(e)}("undefined"!=typeof window?window:this,function(e,t){"use strict";function g(e,t){t=t||i;var n=t.createElement("script");n.text=e,t.head.appendChild(n).parentNode.removeChild(n)}function T(e){var t=!!e&&"length"in e&&e.length,n=y.type(e);return"function"===n||y.isWindow(e)?!1:"array"===n||0===t||"number"==typeof t&&t>0&&t-1 in e}function j(e,t,n){if(y.isFunction(t))return y.grep(e,function(e,i){return!!t.call(e,i,e)!==n});if(t.nodeType)return y.grep(e,function(e){return e===t!==n});if("string"==typeof t){if(D.test(t))return y.filter(t,e,n);t=y.filter(t,e)}return y.grep(e,function(e){return u.call(t,e)>-1!==n&&1===e.nodeType})}function O(e,t){for(;(e=e[t])&&1!==e.nodeType;);return e}function R(e){var t={};return y.each(e.match(P)||[],function(e,n){t[n]=!0}),t}function M(e){return e}function I(e){throw e}function W(e,t,n){var i;try{e&&y.isFunction(i=e.promise)?i.call(e).done(t).fail(n):e&&y.isFunction(i=e.then)?i.call(e,t,n):t.call(void 0,e)}catch(e){n.call(void 0,e)}}function _(){i.removeEventListener("DOMContentLoaded",_),e.removeEventListener("load",_),y.ready()}function U(){this.expando=y.expando+U.uid++}function J(e,t,n){var i;if(void 0===n&&1===e.nodeType)if(i="data-"+t.replace(Q,"-$&").toLowerCase(),n=e.getAttribute(i),"string"==typeof n){try{n="true"===n?!0:"false"===n?!1:"null"===n?null:+n+""===n?+n:Y.test(n)?JSON.parse(n):n}catch(r){}G.set(e,t,n)}else n=void 0;return n}function ie(e,t,n,i){var r,o=1,s=20,a=i?function(){return i.cur()}:function(){return y.css(e,t,"")},u=a(),f=n&&n[3]||(y.cssNumber[t]?"":"px"),l=(y.cssNumber[t]||"px"!==f&&+u)&&Z.exec(y.css(e,t));if(l&&l[3]!==f){f=f||l[3],n=n||[],l=+u||1;do o=o||".5",l/=o,y.style(e,t,l+f);while(o!==(o=a()/u)&&1!==o&&--s)}return n&&(l=+l||+u||0,r=n[1]?l+(n[1]+1)*n[2]:+n[2],i&&(i.unit=f,i.start=l,i.end=r)),r}function oe(e){var t,n=e.ownerDocument,i=e.nodeName,r=re[i];return r?r:(t=n.body.appendChild(n.createElement(i)),r=y.css(t,"display"),t.parentNode.removeChild(t),"none"===r&&(r="block"),re[i]=r,r)}function se(e,t){for(var n,i,r=[],o=0,s=e.length;s>o;o++)i=e[o],i.style&&(n=i.style.display,t?("none"===n&&(r[o]=V.get(i,"display")||null,r[o]||(i.style.display="")),""===i.style.display&&te(i)&&(r[o]=oe(i))):"none"!==n&&(r[o]="none",V.set(i,"display",n)));for(o=0;s>o;o++)null!=r[o]&&(e[o].style.display=r[o]);return e}function ce(e,t){var n="undefined"!=typeof e.getElementsByTagName?e.getElementsByTagName(t||"*"):"undefined"!=typeof e.querySelectorAll?e.querySelectorAll(t||"*"):[];return void 0===t||t&&y.nodeName(e,t)?y.merge([e],n):n}function de(e,t){for(var n=0,i=e.length;i>n;n++)V.set(e[n],"globalEval",!t||V.get(t[n],"globalEval"))}function he(e,t,n,i,r){for(var o,s,a,u,f,l,c=t.createDocumentFragment(),d=[],p=0,h=e.length;h>p;p++)if(o=e[p],o||0===o)if("object"===y.type(o))y.merge(d,o.nodeType?[o]:o);else if(pe.test(o)){for(s=s||c.appendChild(t.createElement("div")),a=(ue.exec(o)||["",""])[1].toLowerCase(),u=le[a]||le._default,s.innerHTML=u[1]+y.htmlPrefilter(o)+u[2],l=u[0];l--;)s=s.lastChild;y.merge(d,s.childNodes),s=c.firstChild,s.textContent=""}else d.push(t.createTextNode(o));for(c.textContent="",p=0;o=d[p++];)if(i&&y.inArray(o,i)>-1)r&&r.push(o);else if(f=y.contains(o.ownerDocument,o),s=ce(c.appendChild(o),"script"),f&&de(s),n)for(l=0;o=s[l++];)fe.test(o.type||"")&&n.push(o);return c}function xe(){return!0}function be(){return!1}function we(){try{return i.activeElement}catch(e){}}function Te(e,t,n,i,r,o){var s,a;if("object"==typeof t){"string"!=typeof n&&(i=i||n,n=void 0);for(a in t)Te(e,a,n,i,t[a],o);return e}if(null==i&&null==r?(r=n,i=n=void 0):null==r&&("string"==typeof n?(r=i,i=void 0):(r=i,i=n,n=void 0)),r===!1)r=be;else if(!r)return e;return 1===o&&(s=r,r=function(e){return y().off(e),s.apply(this,arguments)},r.guid=s.guid||(s.guid=y.guid++)),e.each(function(){y.event.add(this,t,r,i,n)})}function De(e,t){return y.nodeName(e,"table")&&y.nodeName(11!==t.nodeType?t:t.firstChild,"tr")?e.getElementsByTagName("tbody")[0]||e:e}function je(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function Ae(e){var t=Se.exec(e.type);return t?e.type=t[1]:e.removeAttribute("type"),e}function qe(e,t){var n,i,r,o,s,a,u,f;if(1===t.nodeType){if(V.hasData(e)&&(o=V.access(e),s=V.set(t,o),f=o.events)){delete s.handle,s.events={};for(r in f)for(n=0,i=f[r].length;i>n;n++)y.event.add(t,r,f[r][n])}G.hasData(e)&&(a=G.access(e),u=y.extend({},a),G.set(t,u))}}function Le(e,t){var n=t.nodeName.toLowerCase();"input"===n&&ae.test(e.type)?t.checked=e.checked:("input"===n||"textarea"===n)&&(t.defaultValue=e.defaultValue)}function He(e,t,n,i){t=s.apply([],t);var r,o,a,u,f,l,c=0,d=e.length,p=d-1,m=t[0],v=y.isFunction(m);if(v||d>1&&"string"==typeof m&&!h.checkClone&&Ee.test(m))return e.each(function(r){var o=e.eq(r);v&&(t[0]=m.call(this,r,o.html())),He(o,t,n,i)});if(d&&(r=he(t,e[0].ownerDocument,!1,e,i),o=r.firstChild,1===r.childNodes.length&&(r=o),o||i)){for(a=y.map(ce(r,"script"),je),u=a.length;d>c;c++)f=r,c!==p&&(f=y.clone(f,!0,!0),u&&y.merge(a,ce(f,"script"))),n.call(e[c],f,c);if(u)for(l=a[a.length-1].ownerDocument,y.map(a,Ae),c=0;u>c;c++)f=a[c],fe.test(f.type||"")&&!V.access(f,"globalEval")&&y.contains(l,f)&&(f.src?y._evalUrl&&y._evalUrl(f.src):g(f.textContent.replace(Ne,""),l))}return e}function Fe(e,t,n){for(var i,r=t?y.filter(t,e):e,o=0;null!=(i=r[o]);o++)n||1!==i.nodeType||y.cleanData(ce(i)),i.parentNode&&(n&&y.contains(i.ownerDocument,i)&&de(ce(i,"script")),i.parentNode.removeChild(i));return e}function Me(e,t,n){var i,r,o,s,a=e.style;return n=n||Re(e),n&&(s=n.getPropertyValue(t)||n[t],""!==s||y.contains(e.ownerDocument,e)||(s=y.style(e,t)),!h.pixelMarginRight()&&Pe.test(s)&&Oe.test(t)&&(i=a.width,r=a.minWidth,o=a.maxWidth,a.minWidth=a.maxWidth=a.width=s,s=n.width,a.width=i,a.minWidth=r,a.maxWidth=o)),void 0!==s?s+"":s}function Ie(e,t){return{get:function(){return e()?void delete this.get:(this.get=t).apply(this,arguments)}}}function Xe(e){if(e in ze)return e;for(var t=e[0].toUpperCase()+e.slice(1),n=_e.length;n--;)if(e=_e[n]+t,e in ze)return e}function Ue(e,t,n){var i=Z.exec(t);return i?Math.max(0,i[2]-(n||0))+(i[3]||"px"):t}function Ve(e,t,n,i,r){for(var o=n===(i?"border":"content")?4:"width"===t?1:0,s=0;4>o;o+=2)"margin"===n&&(s+=y.css(e,n+ee[o],!0,r)),i?("content"===n&&(s-=y.css(e,"padding"+ee[o],!0,r)),"margin"!==n&&(s-=y.css(e,"border"+ee[o]+"Width",!0,r))):(s+=y.css(e,"padding"+ee[o],!0,r),"padding"!==n&&(s+=y.css(e,"border"+ee[o]+"Width",!0,r)));return s}function Ge(e,t,n){var i,r=!0,o=Re(e),s="border-box"===y.css(e,"boxSizing",!1,o);if(e.getClientRects().length&&(i=e.getBoundingClientRect()[t]),0>=i||null==i){if(i=Me(e,t,o),(0>i||null==i)&&(i=e.style[t]),Pe.test(i))return i;r=s&&(h.boxSizingReliable()||i===e.style[t]),i=parseFloat(i)||0}return i+Ve(e,t,n||(s?"border":"content"),r,o)+"px"}function Ye(e,t,n,i,r){return new Ye.prototype.init(e,t,n,i,r)}function et(){Je&&(e.requestAnimationFrame(et),y.fx.tick())}function tt(){return e.setTimeout(function(){Qe=void 0}),Qe=y.now()}function nt(e,t){var n,i=0,r={height:e};for(t=t?1:0;4>i;i+=2-t)n=ee[i],r["margin"+n]=r["padding"+n]=e;return t&&(r.opacity=r.width=e),r}function it(e,t,n){for(var i,r=(st.tweeners[t]||[]).concat(st.tweeners["*"]),o=0,s=r.length;s>o;o++)if(i=r[o].call(n,t,e))return i}function rt(e,t,n){var i,r,o,s,a,u,f,l,c="width"in t||"height"in t,d=this,p={},h=e.style,g=e.nodeType&&te(e),m=V.get(e,"fxshow");n.queue||(s=y._queueHooks(e,"fx"),null==s.unqueued&&(s.unqueued=0,a=s.empty.fire,s.empty.fire=function(){s.unqueued||a()}),s.unqueued++,d.always(function(){d.always(function(){s.unqueued--,y.queue(e,"fx").length||s.empty.fire()})}));for(i in t)if(r=t[i],Ke.test(r)){if(delete t[i],o=o||"toggle"===r,r===(g?"hide":"show")){if("show"!==r||!m||void 0===m[i])continue;g=!0}p[i]=m&&m[i]||y.style(e,i)}if(u=!y.isEmptyObject(t),u||!y.isEmptyObject(p)){c&&1===e.nodeType&&(n.overflow=[h.overflow,h.overflowX,h.overflowY],f=m&&m.display,null==f&&(f=V.get(e,"display")),l=y.css(e,"display"),"none"===l&&(f?l=f:(se([e],!0),f=e.style.display||f,l=y.css(e,"display"),se([e]))),("inline"===l||"inline-block"===l&&null!=f)&&"none"===y.css(e,"float")&&(u||(d.done(function(){h.display=f}),null==f&&(l=h.display,f="none"===l?"":l)),h.display="inline-block")),n.overflow&&(h.overflow="hidden",d.always(function(){h.overflow=n.overflow[0],h.overflowX=n.overflow[1],h.overflowY=n.overflow[2]})),u=!1;for(i in p)u||(m?"hidden"in m&&(g=m.hidden):m=V.access(e,"fxshow",{display:f}),o&&(m.hidden=!g),g&&se([e],!0),d.done(function(){g||se([e]),V.remove(e,"fxshow");for(i in p)y.style(e,i,p[i])})),u=it(g?m[i]:0,i,d),i in m||(m[i]=u.start,g&&(u.end=u.start,u.start=0))}}function ot(e,t){var n,i,r,o,s;for(n in e)if(i=y.camelCase(n),r=t[i],o=e[n],y.isArray(o)&&(r=o[1],o=e[n]=o[0]),n!==i&&(e[i]=o,delete e[n]),s=y.cssHooks[i],s&&"expand"in s){o=s.expand(o),delete e[i];for(n in o)n in e||(e[n]=o[n],t[n]=r)}else t[i]=r}function st(e,t,n){var i,r,o=0,s=st.prefilters.length,a=y.Deferred().always(function(){delete u.elem}),u=function(){if(r)return!1;for(var t=Qe||tt(),n=Math.max(0,f.startTime+f.duration-t),i=n/f.duration||0,o=1-i,s=0,u=f.tweens.length;u>s;s++)f.tweens[s].run(o);return a.notifyWith(e,[f,o,n]),1>o&&u?n:(a.resolveWith(e,[f]),!1)},f=a.promise({elem:e,props:y.extend({},t),opts:y.extend(!0,{specialEasing:{},easing:y.easing._default},n),originalProperties:t,originalOptions:n,startTime:Qe||tt(),duration:n.duration,tweens:[],createTween:function(t,n){var i=y.Tween(e,f.opts,t,n,f.opts.specialEasing[t]||f.opts.easing);return f.tweens.push(i),i},stop:function(t){var n=0,i=t?f.tweens.length:0;if(r)return this;for(r=!0;i>n;n++)f.tweens[n].run(1);return t?(a.notifyWith(e,[f,1,0]),a.resolveWith(e,[f,t])):a.rejectWith(e,[f,t]),this}}),l=f.props;for(ot(l,f.opts.specialEasing);s>o;o++)if(i=st.prefilters[o].call(f,e,l,f.opts))return y.isFunction(i.stop)&&(y._queueHooks(f.elem,f.opts.queue).stop=y.proxy(i.stop,i)),i;return y.map(l,it,f),y.isFunction(f.opts.start)&&f.opts.start.call(e,f),y.fx.timer(y.extend(u,{elem:e,anim:f,queue:f.opts.queue})),f.progress(f.opts.progress).done(f.opts.done,f.opts.complete).fail(f.opts.fail).always(f.opts.always)}function dt(e){return e.getAttribute&&e.getAttribute("class")||""}function Ct(e,t,n,i){var r;if(y.isArray(t))y.each(t,function(t,r){n||xt.test(e)?i(e,r):Ct(e+"["+("object"==typeof r&&null!=r?t:"")+"]",r,n,i)});else if(n||"object"!==y.type(t))i(e,t);else for(r in t)Ct(e+"["+r+"]",t[r],n,i)}function Ot(e){return function(t,n){"string"!=typeof t&&(n=t,t="*");var i,r=0,o=t.toLowerCase().match(P)||[];if(y.isFunction(n))for(;i=o[r++];)"+"===i[0]?(i=i.slice(1)||"*",(e[i]=e[i]||[]).unshift(n)):(e[i]=e[i]||[]).push(n)}}function Pt(e,t,n,i){function s(a){var u;return r[a]=!0,y.each(e[a]||[],function(e,a){var f=a(t,n,i);return"string"!=typeof f||o||r[f]?o?!(u=f):void 0:(t.dataTypes.unshift(f),s(f),!1)}),u}var r={},o=e===Lt;return s(t.dataTypes[0])||!r["*"]&&s("*")}function Rt(e,t){var n,i,r=y.ajaxSettings.flatOptions||{};for(n in t)void 0!==t[n]&&((r[n]?e:i||(i={}))[n]=t[n]);return i&&y.extend(!0,e,i),e}function Mt(e,t,n){for(var i,r,o,s,a=e.contents,u=e.dataTypes;"*"===u[0];)u.shift(),void 0===i&&(i=e.mimeType||t.getResponseHeader("Content-Type"));if(i)for(r in a)if(a[r]&&a[r].test(i)){u.unshift(r);break}if(u[0]in n)o=u[0];else{for(r in n){if(!u[0]||e.converters[r+" "+u[0]]){o=r;break}s||(s=r)}o=o||s}return o?(o!==u[0]&&u.unshift(o),n[o]):void 0}function It(e,t,n,i){var r,o,s,a,u,f={},l=e.dataTypes.slice();if(l[1])for(s in e.converters)f[s.toLowerCase()]=e.converters[s];for(o=l.shift();o;)if(e.responseFields[o]&&(n[e.responseFields[o]]=t),!u&&i&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),u=o,o=l.shift())if("*"===o)o=u;else if("*"!==u&&u!==o){if(s=f[u+" "+o]||f["* "+o],!s)for(r in f)if(a=r.split(" "),a[1]===o&&(s=f[u+" "+a[0]]||f["* "+a[0]])){s===!0?s=f[r]:f[r]!==!0&&(o=a[0],l.unshift(a[1]));break}if(s!==!0)if(s&&e["throws"])t=s(t);else try{t=s(t)}catch(c){return{state:"parsererror",error:s?c:"No conversion from "+u+" to "+o}}}return{state:"success",data:t}}function zt(e){return y.isWindow(e)?e:9===e.nodeType&&e.defaultView}var n=[],i=e.document,r=Object.getPrototypeOf,o=n.slice,s=n.concat,a=n.push,u=n.indexOf,f={},l=f.toString,c=f.hasOwnProperty,d=c.toString,p=d.call(Object),h={},m="3.1.0",y=function(e,t){return new y.fn.init(e,t)},v=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,x=/^-ms-/,b=/-([a-z])/g,w=function(e,t){return t.toUpperCase()};y.fn=y.prototype={jquery:m,constructor:y,length:0,toArray:function(){return o.call(this)},get:function(e){return null!=e?0>e?this[e+this.length]:this[e]:o.call(this)},pushStack:function(e){var t=y.merge(this.constructor(),e);return t.prevObject=this,t},each:function(e){return y.each(this,e)},map:function(e){return this.pushStack(y.map(this,function(t,n){return e.call(t,n,t)}))},slice:function(){return this.pushStack(o.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(e){var t=this.length,n=+e+(0>e?t:0);return this.pushStack(n>=0&&t>n?[this[n]]:[])},end:function(){return this.prevObject||this.constructor()},push:a,sort:n.sort,splice:n.splice},y.extend=y.fn.extend=function(){var e,t,n,i,r,o,s=arguments[0]||{},a=1,u=arguments.length,f=!1;for("boolean"==typeof s&&(f=s,s=arguments[a]||{},a++),"object"==typeof s||y.isFunction(s)||(s={}),a===u&&(s=this,a--);u>a;a++)if(null!=(e=arguments[a]))for(t in e)n=s[t],i=e[t],s!==i&&(f&&i&&(y.isPlainObject(i)||(r=y.isArray(i)))?(r?(r=!1,o=n&&y.isArray(n)?n:[]):o=n&&y.isPlainObject(n)?n:{},s[t]=y.extend(f,o,i)):void 0!==i&&(s[t]=i));return s},y.extend({expando:"jQuery"+(m+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isFunction:function(e){return"function"===y.type(e)},isArray:Array.isArray,isWindow:function(e){return null!=e&&e===e.window},isNumeric:function(e){var t=y.type(e);return("number"===t||"string"===t)&&!isNaN(e-parseFloat(e))},isPlainObject:function(e){var t,n;return e&&"[object Object]"===l.call(e)?(t=r(e))?(n=c.call(t,"constructor")&&t.constructor,"function"==typeof n&&d.call(n)===p):!0:!1},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},type:function(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?f[l.call(e)]||"object":typeof e},globalEval:function(e){g(e)},camelCase:function(e){return e.replace(x,"ms-").replace(b,w)},nodeName:function(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()},each:function(e,t){var n,i=0;if(T(e))for(n=e.length;n>i&&t.call(e[i],i,e[i])!==!1;i++);else for(i in e)if(t.call(e[i],i,e[i])===!1)break;return e},trim:function(e){return null==e?"":(e+"").replace(v,"")},makeArray:function(e,t){var n=t||[];return null!=e&&(T(Object(e))?y.merge(n,"string"==typeof e?[e]:e):a.call(n,e)),n},inArray:function(e,t,n){return null==t?-1:u.call(t,e,n)},merge:function(e,t){for(var n=+t.length,i=0,r=e.length;n>i;i++)e[r++]=t[i];return e.length=r,e},grep:function(e,t,n){for(var i,r=[],o=0,s=e.length,a=!n;s>o;o++)i=!t(e[o],o),i!==a&&r.push(e[o]);return r},map:function(e,t,n){var i,r,o=0,a=[];if(T(e))for(i=e.length;i>o;o++)r=t(e[o],o,n),null!=r&&a.push(r);else for(o in e)r=t(e[o],o,n),null!=r&&a.push(r);return s.apply([],a)},guid:1,proxy:function(e,t){var n,i,r;return"string"==typeof t&&(n=e[t],t=e,e=n),y.isFunction(e)?(i=o.call(arguments,2),r=function(){return e.apply(t||this,i.concat(o.call(arguments)))},r.guid=e.guid=e.guid||y.guid++,r):void 0},now:Date.now,support:h}),"function"==typeof Symbol&&(y.fn[Symbol.iterator]=n[Symbol.iterator]),y.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(e,t){f["[object "+t+"]"]=t.toLowerCase()});var C=function(e){function se(e,t,i,r){var o,a,f,l,c,h,y,v=t&&t.ownerDocument,T=t?t.nodeType:9;if(i=i||[],"string"!=typeof e||!e||1!==T&&9!==T&&11!==T)return i;if(!r&&((t?t.ownerDocument||t:w)!==p&&d(t),t=t||p,g)){if(11!==T&&(c=J.exec(e)))if(o=c[1]){if(9===T){if(!(f=t.getElementById(o)))return i;if(f.id===o)return i.push(f),i}else if(v&&(f=v.getElementById(o))&&x(t,f)&&f.id===o)return i.push(f),i}else{if(c[2])return L.apply(i,t.getElementsByTagName(e)),i;if((o=c[3])&&n.getElementsByClassName&&t.getElementsByClassName)return L.apply(i,t.getElementsByClassName(o)),i}if(n.qsa&&!S[e+" "]&&(!m||!m.test(e))){if(1!==T)v=t,y=e;else if("object"!==t.nodeName.toLowerCase()){for((l=t.getAttribute("id"))?l=l.replace(te,ne):t.setAttribute("id",l=b),h=s(e),a=h.length;a--;)h[a]="#"+l+" "+ve(h[a]);y=h.join(","),v=K.test(e)&&me(t.parentNode)||t}if(y)try{return L.apply(i,v.querySelectorAll(y)),i}catch(C){}finally{l===b&&t.removeAttribute("id")}}}return u(e.replace($,"$1"),t,i,r)}function ae(){function t(n,r){return e.push(n+" ")>i.cacheLength&&delete t[e.shift()],t[n+" "]=r}var e=[];return t}function ue(e){return e[b]=!0,e}function fe(e){var t=p.createElement("fieldset");try{return!!e(t)}catch(n){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function le(e,t){for(var n=e.split("|"),r=n.length;r--;)i.attrHandle[n[r]]=t}function ce(e,t){var n=t&&e,i=n&&1===e.nodeType&&1===t.nodeType&&e.sourceIndex-t.sourceIndex;if(i)return i;if(n)for(;n=n.nextSibling;)if(n===t)return-1;return e?1:-1}function de(e){return function(t){var n=t.nodeName.toLowerCase();return"input"===n&&t.type===e}}function pe(e){return function(t){var n=t.nodeName.toLowerCase();return("input"===n||"button"===n)&&t.type===e}}function he(e){return function(t){return"label"in t&&t.disabled===e||"form"in t&&t.disabled===e||"form"in t&&t.disabled===!1&&(t.isDisabled===e||t.isDisabled!==!e&&("label"in t||!re(t))!==e)}}function ge(e){return ue(function(t){return t=+t,ue(function(n,i){for(var r,o=e([],n.length,t),s=o.length;s--;)n[r=o[s]]&&(n[r]=!(i[r]=n[r]))})})}function me(e){return e&&"undefined"!=typeof e.getElementsByTagName&&e}function ye(){}function ve(e){for(var t=0,n=e.length,i="";n>t;t++)i+=e[t].value;return i}function xe(e,t,n){var i=t.dir,r=t.next,o=r||i,s=n&&"parentNode"===o,a=C++;return t.first?function(t,n,r){for(;t=t[i];)if(1===t.nodeType||s)return e(t,n,r)}:function(t,n,u){var f,l,c,d=[T,a];if(u){for(;t=t[i];)if((1===t.nodeType||s)&&e(t,n,u))return!0}else for(;t=t[i];)if(1===t.nodeType||s)if(c=t[b]||(t[b]={}),l=c[t.uniqueID]||(c[t.uniqueID]={}),r&&r===t.nodeName.toLowerCase())t=t[i]||t;else{if((f=l[o])&&f[0]===T&&f[1]===a)return d[2]=f[2];if(l[o]=d,d[2]=e(t,n,u))return!0}}}function be(e){return e.length>1?function(t,n,i){for(var r=e.length;r--;)if(!e[r](t,n,i))return!1;return!0}:e[0]}function we(e,t,n){for(var i=0,r=t.length;r>i;i++)se(e,t[i],n);return n}function Te(e,t,n,i,r){for(var o,s=[],a=0,u=e.length,f=null!=t;u>a;a++)(o=e[a])&&(!n||n(o,i,r))&&(s.push(o),f&&t.push(a));return s}function Ce(e,t,n,i,r,o){return i&&!i[b]&&(i=Ce(i)),r&&!r[b]&&(r=Ce(r,o)),ue(function(o,s,a,u){var f,l,c,d=[],p=[],h=s.length,g=o||we(t||"*",a.nodeType?[a]:a,[]),m=!e||!o&&t?g:Te(g,d,e,a,u),y=n?r||(o?e:h||i)?[]:s:m;if(n&&n(m,y,a,u),i)for(f=Te(y,p),i(f,[],a,u),l=f.length;l--;)(c=f[l])&&(y[p[l]]=!(m[p[l]]=c));if(o){if(r||e){if(r){for(f=[],l=y.length;l--;)(c=y[l])&&f.push(m[l]=c);r(null,y=[],f,u)}for(l=y.length;l--;)(c=y[l])&&(f=r?F(o,c):d[l])>-1&&(o[f]=!(s[f]=c))}}else y=Te(y===s?y.splice(h,y.length):y),r?r(null,s,y,u):L.apply(s,y)})}function ke(e){for(var t,n,r,o=e.length,s=i.relative[e[0].type],a=s||i.relative[" "],u=s?1:0,l=xe(function(e){return e===t},a,!0),c=xe(function(e){return F(t,e)>-1},a,!0),d=[function(e,n,i){var r=!s&&(i||n!==f)||((t=n).nodeType?l(e,n,i):c(e,n,i));return t=null,r}];o>u;u++)if(n=i.relative[e[u].type])d=[xe(be(d),n)];else{if(n=i.filter[e[u].type].apply(null,e[u].matches),n[b]){for(r=++u;o>r&&!i.relative[e[r].type];r++);return Ce(u>1&&be(d),u>1&&ve(e.slice(0,u-1).concat({value:" "===e[u-2].type?"*":""})).replace($,"$1"),n,r>u&&ke(e.slice(u,r)),o>r&&ke(e=e.slice(r)),o>r&&ve(e))}d.push(n)}return be(d)}function Ee(e,t){var n=t.length>0,r=e.length>0,o=function(o,s,a,u,l){var c,h,m,y=0,v="0",x=o&&[],b=[],w=f,C=o||r&&i.find.TAG("*",l),k=T+=null==w?1:Math.random()||.1,E=C.length;for(l&&(f=s===p||s||l);v!==E&&null!=(c=C[v]);v++){if(r&&c){for(h=0,s||c.ownerDocument===p||(d(c),a=!g);m=e[h++];)if(m(c,s||p,a)){u.push(c);break}l&&(T=k)}n&&((c=!m&&c)&&y--,o&&x.push(c))}if(y+=v,n&&v!==y){for(h=0;m=t[h++];)m(x,b,s,a);if(o){if(y>0)for(;v--;)x[v]||b[v]||(b[v]=A.call(u));b=Te(b)}L.apply(u,b),l&&!o&&b.length>0&&y+t.length>1&&se.uniqueSort(u)}return l&&(T=k,f=w),x};return n?ue(o):o}var t,n,i,r,o,s,a,u,f,l,c,d,p,h,g,m,y,v,x,b="sizzle"+1*new Date,w=e.document,T=0,C=0,k=ae(),E=ae(),S=ae(),N=function(e,t){return e===t&&(c=!0),0},D={}.hasOwnProperty,j=[],A=j.pop,q=j.push,L=j.push,H=j.slice,F=function(e,t){for(var n=0,i=e.length;i>n;n++)if(e[n]===t)return n;return-1},O="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",P="[\\x20\\t\\r\\n\\f]",R="(?:\\\\.|[\\w-]|[^\x00-\\xa0])+",M="\\["+P+"*("+R+")(?:"+P+"*([*^$|!~]?=)"+P+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+R+"))|)"+P+"*\\]",I=":("+R+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+M+")*)|.*)\\)|)",W=new RegExp(P+"+","g"),$=new RegExp("^"+P+"+|((?:^|[^\\\\])(?:\\\\.)*)"+P+"+$","g"),B=new RegExp("^"+P+"*,"+P+"*"),_=new RegExp("^"+P+"*([>+~]|"+P+")"+P+"*"),z=new RegExp("="+P+"*([^\\]'\"]*?)"+P+"*\\]","g"),X=new RegExp(I),U=new RegExp("^"+R+"$"),V={ID:new RegExp("^#("+R+")"),CLASS:new RegExp("^\\.("+R+")"),TAG:new RegExp("^("+R+"|[*])"),ATTR:new RegExp("^"+M),PSEUDO:new RegExp("^"+I),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+P+"*(even|odd|(([+-]|)(\\d*)n|)"+P+"*(?:([+-]|)"+P+"*(\\d+)|))"+P+"*\\)|)","i"),bool:new RegExp("^(?:"+O+")$","i"),needsContext:new RegExp("^"+P+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+P+"*((?:-\\d)?\\d*)"+P+"*\\)|)(?=[^-]|$)","i")},G=/^(?:input|select|textarea|button)$/i,Y=/^h\d$/i,Q=/^[^{]+\{\s*\[native \w/,J=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,K=/[+~]/,Z=new RegExp("\\\\([\\da-f]{1,6}"+P+"?|("+P+")|.)","ig"),ee=function(e,t,n){var i="0x"+t-65536;return i!==i||n?t:0>i?String.fromCharCode(i+65536):String.fromCharCode(i>>10|55296,1023&i|56320)},te=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\x80-\uFFFF\w-]/g,ne=function(e,t){return t?"\x00"===e?"�":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" ":"\\"+e},ie=function(){d()},re=xe(function(e){return e.disabled===!0},{dir:"parentNode",next:"legend"});try{L.apply(j=H.call(w.childNodes),w.childNodes),j[w.childNodes.length].nodeType}catch(oe){L={apply:j.length?function(e,t){q.apply(e,H.call(t))}:function(e,t){for(var n=e.length,i=0;e[n++]=t[i++];);e.length=n-1}}}n=se.support={},o=se.isXML=function(e){var t=e&&(e.ownerDocument||e).documentElement;return t?"HTML"!==t.nodeName:!1},d=se.setDocument=function(e){var t,r,s=e?e.ownerDocument||e:w;return s!==p&&9===s.nodeType&&s.documentElement?(p=s,h=p.documentElement,g=!o(p),w!==p&&(r=p.defaultView)&&r.top!==r&&(r.addEventListener?r.addEventListener("unload",ie,!1):r.attachEvent&&r.attachEvent("onunload",ie)),n.attributes=fe(function(e){return e.className="i",!e.getAttribute("className")}),n.getElementsByTagName=fe(function(e){return e.appendChild(p.createComment("")),!e.getElementsByTagName("*").length}),n.getElementsByClassName=Q.test(p.getElementsByClassName),n.getById=fe(function(e){return h.appendChild(e).id=b,!p.getElementsByName||!p.getElementsByName(b).length}),n.getById?(i.find.ID=function(e,t){if("undefined"!=typeof t.getElementById&&g){var n=t.getElementById(e);return n?[n]:[]}},i.filter.ID=function(e){var t=e.replace(Z,ee);return function(e){return e.getAttribute("id")===t}}):(delete i.find.ID,i.filter.ID=function(e){var t=e.replace(Z,ee);return function(e){var n="undefined"!=typeof e.getAttributeNode&&e.getAttributeNode("id");return n&&n.value===t}}),i.find.TAG=n.getElementsByTagName?function(e,t){return"undefined"!=typeof t.getElementsByTagName?t.getElementsByTagName(e):n.qsa?t.querySelectorAll(e):void 0}:function(e,t){var n,i=[],r=0,o=t.getElementsByTagName(e);if("*"===e){for(;n=o[r++];)1===n.nodeType&&i.push(n);return i}return o},i.find.CLASS=n.getElementsByClassName&&function(e,t){return"undefined"!=typeof t.getElementsByClassName&&g?t.getElementsByClassName(e):void 0},y=[],m=[],(n.qsa=Q.test(p.querySelectorAll))&&(fe(function(e){h.appendChild(e).innerHTML="<a id='"+b+"'></a><select id='"+b+"-\r\\' msallowcapture=''><option selected=''></option></select>",e.querySelectorAll("[msallowcapture^='']").length&&m.push("[*^$]="+P+"*(?:''|\"\")"),e.querySelectorAll("[selected]").length||m.push("\\["+P+"*(?:value|"+O+")"),e.querySelectorAll("[id~="+b+"-]").length||m.push("~="),e.querySelectorAll(":checked").length||m.push(":checked"),e.querySelectorAll("a#"+b+"+*").length||m.push(".#.+[+~]")}),fe(function(e){e.innerHTML="<a href='' disabled='disabled'></a><select disabled='disabled'><option/></select>";var t=p.createElement("input");t.setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),e.querySelectorAll("[name=d]").length&&m.push("name"+P+"*[*^$|!~]?="),2!==e.querySelectorAll(":enabled").length&&m.push(":enabled",":disabled"),h.appendChild(e).disabled=!0,2!==e.querySelectorAll(":disabled").length&&m.push(":enabled",":disabled"),e.querySelectorAll("*,:x"),m.push(",.*:")})),(n.matchesSelector=Q.test(v=h.matches||h.webkitMatchesSelector||h.mozMatchesSelector||h.oMatchesSelector||h.msMatchesSelector))&&fe(function(e){n.disconnectedMatch=v.call(e,"*"),v.call(e,"[s!='']:x"),y.push("!=",I)}),m=m.length&&new RegExp(m.join("|")),y=y.length&&new RegExp(y.join("|")),t=Q.test(h.compareDocumentPosition),x=t||Q.test(h.contains)?function(e,t){var n=9===e.nodeType?e.documentElement:e,i=t&&t.parentNode;return e===i||!(!i||1!==i.nodeType||!(n.contains?n.contains(i):e.compareDocumentPosition&&16&e.compareDocumentPosition(i)))}:function(e,t){if(t)for(;t=t.parentNode;)if(t===e)return!0;return!1},N=t?function(e,t){if(e===t)return c=!0,0;var i=!e.compareDocumentPosition-!t.compareDocumentPosition;return i?i:(i=(e.ownerDocument||e)===(t.ownerDocument||t)?e.compareDocumentPosition(t):1,1&i||!n.sortDetached&&t.compareDocumentPosition(e)===i?e===p||e.ownerDocument===w&&x(w,e)?-1:t===p||t.ownerDocument===w&&x(w,t)?1:l?F(l,e)-F(l,t):0:4&i?-1:1)}:function(e,t){if(e===t)return c=!0,0;var n,i=0,r=e.parentNode,o=t.parentNode,s=[e],a=[t];if(!r||!o)return e===p?-1:t===p?1:r?-1:o?1:l?F(l,e)-F(l,t):0;if(r===o)return ce(e,t);for(n=e;n=n.parentNode;)s.unshift(n);for(n=t;n=n.parentNode;)a.unshift(n);for(;s[i]===a[i];)i++;return i?ce(s[i],a[i]):s[i]===w?-1:a[i]===w?1:0},p):p},se.matches=function(e,t){return se(e,null,null,t)},se.matchesSelector=function(e,t){if((e.ownerDocument||e)!==p&&d(e),t=t.replace(z,"='$1']"),n.matchesSelector&&g&&!S[t+" "]&&(!y||!y.test(t))&&(!m||!m.test(t)))try{var i=v.call(e,t);if(i||n.disconnectedMatch||e.document&&11!==e.document.nodeType)return i}catch(r){}return se(t,p,null,[e]).length>0},se.contains=function(e,t){return(e.ownerDocument||e)!==p&&d(e),x(e,t)},se.attr=function(e,t){(e.ownerDocument||e)!==p&&d(e);var r=i.attrHandle[t.toLowerCase()],o=r&&D.call(i.attrHandle,t.toLowerCase())?r(e,t,!g):void 0;return void 0!==o?o:n.attributes||!g?e.getAttribute(t):(o=e.getAttributeNode(t))&&o.specified?o.value:null},se.escape=function(e){return(e+"").replace(te,ne)},se.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},se.uniqueSort=function(e){var t,i=[],r=0,o=0;if(c=!n.detectDuplicates,l=!n.sortStable&&e.slice(0),e.sort(N),c){for(;t=e[o++];)t===e[o]&&(r=i.push(o));for(;r--;)e.splice(i[r],1)}return l=null,e},r=se.getText=function(e){var t,n="",i=0,o=e.nodeType;if(o){if(1===o||9===o||11===o){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=r(e)}else if(3===o||4===o)return e.nodeValue}else for(;t=e[i++];)n+=r(t);return n},i=se.selectors={cacheLength:50,createPseudo:ue,match:V,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(Z,ee),e[3]=(e[3]||e[4]||e[5]||"").replace(Z,ee),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||se.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&se.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return V.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&X.test(n)&&(t=s(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(Z,ee).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=k[e+" "];return t||(t=new RegExp("(^|"+P+")"+e+"("+P+"|$)"))&&k(e,function(e){return t.test("string"==typeof e.className&&e.className||"undefined"!=typeof e.getAttribute&&e.getAttribute("class")||"")})},ATTR:function(e,t,n){return function(i){var r=se.attr(i,e);return null==r?"!="===t:t?(r+="","="===t?r===n:"!="===t?r!==n:"^="===t?n&&0===r.indexOf(n):"*="===t?n&&r.indexOf(n)>-1:"$="===t?n&&r.slice(-n.length)===n:"~="===t?(" "+r.replace(W," ")+" ").indexOf(n)>-1:"|="===t?r===n||r.slice(0,n.length+1)===n+"-":!1):!0}},CHILD:function(e,t,n,i,r){var o="nth"!==e.slice(0,3),s="last"!==e.slice(-4),a="of-type"===t;return 1===i&&0===r?function(e){return!!e.parentNode}:function(t,n,u){var f,l,c,d,p,h,g=o!==s?"nextSibling":"previousSibling",m=t.parentNode,y=a&&t.nodeName.toLowerCase(),v=!u&&!a,x=!1;if(m){if(o){for(;g;){for(d=t;d=d[g];)if(a?d.nodeName.toLowerCase()===y:1===d.nodeType)return!1;h=g="only"===e&&!h&&"nextSibling"}return!0}if(h=[s?m.firstChild:m.lastChild],s&&v){for(d=m,c=d[b]||(d[b]={}),l=c[d.uniqueID]||(c[d.uniqueID]={}),f=l[e]||[],p=f[0]===T&&f[1],x=p&&f[2],d=p&&m.childNodes[p];d=++p&&d&&d[g]||(x=p=0)||h.pop();)if(1===d.nodeType&&++x&&d===t){l[e]=[T,p,x];break}}else if(v&&(d=t,c=d[b]||(d[b]={}),l=c[d.uniqueID]||(c[d.uniqueID]={}),f=l[e]||[],p=f[0]===T&&f[1],x=p),x===!1)for(;(d=++p&&d&&d[g]||(x=p=0)||h.pop())&&((a?d.nodeName.toLowerCase()!==y:1!==d.nodeType)||!++x||(v&&(c=d[b]||(d[b]={}),l=c[d.uniqueID]||(c[d.uniqueID]={}),l[e]=[T,x]),d!==t)););return x-=r,x===i||x%i===0&&x/i>=0}}},PSEUDO:function(e,t){var n,r=i.pseudos[e]||i.setFilters[e.toLowerCase()]||se.error("unsupported pseudo: "+e);return r[b]?r(t):r.length>1?(n=[e,e,"",t],i.setFilters.hasOwnProperty(e.toLowerCase())?ue(function(e,n){for(var i,o=r(e,t),s=o.length;s--;)i=F(e,o[s]),e[i]=!(n[i]=o[s])}):function(e){return r(e,0,n)}):r}},pseudos:{not:ue(function(e){var t=[],n=[],i=a(e.replace($,"$1"));return i[b]?ue(function(e,t,n,r){for(var o,s=i(e,null,r,[]),a=e.length;a--;)(o=s[a])&&(e[a]=!(t[a]=o))}):function(e,r,o){return t[0]=e,i(t,null,o,n),t[0]=null,!n.pop()}}),has:ue(function(e){return function(t){return se(e,t).length>0}}),contains:ue(function(e){return e=e.replace(Z,ee),function(t){return(t.textContent||t.innerText||r(t)).indexOf(e)>-1}}),lang:ue(function(e){return U.test(e||"")||se.error("unsupported lang: "+e),e=e.replace(Z,ee).toLowerCase(),function(t){var n;do if(n=g?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang"))return n=n.toLowerCase(),n===e||0===n.indexOf(e+"-");while((t=t.parentNode)&&1===t.nodeType);return!1}}),target:function(t){var n=e.location&&e.location.hash;return n&&n.slice(1)===t.id},root:function(e){return e===h},focus:function(e){return e===p.activeElement&&(!p.hasFocus||p.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:he(!1),disabled:he(!0),checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,e.selected===!0},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!i.pseudos.empty(e);
},header:function(e){return Y.test(e.nodeName)},input:function(e){return G.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){var t;return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(t=e.getAttribute("type"))||"text"===t.toLowerCase())},first:ge(function(){return[0]}),last:ge(function(e,t){return[t-1]}),eq:ge(function(e,t,n){return[0>n?n+t:n]}),even:ge(function(e,t){for(var n=0;t>n;n+=2)e.push(n);return e}),odd:ge(function(e,t){for(var n=1;t>n;n+=2)e.push(n);return e}),lt:ge(function(e,t,n){for(var i=0>n?n+t:n;--i>=0;)e.push(i);return e}),gt:ge(function(e,t,n){for(var i=0>n?n+t:n;++i<t;)e.push(i);return e})}},i.pseudos.nth=i.pseudos.eq;for(t in{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})i.pseudos[t]=de(t);for(t in{submit:!0,reset:!0})i.pseudos[t]=pe(t);return ye.prototype=i.filters=i.pseudos,i.setFilters=new ye,s=se.tokenize=function(e,t){var n,r,o,s,a,u,f,l=E[e+" "];if(l)return t?0:l.slice(0);for(a=e,u=[],f=i.preFilter;a;){(!n||(r=B.exec(a)))&&(r&&(a=a.slice(r[0].length)||a),u.push(o=[])),n=!1,(r=_.exec(a))&&(n=r.shift(),o.push({value:n,type:r[0].replace($," ")}),a=a.slice(n.length));for(s in i.filter)!(r=V[s].exec(a))||f[s]&&!(r=f[s](r))||(n=r.shift(),o.push({value:n,type:s,matches:r}),a=a.slice(n.length));if(!n)break}return t?a.length:a?se.error(e):E(e,u).slice(0)},a=se.compile=function(e,t){var n,i=[],r=[],o=S[e+" "];if(!o){for(t||(t=s(e)),n=t.length;n--;)o=ke(t[n]),o[b]?i.push(o):r.push(o);o=S(e,Ee(r,i)),o.selector=e}return o},u=se.select=function(e,t,r,o){var u,f,l,c,d,p="function"==typeof e&&e,h=!o&&s(e=p.selector||e);if(r=r||[],1===h.length){if(f=h[0]=h[0].slice(0),f.length>2&&"ID"===(l=f[0]).type&&n.getById&&9===t.nodeType&&g&&i.relative[f[1].type]){if(t=(i.find.ID(l.matches[0].replace(Z,ee),t)||[])[0],!t)return r;p&&(t=t.parentNode),e=e.slice(f.shift().value.length)}for(u=V.needsContext.test(e)?0:f.length;u--&&(l=f[u],!i.relative[c=l.type]);)if((d=i.find[c])&&(o=d(l.matches[0].replace(Z,ee),K.test(f[0].type)&&me(t.parentNode)||t))){if(f.splice(u,1),e=o.length&&ve(f),!e)return L.apply(r,o),r;break}}return(p||a(e,h))(o,t,!g,r,!t||K.test(e)&&me(t.parentNode)||t),r},n.sortStable=b.split("").sort(N).join("")===b,n.detectDuplicates=!!c,d(),n.sortDetached=fe(function(e){return 1&e.compareDocumentPosition(p.createElement("fieldset"))}),fe(function(e){return e.innerHTML="<a href='#'></a>","#"===e.firstChild.getAttribute("href")})||le("type|href|height|width",function(e,t,n){return n?void 0:e.getAttribute(t,"type"===t.toLowerCase()?1:2)}),n.attributes&&fe(function(e){return e.innerHTML="<input/>",e.firstChild.setAttribute("value",""),""===e.firstChild.getAttribute("value")})||le("value",function(e,t,n){return n||"input"!==e.nodeName.toLowerCase()?void 0:e.defaultValue}),fe(function(e){return null==e.getAttribute("disabled")})||le(O,function(e,t,n){var i;return n?void 0:e[t]===!0?t.toLowerCase():(i=e.getAttributeNode(t))&&i.specified?i.value:null}),se}(e);y.find=C,y.expr=C.selectors,y.expr[":"]=y.expr.pseudos,y.uniqueSort=y.unique=C.uniqueSort,y.text=C.getText,y.isXMLDoc=C.isXML,y.contains=C.contains,y.escapeSelector=C.escape;var k=function(e,t,n){for(var i=[],r=void 0!==n;(e=e[t])&&9!==e.nodeType;)if(1===e.nodeType){if(r&&y(e).is(n))break;i.push(e)}return i},E=function(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n},S=y.expr.match.needsContext,N=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i,D=/^.[^:#\[\.,]*$/;y.filter=function(e,t,n){var i=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===i.nodeType?y.find.matchesSelector(i,e)?[i]:[]:y.find.matches(e,y.grep(t,function(e){return 1===e.nodeType}))},y.fn.extend({find:function(e){var t,n,i=this.length,r=this;if("string"!=typeof e)return this.pushStack(y(e).filter(function(){for(t=0;i>t;t++)if(y.contains(r[t],this))return!0}));for(n=this.pushStack([]),t=0;i>t;t++)y.find(e,r[t],n);return i>1?y.uniqueSort(n):n},filter:function(e){return this.pushStack(j(this,e||[],!1))},not:function(e){return this.pushStack(j(this,e||[],!0))},is:function(e){return!!j(this,"string"==typeof e&&S.test(e)?y(e):e||[],!1).length}});var A,q=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/,L=y.fn.init=function(e,t,n){var r,o;if(!e)return this;if(n=n||A,"string"==typeof e){if(r="<"===e[0]&&">"===e[e.length-1]&&e.length>=3?[null,e,null]:q.exec(e),!r||!r[1]&&t)return!t||t.jquery?(t||n).find(e):this.constructor(t).find(e);if(r[1]){if(t=t instanceof y?t[0]:t,y.merge(this,y.parseHTML(r[1],t&&t.nodeType?t.ownerDocument||t:i,!0)),N.test(r[1])&&y.isPlainObject(t))for(r in t)y.isFunction(this[r])?this[r](t[r]):this.attr(r,t[r]);return this}return o=i.getElementById(r[2]),o&&(this[0]=o,this.length=1),this}return e.nodeType?(this[0]=e,this.length=1,this):y.isFunction(e)?void 0!==n.ready?n.ready(e):e(y):y.makeArray(e,this)};L.prototype=y.fn,A=y(i);var H=/^(?:parents|prev(?:Until|All))/,F={children:!0,contents:!0,next:!0,prev:!0};y.fn.extend({has:function(e){var t=y(e,this),n=t.length;return this.filter(function(){for(var e=0;n>e;e++)if(y.contains(this,t[e]))return!0})},closest:function(e,t){var n,i=0,r=this.length,o=[],s="string"!=typeof e&&y(e);if(!S.test(e))for(;r>i;i++)for(n=this[i];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(s?s.index(n)>-1:1===n.nodeType&&y.find.matchesSelector(n,e))){o.push(n);break}return this.pushStack(o.length>1?y.uniqueSort(o):o)},index:function(e){return e?"string"==typeof e?u.call(y(e),this[0]):u.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(y.uniqueSort(y.merge(this.get(),y(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),y.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return k(e,"parentNode")},parentsUntil:function(e,t,n){return k(e,"parentNode",n)},next:function(e){return O(e,"nextSibling")},prev:function(e){return O(e,"previousSibling")},nextAll:function(e){return k(e,"nextSibling")},prevAll:function(e){return k(e,"previousSibling")},nextUntil:function(e,t,n){return k(e,"nextSibling",n)},prevUntil:function(e,t,n){return k(e,"previousSibling",n)},siblings:function(e){return E((e.parentNode||{}).firstChild,e)},children:function(e){return E(e.firstChild)},contents:function(e){return e.contentDocument||y.merge([],e.childNodes)}},function(e,t){y.fn[e]=function(n,i){var r=y.map(this,t,n);return"Until"!==e.slice(-5)&&(i=n),i&&"string"==typeof i&&(r=y.filter(i,r)),this.length>1&&(F[e]||y.uniqueSort(r),H.test(e)&&r.reverse()),this.pushStack(r)}});var P=/\S+/g;y.Callbacks=function(e){e="string"==typeof e?R(e):y.extend({},e);var t,n,i,r,o=[],s=[],a=-1,u=function(){for(r=e.once,i=t=!0;s.length;a=-1)for(n=s.shift();++a<o.length;)o[a].apply(n[0],n[1])===!1&&e.stopOnFalse&&(a=o.length,n=!1);e.memory||(n=!1),t=!1,r&&(o=n?[]:"")},f={add:function(){return o&&(n&&!t&&(a=o.length-1,s.push(n)),function i(t){y.each(t,function(t,n){y.isFunction(n)?e.unique&&f.has(n)||o.push(n):n&&n.length&&"string"!==y.type(n)&&i(n)})}(arguments),n&&!t&&u()),this},remove:function(){return y.each(arguments,function(e,t){for(var n;(n=y.inArray(t,o,n))>-1;)o.splice(n,1),a>=n&&a--}),this},has:function(e){return e?y.inArray(e,o)>-1:o.length>0},empty:function(){return o&&(o=[]),this},disable:function(){return r=s=[],o=n="",this},disabled:function(){return!o},lock:function(){return r=s=[],n||t||(o=n=""),this},locked:function(){return!!r},fireWith:function(e,n){return r||(n=n||[],n=[e,n.slice?n.slice():n],s.push(n),t||u()),this},fire:function(){return f.fireWith(this,arguments),this},fired:function(){return!!i}};return f},y.extend({Deferred:function(t){var n=[["notify","progress",y.Callbacks("memory"),y.Callbacks("memory"),2],["resolve","done",y.Callbacks("once memory"),y.Callbacks("once memory"),0,"resolved"],["reject","fail",y.Callbacks("once memory"),y.Callbacks("once memory"),1,"rejected"]],i="pending",r={state:function(){return i},always:function(){return o.done(arguments).fail(arguments),this},"catch":function(e){return r.then(null,e)},pipe:function(){var e=arguments;return y.Deferred(function(t){y.each(n,function(n,i){var r=y.isFunction(e[i[4]])&&e[i[4]];o[i[1]](function(){var e=r&&r.apply(this,arguments);e&&y.isFunction(e.promise)?e.promise().progress(t.notify).done(t.resolve).fail(t.reject):t[i[0]+"With"](this,r?[e]:arguments)})}),e=null}).promise()},then:function(t,i,r){function s(t,n,i,r){return function(){var a=this,u=arguments,f=function(){var e,f;if(!(o>t)){if(e=i.apply(a,u),e===n.promise())throw new TypeError("Thenable self-resolution");f=e&&("object"==typeof e||"function"==typeof e)&&e.then,y.isFunction(f)?r?f.call(e,s(o,n,M,r),s(o,n,I,r)):(o++,f.call(e,s(o,n,M,r),s(o,n,I,r),s(o,n,M,n.notifyWith))):(i!==M&&(a=void 0,u=[e]),(r||n.resolveWith)(a,u))}},l=r?f:function(){try{f()}catch(e){y.Deferred.exceptionHook&&y.Deferred.exceptionHook(e,l.stackTrace),t+1>=o&&(i!==I&&(a=void 0,u=[e]),n.rejectWith(a,u))}};t?l():(y.Deferred.getStackHook&&(l.stackTrace=y.Deferred.getStackHook()),e.setTimeout(l))}}var o=0;return y.Deferred(function(e){n[0][3].add(s(0,e,y.isFunction(r)?r:M,e.notifyWith)),n[1][3].add(s(0,e,y.isFunction(t)?t:M)),n[2][3].add(s(0,e,y.isFunction(i)?i:I))}).promise()},promise:function(e){return null!=e?y.extend(e,r):r}},o={};return y.each(n,function(e,t){var s=t[2],a=t[5];r[t[1]]=s.add,a&&s.add(function(){i=a},n[3-e][2].disable,n[0][2].lock),s.add(t[3].fire),o[t[0]]=function(){return o[t[0]+"With"](this===o?void 0:this,arguments),this},o[t[0]+"With"]=s.fireWith}),r.promise(o),t&&t.call(o,o),o},when:function(e){var t=arguments.length,n=t,i=Array(n),r=o.call(arguments),s=y.Deferred(),a=function(e){return function(n){i[e]=this,r[e]=arguments.length>1?o.call(arguments):n,--t||s.resolveWith(i,r)}};if(1>=t&&(W(e,s.done(a(n)).resolve,s.reject),"pending"===s.state()||y.isFunction(r[n]&&r[n].then)))return s.then();for(;n--;)W(r[n],a(n),s.reject);return s.promise()}});var $=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;y.Deferred.exceptionHook=function(t,n){e.console&&e.console.warn&&t&&$.test(t.name)&&e.console.warn("jQuery.Deferred exception: "+t.message,t.stack,n)},y.readyException=function(t){e.setTimeout(function(){throw t})};var B=y.Deferred();y.fn.ready=function(e){return B.then(e)["catch"](function(e){y.readyException(e)}),this},y.extend({isReady:!1,readyWait:1,holdReady:function(e){e?y.readyWait++:y.ready(!0)},ready:function(e){(e===!0?--y.readyWait:y.isReady)||(y.isReady=!0,e!==!0&&--y.readyWait>0||B.resolveWith(i,[y]))}}),y.ready.then=B.then,"complete"===i.readyState||"loading"!==i.readyState&&!i.documentElement.doScroll?e.setTimeout(y.ready):(i.addEventListener("DOMContentLoaded",_),e.addEventListener("load",_));var z=function(e,t,n,i,r,o,s){var a=0,u=e.length,f=null==n;if("object"===y.type(n)){r=!0;for(a in n)z(e,t,a,n[a],!0,o,s)}else if(void 0!==i&&(r=!0,y.isFunction(i)||(s=!0),f&&(s?(t.call(e,i),t=null):(f=t,t=function(e,t,n){return f.call(y(e),n)})),t))for(;u>a;a++)t(e[a],n,s?i:i.call(e[a],a,t(e[a],n)));return r?e:f?t.call(e):u?t(e[0],n):o},X=function(e){return 1===e.nodeType||9===e.nodeType||!+e.nodeType};U.uid=1,U.prototype={cache:function(e){var t=e[this.expando];return t||(t={},X(e)&&(e.nodeType?e[this.expando]=t:Object.defineProperty(e,this.expando,{value:t,configurable:!0}))),t},set:function(e,t,n){var i,r=this.cache(e);if("string"==typeof t)r[y.camelCase(t)]=n;else for(i in t)r[y.camelCase(i)]=t[i];return r},get:function(e,t){return void 0===t?this.cache(e):e[this.expando]&&e[this.expando][y.camelCase(t)]},access:function(e,t,n){return void 0===t||t&&"string"==typeof t&&void 0===n?this.get(e,t):(this.set(e,t,n),void 0!==n?n:t)},remove:function(e,t){var n,i=e[this.expando];if(void 0!==i){if(void 0!==t){y.isArray(t)?t=t.map(y.camelCase):(t=y.camelCase(t),t=t in i?[t]:t.match(P)||[]),n=t.length;for(;n--;)delete i[t[n]]}(void 0===t||y.isEmptyObject(i))&&(e.nodeType?e[this.expando]=void 0:delete e[this.expando])}},hasData:function(e){var t=e[this.expando];return void 0!==t&&!y.isEmptyObject(t)}};var V=new U,G=new U,Y=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,Q=/[A-Z]/g;y.extend({hasData:function(e){return G.hasData(e)||V.hasData(e)},data:function(e,t,n){return G.access(e,t,n)},removeData:function(e,t){G.remove(e,t)},_data:function(e,t,n){return V.access(e,t,n)},_removeData:function(e,t){V.remove(e,t)}}),y.fn.extend({data:function(e,t){var n,i,r,o=this[0],s=o&&o.attributes;if(void 0===e){if(this.length&&(r=G.get(o),1===o.nodeType&&!V.get(o,"hasDataAttrs"))){for(n=s.length;n--;)s[n]&&(i=s[n].name,0===i.indexOf("data-")&&(i=y.camelCase(i.slice(5)),J(o,i,r[i])));V.set(o,"hasDataAttrs",!0)}return r}return"object"==typeof e?this.each(function(){G.set(this,e)}):z(this,function(t){var n;if(o&&void 0===t){if(n=G.get(o,e),void 0!==n)return n;if(n=J(o,e),void 0!==n)return n}else this.each(function(){G.set(this,e,t)})},null,t,arguments.length>1,null,!0)},removeData:function(e){return this.each(function(){G.remove(this,e)})}}),y.extend({queue:function(e,t,n){var i;return e?(t=(t||"fx")+"queue",i=V.get(e,t),n&&(!i||y.isArray(n)?i=V.access(e,t,y.makeArray(n)):i.push(n)),i||[]):void 0},dequeue:function(e,t){t=t||"fx";var n=y.queue(e,t),i=n.length,r=n.shift(),o=y._queueHooks(e,t),s=function(){y.dequeue(e,t)};"inprogress"===r&&(r=n.shift(),i--),r&&("fx"===t&&n.unshift("inprogress"),delete o.stop,r.call(e,s,o)),!i&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return V.get(e,n)||V.access(e,n,{empty:y.Callbacks("once memory").add(function(){V.remove(e,[t+"queue",n])})})}}),y.fn.extend({queue:function(e,t){var n=2;return"string"!=typeof e&&(t=e,e="fx",n--),arguments.length<n?y.queue(this[0],e):void 0===t?this:this.each(function(){var n=y.queue(this,e,t);y._queueHooks(this,e),"fx"===e&&"inprogress"!==n[0]&&y.dequeue(this,e)})},dequeue:function(e){return this.each(function(){y.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var n,i=1,r=y.Deferred(),o=this,s=this.length,a=function(){--i||r.resolveWith(o,[o])};for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";s--;)n=V.get(o[s],e+"queueHooks"),n&&n.empty&&(i++,n.empty.add(a));return a(),r.promise(t)}});var K=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,Z=new RegExp("^(?:([+-])=|)("+K+")([a-z%]*)$","i"),ee=["Top","Right","Bottom","Left"],te=function(e,t){return e=t||e,"none"===e.style.display||""===e.style.display&&y.contains(e.ownerDocument,e)&&"none"===y.css(e,"display")},ne=function(e,t,n,i){var r,o,s={};for(o in t)s[o]=e.style[o],e.style[o]=t[o];r=n.apply(e,i||[]);for(o in t)e.style[o]=s[o];return r},re={};y.fn.extend({show:function(){return se(this,!0)},hide:function(){return se(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){te(this)?y(this).show():y(this).hide()})}});var ae=/^(?:checkbox|radio)$/i,ue=/<([a-z][^\/\0>\x20\t\r\n\f]+)/i,fe=/^$|\/(?:java|ecma)script/i,le={option:[1,"<select multiple='multiple'>","</select>"],thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};le.optgroup=le.option,le.tbody=le.tfoot=le.colgroup=le.caption=le.thead,le.th=le.td;var pe=/<|&#?\w+;/;!function(){var e=i.createDocumentFragment(),t=e.appendChild(i.createElement("div")),n=i.createElement("input");n.setAttribute("type","radio"),n.setAttribute("checked","checked"),n.setAttribute("name","t"),t.appendChild(n),h.checkClone=t.cloneNode(!0).cloneNode(!0).lastChild.checked,t.innerHTML="<textarea>x</textarea>",h.noCloneChecked=!!t.cloneNode(!0).lastChild.defaultValue}();var ge=i.documentElement,me=/^key/,ye=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,ve=/^([^.]*)(?:\.(.+)|)/;y.event={global:{},add:function(e,t,n,i,r){var o,s,a,u,f,l,c,d,p,h,g,m=V.get(e);if(m)for(n.handler&&(o=n,n=o.handler,r=o.selector),r&&y.find.matchesSelector(ge,r),n.guid||(n.guid=y.guid++),(u=m.events)||(u=m.events={}),(s=m.handle)||(s=m.handle=function(t){return"undefined"!=typeof y&&y.event.triggered!==t.type?y.event.dispatch.apply(e,arguments):void 0}),t=(t||"").match(P)||[""],f=t.length;f--;)a=ve.exec(t[f])||[],p=g=a[1],h=(a[2]||"").split(".").sort(),p&&(c=y.event.special[p]||{},p=(r?c.delegateType:c.bindType)||p,c=y.event.special[p]||{},l=y.extend({type:p,origType:g,data:i,handler:n,guid:n.guid,selector:r,needsContext:r&&y.expr.match.needsContext.test(r),namespace:h.join(".")},o),(d=u[p])||(d=u[p]=[],d.delegateCount=0,c.setup&&c.setup.call(e,i,h,s)!==!1||e.addEventListener&&e.addEventListener(p,s)),c.add&&(c.add.call(e,l),l.handler.guid||(l.handler.guid=n.guid)),r?d.splice(d.delegateCount++,0,l):d.push(l),y.event.global[p]=!0)},remove:function(e,t,n,i,r){var o,s,a,u,f,l,c,d,p,h,g,m=V.hasData(e)&&V.get(e);if(m&&(u=m.events)){for(t=(t||"").match(P)||[""],f=t.length;f--;)if(a=ve.exec(t[f])||[],p=g=a[1],h=(a[2]||"").split(".").sort(),p){for(c=y.event.special[p]||{},p=(i?c.delegateType:c.bindType)||p,d=u[p]||[],a=a[2]&&new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"),s=o=d.length;o--;)l=d[o],!r&&g!==l.origType||n&&n.guid!==l.guid||a&&!a.test(l.namespace)||i&&i!==l.selector&&("**"!==i||!l.selector)||(d.splice(o,1),l.selector&&d.delegateCount--,c.remove&&c.remove.call(e,l));s&&!d.length&&(c.teardown&&c.teardown.call(e,h,m.handle)!==!1||y.removeEvent(e,p,m.handle),delete u[p])}else for(p in u)y.event.remove(e,p+t[f],n,i,!0);y.isEmptyObject(u)&&V.remove(e,"handle events")}},dispatch:function(e){var n,i,r,o,s,a,t=y.event.fix(e),u=new Array(arguments.length),f=(V.get(this,"events")||{})[t.type]||[],l=y.event.special[t.type]||{};for(u[0]=t,n=1;n<arguments.length;n++)u[n]=arguments[n];if(t.delegateTarget=this,!l.preDispatch||l.preDispatch.call(this,t)!==!1){for(a=y.event.handlers.call(this,t,f),n=0;(o=a[n++])&&!t.isPropagationStopped();)for(t.currentTarget=o.elem,i=0;(s=o.handlers[i++])&&!t.isImmediatePropagationStopped();)(!t.rnamespace||t.rnamespace.test(s.namespace))&&(t.handleObj=s,t.data=s.data,r=((y.event.special[s.origType]||{}).handle||s.handler).apply(o.elem,u),void 0!==r&&(t.result=r)===!1&&(t.preventDefault(),t.stopPropagation()));return l.postDispatch&&l.postDispatch.call(this,t),t.result}},handlers:function(e,t){var n,i,r,o,s=[],a=t.delegateCount,u=e.target;if(a&&u.nodeType&&("click"!==e.type||isNaN(e.button)||e.button<1))for(;u!==this;u=u.parentNode||this)if(1===u.nodeType&&(u.disabled!==!0||"click"!==e.type)){for(i=[],n=0;a>n;n++)o=t[n],r=o.selector+" ",void 0===i[r]&&(i[r]=o.needsContext?y(r,this).index(u)>-1:y.find(r,this,null,[u]).length),i[r]&&i.push(o);i.length&&s.push({elem:u,handlers:i})}return a<t.length&&s.push({elem:this,handlers:t.slice(a)}),s},addProp:function(e,t){Object.defineProperty(y.Event.prototype,e,{enumerable:!0,configurable:!0,get:y.isFunction(t)?function(){return this.originalEvent?t(this.originalEvent):void 0}:function(){return this.originalEvent?this.originalEvent[e]:void 0},set:function(t){Object.defineProperty(this,e,{enumerable:!0,configurable:!0,writable:!0,value:t})}})},fix:function(e){return e[y.expando]?e:new y.Event(e)},special:{load:{noBubble:!0},focus:{trigger:function(){return this!==we()&&this.focus?(this.focus(),!1):void 0},delegateType:"focusin"},blur:{trigger:function(){return this===we()&&this.blur?(this.blur(),!1):void 0},delegateType:"focusout"},click:{trigger:function(){return"checkbox"===this.type&&this.click&&y.nodeName(this,"input")?(this.click(),!1):void 0},_default:function(e){return y.nodeName(e.target,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}},y.removeEvent=function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n)},y.Event=function(e,t){return this instanceof y.Event?(e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&e.returnValue===!1?xe:be,this.target=e.target&&3===e.target.nodeType?e.target.parentNode:e.target,this.currentTarget=e.currentTarget,this.relatedTarget=e.relatedTarget):this.type=e,t&&y.extend(this,t),this.timeStamp=e&&e.timeStamp||y.now(),void(this[y.expando]=!0)):new y.Event(e,t)},y.Event.prototype={constructor:y.Event,isDefaultPrevented:be,isPropagationStopped:be,isImmediatePropagationStopped:be,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=xe,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=xe,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=xe,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},y.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,"char":!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:function(e){var t=e.button;return null==e.which&&me.test(e.type)?null!=e.charCode?e.charCode:e.keyCode:!e.which&&void 0!==t&&ye.test(e.type)?1&t?1:2&t?3:4&t?2:0:e.which}},y.event.addProp),y.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,t){y.event.special[e]={delegateType:t,bindType:t,handle:function(e){var n,i=this,r=e.relatedTarget,o=e.handleObj;return(!r||r!==i&&!y.contains(i,r))&&(e.type=o.origType,n=o.handler.apply(this,arguments),e.type=t),n}}}),y.fn.extend({on:function(e,t,n,i){return Te(this,e,t,n,i)},one:function(e,t,n,i){return Te(this,e,t,n,i,1)},off:function(e,t,n){var i,r;if(e&&e.preventDefault&&e.handleObj)return i=e.handleObj,y(e.delegateTarget).off(i.namespace?i.origType+"."+i.namespace:i.origType,i.selector,i.handler),this;if("object"==typeof e){for(r in e)this.off(r,t,e[r]);return this}return(t===!1||"function"==typeof t)&&(n=t,t=void 0),n===!1&&(n=be),this.each(function(){y.event.remove(this,e,n,t)})}});var Ce=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([a-z][^\/\0>\x20\t\r\n\f]*)[^>]*)\/>/gi,ke=/<script|<style|<link/i,Ee=/checked\s*(?:[^=]|=\s*.checked.)/i,Se=/^true\/(.*)/,Ne=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g;y.extend({htmlPrefilter:function(e){return e.replace(Ce,"<$1></$2>")},clone:function(e,t,n){var i,r,o,s,a=e.cloneNode(!0),u=y.contains(e.ownerDocument,e);if(!(h.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||y.isXMLDoc(e)))for(s=ce(a),o=ce(e),i=0,r=o.length;r>i;i++)Le(o[i],s[i]);if(t)if(n)for(o=o||ce(e),s=s||ce(a),i=0,r=o.length;r>i;i++)qe(o[i],s[i]);else qe(e,a);return s=ce(a,"script"),s.length>0&&de(s,!u&&ce(e,"script")),a},cleanData:function(e){for(var t,n,i,r=y.event.special,o=0;void 0!==(n=e[o]);o++)if(X(n)){if(t=n[V.expando]){if(t.events)for(i in t.events)r[i]?y.event.remove(n,i):y.removeEvent(n,i,t.handle);n[V.expando]=void 0}n[G.expando]&&(n[G.expando]=void 0)}}}),y.fn.extend({detach:function(e){return Fe(this,e,!0)},remove:function(e){return Fe(this,e)},text:function(e){return z(this,function(e){return void 0===e?y.text(this):this.empty().each(function(){(1===this.nodeType||11===this.nodeType||9===this.nodeType)&&(this.textContent=e)})},null,e,arguments.length)},append:function(){return He(this,arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=De(this,e);t.appendChild(e)}})},prepend:function(){return He(this,arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=De(this,e);t.insertBefore(e,t.firstChild)}})},before:function(){return He(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return He(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)1===e.nodeType&&(y.cleanData(ce(e,!1)),e.textContent="");return this},clone:function(e,t){return e=null==e?!1:e,t=null==t?e:t,this.map(function(){return y.clone(this,e,t)})},html:function(e){return z(this,function(e){var t=this[0]||{},n=0,i=this.length;if(void 0===e&&1===t.nodeType)return t.innerHTML;if("string"==typeof e&&!ke.test(e)&&!le[(ue.exec(e)||["",""])[1].toLowerCase()]){e=y.htmlPrefilter(e);try{for(;i>n;n++)t=this[n]||{},1===t.nodeType&&(y.cleanData(ce(t,!1)),t.innerHTML=e);t=0}catch(r){}}t&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var e=[];return He(this,arguments,function(t){var n=this.parentNode;y.inArray(this,e)<0&&(y.cleanData(ce(this)),n&&n.replaceChild(t,this))},e)}}),y.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,t){y.fn[e]=function(e){for(var n,i=[],r=y(e),o=r.length-1,s=0;o>=s;s++)n=s===o?this:this.clone(!0),y(r[s])[t](n),a.apply(i,n.get());return this.pushStack(i)}});var Oe=/^margin/,Pe=new RegExp("^("+K+")(?!px)[a-z%]+$","i"),Re=function(t){var n=t.ownerDocument.defaultView;return n&&n.opener||(n=e),n.getComputedStyle(t)};!function(){function t(){if(u){u.style.cssText="box-sizing:border-box;position:relative;display:block;margin:auto;border:1px;padding:1px;top:1%;width:50%",u.innerHTML="",ge.appendChild(a);var t=e.getComputedStyle(u);n="1%"!==t.top,s="2px"===t.marginLeft,r="4px"===t.width,u.style.marginRight="50%",o="4px"===t.marginRight,ge.removeChild(a),u=null}}var n,r,o,s,a=i.createElement("div"),u=i.createElement("div");u.style&&(u.style.backgroundClip="content-box",u.cloneNode(!0).style.backgroundClip="",h.clearCloneStyle="content-box"===u.style.backgroundClip,a.style.cssText="border:0;width:8px;height:0;top:0;left:-9999px;padding:0;margin-top:1px;position:absolute",a.appendChild(u),y.extend(h,{pixelPosition:function(){return t(),n},boxSizingReliable:function(){return t(),r},pixelMarginRight:function(){return t(),o},reliableMarginLeft:function(){return t(),s}}))}();var We=/^(none|table(?!-c[ea]).+)/,$e={position:"absolute",visibility:"hidden",display:"block"},Be={letterSpacing:"0",fontWeight:"400"},_e=["Webkit","Moz","ms"],ze=i.createElement("div").style;y.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=Me(e,"opacity");return""===n?"1":n}}}},cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{"float":"cssFloat"},style:function(e,t,n,i){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var r,o,s,a=y.camelCase(t),u=e.style;return t=y.cssProps[a]||(y.cssProps[a]=Xe(a)||a),s=y.cssHooks[t]||y.cssHooks[a],void 0===n?s&&"get"in s&&void 0!==(r=s.get(e,!1,i))?r:u[t]:(o=typeof n,"string"===o&&(r=Z.exec(n))&&r[1]&&(n=ie(e,t,r),o="number"),null!=n&&n===n&&("number"===o&&(n+=r&&r[3]||(y.cssNumber[a]?"":"px")),h.clearCloneStyle||""!==n||0!==t.indexOf("background")||(u[t]="inherit"),s&&"set"in s&&void 0===(n=s.set(e,n,i))||(u[t]=n)),void 0)}},css:function(e,t,n,i){var r,o,s,a=y.camelCase(t);return t=y.cssProps[a]||(y.cssProps[a]=Xe(a)||a),s=y.cssHooks[t]||y.cssHooks[a],s&&"get"in s&&(r=s.get(e,!0,n)),void 0===r&&(r=Me(e,t,i)),"normal"===r&&t in Be&&(r=Be[t]),""===n||n?(o=parseFloat(r),n===!0||isFinite(o)?o||0:r):r}}),y.each(["height","width"],function(e,t){y.cssHooks[t]={get:function(e,n,i){return n?!We.test(y.css(e,"display"))||e.getClientRects().length&&e.getBoundingClientRect().width?Ge(e,t,i):ne(e,$e,function(){return Ge(e,t,i)}):void 0},set:function(e,n,i){var r,o=i&&Re(e),s=i&&Ve(e,t,i,"border-box"===y.css(e,"boxSizing",!1,o),o);return s&&(r=Z.exec(n))&&"px"!==(r[3]||"px")&&(e.style[t]=n,n=y.css(e,t)),Ue(e,n,s)}}}),y.cssHooks.marginLeft=Ie(h.reliableMarginLeft,function(e,t){return t?(parseFloat(Me(e,"marginLeft"))||e.getBoundingClientRect().left-ne(e,{marginLeft:0},function(){return e.getBoundingClientRect().left}))+"px":void 0}),y.each({margin:"",padding:"",border:"Width"},function(e,t){y.cssHooks[e+t]={expand:function(n){for(var i=0,r={},o="string"==typeof n?n.split(" "):[n];4>i;i++)r[e+ee[i]+t]=o[i]||o[i-2]||o[0];return r}},Oe.test(e)||(y.cssHooks[e+t].set=Ue)}),y.fn.extend({css:function(e,t){return z(this,function(e,t,n){var i,r,o={},s=0;if(y.isArray(t)){for(i=Re(e),r=t.length;r>s;s++)o[t[s]]=y.css(e,t[s],!1,i);return o}return void 0!==n?y.style(e,t,n):y.css(e,t)},e,t,arguments.length>1)}}),y.Tween=Ye,Ye.prototype={constructor:Ye,init:function(e,t,n,i,r,o){this.elem=e,this.prop=n,this.easing=r||y.easing._default,this.options=t,this.start=this.now=this.cur(),this.end=i,this.unit=o||(y.cssNumber[n]?"":"px")},cur:function(){var e=Ye.propHooks[this.prop];return e&&e.get?e.get(this):Ye.propHooks._default.get(this)},run:function(e){var t,n=Ye.propHooks[this.prop];return this.options.duration?this.pos=t=y.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):Ye.propHooks._default.set(this),this}},Ye.prototype.init.prototype=Ye.prototype,Ye.propHooks={_default:{get:function(e){var t;return 1!==e.elem.nodeType||null!=e.elem[e.prop]&&null==e.elem.style[e.prop]?e.elem[e.prop]:(t=y.css(e.elem,e.prop,""),t&&"auto"!==t?t:0)},set:function(e){y.fx.step[e.prop]?y.fx.step[e.prop](e):1!==e.elem.nodeType||null==e.elem.style[y.cssProps[e.prop]]&&!y.cssHooks[e.prop]?e.elem[e.prop]=e.now:y.style(e.elem,e.prop,e.now+e.unit)}}},Ye.propHooks.scrollTop=Ye.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},y.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},_default:"swing"},y.fx=Ye.prototype.init,y.fx.step={};var Qe,Je,Ke=/^(?:toggle|show|hide)$/,Ze=/queueHooks$/;y.Animation=y.extend(st,{tweeners:{"*":[function(e,t){var n=this.createTween(e,t);return ie(n.elem,e,Z.exec(t),n),n}]},tweener:function(e,t){y.isFunction(e)?(t=e,e=["*"]):e=e.match(P);for(var n,i=0,r=e.length;r>i;i++)n=e[i],st.tweeners[n]=st.tweeners[n]||[],st.tweeners[n].unshift(t)},prefilters:[rt],prefilter:function(e,t){t?st.prefilters.unshift(e):st.prefilters.push(e)}}),y.speed=function(e,t,n){var r=e&&"object"==typeof e?y.extend({},e):{complete:n||!n&&t||y.isFunction(e)&&e,duration:e,easing:n&&t||t&&!y.isFunction(t)&&t};return y.fx.off||i.hidden?r.duration=0:r.duration="number"==typeof r.duration?r.duration:r.duration in y.fx.speeds?y.fx.speeds[r.duration]:y.fx.speeds._default,(null==r.queue||r.queue===!0)&&(r.queue="fx"),r.old=r.complete,r.complete=function(){y.isFunction(r.old)&&r.old.call(this),r.queue&&y.dequeue(this,r.queue)},r},y.fn.extend({fadeTo:function(e,t,n,i){return this.filter(te).css("opacity",0).show().end().animate({opacity:t},e,n,i)},animate:function(e,t,n,i){var r=y.isEmptyObject(e),o=y.speed(t,n,i),s=function(){var t=st(this,y.extend({},e),o);(r||V.get(this,"finish"))&&t.stop(!0)};return s.finish=s,r||o.queue===!1?this.each(s):this.queue(o.queue,s)},stop:function(e,t,n){var i=function(e){var t=e.stop;delete e.stop,t(n)};return"string"!=typeof e&&(n=t,t=e,e=void 0),t&&e!==!1&&this.queue(e||"fx",[]),this.each(function(){var t=!0,r=null!=e&&e+"queueHooks",o=y.timers,s=V.get(this);if(r)s[r]&&s[r].stop&&i(s[r]);else for(r in s)s[r]&&s[r].stop&&Ze.test(r)&&i(s[r]);for(r=o.length;r--;)o[r].elem!==this||null!=e&&o[r].queue!==e||(o[r].anim.stop(n),t=!1,o.splice(r,1));(t||!n)&&y.dequeue(this,e)})},finish:function(e){return e!==!1&&(e=e||"fx"),this.each(function(){var t,n=V.get(this),i=n[e+"queue"],r=n[e+"queueHooks"],o=y.timers,s=i?i.length:0;for(n.finish=!0,y.queue(this,e,[]),r&&r.stop&&r.stop.call(this,!0),t=o.length;t--;)o[t].elem===this&&o[t].queue===e&&(o[t].anim.stop(!0),o.splice(t,1));for(t=0;s>t;t++)i[t]&&i[t].finish&&i[t].finish.call(this);delete n.finish})}}),y.each(["toggle","show","hide"],function(e,t){var n=y.fn[t];y.fn[t]=function(e,i,r){return null==e||"boolean"==typeof e?n.apply(this,arguments):this.animate(nt(t,!0),e,i,r)}}),y.each({slideDown:nt("show"),
slideUp:nt("hide"),slideToggle:nt("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,t){y.fn[e]=function(e,n,i){return this.animate(t,e,n,i)}}),y.timers=[],y.fx.tick=function(){var e,t=0,n=y.timers;for(Qe=y.now();t<n.length;t++)e=n[t],e()||n[t]!==e||n.splice(t--,1);n.length||y.fx.stop(),Qe=void 0},y.fx.timer=function(e){y.timers.push(e),e()?y.fx.start():y.timers.pop()},y.fx.interval=13,y.fx.start=function(){Je||(Je=e.requestAnimationFrame?e.requestAnimationFrame(et):e.setInterval(y.fx.tick,y.fx.interval))},y.fx.stop=function(){e.cancelAnimationFrame?e.cancelAnimationFrame(Je):e.clearInterval(Je),Je=null},y.fx.speeds={slow:600,fast:200,_default:400},y.fn.delay=function(t,n){return t=y.fx?y.fx.speeds[t]||t:t,n=n||"fx",this.queue(n,function(n,i){var r=e.setTimeout(n,t);i.stop=function(){e.clearTimeout(r)}})},function(){var e=i.createElement("input"),t=i.createElement("select"),n=t.appendChild(i.createElement("option"));e.type="checkbox",h.checkOn=""!==e.value,h.optSelected=n.selected,e=i.createElement("input"),e.value="t",e.type="radio",h.radioValue="t"===e.value}();var at,ut=y.expr.attrHandle;y.fn.extend({attr:function(e,t){return z(this,y.attr,e,t,arguments.length>1)},removeAttr:function(e){return this.each(function(){y.removeAttr(this,e)})}}),y.extend({attr:function(e,t,n){var i,r,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return"undefined"==typeof e.getAttribute?y.prop(e,t,n):(1===o&&y.isXMLDoc(e)||(r=y.attrHooks[t.toLowerCase()]||(y.expr.match.bool.test(t)?at:void 0)),void 0!==n?null===n?void y.removeAttr(e,t):r&&"set"in r&&void 0!==(i=r.set(e,n,t))?i:(e.setAttribute(t,n+""),n):r&&"get"in r&&null!==(i=r.get(e,t))?i:(i=y.find.attr(e,t),null==i?void 0:i))},attrHooks:{type:{set:function(e,t){if(!h.radioValue&&"radio"===t&&y.nodeName(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}},removeAttr:function(e,t){var n,i=0,r=t&&t.match(P);if(r&&1===e.nodeType)for(;n=r[i++];)e.removeAttribute(n)}}),at={set:function(e,t,n){return t===!1?y.removeAttr(e,n):e.setAttribute(n,n),n}},y.each(y.expr.match.bool.source.match(/\w+/g),function(e,t){var n=ut[t]||y.find.attr;ut[t]=function(e,t,i){var r,o,s=t.toLowerCase();return i||(o=ut[s],ut[s]=r,r=null!=n(e,t,i)?s:null,ut[s]=o),r}});var ft=/^(?:input|select|textarea|button)$/i,lt=/^(?:a|area)$/i;y.fn.extend({prop:function(e,t){return z(this,y.prop,e,t,arguments.length>1)},removeProp:function(e){return this.each(function(){delete this[y.propFix[e]||e]})}}),y.extend({prop:function(e,t,n){var i,r,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&y.isXMLDoc(e)||(t=y.propFix[t]||t,r=y.propHooks[t]),void 0!==n?r&&"set"in r&&void 0!==(i=r.set(e,n,t))?i:e[t]=n:r&&"get"in r&&null!==(i=r.get(e,t))?i:e[t]},propHooks:{tabIndex:{get:function(e){var t=y.find.attr(e,"tabindex");return t?parseInt(t,10):ft.test(e.nodeName)||lt.test(e.nodeName)&&e.href?0:-1}}},propFix:{"for":"htmlFor","class":"className"}}),h.optSelected||(y.propHooks.selected={get:function(e){var t=e.parentNode;return t&&t.parentNode&&t.parentNode.selectedIndex,null},set:function(e){var t=e.parentNode;t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex)}}),y.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){y.propFix[this.toLowerCase()]=this});var ct=/[\t\r\n\f]/g;y.fn.extend({addClass:function(e){var t,n,i,r,o,s,a,u=0;if(y.isFunction(e))return this.each(function(t){y(this).addClass(e.call(this,t,dt(this)))});if("string"==typeof e&&e)for(t=e.match(P)||[];n=this[u++];)if(r=dt(n),i=1===n.nodeType&&(" "+r+" ").replace(ct," ")){for(s=0;o=t[s++];)i.indexOf(" "+o+" ")<0&&(i+=o+" ");a=y.trim(i),r!==a&&n.setAttribute("class",a)}return this},removeClass:function(e){var t,n,i,r,o,s,a,u=0;if(y.isFunction(e))return this.each(function(t){y(this).removeClass(e.call(this,t,dt(this)))});if(!arguments.length)return this.attr("class","");if("string"==typeof e&&e)for(t=e.match(P)||[];n=this[u++];)if(r=dt(n),i=1===n.nodeType&&(" "+r+" ").replace(ct," ")){for(s=0;o=t[s++];)for(;i.indexOf(" "+o+" ")>-1;)i=i.replace(" "+o+" "," ");a=y.trim(i),r!==a&&n.setAttribute("class",a)}return this},toggleClass:function(e,t){var n=typeof e;return"boolean"==typeof t&&"string"===n?t?this.addClass(e):this.removeClass(e):y.isFunction(e)?this.each(function(n){y(this).toggleClass(e.call(this,n,dt(this),t),t)}):this.each(function(){var t,i,r,o;if("string"===n)for(i=0,r=y(this),o=e.match(P)||[];t=o[i++];)r.hasClass(t)?r.removeClass(t):r.addClass(t);else(void 0===e||"boolean"===n)&&(t=dt(this),t&&V.set(this,"__className__",t),this.setAttribute&&this.setAttribute("class",t||e===!1?"":V.get(this,"__className__")||""))})},hasClass:function(e){var t,n,i=0;for(t=" "+e+" ";n=this[i++];)if(1===n.nodeType&&(" "+dt(n)+" ").replace(ct," ").indexOf(t)>-1)return!0;return!1}});var pt=/\r/g,ht=/[\x20\t\r\n\f]+/g;y.fn.extend({val:function(e){var t,n,i,r=this[0];{if(arguments.length)return i=y.isFunction(e),this.each(function(n){var r;1===this.nodeType&&(r=i?e.call(this,n,y(this).val()):e,null==r?r="":"number"==typeof r?r+="":y.isArray(r)&&(r=y.map(r,function(e){return null==e?"":e+""})),t=y.valHooks[this.type]||y.valHooks[this.nodeName.toLowerCase()],t&&"set"in t&&void 0!==t.set(this,r,"value")||(this.value=r))});if(r)return t=y.valHooks[r.type]||y.valHooks[r.nodeName.toLowerCase()],t&&"get"in t&&void 0!==(n=t.get(r,"value"))?n:(n=r.value,"string"==typeof n?n.replace(pt,""):null==n?"":n)}}}),y.extend({valHooks:{option:{get:function(e){var t=y.find.attr(e,"value");return null!=t?t:y.trim(y.text(e)).replace(ht," ")}},select:{get:function(e){for(var t,n,i=e.options,r=e.selectedIndex,o="select-one"===e.type,s=o?null:[],a=o?r+1:i.length,u=0>r?a:o?r:0;a>u;u++)if(n=i[u],(n.selected||u===r)&&!n.disabled&&(!n.parentNode.disabled||!y.nodeName(n.parentNode,"optgroup"))){if(t=y(n).val(),o)return t;s.push(t)}return s},set:function(e,t){for(var n,i,r=e.options,o=y.makeArray(t),s=r.length;s--;)i=r[s],(i.selected=y.inArray(y.valHooks.option.get(i),o)>-1)&&(n=!0);return n||(e.selectedIndex=-1),o}}}}),y.each(["radio","checkbox"],function(){y.valHooks[this]={set:function(e,t){return y.isArray(t)?e.checked=y.inArray(y(e).val(),t)>-1:void 0}},h.checkOn||(y.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})});var gt=/^(?:focusinfocus|focusoutblur)$/;y.extend(y.event,{trigger:function(t,n,r,o){var s,a,u,f,l,d,p,h=[r||i],g=c.call(t,"type")?t.type:t,m=c.call(t,"namespace")?t.namespace.split("."):[];if(a=u=r=r||i,3!==r.nodeType&&8!==r.nodeType&&!gt.test(g+y.event.triggered)&&(g.indexOf(".")>-1&&(m=g.split("."),g=m.shift(),m.sort()),l=g.indexOf(":")<0&&"on"+g,t=t[y.expando]?t:new y.Event(g,"object"==typeof t&&t),t.isTrigger=o?2:3,t.namespace=m.join("."),t.rnamespace=t.namespace?new RegExp("(^|\\.)"+m.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,t.result=void 0,t.target||(t.target=r),n=null==n?[t]:y.makeArray(n,[t]),p=y.event.special[g]||{},o||!p.trigger||p.trigger.apply(r,n)!==!1)){if(!o&&!p.noBubble&&!y.isWindow(r)){for(f=p.delegateType||g,gt.test(f+g)||(a=a.parentNode);a;a=a.parentNode)h.push(a),u=a;u===(r.ownerDocument||i)&&h.push(u.defaultView||u.parentWindow||e)}for(s=0;(a=h[s++])&&!t.isPropagationStopped();)t.type=s>1?f:p.bindType||g,d=(V.get(a,"events")||{})[t.type]&&V.get(a,"handle"),d&&d.apply(a,n),d=l&&a[l],d&&d.apply&&X(a)&&(t.result=d.apply(a,n),t.result===!1&&t.preventDefault());return t.type=g,o||t.isDefaultPrevented()||p._default&&p._default.apply(h.pop(),n)!==!1||!X(r)||l&&y.isFunction(r[g])&&!y.isWindow(r)&&(u=r[l],u&&(r[l]=null),y.event.triggered=g,r[g](),y.event.triggered=void 0,u&&(r[l]=u)),t.result}},simulate:function(e,t,n){var i=y.extend(new y.Event,n,{type:e,isSimulated:!0});y.event.trigger(i,null,t)}}),y.fn.extend({trigger:function(e,t){return this.each(function(){y.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];return n?y.event.trigger(e,t,n,!0):void 0}}),y.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(e,t){y.fn[t]=function(e,n){return arguments.length>0?this.on(t,null,e,n):this.trigger(t)}}),y.fn.extend({hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)}}),h.focusin="onfocusin"in e,h.focusin||y.each({focus:"focusin",blur:"focusout"},function(e,t){var n=function(e){y.event.simulate(t,e.target,y.event.fix(e))};y.event.special[t]={setup:function(){var i=this.ownerDocument||this,r=V.access(i,t);r||i.addEventListener(e,n,!0),V.access(i,t,(r||0)+1)},teardown:function(){var i=this.ownerDocument||this,r=V.access(i,t)-1;r?V.access(i,t,r):(i.removeEventListener(e,n,!0),V.remove(i,t))}}});var mt=e.location,yt=y.now(),vt=/\?/;y.parseXML=function(t){var n;if(!t||"string"!=typeof t)return null;try{n=(new e.DOMParser).parseFromString(t,"text/xml")}catch(i){n=void 0}return(!n||n.getElementsByTagName("parsererror").length)&&y.error("Invalid XML: "+t),n};var xt=/\[\]$/,bt=/\r?\n/g,wt=/^(?:submit|button|image|reset|file)$/i,Tt=/^(?:input|select|textarea|keygen)/i;y.param=function(e,t){var n,i=[],r=function(e,t){var n=y.isFunction(t)?t():t;i[i.length]=encodeURIComponent(e)+"="+encodeURIComponent(null==n?"":n)};if(y.isArray(e)||e.jquery&&!y.isPlainObject(e))y.each(e,function(){r(this.name,this.value)});else for(n in e)Ct(n,e[n],t,r);return i.join("&")},y.fn.extend({serialize:function(){return y.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=y.prop(this,"elements");return e?y.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!y(this).is(":disabled")&&Tt.test(this.nodeName)&&!wt.test(e)&&(this.checked||!ae.test(e))}).map(function(e,t){var n=y(this).val();return null==n?null:y.isArray(n)?y.map(n,function(e){return{name:t.name,value:e.replace(bt,"\r\n")}}):{name:t.name,value:n.replace(bt,"\r\n")}}).get()}});var kt=/%20/g,Et=/#.*$/,St=/([?&])_=[^&]*/,Nt=/^(.*?):[ \t]*([^\r\n]*)$/gm,Dt=/^(?:about|app|app-storage|.+-extension|file|res|widget):$/,jt=/^(?:GET|HEAD)$/,At=/^\/\//,qt={},Lt={},Ht="*/".concat("*"),Ft=i.createElement("a");Ft.href=mt.href,y.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:mt.href,type:"GET",isLocal:Dt.test(mt.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Ht,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":y.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?Rt(Rt(e,y.ajaxSettings),t):Rt(y.ajaxSettings,e)},ajaxPrefilter:Ot(qt),ajaxTransport:Ot(Lt),ajax:function(t,n){function S(t,n,i,a){var f,d,p,w,T,C=n;l||(l=!0,u&&e.clearTimeout(u),r=void 0,s=a||"",k.readyState=t>0?4:0,f=t>=200&&300>t||304===t,i&&(w=Mt(h,k,i)),w=It(h,w,k,f),f?(h.ifModified&&(T=k.getResponseHeader("Last-Modified"),T&&(y.lastModified[o]=T),T=k.getResponseHeader("etag"),T&&(y.etag[o]=T)),204===t||"HEAD"===h.type?C="nocontent":304===t?C="notmodified":(C=w.state,d=w.data,p=w.error,f=!p)):(p=C,(t||!C)&&(C="error",0>t&&(t=0))),k.status=t,k.statusText=(n||C)+"",f?v.resolveWith(g,[d,C,k]):v.rejectWith(g,[k,C,p]),k.statusCode(b),b=void 0,c&&m.trigger(f?"ajaxSuccess":"ajaxError",[k,h,f?d:p]),x.fireWith(g,[k,C]),c&&(m.trigger("ajaxComplete",[k,h]),--y.active||y.event.trigger("ajaxStop")))}"object"==typeof t&&(n=t,t=void 0),n=n||{};var r,o,s,a,u,f,l,c,d,p,h=y.ajaxSetup({},n),g=h.context||h,m=h.context&&(g.nodeType||g.jquery)?y(g):y.event,v=y.Deferred(),x=y.Callbacks("once memory"),b=h.statusCode||{},w={},T={},C="canceled",k={readyState:0,getResponseHeader:function(e){var t;if(l){if(!a)for(a={};t=Nt.exec(s);)a[t[1].toLowerCase()]=t[2];t=a[e.toLowerCase()]}return null==t?null:t},getAllResponseHeaders:function(){return l?s:null},setRequestHeader:function(e,t){return null==l&&(e=T[e.toLowerCase()]=T[e.toLowerCase()]||e,w[e]=t),this},overrideMimeType:function(e){return null==l&&(h.mimeType=e),this},statusCode:function(e){var t;if(e)if(l)k.always(e[k.status]);else for(t in e)b[t]=[b[t],e[t]];return this},abort:function(e){var t=e||C;return r&&r.abort(t),S(0,t),this}};if(v.promise(k),h.url=((t||h.url||mt.href)+"").replace(At,mt.protocol+"//"),h.type=n.method||n.type||h.method||h.type,h.dataTypes=(h.dataType||"*").toLowerCase().match(P)||[""],null==h.crossDomain){f=i.createElement("a");try{f.href=h.url,f.href=f.href,h.crossDomain=Ft.protocol+"//"+Ft.host!=f.protocol+"//"+f.host}catch(E){h.crossDomain=!0}}if(h.data&&h.processData&&"string"!=typeof h.data&&(h.data=y.param(h.data,h.traditional)),Pt(qt,h,n,k),l)return k;c=y.event&&h.global,c&&0===y.active++&&y.event.trigger("ajaxStart"),h.type=h.type.toUpperCase(),h.hasContent=!jt.test(h.type),o=h.url.replace(Et,""),h.hasContent?h.data&&h.processData&&0===(h.contentType||"").indexOf("application/x-www-form-urlencoded")&&(h.data=h.data.replace(kt,"+")):(p=h.url.slice(o.length),h.data&&(o+=(vt.test(o)?"&":"?")+h.data,delete h.data),h.cache===!1&&(o=o.replace(St,""),p=(vt.test(o)?"&":"?")+"_="+yt++ +p),h.url=o+p),h.ifModified&&(y.lastModified[o]&&k.setRequestHeader("If-Modified-Since",y.lastModified[o]),y.etag[o]&&k.setRequestHeader("If-None-Match",y.etag[o])),(h.data&&h.hasContent&&h.contentType!==!1||n.contentType)&&k.setRequestHeader("Content-Type",h.contentType),k.setRequestHeader("Accept",h.dataTypes[0]&&h.accepts[h.dataTypes[0]]?h.accepts[h.dataTypes[0]]+("*"!==h.dataTypes[0]?", "+Ht+"; q=0.01":""):h.accepts["*"]);for(d in h.headers)k.setRequestHeader(d,h.headers[d]);if(h.beforeSend&&(h.beforeSend.call(g,k,h)===!1||l))return k.abort();if(C="abort",x.add(h.complete),k.done(h.success),k.fail(h.error),r=Pt(Lt,h,n,k)){if(k.readyState=1,c&&m.trigger("ajaxSend",[k,h]),l)return k;h.async&&h.timeout>0&&(u=e.setTimeout(function(){k.abort("timeout")},h.timeout));try{l=!1,r.send(w,S)}catch(E){if(l)throw E;S(-1,E)}}else S(-1,"No Transport");return k},getJSON:function(e,t,n){return y.get(e,t,n,"json")},getScript:function(e,t){return y.get(e,void 0,t,"script")}}),y.each(["get","post"],function(e,t){y[t]=function(e,n,i,r){return y.isFunction(n)&&(r=r||i,i=n,n=void 0),y.ajax(y.extend({url:e,type:t,dataType:r,data:n,success:i},y.isPlainObject(e)&&e))}}),y._evalUrl=function(e){return y.ajax({url:e,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,"throws":!0})},y.fn.extend({wrapAll:function(e){var t;return this[0]&&(y.isFunction(e)&&(e=e.call(this[0])),t=y(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){for(var e=this;e.firstElementChild;)e=e.firstElementChild;return e}).append(this)),this},wrapInner:function(e){return y.isFunction(e)?this.each(function(t){y(this).wrapInner(e.call(this,t))}):this.each(function(){var t=y(this),n=t.contents();n.length?n.wrapAll(e):t.append(e)})},wrap:function(e){var t=y.isFunction(e);return this.each(function(n){y(this).wrapAll(t?e.call(this,n):e)})},unwrap:function(e){return this.parent(e).not("body").each(function(){y(this).replaceWith(this.childNodes)}),this}}),y.expr.pseudos.hidden=function(e){return!y.expr.pseudos.visible(e)},y.expr.pseudos.visible=function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},y.ajaxSettings.xhr=function(){try{return new e.XMLHttpRequest}catch(t){}};var Wt={0:200,1223:204},$t=y.ajaxSettings.xhr();h.cors=!!$t&&"withCredentials"in $t,h.ajax=$t=!!$t,y.ajaxTransport(function(t){var n,i;return h.cors||$t&&!t.crossDomain?{send:function(r,o){var s,a=t.xhr();if(a.open(t.type,t.url,t.async,t.username,t.password),t.xhrFields)for(s in t.xhrFields)a[s]=t.xhrFields[s];t.mimeType&&a.overrideMimeType&&a.overrideMimeType(t.mimeType),t.crossDomain||r["X-Requested-With"]||(r["X-Requested-With"]="XMLHttpRequest");for(s in r)a.setRequestHeader(s,r[s]);n=function(e){return function(){n&&(n=i=a.onload=a.onerror=a.onabort=a.onreadystatechange=null,"abort"===e?a.abort():"error"===e?"number"!=typeof a.status?o(0,"error"):o(a.status,a.statusText):o(Wt[a.status]||a.status,a.statusText,"text"!==(a.responseType||"text")||"string"!=typeof a.responseText?{binary:a.response}:{text:a.responseText},a.getAllResponseHeaders()))}},a.onload=n(),i=a.onerror=n("error"),void 0!==a.onabort?a.onabort=i:a.onreadystatechange=function(){4===a.readyState&&e.setTimeout(function(){n&&i()})},n=n("abort");try{a.send(t.hasContent&&t.data||null)}catch(u){if(n)throw u}},abort:function(){n&&n()}}:void 0}),y.ajaxPrefilter(function(e){e.crossDomain&&(e.contents.script=!1)}),y.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){return y.globalEval(e),e}}}),y.ajaxPrefilter("script",function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET")}),y.ajaxTransport("script",function(e){if(e.crossDomain){var t,n;return{send:function(r,o){t=y("<script>").prop({charset:e.scriptCharset,src:e.url}).on("load error",n=function(e){t.remove(),n=null,e&&o("error"===e.type?404:200,e.type)}),i.head.appendChild(t[0])},abort:function(){n&&n()}}}});var Bt=[],_t=/(=)\?(?=&|$)|\?\?/;y.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=Bt.pop()||y.expando+"_"+yt++;return this[e]=!0,e}}),y.ajaxPrefilter("json jsonp",function(t,n,i){var r,o,s,a=t.jsonp!==!1&&(_t.test(t.url)?"url":"string"==typeof t.data&&0===(t.contentType||"").indexOf("application/x-www-form-urlencoded")&&_t.test(t.data)&&"data");return a||"jsonp"===t.dataTypes[0]?(r=t.jsonpCallback=y.isFunction(t.jsonpCallback)?t.jsonpCallback():t.jsonpCallback,a?t[a]=t[a].replace(_t,"$1"+r):t.jsonp!==!1&&(t.url+=(vt.test(t.url)?"&":"?")+t.jsonp+"="+r),t.converters["script json"]=function(){return s||y.error(r+" was not called"),s[0]},t.dataTypes[0]="json",o=e[r],e[r]=function(){s=arguments},i.always(function(){void 0===o?y(e).removeProp(r):e[r]=o,t[r]&&(t.jsonpCallback=n.jsonpCallback,Bt.push(r)),s&&y.isFunction(o)&&o(s[0]),s=o=void 0}),"script"):void 0}),h.createHTMLDocument=function(){var e=i.implementation.createHTMLDocument("").body;return e.innerHTML="<form></form><form></form>",2===e.childNodes.length}(),y.parseHTML=function(e,t,n){if("string"!=typeof e)return[];"boolean"==typeof t&&(n=t,t=!1);var r,o,s;return t||(h.createHTMLDocument?(t=i.implementation.createHTMLDocument(""),r=t.createElement("base"),r.href=i.location.href,t.head.appendChild(r)):t=i),o=N.exec(e),s=!n&&[],o?[t.createElement(o[1])]:(o=he([e],t,s),s&&s.length&&y(s).remove(),y.merge([],o.childNodes))},y.fn.load=function(e,t,n){var i,r,o,s=this,a=e.indexOf(" ");return a>-1&&(i=y.trim(e.slice(a)),e=e.slice(0,a)),y.isFunction(t)?(n=t,t=void 0):t&&"object"==typeof t&&(r="POST"),s.length>0&&y.ajax({url:e,type:r||"GET",dataType:"html",data:t}).done(function(e){o=arguments,s.html(i?y("<div>").append(y.parseHTML(e)).find(i):e)}).always(n&&function(e,t){s.each(function(){n.apply(this,o||[e.responseText,t,e])})}),this},y.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){y.fn[t]=function(e){return this.on(t,e)}}),y.expr.pseudos.animated=function(e){return y.grep(y.timers,function(t){return e===t.elem}).length},y.offset={setOffset:function(e,t,n){var i,r,o,s,a,u,f,l=y.css(e,"position"),c=y(e),d={};"static"===l&&(e.style.position="relative"),a=c.offset(),o=y.css(e,"top"),u=y.css(e,"left"),f=("absolute"===l||"fixed"===l)&&(o+u).indexOf("auto")>-1,f?(i=c.position(),s=i.top,r=i.left):(s=parseFloat(o)||0,r=parseFloat(u)||0),y.isFunction(t)&&(t=t.call(e,n,y.extend({},a))),null!=t.top&&(d.top=t.top-a.top+s),null!=t.left&&(d.left=t.left-a.left+r),"using"in t?t.using.call(e,d):c.css(d)}},y.fn.extend({offset:function(e){if(arguments.length)return void 0===e?this:this.each(function(t){y.offset.setOffset(this,e,t)});var t,n,i,r,o=this[0];if(o)return o.getClientRects().length?(i=o.getBoundingClientRect(),i.width||i.height?(r=o.ownerDocument,n=zt(r),t=r.documentElement,{top:i.top+n.pageYOffset-t.clientTop,left:i.left+n.pageXOffset-t.clientLeft}):i):{top:0,left:0}},position:function(){if(this[0]){var e,t,n=this[0],i={top:0,left:0};return"fixed"===y.css(n,"position")?t=n.getBoundingClientRect():(e=this.offsetParent(),t=this.offset(),y.nodeName(e[0],"html")||(i=e.offset()),i={top:i.top+y.css(e[0],"borderTopWidth",!0),left:i.left+y.css(e[0],"borderLeftWidth",!0)}),{top:t.top-i.top-y.css(n,"marginTop",!0),left:t.left-i.left-y.css(n,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var e=this.offsetParent;e&&"static"===y.css(e,"position");)e=e.offsetParent;return e||ge})}}),y.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(e,t){var n="pageYOffset"===t;y.fn[e]=function(i){return z(this,function(e,i,r){var o=zt(e);return void 0===r?o?o[t]:e[i]:void(o?o.scrollTo(n?o.pageXOffset:r,n?r:o.pageYOffset):e[i]=r)},e,i,arguments.length)}}),y.each(["top","left"],function(e,t){y.cssHooks[t]=Ie(h.pixelPosition,function(e,n){return n?(n=Me(e,t),Pe.test(n)?y(e).position()[t]+"px":n):void 0})}),y.each({Height:"height",Width:"width"},function(e,t){y.each({padding:"inner"+e,content:t,"":"outer"+e},function(n,i){y.fn[i]=function(r,o){var s=arguments.length&&(n||"boolean"!=typeof r),a=n||(r===!0||o===!0?"margin":"border");return z(this,function(t,n,r){var o;return y.isWindow(t)?0===i.indexOf("outer")?t["inner"+e]:t.document.documentElement["client"+e]:9===t.nodeType?(o=t.documentElement,Math.max(t.body["scroll"+e],o["scroll"+e],t.body["offset"+e],o["offset"+e],o["client"+e])):void 0===r?y.css(t,n,a):y.style(t,n,r,a)},t,s?r:void 0,s)}})}),y.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,i){return this.on(t,e,n,i)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)}}),y.parseJSON=JSON.parse,"function"==typeof define&&define.amd&&define("jquery",[],function(){return y});var Xt=e.jQuery,Ut=e.$;return y.noConflict=function(t){return e.$===y&&(e.$=Ut),t&&e.jQuery===y&&(e.jQuery=Xt),y},t||(e.jQuery=e.$=y),y});