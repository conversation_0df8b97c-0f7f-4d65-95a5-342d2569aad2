<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>银行贵宾权益预约中心</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/swiper.min.css">
    <link rel="stylesheet" href="../css/icbc.css">
    <link rel="stylesheet" href="../css/common.css">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" type="text/css" href="../css/jz/jsindex.css">


    <script src="../js/jquery-3.1.0.min.js"></script>
    <script src="../data/lCalendar.js"></script>

    <link rel="stylesheet" href="../data/lCalendar.css">
    <script src="../js/bootstrap.min.js"></script>
    <script src="../js/swiper.min.js"></script>
    <script src="../js/popper.min.js"></script>
    <script src="../js/common.js"></script>
    <script src="../js/vue.min.js"></script>
    <script src="../js/jquery.bpopup.js"></script>
    <script src="../js/flex.js"></script>
</head>
<body>
<div class="container">
    <div class="oneCon">
        <div class="headCon">
            <p class="classHeadTitle">家政服务</p>
            <p class="classHeadDes">保洁清洗/玻璃清洁</p>
            <img class="headIcon" src="../images/jzIcon.png" alt="">
        </div>
        <div class="conList mt40">
            <p class="title">请选择您要预约的家政服务：</p>
            <div class="goodsList">
                <!-- <div class="list listActive mr20">保洁清洗</div> -->
                <!-- <div class="list  mr20">保洁清洗</div> -->

            </div>

        </div>
        <div class="conList ">
            <p class="title">请选择您要预约的服务日期：</p>
            <input id="demo1" type="text" readonly="" class="dataCheck service_date" name="input_date"
                   placeholder="请选择您要预约的服务日期" data-lcalendar="2011-01-1,2019-12-31"/>
            <!-- <input type="date" class="dataCheck"> -->
            <p class="clip">*请您至少提前五天进行下单预约</p>
        </div>
        <div class="conList selectTime  bgColor">
            <p class="title">请选择您要上门服务的时间：</p>
            <div data-time="8:00" class="listTime listActive">8:00上门</div>
            <div data-time="13:00" class="listTime ml40">13:00上门</div>
        </div>

    </div>
    <div class="twoCon">
        <div class="conList ">
            <p class="title">请填写您的预约人信息：</p>
            <ul class="userDetail">
                <li>
                    <p class="liTitle">联系人姓名</p>
                    <input class="userDes consignee_name" placeholder="请输入联系人姓名" type="text">
                </li>
                <li>
                    <p class="liTitle">联系人电话</p>
                    <input class="userDes consignee_phone" placeholder="请输入联系人电话" type="text">
                </li>
                <li>
                    <p class="liTitle">请选择省份</p>
                    <select class="selectName consignee_address_sf" name="" id="">
                        <option value="内蒙古自治区">内蒙古自治区</option>
                    </select>
                </li>
                <li>
                    <p class="liTitle">请选择地区</p>
                    <select class="selectName consignee_address" name="" id="">
                        <option value="">请选择地区</option>
                        <option value="呼和浩特">呼和浩特</option>
                        <option value="包头">包头</option>
                        <option value="赤峰">赤峰</option>
                        <option value="鄂尔多斯">鄂尔多斯</option>
                        <option value="通辽">通辽</option>
                        <option value="满洲里">满洲里</option>
                        <option value="巴彦淖尔">巴彦淖尔</option>
                        <option value="阿拉善">阿拉善</option>
                        <option value="锡林郭勒">锡林郭勒</option>
                        <option value="呼伦贝尔">呼伦贝尔</option>
                        <option value="呼伦贝尔-阿荣旗">呼伦贝尔-阿荣旗</option>
                        <option value="呼伦贝尔-扎兰屯市">呼伦贝尔-扎兰屯市</option>
                        <option value="乌兰察布">乌兰察布</option>
                        <option value="乌海">乌海</option>
                        <option value="牙克石市">牙克石市</option>
                    </select>

                </li>
                <li>
                    <p class="liTitle">详细地址</p>
                    <input class="userDes addressDetail" placeholder="请输入详细地址" type="text">
                </li>
            </ul>
            <div class="submitBtn">立即预约</div>
        </div>
    </div>
    <div class="twoCon">
        <div class="conList ">
            <p class="clipTitle">温馨提示</p>
            <p class="clipDes">服务范围：呼和浩特、包头、赤峰、鄂尔多斯、通辽、满洲里、巴彦淖尔、阿拉善、锡林郭勒、呼伦贝尔、乌兰察布主城区、乌海、牙克石市。</p>
            <p class="clipDes">超出服务范围收费标准：<br>20平米玻璃清洁：每超出1平米，加收15元。<br>
                4小时家居保洁服务：每超出1小时，加收50元。<br>
                超出服务范围的费用，需客户自行与上门服务人员进行结算。</p>
            <p class="clipDes">预约码为虚拟产品，一经售出，不退不换，请确认产品后进行预约。</p>
            <p class="clipDes">银行客服电话：95352，服务供应商客服电话：4008250700</p>
        </div>
    </div>
</div>
<div class="popup displayno">
    <div class="popupCon">
        <img class="closePopup" src="../images/cole.png" alt="">
        <p class="popupTitle">请确认您的预约信息</p>
        <ul class="popupUl">
            <li>
                <p class="popupLiOne">服务内容:</p>
                <p class="popupLiTwo one">保洁清洗</p>
            </li>
            <li>
                <p class="popupLiOne">预约日期:</p>
                <p class="popupLiTwo two">2021年12月1日</p>
            </li>
            <li>
                <p class="popupLiOne">预约时间:</p>
                <p class="popupLiTwo three">13：00上门</p>
            </li>
            <li>
                <p class="popupLiOne">联&nbsp;&nbsp;系&nbsp;&nbsp;人:</p>
                <p class="popupLiTwo four">13：00上门</p>
            </li>
            <li>
                <p class="popupLiOne">联系电话:</p>
                <p class="popupLiTwo five">17718486982</p>
            </li>
            <li>
                <p class="popupLiOne">联系地址:</p>
                <p class="popupLiTwo six">联系地址联系地址联系地址联系地址联系地址联系地址联系地址联系地址联系地址</p>
            </li>
        </ul>
        <div class="lineBr">
            <div class="leftCircle"></div>
            <div class="line"></div>
            <div class="rightCircle"></div>
        </div>
        <p class="popupClip">
            *请您仔细核对以上预约信息，您可在预约服务时间前2个工作日修改服务地址、服务时间和服务内容，如需修改请致电4008250700
        </p>
        <div class="popupconfirmBtn">确认</div>


    </div>
</div>
<div class="timePopup  displayno">
    <div class="timeCon">
        <p class="timeTitle">预约说明</p>
        <img class="timeImg" src="../images/popupBgimg.png" alt="">
        <p class="timeClip">1月01日-2月28日适逢春节期间，家政服务暂停预约，请您选择其他时间进行预约，给您造成的不便敬请谅解。</p>
        <div class="confirmBtn">确定</div>
    </div>
</div>
<div id="m-toast-pop" class="m-toast-pop">
    <div class="m-toast-inner">
        <div class="m-toast-inner-text" id="m-toast-inner-text"></div>
    </div>
</div>
<script>
    (function () {
        var jzinit = {
            goods_id: '',
            goods_name: '',
            consignee_name: "",
            consignee_phone: "",
            consignee_address: '',
            addressDetail: "",
            service_date: "",
            service_time: "",
            goodsList: "",
            init: function () {

                this.bind();
                this.getGoodsList();
                //初始化时间控件
                var str = jzinit.GetDateStr(5) + ',' + jzinit.GetDateStr(180)
                $('#demo1').attr('data-lcalendar', str)
                var calendar = new lCalendar();
                calendar.init({
                    'trigger': '#demo1',
                    'type': 'date'
                });
            },
            bind: function () {
                //点击选择家政列表
                $(document).on('click', '.goodsList .list', function () {
                    $('.goodsList .list').removeClass('listActive')
                    $(this).addClass('listActive');
                    // this.
                });
                //点击时间弹窗的确定
                $('.confirmBtn').on('click', function () {
                    $('.timePopup').addClass('displayno')
                })
                //点击时间选择
                $(document).on('click', '.selectTime .listTime', function () {
                    $('.selectTime .listTime').removeClass('listActive')
                    $(this).addClass('listActive');
                    // this.
                });
                //点击立即预约按钮
                $('.submitBtn').on('click', function () {
                    let timeData = jzinit.nowInDateBetwen($('.service_date').val());
                    if (!timeData) {
                        $('.timePopup').removeClass('displayno')
                        return false;
                    }

                    goods_id = $('.goodsList .listActive').attr('data-id');
                    goods_name = $('.goodsList .listActive').text();
                    consignee_name = $('.consignee_name').val();
                    consignee_phone = $('.consignee_phone').val();
                    consignee_address_sf = $('.consignee_address_sf').find("option:selected").val();
                    consignee_address = $('.consignee_address').find("option:selected").val();
                    addressDetail = $('.addressDetail').val();
                    service_date = $('.service_date').val();
                    service_time = $('.selectTime .listActive').attr('data-time');
                    if (goods_id == '') {
                        return toastinfo("请选择预约的家政服务！");
                    }
                    if (service_date == '') {
                        return toastinfo("请选择预约的日期！");
                    }
                    if (service_time == '') {
                        return toastinfo("请选择上门服务时间！");
                    }
                    if (consignee_name == '') {
                        return toastinfo("请填写联系人姓名");
                    }
                    if (consignee_phone == '') {
                        return toastinfo("请填写联系人电话");
                    }
                    if (consignee_address == '') {
                        return toastinfo("请选择地址");
                    }
                    if (addressDetail == '') {
                        return toastinfo("请填写详细地址");
                    }
                    $('.popup').removeClass('displayno')
                    $('.one').text(goods_name)
                    $('.two').text(service_date)
                    $('.three').text(service_time + "上门")
                    $('.four').text(consignee_name)
                    $('.five').text(consignee_phone)
                    $('.six').text(consignee_address_sf + ' ' + consignee_address + ' ' + addressDetail)
                });

                //点击弹窗的关闭按钮
                $('.closePopup').on('click', function () {
                    $('.popup').addClass('displayno')
                });
                //点击弹窗的确定按钮
                $('.popupconfirmBtn').on('click', function () {
                    $.ajax({
                        type: "POST",
                        url: "/api/order",
                        data: {
                            'goods_id': goods_id,
                            'consignee_name': consignee_name,
                            'consignee_phone': consignee_phone,
                            'consignee_address': consignee_address_sf + ' ' + consignee_address + ' ' + addressDetail,
                            'service_date': service_date,
                            'service_time': service_time

                        },
                        async: true,
                        dataType: "json",
                        xhrFields: {
                            withCredentials: true
                        },
                        success: function (data, status, xhr) {
                            if (data.code == '200') {
                                window.location.href = 'jzresult.html'
                            } else {
                                toastinfo(data.msg);
                            }
                        },
                        error: function (error, xhr, abort) {
                            window.location.href = 'jzresult.html'

                            toastinfo("网络错误，请稍后再试！");
                        }
                    })
                })
            },
            nowInDateBetwen(time) {
                console.log(time);
                var exclude_date = [
                    {
                        'begin': "2023-01-06",
                        'end': "2023-02-05",
                        'msg': '1月6日-2月5日适逢春节期间，家政服务暂停预约，请您选择其他时间进行预约，给您造成的不便敬请谅解。',
                    }
                ];
                var is_hit = false;
                var i_time = Number(time.replace(/-/g, ''));
                exclude_date.forEach((item, i) => {
                    var begin = Number(item['begin'].replace(/-/g, ''));
                    var end = Number(item['end'].replace(/-/g, ''));
                    if (i_time >= begin && i_time <= end) {
                        is_hit = true;
                        $('.timePopup .timeClip').html(item["msg"]);
                        return;
                    }
                });

                return !is_hit;
            },
            getGoodsList() {
                var id = GetQueryString('id');
                if (id == "") {
                    toastinfo('参数不完整');
                    return;
                }
                $.ajax({
                    type: "POST",
                    url: "/api/goods-detail",
                    data: {
                        'goods_id': id,
                    },
                    async: true,
                    dataType: "json",
                    xhrFields: {
                        withCredentials: true
                    },
                    success: function (data, status, xhr) {
                        if (data.code == '200') {
                            var newStr = '';
                            newStr += '<div  data-id="' + data.data.id + '"class="list listActive mr20">' + data.data.goods_name + '</div>'
                            $('.goodsList').html(newStr)
                        } else {
                            toastinfo(data.msg);
                        }
                    },
                    error: function (error, xhr, abort) {
                        toastinfo("网络错误，请稍后再试！");
                    }
                })
            },
            GetDateStr(AddDayCount) {
                var dd = new Date();
                dd.setDate(dd.getDate() + AddDayCount); //获取AddDayCount天后的日期
                var y = dd.getFullYear();
                var m = (dd.getMonth() + 1) < 10 ? "0" + (dd.getMonth() + 1) : (dd.getMonth() + 1); //获取当前月份的日期，不足10补0
                var d = dd.getDate() < 10 ? "0" + dd.getDate() : dd.getDate(); //获取当前几号，不足10补0
                return y + "-" + m + "-" + d;
            }
        }

        jzinit.init()
    })()
</script>
</body>
</html>
