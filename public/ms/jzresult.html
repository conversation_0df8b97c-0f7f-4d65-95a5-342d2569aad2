<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<title>银行贵宾权益预约中心</title>
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
	<link rel="stylesheet" href="../css/bootstrap.min.css">
	<link rel="stylesheet" href="../css/swiper.min.css">
	<link rel="stylesheet" href="../css/icbc.css">
	<link rel="stylesheet" href="../css/common.css">
	<link rel="stylesheet" href="../css/style.css">
	<link rel="stylesheet" type="text/css" href="../css/jz/jsindex.css">
	<link rel="stylesheet" type="text/css" href="../css/jz/aqyresult.css">


	<script src="../js/jquery-3.1.0.min.js"></script>
    <script src="../data/lCalendar.js"></script>

    <link rel="stylesheet" href="../data/lCalendar.css">
	<script src="../js/bootstrap.min.js"></script>
	<script src="../js/swiper.min.js"></script>
	<script src="../js/popper.min.js"></script>
	<script src="../js/common.js"></script>
	<script src="../js/vue.min.js"></script>
	<script src="../js/jquery.bpopup.js"></script>
	<script src="../js/flex.js"></script>
</head>
<body style="background:#FDFDFD;">
<div class="container">
    <img class="headImg" src="../images/success.png" alt="">
    <p class="resultClip">您可在预约服务时间前2个工作日修改服务地址、服务时间和服务内容，如需修改请致电4008250700</p>
    <p class="resultBtn">预约订单查询</p>
</div>
<div class="popup displayno">


	<div class="popupCon2 popupConStyle displayno" style="background:#2575b7;">
	   <img src="../images/cole.png" alt="" class="closeBtn">
		<ul class="headConUl">
			<li>
				<img class="liICon" src="../images/duiCode.png" alt="">
				<input type="text" class="covertCodePopup" placeholder="请输入预约码">

			</li>
			<li>
				<img class="liICon" style="left:0.25rem;" src="../images/phone.png" alt="">
				<input type="text" class="phonePopup" placeholder="请填写手机号">

			</li>
		</ul>
		<div class="confirmBtn orderListBtn">确认</div>

	</div>
	<div class="popupCon3 popupConStyle displayno" >
		<!-- <img src=".../images/cole.png" alt="" class="closeBtn"> -->
		<img class="popupImg" src="../images/errorIcon.png" alt="">
		<p class="errorMsg"></p>
		<div class="confirmBtn close" style="background:#2575B7;color:#fff;">确认</div>

	</div>
</div>
<script>
	//点击底部订单查询
				$('.resultBtn').on('click',function(){
					$.ajax({
						type: "POST",
						url:"/api/is-login",
						async: true,
						xhrFields: {
							withCredentials: true
						},
						success : function (res, status, xhr) {
							if(res.code == '200'){
								window.location.href="../myorder.html?_r=1128";
							}else{
								$('.popup').removeClass('displayno');
								$('.popupCon2').removeClass('displayno')
							}
						},
						error: function (error, xhr, abort) {
							$('.popup').removeClass('displayno');
							$('.popupCon2').removeClass('displayno')
						}
					})
				})
				//点击弹窗的关闭按钮
				$('.closeBtn').on('click',function(){
					$('.popup').addClass('displayno');

				})
							//点击查询列表按钮
				$('.orderListBtn').on('click',function(){
					var  covertCodePopup = $('.covertCodePopup').val();
					var  phonePopup = $('.phonePopup').val();
					if(covertCodePopup==''){
						return toastinfo("请输入正确的预约码！");
					}
					if(!isPhoneNumber(phonePopup)){
						return toastinfo('请输入正确的手机号！');
					}
					$.ajax({
						type: "POST",
						url:"/api/login",
						data: {
							"card_pwd":covertCodePopup,
							"sms_captcha":'',
							"mobile": phonePopup,
							"act":'',

						},
						async: true,
						dataType: "json",
						xhrFields: {
							withCredentials: true
						},
						success : function (res, status, xhr) {
							if(res.code == '200'){
								$('.popup').addClass('displayno');
								$('.popupCon2').addClass('displayno')
								window.location.href= res.data.rdr_url
							}else{
								toastinfo(res.msg);
							}
						},
						error: function (error, xhr, abort) {
							$('.headConBtn').removeClass('noClick');
							toastinfo("网络错误，请稍后再试！");
						}
					})


				})




</script>
</body>
</html>
