<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>洗车网点查询</title>
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <!--            <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/bootstrap.min.css">-->
    <!--        <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/icbc.css">-->
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/common.css">
    <link rel="stylesheet" href="../css/msaddress/address.css">
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/jquery-3.1.0.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/bootstrap.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/common.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/vue.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/flex.js"></script>
    <script src="./address.js?r=241030"></script>
    <script src="./distpicker/js/distpicker.data.js"></script>
    <script src="./distpicker/js/distpicker.js"></script>
    <script src="./distpicker/js/main.js"></script>
    <script src="../js/popper.min.js"></script>
    <style>
        .displayno {
            display: none;
        }

        .popup {
            width: 100%;
            height: 100%;
            position: fixed;
            left: 0;
            top: 0;
            background: rgba(0, 0, 0, 0.6);
            z-index: 99;

        }

        .popupCon {
            width: 7rem;
            padding: 0.3rem 0;
            /*background: #fff;*/
            border-radius: 0.15rem;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
        }

        .popupConStyle {
            width: 7rem;
            padding: 0.3rem;
            background: #fff;
            border-radius: 0.3rem;
            position: fixed;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);

        }

        .closeBtn {
            width: 0.5rem;
            height: 0.5rem;
            position: absolute;
            right: 0.2rem;
            top: 0.2rem;
        }

        .fjtz {
            font-size: .3rem;
            text-align: left;
            text-indent: 2ch;
            padding: .3rem .5rem .7rem .5rem;
            line-height: .5rem;
        }
    </style>
</head>
<body>
<div class="main_div" id="app">
    <div id="distpicker5">
        <div class="form-group">
            <div class="header">
                <select class="form-control" id="province10" v-model="province" data-province="内蒙古"></select>
            </div>
            <div class="gpline"></div>
            <div class="header">
                <select class="form-control" id="city10" v-model="city" value=""></select>
            </div>
            <div class="gpline"></div>
            <div class="header">
                <select class="form-control ellipsis" id="district10" v-model="area" value=""></select>
            </div>
        </div>
    </div>

    <div class="top_header">
        <div class="gap"></div>
        <template v-if="filteredItems !=''">
            <div class="car_address_list" v-for="item in filteredItems">
                <p class="car_name">{{item.siteName}}</p>
                <div class="address_list">
                    <div class="address_icon"><img class="car_icon"
                                                   src="https://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/msaddress/address.png"
                                                   alt=""></div>
                    <div class="wly">网店地址</div>
                    <div class="detail_address">{{item.address}}</div>
                </div>
                <div class="address_list">
                    <div class="address_icon"><img class="car_icon"
                                                   src="https://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/msaddress/phone.png"
                                                   alt=""></div>
                    <div class="wly">联系电话</div>
                    <div class="detail_address" @click="telClick(item.phone)">{{item.phone}}</div>
                </div>
                <div class="address_list">
                    <div class="address_icon"><img class="car_icon"
                                                   src="https://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/msaddress/worktime.png"
                                                   alt=""></div>
                    <div class="wly">营业时间</div>
                    <div class="detail_address">{{item.start}} - {{item.end}}</div>
                </div>
            </div>
        </template>
        <template v-else>
            <div class="no_site">----该地区暂无洗车网点----</div>
        </template>
    </div>


</div>
<!--弹窗提示-->
<!--<div class="popup displayno">-->
<!--    <div class="popupConStyle">-->
<!--        <img src="../images/fjtz1.png" alt="" style="width: 100%;">-->
<!--    </div>-->
<!--</div>-->

<div class="footer"></div>

<script>

    var vm = new Vue({
        el: "#app",
        data: {
            province: "内蒙古",
            city: "",
            area: "",
            items: localdata
        },

        computed: {
            filteredItems: function () {
                const province = this.province;
                const city = this.city;
                let area = this.area;
                return this.items.filter(function (item) {
                    return item.province.includes(province) && item.city.includes(city) && item.area.includes(area);
                    // return item.province.indexOf('内蒙古') !== -1 && item.city.indexOf(city) !== -1 && item.area.indexOf(area) !== -1;
                });
            },
        },
        methods: {
            telClick: function (phone) {
                window.location.href = "tel:" + phone;
            }
        },
        watch: {
            city(newVal, oldVal) {
                if (newVal != oldVal) {
                    this.area = '';
                }
            },
            province(newVal, oldVal) {
                if (newVal != oldVal) {
                    this.city = '';
                }
            }

        }
    });
    // 时间判断
    // (function () {
    //     const start = new Date('2024-01-25 00:00:00').getTime();
    //     const end = new Date('2024-02-25 00:00:00').getTime();
    //
    //     const currentTime=new Date().getTime()
    //     if(currentTime > start && currentTime < end){
    //         $('.popup').removeClass('displayno')
    //     }
    // })()
</script>
</body>
</html>
