  <!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<title>商品详情</title>
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
	<link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/bootstrap.min.css">
	<link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/swiper.min.css">
	<link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/icbc.css">
	<link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/common.css">
	<link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/style.css">
	<link rel="stylesheet" type="text/css" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/detail.css">

 	<script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/jquery-3.1.0.min.js"></script>
  	<script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/bootstrap.min.js"></script>
  	<script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/swiper.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/popper.min.js"></script>
	<script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/common.js"></script>
	<script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/vue.min.js"></script>
	<script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/jquery.bpopup.js"></script>
</head>
<body>
	<div class="container">
		<ul class="nav cm-border-b">
	    	<li class="cm-f-b-c"><p class="on">商品详情</p><p>商品参数</p><p>购物须知</p></li>
	    </ul>
	    <div class="tab tab0">
	    	<!-- 轮播 -->
	        <div class="swiper-container" id="swiper">
	            <div class="swiper-wrapper">
	                <div class="swiper-slide" v-for="item in items">
	                    <img :src='item'>
	                </div>
	            </div>
	            <div class="swiper-pagination"></div>
	        </div>
	    	<!-- <img src="images/01.png" class="banner" style="width: 6.5rem; margin-left: .5rem; margin-top: 1rem;"> -->
	    	<p class="title"></p>
	    	<p class="subtitle"></p>
	    	<div class="darkline"></div>
            <label class="goodlabel" style="padding-bottom:0.05rem;border-bottom: solid .05rem #E03147;">商品详情</label>
	    	<div class="content">

	    	</div>
	    </div>
	    <div class="tab tab1 hide">

	    </div>
	    <div class="tab tab2 hide">

	    </div>

		<div class="recharge cm-font-size-16 cm-color-white cm-f-c-c" onclick="rechargefun()">立即兑换</div>
	</div>

	<!-- toast -->
	<div id="m-toast-pop" class="m-toast-pop">
        <div class="m-toast-inner">
            <div class="m-toast-inner-text" id="m-toast-inner-text"></div>
        </div>
    </div>


	<script>
		var parameter;
		//立即兑换
		function rechargefun(){
			var id = GetQueryString('id');
			if(id == ""){
				toastinfo('参数不完整');
				return;
			}

			//1-实物商品，2-虚拟商品卡密类，3-虚拟商品-异步直充类，4-实物+虚拟
			jump('submit.html' + parameter);
		}

		//获取详情
		function getdetail(){
			var id = GetQueryString('id');
			if(id == ""){
				toastinfo('参数不完整');
				return;
			}
			$.ajax({
	            type: "POST",
	            url: "/api/goods-detail",
	            data: {
	            	'goods_id' : id,
	            },
	            async: true,
	            dataType: "json",
	            xhrFields: {
	                withCredentials: true
	            },
	            success : function (data, status, xhr) {
	            	if(data.code == '200'){
	            		$('.title')[0].innerText = data.data.goods_name;
	            		$('.subtitle')[0].innerText = data.data.goods_attr;
	            		//商品详情
	            		$('.content')[0].innerHTML = data.data.goods_desc;
	            		//商品参数、购物须知
	            		$('.tab1')[0].innerHTML = data.data.goods_params;
	            		$('.tab2')[0].innerHTML = data.data.goods_instr;

	            		//轮播图 goods_imgs
						var vm = new Vue({
						  	el: '#swiper',
						  	data: {
				    			items: data.data.goods_imgs
				  			}
				  		});

	            		parameter = '?id=' + GetQueryString('id') + '&type=' + data.data.goods_type + '&name=' + data.data.goods_name + '&attr=' + data.data.goods_attr + '&proj=' + GetQueryString('proj');
					}else{
	                    toastinfo(data.msg);
	                }
	            },
	            error: function (error, xhr, abort) {
	                toastinfo("网络错误，请稍后再试！");
	            }
	        })
		}
		getdetail();

		var mySwiper = new Swiper('.swiper-container', {
            speed: 2000,
            pagination: {
                el: '.swiper-pagination',
            },
            loop: true,
            on: {
                slideChangeTransitionEnd: function() {
                    console.log(this.activeIndex); //切换结束时，告诉我现在是第几个slide

                   	selindex = this.activeIndex;
                  	if(this.activeIndex == 5){
                  		selindex = 1;
                  	}else if(this.activeIndex == 0){
                  		selindex = 4;
                  	}

                },
                onTouchMove: function() {
                    console.log('onTouchMove');
                }
            },
        })



		//tab切换
		var index;
	    $(document).on('touchstart click', '.nav li p', function (index){
	        $(this).addClass('on').siblings().removeClass('on');
	        index=$(this).index();
	        for(var i = 0; i < 3; i++){
	        	$('.tab')[i].classList.add('hide');
	        }
	        var curtab = '.tab' + index;
	        $(curtab)[0].classList.remove('hide');
	        // $(".main .details").eq(index).show().siblings().hide();
	        $('body').scrollTop(0);
	    });
	</script>
</body>
</html>
