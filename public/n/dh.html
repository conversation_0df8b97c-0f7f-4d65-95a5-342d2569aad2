<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>视频会员权益兑换</title>
    <!-- 九一畅想 0128 -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/bootstrap.min.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/swiper.min.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/icbc.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/common.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/style.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/dh1.css">
<!--    <link rel="stylesheet" href="../css/dh.css">-->

    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/jquery-3.1.0.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/bootstrap.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/swiper.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/popper.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/common.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/vue.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/jquery.bpopup.js"></script>
</head>
<body>
<div class="container">
    <div class="contbox cm-pos-r">
        <div class="cont">
            <div class="continfo cm-tx-c cm-color-gold">视频会员权益兑换</div>
            <div class="formbox cm-pos-r">
                <div class="formboxinner cm-pos-r">
                    <img src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/shadow.png"
                         class="shadowimg shadowtop" alt="">
                    <div class="forminfo cm-f-c-c cm-margin-b-10">
                        <img src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/line2.png" class="lineimg"
                             alt="">
                        <div class="cm-color-lightred cm-font-size-16 cm-margin-l-10 cm-margin-r-10">请确认您的兑换码</div>
                        <img src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/line2.png"
                             class="lineimg lineright" alt="">
                    </div>
                    <div class="formcont">
                        <div class="formitem cm-border-b">
                            <input type="text" name="card" placeholder="请输入兑换码" class="cardpwd">
                        </div>
                    </div>
                    <div class="formbtn cm-color-white cm-f-c-c cm-line-h-1 cm-font-size-18">立即兑换</div>
                    <div class="orderbtn cm-f-c-c cm-font-size-18" style="margin-top: 0.5rem;">查看订单</div>
                    <img src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/shadow.png"
                         class="shadowimg shadowbot" alt="">
                </div>
                <img src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/gift_04.png" class="gifimg"
                     alt="">
                <div class="botline"></div>
            </div>
        </div>
        <div class="cm-tx-c cm-color-gold" style=" font-size: .3rem;margin-top: .3rem;">客服电话：400-8797-816<br/><span style="font-size:.25rem;">（周一至周五 10:00-18:00）</span></div>
        <div class="cm-tx-c cm-color-gold" style="margin-top:.3rem"><span style="font-size:.20rem;">青柠利合科技（北京）有限公司</span></div>
    </div>
</div>

<!-- toast -->
<div id="m-toast-pop" class="m-toast-pop">
    <div class="m-toast-inner">
        <div class="m-toast-inner-text" id="m-toast-inner-text"></div>
    </div>
</div>

<script>
    var _act = '2Vr0xaIb+FdbKUOxEETPQw==';
    // window.sessionStorage.setItem('n-first', window.location.href);

    $('.formbtn').on('click', function () {
        if ($('.cardpwd')[0].value == "") {
            toastinfo('请填写兑换码');
            return;
        }

        $.ajax({
            type: "POST",
            url: "/api/login",
            data: {
                'act': _act,
                'card_pwd': $('.cardpwd')[0].value,
            },
            async: true,
            dataType: "json",
            xhrFields: {
                withCredentials: true
            },
            success: function (res, status, xhr) {
                console.log(res);
                if (res.code == '200') {
                    if (res.data.exchange_state == '3') {
                        toastinfo('该兑换码已使用，即将跳转到我的订单...');
                        setTimeout(function () {
                            jump("/myorder.html?_r=1128");
                        }, 1500);
                    } else {
                        jump(res.data.rdr_url);
                    }

                } else {
                    toastinfo(res.msg);
                }
            },
            error: function (error, xhr, abort) {
                toastinfo("网络错误，请稍后再试！");
            }
        })
    });

    function getCard() {
        var card = GetQueryString('s');
        if (card) {
            // card = card.split(',')[0];
            card = card.split(/[,，]/)[0];
            if (card) {
                $('.cardpwd').val(card);
            }
        }
    }

    getCard();

    $('.orderbtn').on('click', function () {
        if ($('.cardpwd')[0].value == "") {
            toastinfo('请填写兑换码');
            return;
        }

        $.ajax({
            type: "POST",
            url: "/api/login",
            data: {
                'act': _act,
                'card_pwd': $('.cardpwd')[0].value,
            },
            async: true,
            dataType: "json",
            xhrFields: {
                withCredentials: true
            },
            success: function (res, status, xhr) {
                console.log(res);
                if (res.code == '200') {
                    jump("/myorder.html?_r=1128");
                } else {
                    toastinfo(res.msg);
                }
            },
            error: function (error, xhr, abort) {
                toastinfo("网络错误，请稍后再试！");
            }
        })
    });

</script>
</body>
</html>
