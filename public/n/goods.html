<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>商品列表</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/bootstrap.min.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/swiper.min.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/icbc.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/common.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/style.css">
    <link rel="stylesheet" type="text/css" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/goods.css">

    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/jquery-3.1.0.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/bootstrap.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/swiper.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/popper.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/common.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/vue.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/jquery.bpopup.js"></script>
    <style>
        .goodsName {
            font-size: .28rem;
            height: 0.8rem;
            line-height: 0.4rem;
            text-overflow: -o-ellipsis-lastline;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }
    </style>
</head>
<body>
<div class="container">
    <div class="contbox">
        <div class="cm-padded-15 cm-font-size-16">您可以选择以下任意1款商品兑换</div>
        <div class="cm-f cm-f-w listbox" id="app">
            <div class="list" v-for="(item, key) in goodsList" :key="key" @click="openDetail(item.id)">
                <div class="listinner">
                    <div class="imgbox cm-f-c-c">
                        <img :src="item.goods_show_img" :style="{height: wd + 'px'}" class="goodsimg" alt="">
                    </div>
                    <div class="cm-padded-10">
                        <div class="cm-font-size-16 goodsName" v-text="item.goods_name"></div>
<!--                        <div class="cm-f-b-c cm-margin-t-10">-->
<!--                            <div class="cm-font-size-14 cm-color-grey">市场参考价</div>-->
<!--                            <div class="cm-font-size-20 cm-color-lightred">￥109</div>-->
<!--                        </div>-->
                    </div>
                </div>
            </div>
        </div>
        <div class="nomore cm-color-grey cm-font-size-14 cm-margin-t-15 cm-tx-c">没有更多商品啦</div>
        <div class="botbtnbox cm-f-c-c cm-color-white cm-line-h-1" onClick="openWin()">
            <div class="botbtn cm-f-c-c cm-font-size-16">查询兑换记录</div>
        </div>
    </div>
</div>

<!-- toast -->
<div id="m-toast-pop" class="m-toast-pop">
    <div class="m-toast-inner">
        <div class="m-toast-inner-text" id="m-toast-inner-text"></div>
    </div>
</div>

<script>
    var vueData = {
        goodsList: [
        ],
        wd: 0
    }

    function openWin() {
        jump('../myorder.html?_r=1128');
    }

    var vm = new Vue({
        el: '#app',
        data: vueData,
        mounted: function () {
            this.$nextTick(function () {
                var wd = $('.imgbox').width()
                vueData.wd = wd
            })
        },
        methods: {
            openDetail: function (id) {
                $.ajax({
                    type: "POST",
                    url: "/api/is-permit",
                    data: {
                        'goods_id': id

                    },
                    async: true,
                    dataType: "json",
                    xhrFields: {
                        withCredentials: true
                    },
                    success: function (res, status, xhr) {
                        if (res.code == '200') {
                            jump('detail.html?id=' + id + '&proj=' + GetQueryString('proj'));

                        } else {
                            toastinfo(res.msg);
                        }
                    },
                    error: function (error, xhr, abort) {
                        toastinfo("网络错误，请稍后再试！");
                    }
                })
            },

        }
    });
    var fn = function () {
        $.ajax({
            type: "POST",
            url: "/api/goods-list",
            async: true,
            dataType: "json",
            xhrFields: {
                withCredentials: true
            },
            success: function (res, status, xhr) {
                if (res.code == '200') {
                    // console.log(res.msg);
                    vueData.goodsList = res.data['1']['1']
                    console.log(vueData.goodsList)
                } else {
                    toastinfo(data.msg);
                }
            },
            error: function (error, xhr, abort) {
                toastinfo("网络错误，请稍后再试！");
            }
        })
    }()
</script>
</body>
</html>
