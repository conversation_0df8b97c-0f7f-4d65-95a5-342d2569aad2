<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>提交订单</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/bootstrap.min.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/swiper.min.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/icbc.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/common.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/style.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/success.css">

    <link rel="stylesheet" type="text/css" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/submit.css">

    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/jquery-3.1.0.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/bootstrap.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/swiper.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/popper.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/common.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/vue.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/jquery.bpopup.js"></script>
</head>
<body>
<div class="container">
    <div class="titlebox">
        <p class="title"></p>
        <p class="subtitle"></p>
    </div>

    <!-- 邮寄类产品 -->
    <div class="buybox hide">
        <input type="text" name="" placeholder="请输入收货人姓名" class="name">
        <input type="text" name="" placeholder="请输入收货人手机号" class="name phone">
        <div class="sepline buyline"></div>
        <input type="text" name="" placeholder="请输入详细的收货地址" class="address">
    </div>

    <!-- 充值类产品 -->
    <div class="rechargebox hide">
        <p class="title2">充值号码</p>
        <div class="sepline"></div>
        <input type="text" name="" class="recharge" placeholder="请输入充值号码">
    </div>

    <div class="rechargebtn">
        <p>立即兑换</p>
    </div>
</div>

<!-- toast -->
<div id="m-toast-pop" class="m-toast-pop">
    <div class="m-toast-inner">
        <div class="m-toast-inner-text" id="m-toast-inner-text"></div>
    </div>
</div>
<div class="popupError displayno">
    <div class="failbox">
        <div class="failinner">
            <img src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/failicon.png" class="iconfail">
            <div class="cm-color-red cm-tx-c info cm-margin-t-10">兑换失败</div>
            <div class="cm-font-size-14 cm-tx-c cm-margin-t-15 exc_err_msg">您已经兑换过其他商品啦</div>
            <div class="botbtnbox cm-f-c-c cm-color-white cm-line-h-1">
                <div class="botbtn cm-f-c-c cm-font-size-16" onclick="toorders()">查询兑换记录</div>
            </div>
        </div>
    </div>
</div>

<script>
    //1-实物商品，2-虚拟商品卡密类，3-虚拟商品-异步直充类，4-实物+虚拟。
    var type = GetQueryString("type");
    if (type == 1) {
        $('.buybox')[0].classList.remove('hide');
    } else {
        $('.rechargebox')[0].classList.remove('hide');
    }
    $('.title')[0].innerText = GetQueryString("name");
    $('.subtitle')[0].innerText = GetQueryString("attr");


    $('.rechargebtn').on('click', function () {
        if (type == 1) {
            if ($('.name')[0].value == "") {
                toastinfo('请填写姓名');
                return;
            }
            if (!isPhoneNumber($('.phone')[0].value)) {
                toastinfo('请填写正确的手机号');
                return;
            }
            if ($('.address')[0].value.length < 10) {
                toastinfo('请填写完整的省市区及详细收货地址');
                return;
            }
        } else {
            if ($('.recharge')[0].value == "") {
                toastinfo('请填写充值账号');
                return;
            }
        }
        $.ajax({
            type: "POST",
            url: "/api/order",
            data: {
                'goods_id': GetQueryString('id'),
                'consignee_name': $('.name')[0].value,
                'consignee_phone': $('.phone')[0].value,
                'consignee_address': $('.address')[0].value,
                'charge_account': $('.recharge')[0].value
            },
            async: true,
            dataType: "json",
            xhrFields: {
                withCredentials: true
            },
            success: function (data, status, xhr) {
                if (data.code == '200') {
                    // console.log(data.msg);
                    jump('success.html');
                } else {
                    $('.popupError').removeClass('displayno')
                    $('.exc_err_msg').html(data.msg);
                    // toastinfo(data.msg);
                }
            },
            error: function (error, xhr, abort) {
                toastinfo("网络错误，请稍后再试！");
            }
        })
    })

    function toorders() {
        $('.popupError').removeClass('displayno');
        jump('../myorder.html?_r=1128');
    }
</script>
</body>
</html>
