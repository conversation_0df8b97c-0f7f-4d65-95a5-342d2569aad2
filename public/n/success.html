<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>兑换成功</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/bootstrap.min.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/icbc.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/common.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/style.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/n-success.css">

    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/jquery-3.1.0.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/bootstrap.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/common.js"></script>
</head>
<body>
<div class="container">
    <div class="sucbox">
        <div class="cm-color-gold cm-tx-c info cm-margin-t-10">兑换成功</div>
        <div class="cm-color-gold cm-font-size-15 cm-tx-c cm-margin-t-10">正在为您处理订单，请耐心等待</div>
        <div class="botbtnbox cm-f-c-c cm-color-white cm-line-h-1">
            <div class=" cm-f-c-c cm-font-size-18" onclick="toorders()"
                 style="color: #FFFFFF;margin-top:2.5rem;padding-bottom:0.05rem;border-bottom: solid .05rem #ccc;">
                查询兑换记录
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    function toorders() {
        jump('../myorder.html?_r=1128');
    }
</script>
</body>
</html>
