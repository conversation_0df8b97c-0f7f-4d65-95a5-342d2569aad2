<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>权益兑换</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/bootstrap.min.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/swiper.min.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/icbc.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/common.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/style.css">
    <link rel="stylesheet" type="text/css" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/tm/index.css">

    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/jquery-3.1.0.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/bootstrap.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/swiper.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/popper.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/common.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/vue.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/jquery.bpopup.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/flex.js"></script>
</head>

<body>
<div class="container">
    <img class="headImg" src="https://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/nxtea/top.png" alt="">
    <div class="inputBox mt30">
        <p class="inputText">充值账号</p>
        <input class="longInput taobaoId" type="text" placeholder="请输入您的手机号">
    </div>
    <div class="confirmBtn">确定</div>
    <div class="conBox">
        <div class="boxHeader">
            <img class="leftImg" src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/tm/left.png" alt="">
            <p class="conTitle">兑换说明</p>
            <img class="rightImg" src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/tm/right.png" alt="">
        </div>
        <p class="colorNum">1.</p>
        <p class="colorOne">奈雪代金券成功充值后，奈雪券将实时自动充值入用户使用中国大陆地区手机号码“奈雪点单”小程序账户内。未注册“奈雪点单”账户的用户，充值成功后，“奈雪点单”将会为用户自动注册。</p>
        <p class="colorNum">2.</p>
        <p class="colorOne">新老用户充值成功后，可打开“奈雪点单”小程序 ，凭充值商品的手机号码登录“奈雪点单”小程序-我的-奈雪券，查看券码信息。</p>
        <p class="colorNum">3.</p>
        <p class="colorOne">用户消费时，打开“奈雪点单”小程序进入点餐页面，选择心仪的商品，进入支付页面时，点击奈雪券在线抵扣相应的金额，若饮品券金额等于或小于消费金额，用户无需额外支付现金，若消费金额大于饮品券抵扣金额，超额部分需用户现金补差价支付。</p>
        <p class="colorNum">4.</p>
        <p class="colorOne">奈雪代金券充值后有效期1年内，以券有效期为准。</p>
        <p class="colorNum" style="color:#E60033;">5.</p>
        <p class="colorOne" style="color:#E60033;">虛拟产品不支持退换，请仔细核对充值账号后进行充值！</p>
    </div>

    <div class="conBox">
        <div class="boxHeader">
            <img class="leftImg" src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/tm/left.png" alt="">
            <p class="conTitle">使用规则</p>
            <img class="rightImg" src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/tm/right.png" alt="">
        </div>
        <p class="colorNum">1.</p>
        <p class="colorOne">适用商品：在有效期内，购买任意商品享受优惠券面值优惠。</p>
        <p class="colorNum">2.</p>
        <p class="colorOne">适用门店：奈雪内地任意门店(不含机场店)。</p>
        <p class="colorNum">3.</p>
        <p class="colorOne">适用场景：仅限奈雪线下门店出示会员码或"奈雪点单"小
            程序白取/外卖使用，不适用于第三方外送服务。</p>
        <p class="colorNum">4.</p>
        <p class="colorOne">在有效期内优惠券仅限单笔订单当次使用，不与其他优惠活动共享，不可兑换现金，不设找零，不可拆分使用，优惠券抵扣金额不予积分。</p>
        <p class="colorNum">5.</p>
        <p class="colorOne">此券不适用运费。</p>
        <p class="colorNum">6.</p>
        <p class="colorOne">客服咨询电话：4006526678。</p>
    </div>

</div>

</div>
<img class="topImg displayNo" src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/tm/top.png" alt="">
<div class="popup displayNo">
    <div class="popupCon">
        <img class="closeImg" src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/cole.png" alt="">
        <p class="text1">请确认您的充值账号</p>
        <p class="text1">充值账号：<span class="tmNumber"></span></p>
        <p class="text2">请仔细核对您填写的充值账号是否正确，避免错充。</p>
        <div class="reset">修改</div>
        <div class="popupConfirm">确认</div>
    </div>
</div>
<!-- toast -->
<div id="m-toast-pop" style="z-index:9999;" class="m-toast-pop">
    <div class="m-toast-inner">
        <div class="m-toast-inner-text" id="m-toast-inner-text"></div>
    </div>
</div>

<script>
    (function () {
        var tmIndex = {
            init: function () {
                this.bind();
            },
            bind: function () {
                //监听页面滚动
                $(document).scroll(function () {
                    var scroH = $(document).scrollTop();
                    var viewH = $(window).height();
                    var contentH = $(document).height();
                    if (scroH > 300) {
                        $('.topImg').removeClass('displayNo')

                    }
                    if (scroH < 300) {
                        $('.topImg').addClass('displayNo')

                    }
                });
                //点击淘宝文字
                $('.seeDetail').on('click', function () {
                    $("html,body").animate({
                        scrollTop: 400
                    }, 500);
                });
                //点击回到顶部按钮
                $('.topImg').on('click', function () {
                    $("html,body").animate({
                        scrollTop: 0
                    }, 500);
                });
                //点击弹窗的关闭按钮
                $('.closeImg').on('click', function () {
                    $('.popup').addClass('displayNo')
                });
                //点击修改按钮弹窗关闭
                $('.reset').on('click', function () {
                    $('.popup').addClass('displayNo')

                });
                //点击确定按钮
                $('.confirmBtn').on('click', function () {
                    var taobaoId = $('.taobaoId').val();
                    if (taobaoId == '') {
                        toastinfo('请输入充值账号！');
                        return false;
                    }
                    $('.tmNumber').text(taobaoId);
                    $('.popup').removeClass('displayNo')
                });
                //点击弹窗的确定按钮
                $('.popupConfirm').on('click', function () {
                    if ($('.popupConfirm').hasClass('noClick')) {
                        return;
                    }
                    var goods_id = GetQueryString('goods_id');
                    var taobaoId = $('.taobaoId').val();
                    $('.popupConfirm').addClass('noClick');
                    $.ajax({//确定绑定
                        type: "POST",
                        url: "/api/order",
                        data: {
                            "goods_id": goods_id,
                            "charge_account": taobaoId,
                        },
                        async: true,
                        dataType: "json",
                        xhrFields: {
                            withCredentials: true
                        },
                        success: function (res, status, xhr) {
                            if (res.code == '200') {
                                $('.popup').addClass('displayNo');
                                $('.popupConfirm').removeClass('noClick');
                                window.location.href = './success.html'
                            } else if (res.code == '3030') {
                                //超限
                                $('.popupConfirm').removeClass('noClick');
                                $('.popup').addClass('displayNo');
                                window.location.href = './error.html?_v=1'//超限跳转
                            } else {
                                toastinfo(res.msg);
                            }
                        },
                        error: function (error, xhr, abort) {
                            $('.popupConfirm').removeClass('noClick');
                            window.location.href = './error.html';

                            toastinfo("网络错误，请稍后再试！");
                        }
                    })

                })
            }

        };
        tmIndex.init();

    })()

</script>
</body>

</html>
