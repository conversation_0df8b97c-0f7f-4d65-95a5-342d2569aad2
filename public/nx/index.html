<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>权益兑换</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/bootstrap.min.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/swiper.min.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/icbc.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/common.css">
    <link rel="stylesheet" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/style.css">
    <link rel="stylesheet" type="text/css" href="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/css/tm/index.css">

    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/jquery-3.1.0.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/bootstrap.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/swiper.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/popper.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/common.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/vue.min.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/jquery.bpopup.js"></script>
    <script src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/js/flex.js"></script>
</head>

<body>
<div class="container">
    <img class="headImg" src="https://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/nxtea/top.png" alt="">
    <div class="inputBox mt30">
        <p class="inputText">兑换码</p>
        <input class="longInput card_pwd" type="text" placeholder="点击输入兑换码">
    </div>
    <div class="inputBox">
        <p class="inputText">手机号</p>
        <input class="longInput mobile" type="text" placeholder="点击输入手机号">
    </div>
    <div class="inputBoxShort">
        <p class="inputText">校验码</p>
        <input class="longInput captcha" type="text" placeholder="点击输入图片校验码">
    </div>
    <div class="inputBoxRight">
        <img class="codeImg" src="" alt="">
    </div>
    <div class="inputBoxShort">
        <p class="inputText">验证码</p>
        <input class="longInput sms_captcha" type="text" placeholder="点击输入短信验证码" maxlength="6">
    </div>
    <div class="inputBoxRight ">
        <div class="getCode ">获取验证码</div>
    </div>
    <div class="confirmBtn indexConfirm">确定</div>

    <div class="conBox">
        <div class="boxHeader">
            <img class="leftImg" src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/tm/left.png" alt="">
            <p class="conTitle">兑换说明</p>
            <img class="rightImg" src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/tm/right.png" alt="">
        </div>
        <p class="colorNum">1.</p>
        <p class="colorOne">本礼品券由北京明苑风华文化传媒有限公司提供，下单后视为您同意授权您的手机号码给该公司为您提供充值服务。</p>
        <p class="colorNum">2.</p>
        <p class="colorOne">
            奈雪代金券成功充值后，奈雪券将实时自动充值入用户使用中国大陆地区手机号码“奈雪点单”小程序账户内。未注册“奈雪点单”账户的用户，充值成功后，“奈雪点单”将会为用户自动注册。</p>
        <p class="colorNum">3.</p>
        <p class="colorOne">新老用户充值成功后，可打开“奈雪点单”小程序
            ，凭充值商品的手机号码登录“奈雪点单”小程序-我的-奈雪券，查看券码信息。</p>
        <p class="colorNum">4.</p>
        <p class="colorOne">
            用户消费时，打开“奈雪点单”小程序进入点餐页面，选择心仪的商品，进入支付页面时，点击奈雪券在线抵扣相应的金额，若饮品券金额等于或小于消费金额，用户无需额外支付现金，若消费金额大于饮品券抵扣金额，超额部分需用户现金补差价支付。</p>
        <p class="colorNum">5.</p>
        <p class="colorOne">奈雪代金券充值后有效期1年内，以券有效期为准。</p>
        <p class="colorNum" style="color:#E60033;">6.</p>
        <p class="colorOne" style="color:#E60033;">虛拟产品不支持退换，请仔细核对充值账号后进行充值！</p>
    </div>

    <div class="conBox">
        <div class="boxHeader">
            <img class="leftImg" src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/tm/left.png" alt="">
            <p class="conTitle">使用规则</p>
            <img class="rightImg" src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/tm/right.png" alt="">
        </div>
        <p class="colorNum">1.</p>
        <p class="colorOne">适用商品：在有效期内，购买任意商品享受优惠券面值优惠。</p>
        <p class="colorNum">2.</p>
        <p class="colorOne">适用门店：奈雪内地任意门店(不含机场店)。</p>
        <p class="colorNum">3.</p>
        <p class="colorOne">适用场景：仅限奈雪线下门店出示会员码或"奈雪点单"小
            程序白取/外卖使用，不适用于第三方外送服务。</p>
        <p class="colorNum">4.</p>
        <p class="colorOne">在有效期内优惠券仅限单笔订单当次使用，不与其他优惠活动共享，不可兑换现金，不设找零，不可拆分使用，优惠券抵扣金额不予积分。</p>
        <p class="colorNum">5.</p>
        <p class="colorOne">此券不适用运费。</p>
        <p class="colorNum">6.</p>
        <p class="colorOne">客服咨询电话：4006526678。</p>
    </div>
</div>

</div>
<img class="topImg displayNo" src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/tm/top.png" alt="">
<div class="popup displayNo">
    <div class="popupCon">
        <img class="closeImg" src="http://star6.oss-cn-beijing.aliyuncs.com/gift/public/images/cole.png" alt="">
        <p class="popupTextOne"> 兑换码已使用</p>
        <p class="popupTextTwo">您可点击“查询订单”查询兑换记录</p>
        <div class="seeOrderlist">查询订单</div>
    </div>
</div>
<!-- toast -->
<div id="m-toast-pop" style="z-index:9999;" class="m-toast-pop">
    <div class="m-toast-inner">
        <div class="m-toast-inner-text" id="m-toast-inner-text"></div>
    </div>
</div>

<script>
    (function () {
        var tmIndex = {
            init: function () {
                this.bind();
                this.getCodImg();
                this.getCard();
            },
            bind: function () {
                //监听页面滚动
                $(document).scroll(function () {
                    var scroH = $(document).scrollTop();
                    var viewH = $(window).height();
                    var contentH = $(document).height();

                    if (scroH > 300) {
                        $('.topImg').removeClass('displayNo');
                    }
                    if (scroH < 300) {
                        $('.topImg').addClass('displayNo');
                    }
                });
                //点击回到顶部按钮
                $('.topImg').on('click', function () {
                    $("html,body").animate({
                        scrollTop: 0
                    }, 500);
                });
                //点击弹窗的关闭按钮
                $('.closeImg').on('click', function () {
                    $('.popup').addClass('displayNo');
                });
                //点击获取验证码
                $('.getCode').on('click', function () {
                    var phoneNum = $('.mobile').val();
                    var captcha = $('.captcha').val();
                    //首先校验手机号
                    if (isPhoneNumber(phoneNum)) {
                        if (captcha == '' || captcha.length != 4) {
                            toastinfo('请输入正确的校验码！');
                            return;
                        }
                        $.ajax({//发送验证码
                            type: "POST",
                            url: "/api/captcha",
                            data: {
                                'mobile': phoneNum,
                                'captcha': captcha,//图形验证码
                                'act': 'Sd5lQU9Jv4TMuHKqj/hnBA==',
                            },
                            async: true,
                            dataType: "json",
                            xhrFields: {
                                withCredentials: true
                            },
                            success: function (res, status, xhr) {
                                if (res.code == '200') {
                                    tmIndex.countDown(60);
                                } else {
                                    $('.codeImg').click();
                                    toastinfo(res.msg);
                                }
                            },
                            error: function (xhr, textStatus, errorThrown) {
                                if (xhr.status == 429) {
                                    $('.codeImg').click();
                                    toastinfo("操作太频繁了，请稍后再试！");
                                } else {
                                    toastinfo("网络错误，请稍后再试！");
                                }
                            }
                        })

                    } else {
                        toastinfo('请输入正确的手机号！')
                    }
                });
                //点击查询订单
                $('.seeOrderlist').on('click', function () {
                    $('.popup').addClass('displayno');
                    jump('../myorder.html?_r=1128');
                });
                //点击校验码获取校验码
                $('.codeImg').on('click', function () {

                    var src = $('.codeImg').attr('src').split('?');
                    if (src.length > 1) {
                        $('.codeImg').attr('src', src[0] + "?" + src[1] + "?" + Math.random());
                    } else {
                        $('.codeImg').attr('src', src[0] + "?" + Math.random());
                    }

                });
                //点击首页的确定按钮
                $('.indexConfirm').on('click', function () {
                    var phoneNum = $('.mobile').val();
                    var card_pwd = $('.card_pwd').val();//兑换码
                    var captcha = $('.captcha').val();//校验码
                    var sms_captcha = $('.sms_captcha').val();//短信验证码
                    if (card_pwd == '') {
                        return toastinfo('请输入兑换码！');
                    }
                    if (!isPhoneNumber(phoneNum)) {
                        return toastinfo('请输入正确的手机号！');
                    }
                    if (captcha == '') {
                        return toastinfo('请输入校验码！');
                    }
                    if (sms_captcha == '') {
                        return toastinfo('请输入验证码！');
                    }
                    if ($('.indexConfirm').hasClass('noClick')) {
                        return;
                    }
                    $('.indexConfirm').addClass('noClick');
                    $.ajax({
                        type: "POST",
                        url: "/api/login",
                        data: {
                            "card_pwd": card_pwd,
                            "captcha": captcha,
                            "mobile": phoneNum,
                            "sms_captcha": sms_captcha,
                            "act": 'Uk6mT6aqI8E57G7mkkkc8A==', //Nxtea
                        },
                        async: true,
                        dataType: "json",
                        xhrFields: {
                            withCredentials: true
                        },
                        success: function (res, textStatus, xhr) {
                            $('.indexConfirm').removeClass('noClick');
                            if (res.code == '200') {
                                if (res.data.exchange_state == '3') {
                                    $('.popup').removeClass('displayNo');
                                } else {
                                    window.location.href = res.data.rdr_url + '?goods_id=' + res.data.goods_id
                                }
                            } else {
                                $('.codeImg').click();
                                toastinfo(res.msg);
                            }
                        },
                        //有以下三个参数：XMLHttpRequest 对象、错误信息、（可选）捕获的异常对象。
                        //如果发生了错误，错误信息（第二个参数）除了得到 null 之外，还可能是 "timeout", "error", "notmodified" 和 "parsererror"。
                        error: function (xhr, textStatus, errorThrown) {
                            $('.indexConfirm').removeClass('noClick');
                            if (xhr.status == 429) {
                                $('.codeImg').click();
                                toastinfo("操作太频繁了，请稍后再试！");
                            } else {
                                toastinfo("网络错误，请稍后再试！");
                            }
                        }
                    })

                });

            },
            getCodImg: function () {
                $.ajax({
                    type: "GET",
                    url: "/api/captcha-img",
                    async: true,
                    xhrFields: {
                        withCredentials: true
                    },
                    success: function (res, status, xhr) {
                        $('.codeImg').attr('src', res)

                    },
                    error: function (xhr, textStatus, errorThrown) {

                    }
                })

            },
            countDown: function (timeNum) {
                if ($(".getCode").hasClass("noClick")) {
                    return;
                }
                $('.getCode').addClass('noClick');
                var time;
                var num = timeNum;
                time = setInterval(function () {
                    num = num - 1;
                    if (num <= 0) {
                        num = timeNum;
                        $(".getCode").html('获取验证码');
                        $('.getCode').removeClass('noClick');
                        clearInterval(time);
                        return false;
                    } else {
                        $('.getCode').html(num);
                    }
                }, 1000)
            },
            getCard: function () {
                var card = GetQueryString('s');
                if (card) {
                    card = card.split(/[.,，。]/)[0];
                    if (card) {
                        $('.card_pwd').val(card);
                    }
                }
            }
        };
        tmIndex.init()

    })()

</script>
</body>

</html>
